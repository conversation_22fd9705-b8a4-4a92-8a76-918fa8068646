/**
 * Represents timing information for quantum job execution
 */
export interface TimeStamps {
  createdAt: string; // ISO date string
  endedAt: string;   // ISO date string
  executionDuration: number; // Duration in seconds
}

/**
 * Status values for quantum jobs
 */
export type JobStatus = 'COMPLETED' | 'INITIALIZED' | 'RUNNING' | 'FAILED' | 'CANCELLED';

/**
 * Experiment types supported by the quantum platform
 */
export type ExperimentType = 'gate_model' | 'annealing' | 'analog';

/**
 * Quantum computing providers/vendors
 */
export type Provider = 'aws' | 'ibm' | 'google' | 'rigetti' | 'ionq';

/**
 * Main quantum job interface
 */
export interface QuantumJob {
  // Core identification
  _id: string;
  user: string;
  
  // Timing information (optional for incomplete jobs)
  timeStamps?: TimeStamps;
  
  // Status tracking
  status: JobStatus;
  qbraidStatus: JobStatus;
  message: string;
  
  // Device information
  vendorDeviceId: string;
  qbraidDeviceId: string;
  device: string;
  vendor: Provider;
  provider: Provider;
  
  // Branding/UI assets
  logo: string;
  logoDark: string;
  
  // Job configuration
  experimentType: ExperimentType;
  shots: number;
  escrow: number;
  cost?: number; // Only present for completed jobs
  
  // Circuit information
  circuitNumQubits: number;
  circuitBatchNumQubits: number[];
  circuitBatchDepth: number[];
  
  // Quantum-specific parameters (arrays for batch operations)
  sites: any[];
  rabiArray: any[];
  detuningArray: any[];
  phaseArray: any[];
  filling: any[];
  
  // Queue information
  queuePosition: number | null;
  queueDepth: number | null;
  
  // Results
  measurements: any[];
  measurementCounts: any; // null for incomplete jobs
  
  // Flags
  prelight: boolean;
  
  // Timestamps
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

/**
 * Type for an array of quantum jobs (typical API response)
 */
export type QuantumJobList = QuantumJob[];

/**
 * Utility type for filtering jobs by status
 */
export type CompletedQuantumJob = QuantumJob & {
  timeStamps: TimeStamps;
  cost: number;
  measurementCounts: any;
};

/**
 * Type guard to check if a job is completed
 */
export function isCompletedJob(job: QuantumJob): job is CompletedQuantumJob {
  return job.status === 'COMPLETED' && 
         job.timeStamps !== undefined && 
         job.cost !== undefined;
}

// Quantum jobs cache types
export interface QuantumJobsCacheData {
  jobs: QuantumJob[];
  lastUpdated: Date;
  isLoading: boolean;
  error?: string;
}