// Billing Model
interface Billing {
  _id: string;
  user: string; // ref: 'users'
  email: string;
  stripeId?: string;
  oneTimeTransactions: string[];
  allTransactions: string[];
  appliedPromos: string[];
  subscriptionTransactions: string[];
  pastSubscriptions: object[];
  currentSubscriptionStripeId?: string;
  currentSubscriptionEndDate?: Date;
  cancelAtCycleEnd?: boolean;
  activePlanName: string;
  currentSubscriptionPriceId?: string;
  currentSubscriptionRecuring?: string;
  currentSubscriptionStartDate?: Date;
  tempPlanName?: string;
  tempPlanAction?: string;
  creditCardOnStripe: boolean;
  qBraidCredits: number;
  cummulativeQBraidCredits: number;
  cumulativePromotionalQBraidCredits: number;
  createdAt: Date;
  updatedAt: Date;
}

// Devices Model
interface Device {
  _id: string;
  name: string;
  qrn: string;
  vrn: string;
  providerId: string; // ref: 'providers' - Which provider owns this device
  provider:
    | 'AWS'
    | 'IBM'
    | 'IQM'
    | 'IonQ'
    | 'OQC'
    | 'QuEra'
    | 'Rigetti'
    | 'NEC'
    | 'qBraid'
    | 'Azure'
    | 'Pasqal'
    | 'Quantinuum'
    | 'Equal1';
  about?: string;
  deviceDescription?: string;
  deviceImage?: string;
  spec: string[];
  vendor: 'AWS' | 'IBM' | 'qBraid' | 'Azure';
  runInputTypes: string[];
  paradigm: string;
  type: 'Simulator' | 'QPU';
  numberQubits?: number;
  modality?: string;
  processorType?: string;
  noiseModels: string[];
  status: 'ONLINE' | 'OFFLINE' | 'RETIRED';
  isAvailable: boolean;
  nextAvailable?: Date;
  availabilityCD?: string;
  pendingJobs?: number;
  avgQueueTime?: number;
  statusMsg?: string;
  statusRefresh?: Date;
  pricing: {
    perTask?: number;
    perShot?: number;
    perMinute?: number;
  };
  visibility: string;
  permissionsNodes?: string[];
  whiteListedDomains?: string[];
  blackListedDomains?: string[];
  activeVersion?: string;
  createdAt: Date;
  updatedAt: Date;
}

// OrganizationUsers Model
interface OrganizationUser {
  _id: string;
  user?: string; // ref: 'users'
  organization: string; // ref: 'organizations'
  role: string;
  email: string;
  invited: boolean;
  accepted: boolean;
  orgBalance?: number;
  deductionRequest: {
    status: 'none' | 'pending' | 'accepted' | 'rejected';
    requestedAt?: Date;
    creditAmount?: number;
  };
  permissionsNodes: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Organizations Model
interface Organization {
  _id: string;
  name: string;
  orgEmail: string;
  courses: string[]; // ref: 'courses'
  classes: string[]; // ref: 'classes'
  description?: string;
  logo?: string;
  logoDark?: string;
  owner: string; // ref: 'users'
  ownerEmail: string;
  billing?: string; // ref: 'organization-billing'
  devices: string[]; // ref: 'devices' - DEPRECATED: use deviceAccess instead
  gitHubOrgUrl?: string;
  gitHubOrgToken?: string;

  // Single provider relationship
  providerId?: string; // ref: 'providers' - The provider this org owns/represents
  providerStatus: 'none' | 'pending' | 'approved' | 'rejected';
  ownsProvider: boolean; // true if org created/owns the provider

  // Device access control for external devices
  deviceAccess: {
    read: string[]; // deviceIds org can read from
    write: string[]; // deviceIds org can write to
    custom: string[]; // deviceIds with custom permissions
  };

  metadata: {
    acknowledgedTerms: boolean;
    marketing: boolean;
    tags: object;
    newOrg: boolean;
    providerOnboardingCompleted?: boolean;
    providerOnboardingStep?: 'pending' | 'approved' | 'rejected';
    lastProviderCheck?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

// QuantumJob Model
interface QuantumJob {
  _id: string;
  user: string; // ref: 'users'
  email?: string;
  vendor?: string;
  provider?: string;
  qbraidJobId?: string;
  vendorJobId?: string;
  qbraidDeviceId?: string;
  vendorDeviceId?: string;
  slurmJobId?: number;
  device?: string; // ref: 'devices'
  circuitNumQubits?: number;
  circuitDepth?: number;
  circuitBatchNumQubits?: any[];
  circuitBatchDepth?: any[];
  openQasm?: string;
  numVariables?: number;
  numAtoms?: number;
  sites?: any[];
  rabiArray?: any[];
  detuningArray?: any[];
  phaseArray?: any[];
  filling?: any[];
  shots?: number;
  escrow?: number;
  cost?: number;
  status?: string;
  qbraidStatus?: string;
  statusText?: string;
  logo?: string;
  logoDark?: string;
  queuePosition?: number;
  queueDepth?: number;
  visualizationUrl?: string;
  measurementCounts?: object;
  measurements?: any[];
  prelight: boolean;
  timeStamps: {
    createdAt?: Date;
    endedAt?: Date;
    queuedAt?: Date;
    executionDuration?: number;
  };
  tags?: object;
  experimentType?: 'gate_model' | 'ahs' | 'annealing' | 'photonic' | 'other';
  createdAt: Date;
  updatedAt: Date;
}

// User Model
interface User {
  _id: string;
  jupyterUsername: string;
  email: string;
  cognitoId: string;
  xanaduCognitoId?: string;
  qusteamCognitoId?: string;
  equal1CognitoId?: string;
  queraCognitoId?: string;
  userName: string;
  gtagId: string;
  firstName: string;
  lastName: string;
  allLogin?: string; // ref: 'all-logins'
  personalInformation: {
    workHistory: string;
    favQC: string;
    favQCFramework: string[];
    profilePhoto?: string;
    levelOfEducation: string;
    country: string;
    phoneNumber?: number;
    bio: string;
    linkedIn: string;
    githubUrl: string;
    twitterUrl: string;
    googleScholar: string;
    personalWebsite: string;
    interests: string[];
    karma: number;
    isPrivate: boolean;
  };
  emailVerified: boolean;
  notifications: string[];
  billing?: string; // ref: 'billings'
  analytics?: string; // ref: 'user-analytics'
  quantum: {
    quantumJobs: string[]; // ref: 'quantum-jobs'
    quantumConfig?: string; // ref: 'quantum-config'
  };
  learn: {
    registeredCourses: string[]; // ref: 'user-courses-interactions'
    ownCourses: string[]; // ref: 'courses'
    questionsCreated: string[]; // ref: 'questions-created'
  };
  lab: {
    codeSnippets: string[]; // ref: 'code-snippets'
    environments: string[]; // ref: 'environments'
    pinnedEnvironments: string[];
    sortedEnvironments: string[]; // ref: 'environments'
    config?: string; // ref: 'lab-config'
    blog: string[]; // ref: 'courses'
  };
  codeSnippetsUpvoted: string[]; // ref: 'code-snippets'
  codeSnippetsBookmarked: string[]; // ref: 'code-snippets'
  blogsSubmitted: string[]; // ref: 'courses'
  blogsBookmarked: string[]; // ref: 'courses'
  blogsViewed: string[]; // ref: 'courses'
  blogsUpvoted: string[]; // ref: 'courses'
  sharedNotebooks: string[];
  papersBookmarked: string[];
  permissionsNodes: string[];
  metadata: {
    cookieConsentGiven?: boolean;
    cookieConsentObject: {
      analytics_storage: 'denied' | 'granted';
      personalization_storage: 'denied' | 'granted';
    };
    acknowledgedTerms: boolean;
    marketing: boolean;
    tags: object;
    newUser: boolean;
    tourUser: boolean;
    lowCreditAlert: boolean;
    defaultLanguage?: string;
    acceptedIntelTerms?: boolean;
    gitHubOrgUrl?: string;
    gitHubCypherText?: string;
    courseBuilderIntroduced?: boolean;
    miningDetected?: boolean;
  };
  tempRefreshToken?: string;
  otp?: string;
  verifyWithOtp: boolean;
  apiKey?: string;
  necId?: string;
  necPassword?: string;
  diskUsage: {
    totalGB: number;
    quotaGB: number;
    timestamp: Date;
    storageStatus: 'Storage' | 'Retrieving' | 'Filestore';
  };
  role: 'user' | 'admin';
  clusterId: string;
  createdAt: Date;
  updatedAt: Date;
}
