export interface UserProfile {
  _id?: { $oid: string };
  personalInformation?: {
    workHistory?: string;
    favQC?: string;
    favQCFramework?: string[];
    levelOfEducation?: string;
    country?: string;
    bio?: string;
    linkedIn?: string;
    githubUrl?: string;
    twitterUrl?: string;
    googleScholar?: string;
    personalWebsite?: string;
    interests?: string[];
    karma?: number;
    isPrivate?: boolean;
  };
  quantum?: {
    quantumJobs?: any[];
  };
  learn?: {
    registeredCourses?: { $oid: string }[];
    ownCourses?: any[];
    questionsCreated?: any[];
  };
  lab?: {
    codeSnippets?: any[];
    environments?: any[];
    pinnedEnvironments?: any[];
    sortedEnvironments?: any[];
    blog?: any[];
  };
  metadata?: {
    cookieConsentObject?: {
      analytics_storage?: string;
      personalization_storage?: string;
    };
    cookieConsentGiven?: boolean;
    acknowledgedTerms?: boolean;
    marketing?: boolean;
    newUser?: boolean;
    tourUser?: boolean;
    lowCreditAlert?: boolean;
    defaultLanguage?: string | null;
    acceptedIntelTerms?: boolean;
    courseBuilderIntroduced?: boolean;
    miningDetected?: boolean;
    tags?: Record<string, any>;
  };
  diskUsage?: {
    totalGB?: number;
    quotaGB?: number;
    timestamp?: { $date: string };
    storageStatus?: string;
  };
  firstName?: string;
  lastName?: string;
  emailVerified?: boolean;
  notifications?: any[];
  billing?: { $oid: string };
  codeSnippetsUpvoted?: any[];
  codeSnippetsBookmarked?: any[];
  blogsSubmitted?: any[];
  blogsBookmarked?: any[];
  blogsViewed?: any[];
  blogsUpvoted?: any[];
  sharedNotebooks?: any[];
  papersBookmarked?: any[];
  permissionsNodes?: string[];
  tempRefreshToken?: string;
  otp?: string;
  verifyWithOtp?: boolean;
  role?: string;
  jupyterUsername?: string;
  cognitoId?: string;
  email?: string;
  userName?: string;
  gtagId?: string;
  apiKey?: string;
  allLogin?: { $oid: string };
  createdAt?: { $date: string };
  updatedAt?: { $date: string };
  __v?: number;
  clusterId?: string;
}
