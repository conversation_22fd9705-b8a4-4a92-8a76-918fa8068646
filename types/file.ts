// File upload and security types

export interface SecurityScanResult {
  isSafe: boolean;
  issues: SecurityIssue[];
  scanTime: Date;
}

export interface SecurityIssue {
  type: 'virus' | 'malware' | 'suspicious' | 'invalid_type';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
}

export interface FileSecurityConfig {
  maxFileSize: number; // in bytes
  allowedTypes: string[];
  requireVirusScan: boolean;
  scanTimeout?: number; // in milliseconds
}

export interface S3UploadConfig {
  bucket: string;
  region: string;
  accessKey?: string;
  secretKey?: string;
  presignedUrl?: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: FileMetadata;
}

export interface FileMetadata {
  originalName: string;
  size: number;
  type: string;
  lastModified: Date;
  uploadedAt: Date;
  checksum?: string;
}

export interface FileUploadConfig {
  maxFileSize: number;
  allowedTypes: string[];
  multiple?: boolean;
  onUploadStart?: (files: File[]) => void;
  onUploadProgress?: (progress: number) => void;
  onUploadComplete?: (results: FileUploadResult[]) => void;
  onUploadError?: (error: string) => void;
  securityConfig?: FileSecurityConfig;
}

export interface FileUploadResult {
  file: File;
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: FileMetadata;
}

export interface BatchUploadResult {
  totalFiles: number;
  successful: number;
  failed: number;
  results: FileUploadResult[];
}

export interface ImageUploadConfig {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0-1
  allowedTypes: string[];
  maxSize: number; // in bytes
  generateThumbnails?: boolean;
  thumbnailSize?: number;
}

export interface ImageUploadResult {
  originalFile: File;
  compressedFile?: File;
  thumbnails?: { [size: string]: string };
  url: string;
  success: boolean;
  error?: string;
}