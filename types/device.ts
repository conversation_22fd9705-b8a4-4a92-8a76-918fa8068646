// Device-related types for use across the app

/**
 * Represents a quantum device and its properties.
 */
export interface DeviceData {
  requestedByOrganization: any;
  name: string;
  type: string;
  paradigm: string;
  modality: string;
  processorType: string;
  numberQubits: string;
  qrn: string;
  vendor: string;
  providerId: string | { _id: string; provider: string }; // Updated to new structure
  vrn: string;
  runInputTypes: string | string[] | undefined;
  noiseModels?: string | string[];
  status: string;
  visibility: string;
  whiteListedDomains?: string | string[];
  blackListedDomains?: string | string[];
  pricing: {
    perMinute: string;
    perTask: string;
    perShot: string;
  };
  deviceDescription: string;
  about: string;
  deviceImage: string;
  technicalSpecifications: string;
  // Optional notes or suggestions provided by user
  notes?: string;

  isAvailable: string;
  // Added for admin and pending device UI
  requestedBy?: string;
  verified?: boolean;
  statusMsg?: string;
  pricingType?: 'perMinute' | 'perTaskPerShot';
  // Stores pending edit requests (for edit device requests)
  pendingEdits?: Record<string, any> | null;
}

/**
 * Props for the DeviceCard component.
 */
export interface DeviceCardProps {
  qrn: string;
  _id?: string; // Device ID used for access control
  name: string;
  type: string;
  paradigm: string;
  deviceDescription: string;
  numberQubits: string;
  status: string;
  providerId: string | { _id: string; provider: string }; // Updated to new structure
  modality: string;
  processorType: string;
  pricing: {
    perMinute: string;
    perTask: string;
    perShot: string;
  };
  vendor: string;
  pendingJobs: number;
  runInputTypes?: string | string[];
  noiseModels: string[];
  defaultTab?: string;
  /**
   * Optional handler to trigger when the user clicks the "Edit" action. This is primarily
   * used by the devices listing page to open the edit-device modal.
   */
  onEdit?: () => void;
  // Added for pending device UI
  verified?: boolean;
  visibility?: string;
  requestedBy?: string;
  statusMsg?: string;
  // Added for access control
  isAvailable?: string;
  whiteListedDomains?: string | string[];
  blackListedDomains?: string | string[];
  // Optional notes or suggestions provided by user
  notes?: string;
  // Added for pending edits
  pendingEdits?: Record<string, any> | null;
}

// Device management types
export type AvailableDevice = {
  modality: string;
  _id: string;
  name: string;
  provider: string;
  type: string;
  status: string;
  isAvailable: boolean;
  deviceDescription: string;
  logo: {
    light: string;
    dark: string;
  };
  deviceAbout: string;
  numberQubits: number;
  processorType: string;
  architecture: string;
  vendor: string;
  paradigm: string;
  about: string;
  spec: string[];
  runInputTypes: string[];
  noiseModels: string[];
  qrn: string;
};

export interface PublicDevice {
  _id: string;
  name: string;
  qrn: string;
  provider: string; // Provider name like "AWS", "IBM", etc.
  type: 'QPU' | 'Simulator';
  numberQubits?: number;
  status: 'ONLINE' | 'OFFLINE';
  isAvailable: boolean;
  modality?: string;
  processorType?: string;
  deviceDescription?: string;
  deviceAbout?: string;
  logo?: {
    light: string;
    dark?: string;
  };
  // Technical specifications (safe to show)
  vendor?: string;
  paradigm?: string;
  about?: string;
  spec?: string[];
  runInputTypes?: string[];
  noiseModels?: string[];
}

export interface DevicePatch {
  name?: string;
  type?: string;
  paradigm?: string;
  modality?: string;
  processorType?: string;
  numberQubits?: string;
  vendor?: string;
  objArg?: string;
  runPackage?: string;
  runInputTypes?: string | string[];
  noiseModels?: string | string[];
  status?: string;
  visibility?: string;
  whiteListedDomains?: string | string[];
  blackListedDomains?: string | string[];
  pricing?: {
    perMinute?: string;
    perTask?: string;
    perShot?: string;
  };
  deviceDescription?: string;
  about?: string;
  providerDescription?: string;
  deviceImage?: string;
  technicalSpecifications?: string;
  notes?: string;
  isAvailable?: string;
  pricingType?: 'perMinute' | 'perTaskPerShot';
}
export enum FieldType {
  TEXT = 'text',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  TEXTAREA = 'textarea',
  DATE = 'date',
  TIME = 'time',
}
export enum FieldValidation {
  REQUIRED = 'required',
  OPTIONAL = 'optional',
  EMAIL = 'email',
  URL = 'url',
  PHONE = 'phone',
}
export interface UseDeviceFieldProps {
  name: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  description?: string;
  validation?: FieldValidation;
  options?: Array<{ value: string; label: string }>;
  disabled?: boolean;
  className?: string;
}
