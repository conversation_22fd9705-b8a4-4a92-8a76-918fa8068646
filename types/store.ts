// Store and state management types

export interface FilterOptions {
  status?: string[];
  providers?: string[];
  paradigms?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface DevicePerformanceData {
  deviceId: string;
  deviceName: string;
  uptime: number;
  errorRate: number;
  avgExecutionTime: number;
  totalJobs: number;
  successfulJobs: number;
  failedJobs: number;
  dateRange: {
    start: Date;
    end: Date;
  };
}

export interface QubitUsageData {
  date: string;
  totalQubitsUsed: number;
  avgQubitsPerJob: number;
  peakQubitsUsed: number;
  deviceBreakdown: {
    [deviceId: string]: {
      qubitsUsed: number;
      jobsRun: number;
    };
  };
}