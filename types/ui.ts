// UI component types for use across the app
import type { ReactNode, ComponentType } from 'react';

export interface PageHeaderProps {
  title: string;
  description?: string;
  children?: ReactNode;
}

export interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  children: ReactNode;
}

export interface BadgeProps {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
  children: ReactNode;
}

export interface SidebarNavItem {
  title: string;
  href?: string;
  icon?: ReactNode;
  disabled?: boolean;
  external?: boolean;
}

export interface SidebarNavProps {
  items: SidebarNavItem[];
  className?: string;
}

export interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export interface LoadingStateProps {
  message?: string;
  className?: string;
}

export interface ProfileCompletionBannerProps {
  completionPercentage: number;
  onDismiss?: () => void;
  className?: string;
}

export interface DateRangePickerProps {
  value?: {
    from: Date;
    to: Date;
  };
  onChange?: (value: { from: Date; to: Date } | undefined) => void;
  className?: string;
}

export interface GradientButtonProps extends ButtonProps {
  gradient?: 'primary' | 'secondary';
}

export interface AccessDeniedProps {
  message?: string;
  onBack?: () => void;
  className?: string;
}

export interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export type ChartConfig = {
  [key: string]: {
    label?: string;
    color?: string;
  };
};

export interface CustomSelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface CustomSelectProps {
  options: CustomSelectOption[];
  value?: string | number;
  onChange: (value: string | number) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export type TableSkeletonProps = {
  rows?: number;
  columns?: number;
  className?: string;
};

export type PaginationControlsProps = {
  page: number;
  totalPages: number;
  onPrevious: () => void;
  onNext: () => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  pageSizeOptions?: number[];
  disabled?: boolean;
  totalItems?: number;
};

export interface DataTableProps<T = any> {
  data?: T[];
  columnsOverride?: any[];
  hideToolbar?: boolean;
  headerTitle?: string;
  HeaderIcon?: ComponentType<{ className?: string }>;
  isLoading?: boolean;
  initialState?: {
    pagination?: {
      pageSize: number;
      pageIndex: number;
    };
    sorting?: {
      id: string;
      desc: boolean;
    }[];
  };
  onPaginationChange?: (updater: any) => void;
  onExportPDF?: () => void;
}
