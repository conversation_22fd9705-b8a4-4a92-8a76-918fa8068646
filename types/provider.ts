const sample = {
  json: {
    assets: { sdkLinks: [] },
    status: 'public',
    ownerEmails: [],
    _id: '67175ce122b43201cd5191fa',
    provider: 'Xanadu',
    devices: [],
    __v: 0,
    logoUrl: 'https://qbraid-static.s3.amazonaws.com/logos/xanadu-tall.png',
    logoUrlDark: 'https://qbraid-static.s3.amazonaws.com/logos/xanadu_tall_dark.png',
    updatedAt: '2025-07-23T20:13:00.047Z',
  },
};
const sample2 = {
  json: {
    assets: { sdkLinks: [] },
    status: 'public',
    ownerEmails: [],
    _id: '67175a7822b43201cd5191c1',
    provider: 'AWS',
    providerDescription: 'Amazon Braket provides GPU powered simulators for quantum computing',
    about: 'https://qbraid.webflow.io/quantum-pricing/amazon-braket',
    devices: [
      {
        spec: [
          'DM1 is an on-demand, high-performance, density matrix simulator. It can simulate circuits of up to 17 qubits.',
          'DM1 has a maximum runtime of 6 hours, a default of 35 concurrent quantum tasks, and  a maximum of 50 concurrent quantum tasks.',
        ],
        _id: '67175a7822b43201cd5191c2',
        name: 'DM1',
        des: 'Density matrix simulator (DM1)',
        about: 'https://docs.aws.amazon.com/braket/latest/developerguide/braket-simulator-dm1.html',
      },
      {
        spec: [
          'SV1 is an on-demand, high-performance, universal state vector simulator. It can simulate circuits of up to 34 qubits. You can expect a 34-qubit, dense, and square circuit (circuit depth = 34) to take approximately 1–2 hours to complete, depending on the type of gates used and other factors.  Circuits with all-to-all gates are well suited for SV1. It returns results  in forms such as a full state vector or an array of amplitudes.',
        ],
        _id: '67175a7822b43201cd5191c3',
        name: 'SV1',
        des: 'State vector simulator (SV1)',
        about: 'https://docs.aws.amazon.com/braket/latest/developerguide/braket-simulator-sv1.html',
      },
      {
        spec: [
          'TN1 is an on-demand, high-performance, tensor network simulator. TN1 can simulate certain circuit types with up to 50 qubits and a circuit depth of 1,000 or smaller. TN1 is  particularly powerful for sparse circuits, circuits with local gates, and other circuits  with special structure, such as quantum Fourier transform (QFT) circuits. TN1 operates in two phases. First, the rehearsal phase attempts to identify an efficient computational path for your circuit so TN1 can estimate the runtime of the next stage, which is called the contraction phase. If the estimated contraction time exceeds the TN1 simulation runtime limit, TN1 does not attempt contraction. TN1 has a runtime limit of 6 hours. It is limited to a maximum of 10 (5 in eu-west-2) concurrent quantum tasks.',
        ],
        _id: '67175a7822b43201cd5191c4',
        name: 'TN1',
        des: 'Tensor network simulator (TN1)',
        about: 'https://docs.aws.amazon.com/braket/latest/developerguide/braket-simulator-tn1.html',
      },
    ],
    __v: 0,
    logoUrl: 'https://qbraid-static.s3.amazonaws.com/lab-docs/logos-sqr/aws_light_theme.png',
    logoUrlDark: 'https://qbraid-static.s3.amazonaws.com/lab-docs/logos-full/aws_dark.png',
    updatedAt: '2025-07-23T20:13:00.047Z',
  },
};
//based on sample 1 and 2, create a type for the provider response
export type ProviderResponseType = {
  assets: { sdkLinks: string[] };
  status: 'public' | 'private' | 'removed' | 'pending_approval';
  ownerEmails: string[];
  _id: string;
  provider: string;
  providerDescription?: string;
  about?: string;
  devices: DeviceResponseType[];
  __v: number;
  logoUrl: string;
  logoUrlDark: string;
  updatedAt: string;
};

export type DeviceResponseType = {
  spec: string[];
  _id: {
    $oid: string;
  };
  providerId?: string | { _id: string; provider: string }; // Updated to match new structure
  name: string;
  des: string;
  about: string;
  deviceImage?: string;
};

// New types for device access management
export type DeviceAccessLevel = 'read' | 'write';

export type DeviceAccessRequest = {
  _id: string;
  requester: {
    _id: string;
    email: string;
    name: string;
  };
  requesterEmail: string;
  organization: {
    _id: string;
    name: string;
    orgEmail?: string;
  };
  deviceId: {
    _id: string;
    name: string;
    qrn: string;
  };
  providerId: {
    _id: string;
    provider: string;
  };
  deviceName: string;
  providerName: string;
  requestType: DeviceAccessLevel;
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  justification: string;
  metadata?: {
    accessDuration?: string;
    notes?: string;
  };
  approvedBy?: {
    _id: string;
    email: string;
    name: string;
  };
  approverEmail?: string;
  deniedReason?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
};

export type DeviceAccessRequestsResponse = {
  json: {
    requests: DeviceAccessRequest[];
    count: number;
  };
};

export type DeviceAccessRequestsResponseQuery = {
  requests: DeviceAccessRequest[];
  count: number;
};

export type OrgDeviceAccess = {
  read: Array<{
    _id: string;
    [key: string]: any;
  }>;
  write: Array<{
    _id: string;
    [key: string]: any;
  }>;
  custom: Array<{
    _id: string;
    [key: string]: any;
  }>;
};
