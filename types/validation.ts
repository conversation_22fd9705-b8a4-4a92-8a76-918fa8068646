// Validation and form types
import type { ReactNode, FormEvent } from 'react';
import { z } from 'zod';
import type { Path } from 'react-hook-form';

export type FieldType = 'string' | 'number' | 'boolean' | 'array';

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  message?: string;
}

export type FieldValidationConfig = Record<string, FieldValidation>;

// Placeholder schemas for device validation - these should be defined in the appropriate validation files
const deviceAccessRequestSchema = z.object({});
const createDeviceSchema = z.object({});
const deviceFilterSchema = z.object({});
const availableDeviceSchema = z.object({});
const deviceAccessRequestResponseSchema = z.object({});
const baseDeviceSchema = z.object({});
const DeviceFieldSchema = z.object({});
const deviceFieldSchemas = {};

// Device validation types
export type DeviceAccessRequestInput = z.infer<typeof deviceAccessRequestSchema>;
export type CreateDeviceInput = z.infer<typeof createDeviceSchema>;
export type DeviceFilterInput = z.infer<typeof deviceFilterSchema>;
export type AvailableDeviceValidated = z.infer<typeof availableDeviceSchema>;
export type DeviceAccessRequestValidated = z.infer<typeof deviceAccessRequestResponseSchema>;
export type DeviceFormType = z.infer<typeof baseDeviceSchema>;

export interface BasicInfoStepProps {
  form: any;
  errors: any;
  touched: any;
  setFieldValue: any;
  setFieldTouched: any;
}

export interface AccessPricingStepProps {
  form: any;
  errors: any;
  touched: any;
  setFieldValue: any;
  setFieldTouched: any;
}

export interface TechnicalStepProps {
  form: any;
  errors: any;
  touched: any;
  setFieldValue: any;
  setFieldTouched: any;
}

export interface DeviceFormValues {
  name: string;
  type: string;
  paradigm: string;
  modality: string;
  processorType: string;
  numberQubits: string;
  vendor: string;
  providerId: string;
  objArg: string;
  runPackage: string;
  runInputTypes: string | string[];
  noiseModels: string | string[];
  status: string;
  visibility: string;
  whiteListedDomains: string | string[];
  blackListedDomains: string | string[];
  pricing: {
    perMinute: string;
    perTask: string;
    perShot: string;
  };
  deviceDescription: string;
  about: string;
  providerDescription: string;
  deviceImage: string;
  technicalSpecifications: string;
  notes?: string;
  isAvailable: string;
  pricingType: 'perMinute' | 'perTaskPerShot';
}

export interface DeviceStepValues {
  basicInfo: Partial<DeviceFormValues>;
  accessPricing: Partial<DeviceFormValues>;
  technical: Partial<DeviceFormValues>;
}

export type SetFieldValue = <K extends Path<DeviceFormType>>(
  field: K,
  value: DeviceFormType[K],
  shouldValidate?: boolean
) => void;

export type SetSelectFieldValue = <K extends Path<DeviceFormType>>(
  field: K,
  value: string | string[],
  shouldValidate?: boolean
) => void;

export interface DevicePricing {
  perMinute: string;
  perTask: string;
  perShot: string;
}

export interface DeviceApiPayload {
  name: string;
  type: string;
  paradigm: string;
  modality: string;
  processorType: string;
  numberQubits: string;
  vendor: string;
  providerId: string;
  objArg: string;
  runPackage: string;
  runInputTypes: string | string[];
  noiseModels: string | string[];
  status: string;
  visibility: string;
  whiteListedDomains: string | string[];
  blackListedDomains: string | string[];
  pricing: DevicePricing;
  deviceDescription: string;
  about: string;
  providerDescription: string;
  deviceImage: string;
  technicalSpecifications: string;
  notes?: string;
  isAvailable: string;
  pricingType: 'perMinute' | 'perTaskPerShot';
}

export interface DeviceCreatePayload extends Partial<DeviceFormValues> {
  providerId: string;
}

export interface ChangedFields {
  [key: string]: {
    oldValue: any;
    newValue: any;
  };
}

export interface CreateDevicePayload {
  deviceData: DeviceApiPayload;
  formData: FormData;
  fileUploads: {
    deviceImage?: File;
    [key: string]: File | undefined;
  };
}

export interface ProviderInfo {
  id: string;
  name: string;
  provider: string;
}

export interface UseDeviceFormProps {
  initialData?: Partial<DeviceFormValues>;
  providerId?: string;
  onSubmit: (values: DeviceFormValues) => void | Promise<void>;
  onCancel?: () => void;
}

export interface UseDeviceFormReturn {
  form: any;
  handleSubmit: (e?: FormEvent) => void;
  isSubmitting: boolean;
  errors: any;
  touched: any;
  values: DeviceFormValues;
  setFieldValue: SetFieldValue;
  setFieldTouched: (field: string, isTouched: boolean, shouldValidate?: boolean) => void;
  resetForm: () => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLastStep: boolean;
  fileUploads: FileUploadState;
  updateFileUpload: (key: string, file: File | null) => void;
  removeFileUpload: (key: string) => void;
  providerInfo?: ProviderInfo;
}

export interface FileUploadState {
  [key: string]: File | null;
}

export interface FormDataConfig {
  deviceImage: {
    required: boolean;
    maxSize: number;
    allowedTypes: string[];
  };
  technicalSpecs: {
    required: boolean;
    maxSize: number;
    allowedTypes: string[];
  };
}

export type DeviceFieldName = keyof typeof deviceFieldSchemas;
export type DeviceField = z.infer<typeof DeviceFieldSchema>;