// Job-related types for use across the app

/**
 * Represents a single job row.
 */
export interface JobsRowProps {
  _id?: string;
  qbraidDeviceId: string;
  qbraidStatus?: string;
  statusText?: string;
  vendor?: string;
  provider?: string;
  providerId?: string | { _id: string; provider: string }; // Added to match device structure
  experimentType?: string;
  shots?: number;
  cost?: number;
  timeStamps?: {
    createdAt?: Date | string;
    endedAt?: Date | string;
  };
  queuePosition?: number;
}

/**
 * Represents a collection of jobs for display.
 */
export interface JobsDisplayProps {
  jobsArray: JobsRowProps[];
}
