// Authentication and session related types

/**
 * Standard return type for all authentication operations
 */
export interface AuthResult {
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  alreadyAuthenticated?: boolean;
  nextStep?: string;
  redirectTo?: string;
  email?: string;
  csrfToken?: string;
}

/**
 * Application permissions enum for RBAC
 */
export enum Permission {
  ViewDevices = 'view:devices',
  ManageDevices = 'manage:devices',
  ViewProfile = 'view:profile',
  EditProfile = 'edit:profile',
  ViewTeam = 'view:team',
  ManageTeam = 'manage:team',
  ViewEarnings = 'view:earnings',
  ManageEarnings = 'manage:earnings',
  ViewJobs = 'view:jobs',
  ManageJobs = 'manage:jobs',
  ViewProviders = 'view:providers',
  ManageProviders = 'manage:providers',
}

export const externalRoleToPermissions: Record<string, Permission[]> = {
  viewer: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
    Permission.ViewProviders,
  ],
  manager: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewEarnings,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewJobs,
    Permission.ViewProviders,
  ],
  admin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.ViewProviders,
    Permission.ManageProviders,
  ],
  owner: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.ViewProviders,
    Permission.ManageProviders,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
  ],
};
/**
 * External role mapping configuration
 */
export interface ExternalRoleMapping {
  [externalRole: string]: Permission[];
}

/**
 * Session payload interface for JWT tokens - enhanced with RBAC
 */
export interface SessionPayload {
  name?: string;
  username: string;
  userId?: string;
  email: string;
  organizationRoles?: string[];
  signedIn: boolean;
  iat: number;
  exp: number;
  jti: string; // JWT ID for session tracking
  browserFingerprint?: string; // Browser fingerprint for session binding
}

/**
 * CSRF token interface for security validation
 */
export interface CSRFToken {
  token: string;
  exp: number;
}

/**
 * User data interface for session creation - enhanced with RBAC
 */
export interface UserSessionData {
  username: string;
  email: string;
  userId?: string;
}

/**
 * External API role response interface
 */
export interface ExternalRoleResponse {
  roles: string[];
  email: string;
  timestamp?: number;
}
export interface Organization {
  org: {
    role: string;
    email: string;
    organization: {
      name: string;
      _id: string;
    };
  };
}

export interface OrganizationsResponse {
  organizations: Organization[];
  pagination: {
    currentPage: number;
    limit: string;
    totalPages: number;
    totalOrganizations: number;
  };
}

/**
 * Cognito tokens interface for AWS authentication
 */
export interface CognitoTokens {
  accessToken?: string;
  idToken?: string;
}

/**
 * Auth configuration interface for Amplify setup
 */
export interface AuthConfig {
  Auth: {
    Cognito: {
      userPoolId: string;
      userPoolClientId: string;
      identityPoolId: string;
      signUpVerificationMethod: 'code' | 'link';
      loginWith: {
        email: boolean;
        username: boolean;
        phone: boolean;
      };
    };
  };
  Storage?: any;
}

/**
 * Custom token storage interface for server-side operations
 */
export interface TokenStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

/**
 * Form state interface for authentication forms
 */
export interface AuthFormState {
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  nextStep?: string;
  redirectTo?: string;
  email?: string;
  csrfToken?: string;
}

/**
 * User details interface from Cognito
 */
export interface UserDetails {
  userId: string;
  username: string;
  email?: string;
  attributes?: Record<string, any>;
}

/**
 * Sign-in form data interface
 */
export interface SignInFormData {
  email: string;
  password: string;
  csrfToken: string;
}

/**
 * Sign-up form data interface
 */
export interface SignUpFormData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  csrfToken: string;
}

/**
 * Email verification form data interface
 */
export interface VerificationFormData {
  email: string;
  code: string;
  csrfToken: string;
}

/**
 * Password reset form data interface
 */
export interface PasswordResetFormData {
  email: string;
  code?: string;
  newPassword?: string;
  csrfToken: string;
}

/**
 * Authentication error types from Cognito
 */
export type CognitoErrorType =
  | 'UserNotConfirmedException'
  | 'NotAuthorizedException'
  | 'UserNotFoundException'
  | 'PasswordResetRequiredException'
  | 'TooManyRequestsException'
  | 'UnexpectedSignInInterruptionException'
  | 'InvalidParameterException'
  | 'UsernameExistsException'
  | 'InvalidPasswordException'
  | 'UserAlreadyAuthenticatedException';

/**
 * Authentication step types from Cognito
 */
export type AuthStepType = 'CONFIRM_SIGN_UP' | 'RESET_PASSWORD' | 'CONFIRM_PASSWORD_RESET' | 'DONE';

/**
 * User status types
 */
export type UserStatus = 'CONFIRMED' | 'UNCONFIRMED' | 'ARCHIVED' | 'COMPROMISED' | 'UNKNOWN';

/**
 * Auth user interface for client-side authentication
 */
export interface AuthUser {
  username: string;
  email: string;
  attributes: {
    email: string;
    email_verified: boolean;
    sub: string;
    [key: string]: any;
  };
}

// External roles and user management types
export interface UserOrgRole {
  userId: string;
  orgId: string;
  role: 'owner' | 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  joinedAt: Date;
  lastActive?: Date;
}

export interface UserOrgRoles {
  userId: string;
  organizations: UserOrgRole[];
}

export type UserRole = 'owner' | 'admin' | 'manager' | 'viewer';

export interface AddProviderFormProps {
  onSubmit: (data: AddProviderFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export interface AddProviderFormData {
  providerName: string;
  providerType: string;
  contactEmail: string;
  description?: string;
  website?: string;
  phoneNumber?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
}

export interface ProviderSubmissionResponse {
  success: boolean;
  message: string;
  providerId?: string;
  errors?: string[];
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}
