{"$schema": "https://ui.shadcn.com/registry.schema.json", "name": "qbraid-partner-dashboard-registry", "version": 1, "items": [{"name": "access-denied", "type": "component", "files": [{"path": "components/ui/access-denied.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nexport interface AccessDeniedProps {\n  title?: string;\n  description?: string;\n  icon?: LucideIcon;\n  backUrl?: string;\n  backText?: string;\n  className?: string;\n  variant?: 'page' | 'modal';\n}\n\nexport function AccessDenied({\n  title = 'Access Denied',\n  description = 'You do not have permission to access this resource.',\n  icon: Icon,\n  backUrl = '/',\n  backText = 'Return to Dashboard',\n  className,\n  variant = 'page',\n}: AccessDeniedProps) {\n  const containerClasses = variant === 'page' \n    ? 'flex min-h-screen items-center justify-center bg-[#0f0f0f]'\n    : 'flex items-center justify-center p-8';\n\n  return (\n    <div className={cn(containerClasses, className)}>\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 0.3 }}\n        className={cn(\n          'max-w-md rounded-xl border border-border bg-card p-8 text-center shadow-2xl',\n          variant === 'modal' && 'max-w-sm'\n        )}\n      >\n        {Icon && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3, delay: 0.1 }}\n            className=\"mb-4 text-red-500\"\n          >\n            <Icon className=\"mx-auto size-16\" />\n          </motion.div>\n        )}\n        \n        <motion.h2\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.2 }}\n          className=\"mb-2 text-xl font-semibold text-foreground\"\n        >\n          {title}\n        </motion.h2>\n        \n        <motion.p\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.3 }}\n          className=\"mb-6 text-muted-foreground\"\n        >\n          {description}\n        </motion.p>\n        \n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.4 }}\n        >\n          <Link href={backUrl}>\n            <Button className=\"bg-brand font-semibold text-brand-foreground shadow-lg transition-all duration-200 hover:scale-105 hover:bg-brand/90\">\n              {backText}\n            </Button>\n          </Link>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "alert-dialog", "type": "component", "files": [{"path": "components/ui/alert-dialog.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\n\nimport { cn } from '@/lib/utils';\nimport { buttonVariants } from '@/components/ui/button';\n\nconst AlertDialog = AlertDialogPrimitive.Root;\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger;\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal;\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\n      className,\n    )}\n    {...props}\n    ref={ref}\n  />\n));\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\n        className,\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n));\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName;\n\nconst AlertDialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn('flex flex-col space-y-2 text-center sm:text-left', className)} {...props} />\n);\nAlertDialogHeader.displayName = 'AlertDialogHeader';\n\nconst AlertDialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2', className)}\n    {...props}\n  />\n);\nAlertDialogFooter.displayName = 'AlertDialogFooter';\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn('text-lg font-semibold', className)}\n    {...props}\n  />\n));\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName;\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nAlertDialogDescription.displayName = AlertDialogPrimitive.Description.displayName;\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action ref={ref} className={cn(buttonVariants(), className)} {...props} />\n));\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName;\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(buttonVariants({ variant: 'outline' }), 'mt-2 sm:mt-0', className)}\n    {...props}\n  />\n));\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName;\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "alert", "type": "component", "files": [{"path": "components/ui/alert.tsx", "content": "import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst alertVariants = cva(\n  'relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7',\n  {\n    variants: {\n      variant: {\n        default: 'bg-background text-foreground',\n        destructive:\n          'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  },\n);\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div ref={ref} role=\"alert\" className={cn(alertVariants({ variant }), className)} {...props} />\n));\nAlert.displayName = 'Alert';\n\nconst AlertTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h5\n      ref={ref}\n      className={cn('mb-1 font-medium leading-none tracking-tight', className)}\n      {...props}\n    />\n  ),\n);\nAlertTitle.displayName = 'AlertTitle';\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('text-sm [&_p]:leading-relaxed', className)} {...props} />\n));\nAlertDescription.displayName = 'AlertDescription';\n\nexport { Alert, AlertTitle, AlertDescription };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "avatar", "type": "component", "files": [{"path": "components/ui/avatar.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "badge", "type": "component", "files": [{"path": "components/ui/badge.tsx", "content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "breadcrumb", "type": "component", "files": [{"path": "components/ui/breadcrumb.tsx", "content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ReactNode\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />)\nBreadcrumb.displayName = \"Breadcrumb\"\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n))\nBreadcrumbList.displayName = \"BreadcrumbList\"\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n))\nBreadcrumbItem.displayName = \"BreadcrumbItem\"\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = \"BreadcrumbLink\"\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n))\nBreadcrumbPage.displayName = \"BreadcrumbPage\"\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:w-3.5 [&>svg]:h-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\"\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"size-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = \"BreadcrumbElipssis\"\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "button", "type": "component", "files": [{"path": "components/ui/button.tsx", "content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"size-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "calendar", "type": "component", "files": [{"path": "components/ui/calendar.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\"\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button, buttonVariants } from \"@/components/ui/button\"\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  captionLayout = \"label\",\n  buttonVariant = \"ghost\",\n  formatters,\n  components,\n  ...props\n}: React.ComponentProps<typeof DayPicker> & {\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\n}) {\n  const defaultClassNames = getDefaultClassNames()\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\n        \"bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\n        className\n      )}\n      captionLayout={captionLayout}\n      formatters={{\n        formatMonthDropdown: (date) =>\n          date.toLocaleString(\"default\", { month: \"short\" }),\n        ...formatters,\n      }}\n      classNames={{\n        root: cn(\"w-fit\", defaultClassNames.root),\n        months: cn(\n          \"relative flex flex-col gap-4 md:flex-row\",\n          defaultClassNames.months\n        ),\n        month: cn(\"flex w-full flex-col gap-4\", defaultClassNames.month),\n        nav: cn(\n          \"absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1\",\n          defaultClassNames.nav\n        ),\n        button_previous: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\n          defaultClassNames.button_previous\n        ),\n        button_next: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\n          defaultClassNames.button_next\n        ),\n        month_caption: cn(\n          \"flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]\",\n          defaultClassNames.month_caption\n        ),\n        dropdowns: cn(\n          \"flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium\",\n          defaultClassNames.dropdowns\n        ),\n        dropdown_root: cn(\n          \"has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border\",\n          defaultClassNames.dropdown_root\n        ),\n        dropdown: cn(\n          \"bg-popover absolute inset-0 opacity-0\",\n          defaultClassNames.dropdown\n        ),\n        caption_label: cn(\n          \"select-none font-medium\",\n          captionLayout === \"label\"\n            ? \"text-sm\"\n            : \"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5\",\n          defaultClassNames.caption_label\n        ),\n        table: \"w-full border-collapse\",\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\n        weekday: cn(\n          \"text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal\",\n          defaultClassNames.weekday\n        ),\n        week: cn(\"mt-2 flex w-full\", defaultClassNames.week),\n        week_number_header: cn(\n          \"w-[--cell-size] select-none\",\n          defaultClassNames.week_number_header\n        ),\n        week_number: cn(\n          \"text-muted-foreground select-none text-[0.8rem]\",\n          defaultClassNames.week_number\n        ),\n        day: cn(\n          \"group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md\",\n          defaultClassNames.day\n        ),\n        range_start: cn(\n          \"bg-accent rounded-l-md\",\n          defaultClassNames.range_start\n        ),\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\n        range_end: cn(\"bg-accent rounded-r-md\", defaultClassNames.range_end),\n        today: cn(\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\n          defaultClassNames.today\n        ),\n        outside: cn(\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\n          defaultClassNames.outside\n        ),\n        disabled: cn(\n          \"text-muted-foreground opacity-50\",\n          defaultClassNames.disabled\n        ),\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\n        ...classNames,\n      }}\n      components={{\n        Root: ({ className, rootRef, ...props }) => {\n          return (\n            <div\n              data-slot=\"calendar\"\n              ref={rootRef}\n              className={cn(className)}\n              {...props}\n            />\n          )\n        },\n        Chevron: ({ className, orientation, ...props }) => {\n          if (orientation === \"left\") {\n            return (\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\n            )\n          }\n\n          if (orientation === \"right\") {\n            return (\n              <ChevronRightIcon\n                className={cn(\"size-4\", className)}\n                {...props}\n              />\n            )\n          }\n\n          return (\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\n          )\n        },\n        DayButton: CalendarDayButton,\n        WeekNumber: ({ children, ...props }) => {\n          return (\n            <td {...props}>\n              <div className=\"flex size-[--cell-size] items-center justify-center text-center\">\n                {children}\n              </div>\n            </td>\n          )\n        },\n        ...components,\n      }}\n      {...props}\n    />\n  )\n}\n\nfunction CalendarDayButton({\n  className,\n  day,\n  modifiers,\n  ...props\n}: React.ComponentProps<typeof DayButton>) {\n  const defaultClassNames = getDefaultClassNames()\n\n  const ref = React.useRef<HTMLButtonElement>(null)\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus()\n  }, [modifiers.focused])\n\n  return (\n    <Button\n      ref={ref}\n      variant=\"ghost\"\n      size=\"icon\"\n      data-day={day.date.toLocaleDateString()}\n      data-selected-single={\n        modifiers.selected &&\n        !modifiers.range_start &&\n        !modifiers.range_end &&\n        !modifiers.range_middle\n      }\n      data-range-start={modifiers.range_start}\n      data-range-end={modifiers.range_end}\n      data-range-middle={modifiers.range_middle}\n      className={cn(\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70\",\n        defaultClassNames.day,\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Calendar, CalendarDayButton }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "canvas-reveal-effect", "type": "component", "files": [{"path": "components/ui/canvas-reveal-effect.tsx", "content": "'use client';\nimport { cn } from '@/lib/utils';\nimport { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';\nimport React, { useMemo, useRef } from 'react';\nimport * as THREE from 'three';\n\nexport const CanvasRevealEffect = ({\n  animationSpeed = 0.4,\n  opacities = [0.3, 0.3, 0.3, 0.5, 0.5, 0.5, 0.8, 0.8, 0.8, 1],\n  colors = [[0, 255, 255]],\n  containerClassName,\n  dotSize,\n  showGradient = true,\n}: {\n  /**\n   * 0.1 - slower\n   * 1.0 - faster\n   */\n  animationSpeed?: number;\n  opacities?: number[];\n  colors?: number[][];\n  containerClassName?: string;\n  dotSize?: number;\n  showGradient?: boolean;\n}) => {\n  return (\n    <div className={cn('h-full relative bg-white w-full', containerClassName)}>\n      <div className=\"size-full\">\n        <DotMatrix\n          colors={colors ?? [[0, 255, 255]]}\n          dotSize={dotSize ?? 3}\n          opacities={opacities ?? [0.3, 0.3, 0.3, 0.5, 0.5, 0.5, 0.8, 0.8, 0.8, 1]}\n          shader={`\n              float animation_speed_factor = ${animationSpeed.toFixed(1)};\n              float intro_offset = distance(u_resolution / 2.0 / u_total_size, st2) * 0.01 + (random(st2) * 0.15);\n              opacity *= step(intro_offset, u_time * animation_speed_factor);\n              opacity *= clamp((1.0 - step(intro_offset + 0.1, u_time * animation_speed_factor)) * 1.25, 1.0, 1.25);\n            `}\n          center={['x', 'y']}\n        />\n      </div>\n      {showGradient && <div className=\"absolute inset-0 bg-gradient-to-t from-gray-950 to-[84%]\" />}\n    </div>\n  );\n};\n\ninterface DotMatrixProps {\n  colors?: number[][];\n  opacities?: number[];\n  totalSize?: number;\n  dotSize?: number;\n  shader?: string;\n  center?: ('x' | 'y')[];\n}\n\nconst DotMatrix: React.FC<DotMatrixProps> = ({\n  colors = [[0, 0, 0]],\n  opacities = [0.04, 0.04, 0.04, 0.04, 0.04, 0.08, 0.08, 0.08, 0.08, 0.14],\n  totalSize = 4,\n  dotSize = 2,\n  shader = '',\n  center = ['x', 'y'],\n}) => {\n  const uniforms = React.useMemo(() => {\n    let colorsArray = [colors[0], colors[0], colors[0], colors[0], colors[0], colors[0]];\n    if (colors.length === 2) {\n      colorsArray = [colors[0], colors[0], colors[0], colors[1], colors[1], colors[1]];\n    } else if (colors.length === 3) {\n      colorsArray = [colors[0], colors[0], colors[1], colors[1], colors[2], colors[2]];\n    }\n\n    return {\n      u_colors: {\n        value: colorsArray.map((color) => [color[0] / 255, color[1] / 255, color[2] / 255]),\n        type: 'uniform3fv',\n      },\n      u_opacities: {\n        value: opacities,\n        type: 'uniform1fv',\n      },\n      u_total_size: {\n        value: totalSize,\n        type: 'uniform1f',\n      },\n      u_dot_size: {\n        value: dotSize,\n        type: 'uniform1f',\n      },\n    };\n  }, [colors, opacities, totalSize, dotSize]);\n\n  return (\n    <Shader\n      source={`\n        precision mediump float;\n        in vec2 fragCoord;\n\n        uniform float u_time;\n        uniform float u_opacities[10];\n        uniform vec3 u_colors[6];\n        uniform float u_total_size;\n        uniform float u_dot_size;\n        uniform vec2 u_resolution;\n        out vec4 fragColor;\n        float PHI = 1.61803398874989484820459;\n        float random(vec2 xy) {\n            return fract(tan(distance(xy * PHI, xy) * 0.5) * xy.x);\n        }\n        float map(float value, float min1, float max1, float min2, float max2) {\n            return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n        }\n        void main() {\n            vec2 st = fragCoord.xy;\n            ${\n              center.includes('x')\n                ? 'st.x -= abs(floor((mod(u_resolution.x, u_total_size) - u_dot_size) * 0.5));'\n                : ''\n            }\n            ${\n              center.includes('y')\n                ? 'st.y -= abs(floor((mod(u_resolution.y, u_total_size) - u_dot_size) * 0.5));'\n                : ''\n            }\n      float opacity = step(0.0, st.x);\n      opacity *= step(0.0, st.y);\n\n      vec2 st2 = vec2(int(st.x / u_total_size), int(st.y / u_total_size));\n\n      float frequency = 5.0;\n      float show_offset = random(st2);\n      float rand = random(st2 * floor((u_time / frequency) + show_offset + frequency) + 1.0);\n      opacity *= u_opacities[int(rand * 10.0)];\n      opacity *= 1.0 - step(u_dot_size / u_total_size, fract(st.x / u_total_size));\n      opacity *= 1.0 - step(u_dot_size / u_total_size, fract(st.y / u_total_size));\n\n      vec3 color = u_colors[int(show_offset * 6.0)];\n\n      ${shader}\n\n      fragColor = vec4(color, opacity);\n      fragColor.rgb *= fragColor.a;\n        }`}\n      uniforms={uniforms}\n      maxFps={60}\n    />\n  );\n};\n\ntype Uniforms = {\n  [key: string]: {\n    value: number[] | number[][] | number;\n    type: string;\n  };\n};\nconst ShaderMaterial = ({\n  source,\n  uniforms,\n  maxFps = 60,\n}: {\n  source: string;\n  hovered?: boolean;\n  maxFps?: number;\n  uniforms: Uniforms;\n}) => {\n  const { size } = useThree();\n  const ref = useRef<THREE.Mesh>(null!);\n  let lastFrameTime = 0;\n\n  useFrame(({ clock }) => {\n    if (!ref.current) return;\n    const timestamp = clock.getElapsedTime();\n    if (timestamp - lastFrameTime < 1 / maxFps) {\n      return;\n    }\n    lastFrameTime = timestamp;\n\n    const material: any = ref.current.material;\n    const timeLocation = material.uniforms.u_time;\n    timeLocation.value = timestamp;\n  });\n\n  const getUniforms = () => {\n    const preparedUniforms: any = {};\n\n    for (const uniformName in uniforms) {\n      const uniform: any = uniforms[uniformName];\n\n      switch (uniform.type) {\n        case 'uniform1f': {\n          preparedUniforms[uniformName] = { value: uniform.value, type: '1f' };\n          break;\n        }\n        case 'uniform3f': {\n          preparedUniforms[uniformName] = {\n            value: new THREE.Vector3().fromArray(uniform.value),\n            type: '3f',\n          };\n          break;\n        }\n        case 'uniform1fv': {\n          preparedUniforms[uniformName] = { value: uniform.value, type: '1fv' };\n          break;\n        }\n        case 'uniform3fv': {\n          preparedUniforms[uniformName] = {\n            value: uniform.value.map((v: number[]) => new THREE.Vector3().fromArray(v)),\n            type: '3fv',\n          };\n          break;\n        }\n        case 'uniform2f': {\n          preparedUniforms[uniformName] = {\n            value: new THREE.Vector2().fromArray(uniform.value),\n            type: '2f',\n          };\n          break;\n        }\n        default: {\n          console.error(`Invalid uniform type for '${uniformName}'.`);\n          break;\n        }\n      }\n    }\n\n    preparedUniforms['u_time'] = { value: 0, type: '1f' };\n    preparedUniforms['u_resolution'] = {\n      value: new THREE.Vector2(size.width * 2, size.height * 2),\n    }; // Initialize u_resolution\n    return preparedUniforms;\n  };\n\n  // Shader material\n  const material = useMemo(() => {\n    const materialObject = new THREE.ShaderMaterial({\n      vertexShader: `\n      precision mediump float;\n      in vec2 coordinates;\n      uniform vec2 u_resolution;\n      out vec2 fragCoord;\n      void main(){\n        float x = position.x;\n        float y = position.y;\n        gl_Position = vec4(x, y, 0.0, 1.0);\n        fragCoord = (position.xy + vec2(1.0)) * 0.5 * u_resolution;\n        fragCoord.y = u_resolution.y - fragCoord.y;\n      }\n      `,\n      fragmentShader: source,\n      uniforms: getUniforms(),\n      glslVersion: THREE.GLSL3,\n\n      blending: THREE.CustomBlending,\n      blendSrc: THREE.SrcAlphaFactor,\n      blendDst: THREE.OneFactor,\n    });\n\n    return materialObject;\n  }, [size.width, size.height, source, getUniforms]);\n\n  return (\n    <mesh ref={ref as any}>\n      <planeGeometry args={[2, 2]} />\n      <primitive object={material} attach=\"material\" />\n    </mesh>\n  );\n};\n\nconst Shader: React.FC<ShaderProps> = ({ source, uniforms, maxFps = 60 }) => {\n  return (\n    <Canvas className=\"absolute inset-0  size-full\">\n      <ShaderMaterial source={source} uniforms={uniforms} maxFps={maxFps} />\n    </Canvas>\n  );\n};\ninterface ShaderProps {\n  source: string;\n  uniforms: {\n    [key: string]: {\n      value: number[] | number[][] | number;\n      type: string;\n    };\n  };\n  maxFps?: number;\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "card-spotlight", "type": "component", "files": [{"path": "components/ui/card-spotlight.tsx", "content": "'use client';\n\nimport { useMotionValue, motion, useMotionTemplate } from 'motion/react';\nimport React, { MouseEvent as ReactMouseEvent, useState } from 'react';\nimport { cn } from '@/lib/utils';\nimport { CanvasRevealEffect } from '@/components/ui/canvas-reveal-effect';\nimport { useThemeHydrated } from '@/hooks/use-theme';\n\nexport const CardSpotlight = ({\n  children,\n  radius = 350,\n  color = '#262626',\n  className,\n  showGradient = true,\n  colors = [\n    [138, 43, 226],\n    [75, 0, 130],\n  ],\n  lightModeColors,\n  ...props\n}: {\n  radius?: number;\n  color?: string;\n  showGradient?: boolean;\n  colors?: number[][];\n  lightModeColors?: number[][];\n  children: React.ReactNode;\n} & React.HTMLAttributes<HTMLDivElement>) => {\n  const { currentTheme } = useThemeHydrated();\n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n\n  function handleMouseMove({ currentTarget, clientX, clientY }: ReactMouseEvent<HTMLDivElement>) {\n    const { left, top } = currentTarget.getBoundingClientRect();\n\n    mouseX.set(clientX - left);\n    mouseY.set(clientY - top);\n  }\n\n  const [isHovering, setIsHovering] = useState(false);\n  const handleMouseEnter = () => setIsHovering(true);\n  const handleMouseLeave = () => setIsHovering(false);\n\n  // Theme-aware colors - use resolvedTheme for more reliable detection\n  const themeColors =\n    currentTheme === 'light'\n      ? lightModeColors || [\n          [200, 180, 255],\n          [255, 255, 255],\n        ] // Use provided light colors or default light purple\n      : colors; // Use provided colors for dark mode\n\n  return (\n    <div\n      className={cn(\n        'group/spotlight p-10 rounded-md relative border border-neutral-800 bg-black dark:border-neutral-800',\n        className,\n      )}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      {...props}\n    >\n      <motion.div\n        className=\"pointer-events-none absolute -inset-px z-0 rounded-md opacity-0 transition duration-300 group-hover/spotlight:opacity-100\"\n        style={{\n          backgroundColor: color,\n          maskImage: useMotionTemplate`\n            radial-gradient(\n              ${radius}px circle at ${mouseX}px ${mouseY}px,\n              white,\n              transparent 80%\n            )\n          `,\n        }}\n      >\n        {isHovering && (\n          <CanvasRevealEffect\n            animationSpeed={5}\n            containerClassName=\"bg-transparent absolute inset-0 pointer-events-none\"\n            colors={themeColors}\n            dotSize={3}\n            showGradient={showGradient}\n          />\n        )}\n      </motion.div>\n      <div className=\"relative z-10\">{children}</div>\n    </div>\n  );\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["canvas-reveal-effect"]}, {"name": "card", "type": "component", "files": [{"path": "components/ui/card.tsx", "content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "carousel", "type": "component", "files": [{"path": "components/ui/carousel.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport useEmblaCarousel, { type UseEmblaCarouselType } from 'embla-carousel-react';\nimport { ArrowLeft, ArrowRight } from 'lucide-react';\nimport { useEffect, useContext, useState, useCallback } from 'react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\n\ntype CarouselApi = UseEmblaCarouselType[1];\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>;\ntype CarouselOptions = UseCarouselParameters[0];\ntype CarouselPlugin = UseCarouselParameters[1];\n\ntype CarouselProps = {\n  opts?: CarouselOptions;\n  plugins?: CarouselPlugin;\n  orientation?: 'horizontal' | 'vertical';\n  setApi?: (api: CarouselApi) => void;\n};\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0];\n  api: ReturnType<typeof useEmblaCarousel>[1];\n  scrollPrev: () => void;\n  scrollNext: () => void;\n  canScrollPrev: boolean;\n  canScrollNext: boolean;\n} & CarouselProps;\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null);\n\nfunction useCarousel() {\n  const context = useContext(CarouselContext);\n\n  if (!context) {\n    throw new Error('useCarousel must be used within a <Carousel />');\n  }\n\n  return context;\n}\n\nconst Carousel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & CarouselProps\n>(({ orientation = 'horizontal', opts, setApi, plugins, className, children, ...props }, ref) => {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === 'horizontal' ? 'x' : 'y',\n    },\n    plugins,\n  );\n  const [canScrollPrev, setCanScrollPrev] = useState(false);\n  const [canScrollNext, setCanScrollNext] = useState(false);\n\n  const onSelect = useCallback((api: CarouselApi) => {\n    if (!api) {\n      return;\n    }\n\n    setCanScrollPrev(api.canScrollPrev());\n    setCanScrollNext(api.canScrollNext());\n  }, []);\n\n  const scrollPrev = useCallback(() => {\n    api?.scrollPrev();\n  }, [api]);\n\n  const scrollNext = useCallback(() => {\n    api?.scrollNext();\n  }, [api]);\n\n  const handleKeyDown = useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === 'ArrowLeft') {\n        event.preventDefault();\n        scrollPrev();\n      } else if (event.key === 'ArrowRight') {\n        event.preventDefault();\n        scrollNext();\n      }\n    },\n    [scrollPrev, scrollNext],\n  );\n\n  useEffect(() => {\n    if (!api || !setApi) {\n      return;\n    }\n\n    setApi(api);\n  }, [api, setApi]);\n\n  useEffect(() => {\n    if (!api) {\n      return;\n    }\n\n    onSelect(api);\n    api.on('reInit', onSelect);\n    api.on('select', onSelect);\n\n    return () => {\n      api?.off('select', onSelect);\n    };\n  }, [api, onSelect]);\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api: api,\n        opts,\n        orientation: orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        ref={ref}\n        onKeyDownCapture={handleKeyDown}\n        className={cn('relative', className)}\n        role=\"region\"\n        aria-roledescription=\"carousel\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  );\n});\nCarousel.displayName = 'Carousel';\n\nconst CarouselContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => {\n    const { carouselRef, orientation } = useCarousel();\n\n    return (\n      <div ref={carouselRef} className=\"overflow-hidden\">\n        <div\n          ref={ref}\n          className={cn(\n            'flex',\n            orientation === 'horizontal' ? '-ml-4' : '-mt-4 flex-col',\n            className,\n          )}\n          {...props}\n        />\n      </div>\n    );\n  },\n);\nCarouselContent.displayName = 'CarouselContent';\n\nconst CarouselItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => {\n    const { orientation } = useCarousel();\n\n    return (\n      <div\n        ref={ref}\n        role=\"group\"\n        aria-roledescription=\"slide\"\n        className={cn(\n          'min-w-0 shrink-0 grow-0 basis-full',\n          orientation === 'horizontal' ? 'pl-4' : 'pt-4',\n          className,\n        )}\n        {...props}\n      />\n    );\n  },\n);\nCarouselItem.displayName = 'CarouselItem';\n\nconst CarouselPrevious = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(\n  ({ className, variant = 'outline', size = 'icon', ...props }, ref) => {\n    const { orientation, scrollPrev, canScrollPrev } = useCarousel();\n\n    return (\n      <Button\n        ref={ref}\n        variant={variant}\n        size={size}\n        className={cn(\n          'absolute  h-8 w-8 rounded-full',\n          orientation === 'horizontal'\n            ? '-left-12 top-1/2 -translate-y-1/2'\n            : '-top-12 left-1/2 -translate-x-1/2 rotate-90',\n          className,\n        )}\n        disabled={!canScrollPrev}\n        onClick={scrollPrev}\n        {...props}\n      >\n        <ArrowLeft className=\"size-4\" />\n        <span className=\"sr-only\">Previous slide</span>\n      </Button>\n    );\n  },\n);\nCarouselPrevious.displayName = 'CarouselPrevious';\n\nconst CarouselNext = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(\n  ({ className, variant = 'outline', size = 'icon', ...props }, ref) => {\n    const { orientation, scrollNext, canScrollNext } = useCarousel();\n\n    return (\n      <Button\n        ref={ref}\n        variant={variant}\n        size={size}\n        className={cn(\n          'absolute h-8 w-8 rounded-full',\n          orientation === 'horizontal'\n            ? '-right-12 top-1/2 -translate-y-1/2'\n            : '-bottom-12 left-1/2 -translate-x-1/2 rotate-90',\n          className,\n        )}\n        disabled={!canScrollNext}\n        onClick={scrollNext}\n        {...props}\n      >\n        <ArrowRight className=\"size-4\" />\n        <span className=\"sr-only\">Next slide</span>\n      </Button>\n    );\n  },\n);\nCarouselNext.displayName = 'CarouselNext';\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "chart", "type": "component", "files": [{"path": "components/ui/chart.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replaceAll(':', \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (colorConfig.length === 0) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {nestLabel ? null : tooltipLabel}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"size-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "checkbox", "type": "component", "files": [{"path": "components/ui/checkbox.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"size-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "clean-minimal-sign-in", "type": "component", "files": [{"path": "components/ui/clean-minimal-sign-in.tsx", "content": "'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { useState } from 'react';\r\nimport Image from 'next/image';\r\n\r\nimport { LogIn, Lock, Mail } from 'lucide-react';\r\n\r\nconst SignIn2 = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState('');\r\n\r\n  const validateEmail = (email: string) => {\r\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\r\n  };\r\n\r\n  const handleSignIn = () => {\r\n    if (!email || !password) {\r\n      setError('Please enter both email and password.');\r\n      return;\r\n    }\r\n    if (!validateEmail(email)) {\r\n      setError('Please enter a valid email address.');\r\n      return;\r\n    }\r\n    setError('');\r\n    alert('Sign in successful! (Demo)');\r\n  };\r\n\r\n  return (\r\n    <div className=\"z-1 flex min-h-screen w-full items-center justify-center rounded-xl  bg-white\">\r\n      <div className=\"shadow-opacity-10 flex w-full max-w-sm flex-col  items-center rounded-3xl border border-blue-100 bg-gradient-to-b from-sky-50/50 to-white p-8 text-black shadow-xl\">\r\n        <div className=\"shadow-opacity-5 mb-6 flex size-14 items-center justify-center rounded-2xl bg-white shadow-lg\">\r\n          <LogIn className=\"size-7 text-black\" />\r\n        </div>\r\n        <h2 className=\"mb-2 text-center text-2xl font-semibold\">Sign in with email</h2>\r\n        <p className=\"mb-6 text-center text-sm text-gray-500\">\r\n          Make a new doc to bring your words, data, and teams together. For free\r\n        </p>\r\n        <div className=\"mb-2 flex w-full flex-col gap-3\">\r\n          <div className=\"relative\">\r\n            <span className=\"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400\">\r\n              <Mail className=\"size-4\" />\r\n            </span>\r\n            <input\r\n              placeholder=\"Email\"\r\n              type=\"email\"\r\n              value={email}\r\n              className=\"w-full rounded-xl border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-200\"\r\n              onChange={(e) => setEmail(e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"relative\">\r\n            <span className=\"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400\">\r\n              <Lock className=\"size-4\" />\r\n            </span>\r\n            <input\r\n              placeholder=\"Password\"\r\n              type=\"password\"\r\n              value={password}\r\n              className=\"w-full rounded-xl border border-gray-200 bg-gray-50 px-10 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-200\"\r\n              onChange={(e) => setPassword(e.target.value)}\r\n            />\r\n            <span className=\"absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer select-none text-xs text-gray-400\"></span>\r\n          </div>\r\n          <div className=\"flex w-full justify-end\">\r\n            {error && <div className=\"text-left text-sm text-red-500\">{error}</div>}\r\n            <button className=\"text-xs  font-medium hover:underline\">Forgot password?</button>\r\n          </div>\r\n        </div>\r\n        <button\r\n          onClick={handleSignIn}\r\n          className=\"mb-4 mt-2 w-full cursor-pointer rounded-xl bg-gradient-to-b from-gray-700 to-gray-900 py-2 font-medium text-white shadow transition hover:brightness-105\"\r\n        >\r\n          Get Started\r\n        </button>\r\n        <div className=\"my-2 flex w-full items-center\">\r\n          <div className=\"grow border-t border-dashed border-gray-200\"></div>\r\n          <span className=\"mx-2 text-xs text-gray-400\">Or sign in with</span>\r\n          <div className=\"grow border-t border-dashed border-gray-200\"></div>\r\n        </div>\r\n        <div className=\"mt-2 flex w-full justify-center gap-3\">\r\n          <button className=\"flex size-12 grow items-center justify-center rounded-xl border bg-white transition hover:bg-gray-100\">\r\n            <Image\r\n              src=\"https://www.svgrepo.com/show/475656/google-color.svg\"\r\n              alt=\"Google\"\r\n              width={24}\r\n              height={24}\r\n              className=\"size-6\"\r\n            />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { SignIn2 };\r\n", "type": "tsx"}], "dependencies": []}, {"name": "collapsible", "type": "component", "files": [{"path": "components/ui/collapsible.tsx", "content": "\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n", "type": "tsx"}], "dependencies": []}, {"name": "command", "type": "component", "files": [{"path": "components/ui/command.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport { type DialogProps } from '@radix-ui/react-dialog';\nimport { Command as CommandPrimitive } from 'cmdk';\nimport { Search } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\nimport { Dialog, DialogContent } from '@/components/ui/dialog';\n\nconst Command = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive\n    ref={ref}\n    className={cn(\n      'flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground',\n      className,\n    )}\n    {...props}\n  />\n));\nCommand.displayName = CommandPrimitive.displayName;\n\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\n  return (\n    <Dialog {...props}>\n      <DialogContent className=\"overflow-hidden p-0 shadow-lg\">\n        <Command className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:size-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:size-5\">\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nconst CommandInput = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Input>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\n>(({ className, ...props }, ref) => (\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\n    <Search className=\"mr-2 size-4 shrink-0 opacity-50\" />\n    <CommandPrimitive.Input\n      ref={ref}\n      className={cn(\n        'flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',\n        className,\n      )}\n      {...props}\n    />\n  </div>\n));\n\nCommandInput.displayName = CommandPrimitive.Input.displayName;\n\nconst CommandList = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.List\n    ref={ref}\n    className={cn('max-h-[300px] overflow-y-auto overflow-x-hidden', className)}\n    {...props}\n  />\n));\n\nCommandList.displayName = CommandPrimitive.List.displayName;\n\nconst CommandEmpty = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Empty>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\n>((props, ref) => (\n  <CommandPrimitive.Empty ref={ref} className=\"py-6 text-center text-sm\" {...props} />\n));\n\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName;\n\nconst CommandGroup = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Group>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Group\n    ref={ref}\n    className={cn(\n      'overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground',\n      className,\n    )}\n    {...props}\n  />\n));\n\nCommandGroup.displayName = CommandPrimitive.Group.displayName;\n\nconst CommandSeparator = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 h-px bg-border', className)}\n    {...props}\n  />\n));\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName;\n\nconst CommandItem = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      className,\n    )}\n    {...props}\n  />\n));\n\nCommandItem.displayName = CommandPrimitive.Item.displayName;\n\nconst CommandShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest text-muted-foreground', className)}\n      {...props}\n    />\n  );\n};\nCommandShortcut.displayName = 'CommandShortcut';\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["dialog"]}, {"name": "data-table", "type": "component", "files": [{"path": "components/ui/data-table.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport {\n  DndContext,\n  KeyboardSensor,\n  MouseSensor,\n  TouchSensor,\n  closestCenter,\n  useSensor,\n  useSensors,\n  type DragEndEvent,\n  type UniqueIdentifier,\n} from '@dnd-kit/core';\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\nimport {\n  SortableContext,\n  arrayMove,\n  useSortable,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport {\n  ColumnDef,\n  ColumnFiltersState,\n  Row,\n  SortingState,\n  VisibilityState,\n  flexRender,\n  getCoreRowModel,\n  getFacetedRowModel,\n  getFacetedUniqueValues,\n  getFilteredRowModel,\n  getPaginationRowModel,\n  getSortedRowModel,\n  useReactTable,\n} from '@tanstack/react-table';\nimport { rankItem } from '@tanstack/match-sorter-utils';\nimport {\n  CheckCircle2Icon,\n  CheckCircleIcon,\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n  ChevronsLeftIcon,\n  ChevronsRightIcon,\n  ColumnsIcon,\n  GripVerticalIcon,\n  LoaderIcon,\n  MoreVerticalIcon,\n  PlusIcon,\n  Download,\n} from 'lucide-react';\n// removed unused chart and toast imports\nimport { z } from 'zod';\n\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Label } from '@/components/ui/label';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Skeleton } from '@/components/ui/skeleton';\n\nexport const schema = z.object({\n  id: z.number(),\n  name: z.string(),\n  email: z.string(),\n  role: z.string(),\n  status: z.string(),\n});\n\nexport const dummyUsers: z.infer<typeof schema>[] = [\n  { id: 1, name: 'Alice Johnson', email: '<EMAIL>', role: 'Admin', status: 'online' },\n  { id: 2, name: 'Bob Williams', email: '<EMAIL>', role: 'Member', status: 'online' },\n  { id: 3, name: 'Charlie Brown', email: '<EMAIL>', role: 'Viewer', status: 'offline' },\n  { id: 4, name: 'Dana Lee', email: '<EMAIL>', role: 'Member', status: 'online' },\n];\n\n// Create a separate component for the drag handle\nfunction DragHandle({ id }: { id: number }) {\n  const { attributes, listeners } = useSortable({\n    id,\n  });\n\n  return (\n    <Button\n      {...attributes}\n      {...listeners}\n      variant=\"ghost\"\n      size=\"icon\"\n      className=\"size-7 text-muted-foreground hover:bg-transparent\"\n    >\n      <GripVerticalIcon className=\"size-3 text-muted-foreground\" />\n      <span className=\"sr-only\">Drag to reorder</span>\n    </Button>\n  );\n}\n\n// Default columns for generic user table. Can be overridden via props.\nexport const defaultUserColumns: ColumnDef<z.infer<typeof schema>>[] = [\n  {\n    id: 'drag',\n    header: () => null,\n    cell: ({ row }) => <DragHandle id={row.original.id} />,\n  },\n  {\n    id: 'select',\n    header: ({ table }) => (\n      <div className=\"flex items-center justify-center\">\n        <Checkbox\n          checked={\n            table.getIsAllPageRowsSelected() ||\n            (table.getIsSomePageRowsSelected() && 'indeterminate')\n          }\n          onCheckedChange={(value: boolean | 'indeterminate') =>\n            table.toggleAllPageRowsSelected(!!value)\n          }\n          aria-label=\"Select all\"\n        />\n      </div>\n    ),\n    cell: ({ row }) => (\n      <div className=\"flex items-center justify-center\">\n        <Checkbox\n          checked={row.getIsSelected()}\n          onCheckedChange={(value: boolean | 'indeterminate') => row.toggleSelected(!!value)}\n          aria-label=\"Select row\"\n        />\n      </div>\n    ),\n    enableSorting: false,\n    enableHiding: false,\n  },\n  {\n    accessorKey: 'name',\n    header: 'Name',\n    cell: ({ row }) => <span className=\"max-w-[150px] truncate\">{row.original.name}</span>,\n    enableHiding: false,\n  },\n  {\n    accessorKey: 'email',\n    header: 'Email',\n    cell: ({ row }) => <span className=\"max-w-[180px] truncate\">{row.original.email}</span>,\n  },\n  {\n    accessorKey: 'role',\n    header: 'Role',\n    cell: ({ row }) => (\n      <Badge variant=\"outline\" className=\"px-1.5 text-muted-foreground\">\n        {row.original.role}\n      </Badge>\n    ),\n  },\n  {\n    accessorKey: 'status',\n    header: 'Status',\n    cell: ({ row }) => (\n      <Badge variant=\"outline\" className=\"flex gap-1 px-1.5 text-muted-foreground [&_svg]:size-3\">\n        {row.original.status.toLowerCase() === 'online' ? (\n          <CheckCircle2Icon className=\"text-green-500 dark:text-green-400\" />\n        ) : (\n          <LoaderIcon />\n        )}\n        {row.original.status}\n      </Badge>\n    ),\n  },\n  // removed target, limit, reviewer columns\n  {\n    id: 'actions',\n    cell: () => (\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            className=\"flex size-8 text-muted-foreground data-[state=open]:bg-muted\"\n            size=\"icon\"\n          >\n            <MoreVerticalIcon />\n            <span className=\"sr-only\">Open menu</span>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-32\">\n          <DropdownMenuItem>Edit</DropdownMenuItem>\n          <DropdownMenuItem>Make a copy</DropdownMenuItem>\n          <DropdownMenuItem>Favorite</DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem>Delete</DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n    ),\n  },\n];\n\nfunction DraggableRow({ row }: { row: Row<any> }) {\n  const { transform, transition, setNodeRef, isDragging } = useSortable({\n    id: row.original.id,\n  });\n\n  return (\n    <TableRow\n      data-state={row.getIsSelected() && 'selected'}\n      data-dragging={isDragging}\n      ref={setNodeRef}\n      className=\"relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80\"\n      style={{\n        transform: CSS.Transform.toString(transform),\n        transition: transition,\n      }}\n    >\n      {row.getVisibleCells().map((cell) => (\n        <TableCell key={cell.id}>\n          {flexRender(cell.column.columnDef.cell, cell.getContext())}\n        </TableCell>\n      ))}\n    </TableRow>\n  );\n}\n\nexport interface DataTableProps {\n  data?: any[];\n  columnsOverride?: ColumnDef<any>[];\n  hideToolbar?: boolean;\n  headerTitle?: string;\n  HeaderIcon?: React.ComponentType<{ className?: string }>;\n  isLoading?: boolean;\n  initialState?: {\n    pagination?: {\n      pageSize: number;\n      pageIndex: number;\n    };\n    sorting?: {\n      id: string;\n      desc: boolean;\n    }[];\n  };\n  onPaginationChange?: (updater: any) => void;\n  onExportPDF?: () => void;\n}\n\nexport function DataTable({\n  data: initialData = dummyUsers,\n  columnsOverride,\n  hideToolbar = false,\n  headerTitle,\n  HeaderIcon,\n  isLoading = false,\n  initialState,\n  onPaginationChange,\n  onExportPDF,\n}: DataTableProps) {\n  const [data, setData] = React.useState(() => initialData);\n  const [globalFilter, setGlobalFilter] = React.useState('');\n  // Sync internal data state when prop changes (e.g., switching devices)\n  React.useEffect(() => {\n    setData(initialData);\n  }, [initialData]);\n  const [rowSelection, setRowSelection] = React.useState({});\n  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);\n  const [sorting, setSorting] = React.useState<SortingState>([]);\n  const [pagination, setPagination] = React.useState({\n    pageIndex: initialState?.pagination?.pageIndex ?? 0,\n    pageSize: initialState?.pagination?.pageSize ?? 10,\n  });\n  const sortableId = React.useId();\n  const sensors = useSensors(\n    useSensor(MouseSensor, {}),\n    useSensor(TouchSensor, {}),\n    useSensor(KeyboardSensor, {}),\n  );\n\n  const dataIds = React.useMemo<UniqueIdentifier[]>(() => data?.map(({ id }) => id) || [], [data]);\n\n  const activeColumns = columnsOverride ?? defaultUserColumns;\n\n  const handlePaginationChange = React.useCallback(\n    (updater: any) => {\n      const newPagination = typeof updater === 'function' ? updater(pagination) : updater;\n      setPagination(newPagination);\n      onPaginationChange?.(newPagination);\n    },\n    [onPaginationChange, pagination],\n  );\n\n  const fuzzyFilter = React.useCallback(\n    (row: any, columnId: string, value: string, addMeta: any) => {\n      // Helper function to normalize strings for comparison (remove special chars and spaces)\n      const normalizeForSearch = (str: string) => {\n        return str.toLowerCase().replaceAll(/[,-\\s]/g, ''); // Keep forward slashes\n      };\n\n      // Special handling for date search in createdEnded column\n      if (columnId === 'createdEnded' && value) {\n        const cellValue = row.original.timeStamps?.createdAt;\n        if (!cellValue) return false;\n\n        const date = new Date(cellValue);\n        if (isNaN(date.getTime())) return false; // Invalid date\n\n        const normalizedSearch = normalizeForSearch(value);\n\n        // Format the date in various ways for matching\n        const formats = [\n          date.toLocaleDateString(), // e.g. 7/10/2025\n          date.toLocaleString(), // e.g. 7/10/2025, 12:24 PM\n          date.toISOString().split('T')[0], // YYYY-MM-DD\n          `${date.getMonth() + 1}/${date.getDate()}`, // M/D\n          `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`, // M/D/YYYY\n          `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}/${date.getFullYear()}`, // MM/DD/YYYY\n          date.toLocaleTimeString(), // time only\n          // Add some common partial date formats\n          `${date.getFullYear()}`,\n          `${date.getMonth() + 1}/${date.getFullYear()}`,\n          `${date.getDate()}/${date.getFullYear()}`,\n        ];\n\n        // Check if normalized search matches any normalized format\n        return formats.some((format) => normalizeForSearch(format).includes(normalizedSearch));\n      }\n\n      // Special handling for any other date-like fields\n      // You can extend this to handle other date columns\n      const cellValue = row.getValue(columnId);\n      if (\n        cellValue &&\n        (cellValue instanceof Date ||\n          (typeof cellValue === 'string' && /^\\d{4}-\\d{2}-\\d{2}/.test(cellValue)))\n      ) {\n        const date = new Date(cellValue);\n        if (!isNaN(date.getTime())) {\n          const normalizedSearch = normalizeForSearch(value);\n          const formats = [\n            date.toLocaleDateString(),\n            date.toLocaleString(),\n            date.toISOString().split('T')[0],\n            `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`,\n          ];\n\n          const dateMatches = formats.some((format) =>\n            normalizeForSearch(format).includes(normalizedSearch),\n          );\n\n          if (dateMatches) return true;\n        }\n      }\n\n      // Default fuzzy search for other columns\n      try {\n        const itemRank = rankItem(row.getValue(columnId), value);\n        addMeta({ itemRank });\n        return itemRank.passed;\n      } catch {\n        // Fallback to simple string matching if rankItem fails\n        const cellValue = row.getValue(columnId);\n        return cellValue?.toString()?.toLowerCase()?.includes(value.toLowerCase()) ?? false;\n      }\n    },\n    [],\n  );\n\n  const table = useReactTable({\n    data,\n    columns: activeColumns,\n    filterFns: {\n      fuzzy: fuzzyFilter,\n    },\n    state: {\n      sorting,\n      columnVisibility,\n      rowSelection,\n      columnFilters,\n      pagination,\n      globalFilter,\n    },\n    globalFilterFn: fuzzyFilter,\n    onGlobalFilterChange: setGlobalFilter,\n    getRowId: (row) => {\n      if (row.id !== undefined) {\n        return row.id.toString();\n      }\n      // Fallback to using index as id if no id property exists\n      return Math.random().toString(36).slice(2);\n    },\n    enableRowSelection: true,\n    onRowSelectionChange: setRowSelection,\n    onSortingChange: setSorting,\n    onColumnFiltersChange: setColumnFilters,\n    onColumnVisibilityChange: setColumnVisibility,\n    onPaginationChange: handlePaginationChange,\n    getCoreRowModel: getCoreRowModel(),\n    getFilteredRowModel: getFilteredRowModel(),\n    getPaginationRowModel: getPaginationRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getFacetedRowModel: getFacetedRowModel(),\n    getFacetedUniqueValues: getFacetedUniqueValues(),\n  });\n\n  function handleDragEnd(event: DragEndEvent) {\n    const { active, over } = event;\n    if (active && over && active.id !== over.id) {\n      setData((data) => {\n        const oldIndex = dataIds.indexOf(active.id);\n        const newIndex = dataIds.indexOf(over.id);\n        return arrayMove(data, oldIndex, newIndex);\n      });\n    }\n  }\n\n  if (hideToolbar) {\n    return (\n      <div className=\"flex w-full flex-col gap-4 \">\n        <div className=\"mt-2 overflow-hidden rounded-lg border\">\n          <DndContext\n            collisionDetection={closestCenter}\n            modifiers={[restrictToVerticalAxis]}\n            onDragEnd={handleDragEnd}\n            sensors={sensors}\n            id={sortableId}\n          >\n            <Table>\n              <TableHeader className=\"sticky top-0 z-10 bg-sidebar\">\n                {table.getHeaderGroups().map((headerGroup) => (\n                  <TableRow key={headerGroup.id}>\n                    {headerGroup.headers.map((header) => (\n                      <TableHead key={header.id} colSpan={header.colSpan}>\n                        {header.isPlaceholder\n                          ? null\n                          : flexRender(header.column.columnDef.header, header.getContext())}\n                      </TableHead>\n                    ))}\n                  </TableRow>\n                ))}\n              </TableHeader>\n              <TableBody>\n                {isLoading ? (\n                  Array.from({length: 5}).map((_, idx) => (\n                    <TableRow key={idx}>\n                      {activeColumns.map((_, colIdx) => (\n                        <TableCell key={colIdx}>\n                          <Skeleton className=\"h-4 w-full\" />\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  ))\n                ) : (table.getRowModel().rows.length > 0 ? (\n                  <SortableContext items={dataIds} strategy={verticalListSortingStrategy}>\n                    {table.getRowModel().rows.map((row) => (\n                      <DraggableRow key={row.id} row={row} />\n                    ))}\n                  </SortableContext>\n                ) : (\n                  <TableRow>\n                    <TableCell colSpan={activeColumns.length} className=\"h-24 text-center\">\n                      No results.\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </DndContext>\n        </div>\n      </div>\n    );\n  }\n\n  // Default full toolbar version\n  return (\n    <div className=\"flex w-full flex-col gap-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          {HeaderIcon && <HeaderIcon className=\"size-5 text-muted-foreground\" />}\n          {headerTitle && <h3 className=\"text-lg font-semibold text-foreground\">{headerTitle}</h3>}\n          <input\n            placeholder=\"Search all columns...\"\n            value={globalFilter ?? ''}\n            onChange={(e) => setGlobalFilter(e.target.value)}\n            className=\"h-9 rounded-md border border-border bg-background px-3 py-1 text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n          />\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {onExportPDF && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onExportPDF}\n              className=\"h-9 border border-border bg-background px-3 py-1 text-sm text-foreground hover:bg-accent hover:text-accent-foreground\"\n            >\n              <Download className=\"mr-2 size-4\" /> Export PDF\n            </Button>\n          )}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"h-9 border border-border bg-background px-3 py-1 text-sm text-foreground hover:bg-accent hover:text-accent-foreground\"\n              >\n                <ColumnsIcon />\n                <span className=\"hidden lg:inline\">Customize Columns</span>\n                <span className=\"lg:hidden\">Columns</span>\n                <ChevronDownIcon />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-56\">\n              {table\n                .getAllColumns()\n                .filter((column) => column.accessorFn !== undefined && column.getCanHide())\n                .map((column) => {\n                  return (\n                    <DropdownMenuCheckboxItem\n                      key={column.id}\n                      className=\"capitalize\"\n                      checked={column.getIsVisible()}\n                      onCheckedChange={(value) => column.toggleVisibility(!!value)}\n                    >\n                      {column.id}\n                    </DropdownMenuCheckboxItem>\n                  );\n                })}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n\n      <div className=\"overflow-hidden rounded-lg border\">\n        <DndContext\n          collisionDetection={closestCenter}\n          modifiers={[restrictToVerticalAxis]}\n          onDragEnd={handleDragEnd}\n          sensors={sensors}\n          id={sortableId}\n        >\n          <Table>\n            <TableHeader className=\"sticky top-0 z-10 bg-sidebar\">\n              {table.getHeaderGroups().map((headerGroup) => (\n                <TableRow key={headerGroup.id}>\n                  {headerGroup.headers.map((header) => {\n                    return (\n                      <TableHead key={header.id} colSpan={header.colSpan}>\n                        {header.isPlaceholder\n                          ? null\n                          : flexRender(header.column.columnDef.header, header.getContext())}\n                      </TableHead>\n                    );\n                  })}\n                </TableRow>\n              ))}\n            </TableHeader>\n            <TableBody className=\"**:data-[slot=table-cell]:first:w-8\">\n              {isLoading ? (\n                Array.from({length: 5}).map((_, idx) => (\n                  <TableRow key={idx}>\n                    {activeColumns.map((_, colIdx) => (\n                      <TableCell key={colIdx}>\n                        <Skeleton className=\"h-4 w-full\" />\n                      </TableCell>\n                    ))}\n                  </TableRow>\n                ))\n              ) : (table.getRowModel().rows?.length ? (\n                <SortableContext items={dataIds} strategy={verticalListSortingStrategy}>\n                  {table.getRowModel().rows.map((row) => (\n                    <DraggableRow key={row.id} row={row} />\n                  ))}\n                </SortableContext>\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={activeColumns.length} className=\"h-24 text-center\">\n                    No results.\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </DndContext>\n      </div>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"hidden flex-1 text-sm text-muted-foreground lg:flex\">\n          {table.getFilteredRowModel().rows.length} row(s) total.\n        </div>\n        <div className=\"flex w-full items-center gap-8 lg:w-fit\">\n          <div className=\"hidden items-center gap-2 lg:flex\">\n            <Label htmlFor=\"rows-per-page\" className=\"text-sm font-medium\">\n              Rows per page\n            </Label>\n            <Select\n              value={`${table.getState().pagination.pageSize}`}\n              onValueChange={(value) => {\n                table.setPageSize(Number(value));\n              }}\n            >\n              <SelectTrigger\n                className=\"h-9 w-20 border border-border bg-background px-3 py-1 text-sm text-foreground\"\n                id=\"rows-per-page\"\n              >\n                <SelectValue placeholder={table.getState().pagination.pageSize} />\n              </SelectTrigger>\n              <SelectContent side=\"top\">\n                {[5, 10, 20, 30, 40, 50].map((pageSize) => (\n                  <SelectItem key={pageSize} value={`${pageSize}`}>\n                    {pageSize}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n          <div className=\"flex w-fit items-center justify-center text-sm font-medium\">\n            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}\n          </div>\n          <div className=\"ml-auto flex items-center gap-2 lg:ml-0\">\n            <Button\n              variant=\"outline\"\n              className=\"hidden size-8 p-0 lg:flex\"\n              onClick={() => table.setPageIndex(0)}\n              disabled={!table.getCanPreviousPage()}\n            >\n              <span className=\"sr-only\">Go to first page</span>\n              <ChevronsLeftIcon />\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"size-8\"\n              size=\"icon\"\n              onClick={() => table.previousPage()}\n              disabled={!table.getCanPreviousPage()}\n            >\n              <span className=\"sr-only\">Go to previous page</span>\n              <ChevronLeftIcon />\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"size-8\"\n              size=\"icon\"\n              onClick={() => table.nextPage()}\n              disabled={!table.getCanNextPage()}\n            >\n              <span className=\"sr-only\">Go to next page</span>\n              <ChevronRightIcon />\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"hidden size-8 lg:flex\"\n              size=\"icon\"\n              onClick={() => table.setPageIndex(table.getPageCount() - 1)}\n              disabled={!table.getCanNextPage()}\n            >\n              <span className=\"sr-only\">Go to last page</span>\n              <ChevronsRightIcon />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "type": "tsx"}], "dependencies": ["badge", "button", "checkbox", "dropdown-menu", "label", "select", "skeleton", "table", "tabs"]}, {"name": "dialog", "type": "component", "files": [{"path": "components/ui/dialog.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { X } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\n      className,\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, onInteractOutside, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\n        className,\n      )}\n      onInteractOutside={onInteractOutside || ((e) => e.preventDefault())}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"size-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn('flex flex-col space-y-1.5 text-center sm:text-left', className)} {...props} />\n);\nDialogHeader.displayName = 'DialogHeader';\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2', className)}\n    {...props}\n  />\n);\nDialogFooter.displayName = 'DialogFooter';\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn('text-lg font-semibold leading-none tracking-tight', className)}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "drawer", "type": "component", "files": [{"path": "components/ui/drawer.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Drawer = ({\n  shouldScaleBackground = true,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\n  <DrawerPrimitive.Root\n    shouldScaleBackground={shouldScaleBackground}\n    {...props}\n  />\n)\nDrawer.displayName = \"Drawer\"\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\n    {...props}\n  />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = \"DrawerContent\"\n\nconst DrawerHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\n    {...props}\n  />\n)\nDrawerHeader.displayName = \"DrawerHeader\"\n\nconst DrawerFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n    {...props}\n  />\n)\nDrawerFooter.displayName = \"DrawerFooter\"\n\nconst DrawerTitle = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "dropdown-menu", "type": "component", "files": [{"path": "components/ui/dropdown-menu.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"size-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"size-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "empty-state", "type": "component", "files": [{"path": "components/ui/empty-state.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nexport interface EmptyStateProps {\n  icon?: LucideIcon;\n  title: string;\n  description?: string;\n  actions?: React.ReactNode;\n  className?: string;\n  variant?: 'default' | 'card' | 'page';\n}\n\nexport function EmptyState({\n  icon: Icon,\n  title,\n  description,\n  actions,\n  className,\n  variant = 'default',\n}: EmptyStateProps) {\n  const baseClasses = 'text-center';\n  \n  if (variant === 'page') {\n    return (\n      <div className={cn('flex min-h-screen items-center justify-center bg-background', className)}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.3 }}\n          className=\"max-w-md p-8\"\n        >\n          {Icon && (\n            <motion.div\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.1 }}\n              className=\"mb-6 flex justify-center\"\n            >\n              <Icon className=\"size-16 text-muted-foreground\" />\n            </motion.div>\n          )}\n          <motion.h2\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n            className=\"mb-2 text-xl font-semibold text-foreground\"\n          >\n            {title}\n          </motion.h2>\n          {description && (\n            <motion.p\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.3 }}\n              className=\"mb-6 text-muted-foreground\"\n            >\n              {description}\n            </motion.p>\n          )}\n          {actions && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.4 }}\n              className=\"flex justify-center gap-3\"\n            >\n              {actions}\n            </motion.div>\n          )}\n        </motion.div>\n      </div>\n    );\n  }\n\n  if (variant === 'card') {\n    return (\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 0.3 }}\n        className={cn(\n          'rounded-xl border border-border bg-card p-8 text-center shadow-sm',\n          className\n        )}\n      >\n        {Icon && (\n          <div className=\"mb-4 flex justify-center\">\n            <Icon className=\"size-12 text-muted-foreground\" />\n          </div>\n        )}\n        <h3 className=\"mb-2 text-lg font-semibold text-foreground\">{title}</h3>\n        {description && (\n          <p className=\"mb-4 text-sm text-muted-foreground\">{description}</p>\n        )}\n        {actions && <div className=\"flex justify-center gap-2\">{actions}</div>}\n      </motion.div>\n    );\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      className={cn(baseClasses, 'py-12', className)}\n    >\n      {Icon && <Icon className=\"mx-auto mb-4 size-12 text-muted-foreground\" />}\n      <h3 className=\"mb-2 text-lg font-semibold text-foreground\">{title}</h3>\n      {description && (\n        <p className=\"mb-4 text-muted-foreground\">{description}</p>\n      )}\n      {actions && <div className=\"flex justify-center gap-2\">{actions}</div>}\n    </motion.div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "file-upload", "type": "component", "files": [{"path": "components/ui/file-upload.tsx", "content": "'use client';\n\nimport React from 'react';\nimport { useState, useRef } from 'react';\nimport { Upload, X, File as FileIcon, AlertCircle } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { toast } from 'sonner';\nimport { validateClientFile, formatFileSize, getFileIcon } from '@/lib/files/file-upload-client';\n\ninterface FileUploadProps {\n  id?: string;\n  onUpload: (url: string) => void;\n  onFileSelect?: (file: File) => void;\n  onRemove?: () => void;\n  currentFile?: File | string | null;\n  className?: string;\n  disabled?: boolean;\n  placeholder?: string;\n  accept?: string;\n  allowedTypes?: string[];\n  maxFileSize?: number;\n  showPreview?: boolean;\n  compact?: boolean; // New prop for compact mode\n}\n\nexport function FileUpload({\n  id,\n  onUpload,\n  onFileSelect,\n  onRemove,\n  currentFile,\n  className = '',\n  disabled = false,\n  placeholder = 'Upload file',\n  accept,\n  allowedTypes,\n  maxFileSize,\n  showPreview = true,\n  compact = false, // Default to false for backward compatibility\n}: FileUploadProps) {\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const resetUploadState = () => {\n    setError(null);\n\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleFileSelect = async (file: File) => {\n    // Use client-side validation utilities\n    const validation = validateClientFile(file, maxFileSize, allowedTypes);\n    if (!validation.valid) {\n      setError(validation.error || 'Invalid file');\n      toast.error(validation.error || 'Invalid file');\n      return;\n    }\n\n    // Reset error state\n    setError(null);\n\n    // Store the file for later upload\n    onFileSelect?.(file);\n    onUpload(file.name); // Use file name as temporary identifier\n  };\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n    if (disabled) return;\n    const file = event.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\n    if (!disabled) {\n      event.preventDefault();\n    }\n  };\n\n  const triggerFileSelect = () => {\n    if (!disabled && fileInputRef.current) {\n      fileInputRef.current.click();\n    }\n  };\n\n  const handleRemove = () => {\n    onRemove?.();\n  };\n\n  const getDisplayFileName = () => {\n    if (!currentFile) return null;\n    if (typeof currentFile === 'string') {\n      // If it's a URL, extract the filename\n      return currentFile.split('/').pop() || currentFile;\n    }\n    return currentFile.name;\n  };\n\n  const getFileTypeIcon = () => {\n    if (!currentFile) return <FileIcon className=\"size-8 text-muted-foreground\" />;\n\n    const fileType =\n      typeof currentFile === 'string'\n        ? 'application/octet-stream' // Default for URL strings\n        : currentFile.type;\n\n    const icon = getFileIcon(fileType);\n    return <span className=\"text-2xl\">{icon}</span>;\n  };\n\n  const formatAcceptedTypes = () => {\n    if (!allowedTypes || allowedTypes.length === 0) return 'All file types';\n\n    // Create a more human-readable format\n    const formatMap: Record<string, string> = {\n      'application/pdf': 'PDF',\n      'application/msword': 'Word',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'text/csv': 'CSV',\n      'application/json': 'JSON',\n      'text/plain': 'Text',\n      'text/x-python': 'Python',\n      'text/javascript': 'JavaScript',\n      'application/javascript': 'JavaScript',\n      'application/x-yaml': 'YAML',\n      'text/yaml': 'YAML',\n      'application/yaml': 'YAML',\n    };\n\n    const formats = allowedTypes.map(\n      (type) => formatMap[type] || type.split('/')[1]?.toUpperCase() || type,\n    );\n\n    // Remove duplicates and sort\n    const uniqueFormats = [...new Set(formats)].sort();\n\n    if (compact) {\n      // For compact mode, show fewer formats\n      return uniqueFormats.length <= 3 ? uniqueFormats.join(', ') : uniqueFormats.join(', ');\n    }\n\n    return uniqueFormats.join(', ');\n  };\n\n  const formatMaxSize = () => {\n    if (!maxFileSize) return '';\n    return ` up to ${formatFileSize(maxFileSize)}`;\n  };\n\n  return (\n    <div className={`${className}`}>\n      <input\n        id={id}\n        ref={fileInputRef}\n        type=\"file\"\n        accept={accept}\n        onChange={handleFileChange}\n        className=\"hidden\"\n        disabled={disabled}\n      />\n\n      {currentFile && showPreview ? (\n        <div className=\"group relative\">\n          <div className=\"relative h-fit w-full overflow-hidden rounded-md border border-border/50 bg-muted/30 p-3\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"text-base opacity-60\">{getFileTypeIcon()}</div>\n              <div className=\"min-w-0 flex-1\">\n                <p className=\"truncate text-xs font-medium text-foreground\">\n                  {getDisplayFileName()}\n                </p>\n                {typeof currentFile !== 'string' && (\n                  <p className=\"text-xs text-muted-foreground/70\">\n                    {formatFileSize(currentFile.size)}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Error overlay */}\n            {error && (\n              <div className=\"absolute inset-0 flex flex-col items-center justify-center gap-2 bg-destructive/90 backdrop-blur-sm\">\n                <AlertCircle className=\"size-4 text-destructive-foreground\" />\n                <p className=\"px-2 text-center text-xs text-destructive-foreground\">{error}</p>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={resetUploadState}\n                  className=\"h-6 bg-background/50 px-2 text-xs\"\n                >\n                  Dismiss\n                </Button>\n              </div>\n            )}\n\n            {/* Remove button */}\n            {!disabled && !error && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"absolute right-1 top-1 size-5 p-0 opacity-0 transition-opacity hover:bg-destructive/20 hover:text-destructive group-hover:opacity-70\"\n                onClick={handleRemove}\n              >\n                <X className=\"size-3\" />\n              </Button>\n            )}\n          </div>\n        </div>\n      ) : (\n        <div\n          className={`rounded-lg border-2 border-dashed border-border p-6 text-center transition-colors hover:border-primary/50 ${\n            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'\n          } ${compact ? 'p-4' : ''}`}\n          onDrop={handleDrop}\n          onDragOver={handleDragOver}\n          onClick={triggerFileSelect}\n          role=\"button\"\n          tabIndex={disabled ? -1 : 0}\n          onKeyDown={(e) => {\n            if ((e.key === 'Enter' || e.key === ' ') && !disabled) {\n              e.preventDefault();\n              triggerFileSelect();\n            }\n          }}\n        >\n          <div className=\"flex flex-col items-center gap-2\">\n            {error ? (\n              <div className=\"flex flex-col items-center gap-2\">\n                <AlertCircle className=\"size-8 text-destructive\" />\n                <span className=\"text-sm text-destructive\">{error}</span>\n              </div>\n            ) : (\n              <FileIcon className=\"size-8 text-muted-foreground\" />\n            )}\n\n            {!error && (\n              <div className=\"text-sm\">\n                <span className=\"font-medium text-foreground\">{placeholder}</span>\n                {!compact && (\n                  <p className=\"mt-1 text-muted-foreground\">Drag and drop or click to browse</p>\n                )}\n              </div>\n            )}\n\n            <p className=\"text-xs text-muted-foreground\">\n              {formatAcceptedTypes()}\n              {formatMaxSize()}\n            </p>\n          </div>\n        </div>\n      )}\n\n      {!currentFile && !compact && (\n        <Button\n          variant=\"outline\"\n          onClick={triggerFileSelect}\n          disabled={disabled}\n          className=\"w-full\"\n        >\n          <Upload className=\"mr-2 size-4\" />\n          Choose File\n        </Button>\n      )}\n\n      {/* Retry button for errors */}\n      {error && !currentFile && !compact && (\n        <Button\n          variant=\"outline\"\n          onClick={() => {\n            resetUploadState();\n            triggerFileSelect();\n          }}\n          className=\"w-full\"\n        >\n          Try Again\n        </Button>\n      )}\n    </div>\n  );\n}\n", "type": "tsx"}], "dependencies": ["button"]}, {"name": "form", "type": "component", "files": [{"path": "components/ui/form.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  ControllerProps,\n  FieldPath,\n  FieldValues,\n  FormProvider,\n  useFormContext,\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState, formState } = useFormContext();\n\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\n\nconst FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => {\n    const id = React.useId();\n\n    return (\n      <FormItemContext.Provider value={{ id }}>\n        <div ref={ref} className={cn('space-y-2', className)} {...props} />\n      </FormItemContext.Provider>\n    );\n  },\n);\nFormItem.displayName = 'FormItem';\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && 'text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n});\nFormLabel.displayName = 'FormLabel';\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={error ? `${formDescriptionId} ${formMessageId}` : `${formDescriptionId}`}\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n});\nFormControl.displayName = 'FormControl';\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  );\n});\nFormDescription.displayName = 'FormDescription';\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message) : children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn('text-sm font-medium text-destructive', className)}\n      {...props}\n    >\n      {body}\n    </p>\n  );\n});\nFormMessage.displayName = 'FormMessage';\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["label"]}, {"name": "gradient-button", "type": "component", "files": [{"path": "components/ui/gradient-button.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { Button, ButtonProps } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nexport interface GradientButtonProps extends ButtonProps {\n  gradient?: 'brand' | 'blue' | 'green' | 'orange' | 'red' | 'purple';\n  icon?: LucideIcon;\n  iconPosition?: 'left' | 'right';\n  shimmer?: boolean;\n}\n\nconst gradientVariants = {\n  brand: 'bg-brand text-brand-foreground shadow-lg hover:bg-brand/90',\n  blue: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:from-blue-600 hover:to-blue-700',\n  green: 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:from-green-600 hover:to-green-700',\n  orange: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg hover:from-orange-600 hover:to-orange-700',\n  red: 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:from-red-600 hover:to-red-700',\n  purple: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg hover:from-purple-600 hover:to-purple-700',\n};\n\nexport function GradientButton({\n  children,\n  className,\n  gradient = 'brand',\n  icon: Icon,\n  iconPosition = 'left',\n  shimmer = false,\n  disabled,\n  ...props\n}: GradientButtonProps) {\n  const gradientClass = gradientVariants[gradient];\n  \n  return (\n    <Button\n      className={cn(\n        'relative overflow-hidden transition-all duration-200 hover:scale-105 disabled:scale-100 disabled:opacity-50',\n        gradientClass,\n        shimmer && !disabled && 'before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:animate-[shimmer_2s_infinite]',\n        className\n      )}\n      disabled={disabled}\n      {...props}\n    >\n      {Icon && iconPosition === 'left' && (\n        <Icon className=\"mr-2 size-4\" />\n      )}\n      {children}\n      {Icon && iconPosition === 'right' && (\n        <Icon className=\"ml-2 size-4\" />\n      )}\n      \n      {shimmer && !disabled && (\n        <style jsx>{`\n          @keyframes shimmer {\n            0% {\n              transform: translateX(-100%);\n            }\n            100% {\n              transform: translateX(100%);\n            }\n          }\n        `}</style>\n      )}\n    </Button>\n  );\n}\n\n// Special variant for the Return to Dashboard button pattern\nexport function ReturnButton({\n  href = '/',\n  children = 'Return to Dashboard',\n  className,\n  ...props\n}: Omit<GradientButtonProps, 'gradient'> & { href?: string }) {\n  return (\n    <motion.a\n      href={href}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      className={cn('inline-block', className)}\n    >\n      <GradientButton gradient=\"brand\" {...props}>\n        {children}\n      </GradientButton>\n    </motion.a>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "input-otp", "type": "component", "files": [{"path": "components/ui/input-otp.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { OTPInput, OTPInputContext } from \"input-otp\"\nimport { Dot } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst InputOTP = React.forwardRef<\n  React.ElementRef<typeof OTPInput>,\n  React.ComponentPropsWithoutRef<typeof OTPInput>\n>(({ className, containerClassName, ...props }, ref) => (\n  <OTPInput\n    ref={ref}\n    containerClassName={cn(\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\n      containerClassName\n    )}\n    className={cn(\"disabled:cursor-not-allowed\", className)}\n    {...props}\n  />\n))\nInputOTP.displayName = \"InputOTP\"\n\nconst InputOTPGroup = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\n))\nInputOTPGroup.displayName = \"InputOTPGroup\"\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext)\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index]\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\n        isActive && \"z-10 ring-2 ring-ring ring-offset-background\",\n        className\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\n          <div className=\"animate-caret-blink h-4 w-px bg-foreground duration-1000\" />\n        </div>\n      )}\n    </div>\n  )\n})\nInputOTPSlot.displayName = \"InputOTPSlot\"\n\nconst InputOTPSeparator = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ ...props }, ref) => (\n  <div ref={ref} role=\"separator\" {...props}>\n    <Dot />\n  </div>\n))\nInputOTPSeparator.displayName = \"InputOTPSeparator\"\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "input", "type": "component", "files": [{"path": "components/ui/input.tsx", "content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "label", "type": "component", "files": [{"path": "components/ui/label.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "loader", "type": "component", "files": [{"path": "components/ui/loader.tsx", "content": "'use client';\nimport { motion } from 'framer-motion';\nimport React from 'react';\n\n// Minimal bouncing dots - 3 dots with simple bounce\nexport const LoaderOne = () => {\n  return (\n    <div className=\"flex items-center gap-1\">\n      {[0, 1, 2].map((i) => (\n        <motion.div\n          key={i}\n          className=\"size-2 rounded-full bg-purple-400\"\n          animate={{ y: [0, -8, 0] }}\n          transition={{\n            duration: 0.6,\n            repeat: Infinity,\n            delay: i * 0.1,\n            ease: 'easeInOut',\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// Minimal sliding dots - simple horizontal movement\nexport const LoaderTwo = () => {\n  return (\n    <div className=\"flex items-center space-x-1\">\n      {[0, 1, 2].map((i) => (\n        <motion.div\n          key={i}\n          className=\"size-2 rounded-full bg-purple-400\"\n          animate={{ x: [0, 12, 0] }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: i * 0.2,\n            ease: 'easeInOut',\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// Minimal spinning circle - single element\nexport const LoaderThree = () => {\n  return (\n    <motion.div\n      className=\"size-8 rounded-full border-2 border-purple-400 border-t-transparent\"\n      animate={{ rotate: 360 }}\n      transition={{\n        duration: 1,\n        repeat: Infinity,\n        ease: 'linear',\n      }}\n    />\n  );\n};\n\n// Minimal pulse - simple scale animation\nexport const LoaderFour = ({ text = 'Loading...' }: { text?: string }) => {\n  return (\n    <motion.div\n      className=\"text-sm font-medium text-gray-400\"\n      animate={{ opacity: [0.5, 1, 0.5] }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n    >\n      {text}\n    </motion.div>\n  );\n};\n", "type": "tsx"}], "dependencies": []}, {"name": "loading-state", "type": "component", "files": [{"path": "components/ui/loading-state.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingStateProps {\n  message?: string;\n  description?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  icon?: React.ReactNode;\n}\n\nconst sizeClasses = {\n  sm: {\n    icon: 'size-8',\n    title: 'text-lg',\n    description: 'text-sm',\n  },\n  md: {\n    icon: 'size-12',\n    title: 'text-xl',\n    description: 'text-base',\n  },\n  lg: {\n    icon: 'size-16',\n    title: 'text-2xl',\n    description: 'text-lg',\n  },\n};\n\nexport function LoadingState({\n  message = 'Loading...',\n  description,\n  size = 'md',\n  className,\n  icon,\n}: LoadingStateProps) {\n  const sizes = sizeClasses[size];\n\n  return (\n    <div className={cn('flex min-h-screen items-center justify-center bg-background', className)}>\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 0.3 }}\n        className=\"p-8 text-center\"\n      >\n        {icon || (\n          <Loader2 className={cn('mx-auto mb-4 animate-spin text-brand', sizes.icon)} />\n        )}\n        <motion.h2\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.1 }}\n          className={cn('mb-2 font-semibold text-foreground', sizes.title)}\n        >\n          {message}\n        </motion.h2>\n        {description && (\n          <motion.p\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n            className=\"text-muted-foreground\"\n          >\n            {description}\n          </motion.p>\n        )}\n      </motion.div>\n    </div>\n  );\n}\n\n// Component for loading cards or sections\nexport function LoadingCard({\n  lines = 3,\n  className,\n}: {\n  lines?: number;\n  className?: string;\n}) {\n  return (\n    <div className={cn('animate-pulse space-y-4', className)}>\n      <div className=\"h-4 rounded bg-muted\" />\n      {Array.from({ length: lines - 1 }).map((_, i) => (\n        <div\n          key={i}\n          className={cn('h-4 rounded bg-muted', i === lines - 2 ? 'w-3/4' : '')}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Component for loading skeleton grids\nexport function LoadingSkeletonGrid({\n  items = 4,\n  className,\n}: {\n  items?: number;\n  className?: string;\n}) {\n  return (\n    <div className={cn('grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>\n      {Array.from({ length: items }).map((_, i) => (\n        <LoadingCard key={i} lines={3} />\n      ))}\n    </div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "page-header", "type": "component", "files": [{"path": "components/ui/page-header.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport interface PageHeaderProps {\n  title: string;\n  description?: string;\n  icon?: LucideIcon;\n  iconGradient?: string;\n  actions?: React.ReactNode;\n  className?: string;\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon: Icon,\n  iconGradient = 'from-brand to-brand/80',\n  actions,\n  className,\n}: PageHeaderProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n      className={cn('space-y-6', className)}\n    >\n      <div className=\"flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between\">\n        <div className=\"space-y-3\">\n          {Icon && (\n            <div className=\"flex items-center gap-3\">\n              <motion.div\n                initial={{ rotate: -180, scale: 0 }}\n                animate={{ rotate: 0, scale: 1 }}\n                transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}\n                className={cn('rounded-lg bg-gradient-to-br p-2', iconGradient)}\n              >\n                <Icon className=\"size-6 text-brand-foreground\" />\n              </motion.div>\n              <motion.h1\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5, delay: 0.1 }}\n                className=\"bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent\"\n              >\n                {title}\n              </motion.h1>\n            </div>\n          )}\n          {!Icon && (\n            <motion.h1\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              className=\"bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent\"\n            >\n              {title}\n            </motion.h1>\n          )}\n          {description && (\n            <motion.p\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              className=\"max-w-2xl text-lg text-muted-foreground\"\n            >\n              {description}\n            </motion.p>\n          )}\n        </div>\n        {actions && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3, delay: 0.3 }}\n            className=\"flex items-center gap-3\"\n          >\n            {actions}\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "pagination-controls", "type": "component", "files": [{"path": "components/ui/pagination-controls.tsx", "content": "import { Button } from '@/components/ui/button';\n\nexport type PaginationControlsProps = {\n  page: number;\n  totalPages: number;\n  onPrevious: () => void;\n  onNext: () => void;\n  pageSize: number;\n  onPageSizeChange: (size: number) => void;\n  pageSizeOptions?: number[];\n  disabled?: boolean;\n  totalItems?: number;\n};\n\nexport function PaginationControls({\n  page,\n  totalPages,\n  onPrevious,\n  onNext,\n  pageSize,\n  onPageSizeChange,\n  pageSizeOptions = [5, 10, 25, 50],\n  disabled = false,\n  totalItems = 0,\n}: PaginationControlsProps) {\n  const startItem = page * pageSize + 1;\n  const endItem = Math.min((page + 1) * pageSize, totalItems);\n\n  return (\n    <div className=\"mt-6 flex flex-wrap items-center justify-between gap-4\">\n      <div className=\"flex items-center space-x-3\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          className=\"border-border/50 bg-background/50 font-semibold text-foreground transition-colors hover:bg-background/70 disabled:opacity-50\"\n          onClick={onPrevious}\n          disabled={disabled || page === 0}\n        >\n          Previous\n        </Button>\n        <span className=\"px-3 text-base font-medium text-muted-foreground\">\n          {totalItems > 0 ? `${startItem}-${endItem} of ${totalItems}` : 'No items'}\n        </span>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          className=\"border-border/50 bg-background/50 font-semibold text-foreground transition-colors hover:bg-background/70 disabled:opacity-50\"\n          onClick={onNext}\n          disabled={disabled || page >= totalPages - 1}\n        >\n          Next\n        </Button>\n      </div>\n      <div className=\"flex items-center space-x-3\">\n        <span className=\"text-base font-medium text-muted-foreground\">Show:</span>\n        <select\n          value={pageSize}\n          onChange={(e) => onPageSizeChange(Number(e.target.value))}\n          className=\"rounded border border-border/50 bg-background/50 px-4 py-2 text-base font-medium text-foreground transition-colors hover:bg-background/70 focus:outline-none focus:ring-2 focus:ring-brand/50\"\n        >\n          {pageSizeOptions.map((size) => (\n            <option key={size} value={size}>\n              {size}\n            </option>\n          ))}\n        </select>\n      </div>\n    </div>\n  );\n}\n", "type": "tsx"}], "dependencies": ["button"]}, {"name": "pagination", "type": "component", "files": [{"path": "components/ui/pagination.tsx", "content": "import * as React from 'react';\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\nimport { ButtonProps, buttonVariants } from '@/components/ui/button';\n\nconst Pagination = ({ className, ...props }: React.ComponentProps<'nav'>) => (\n  <nav\n    role=\"navigation\"\n    aria-label=\"pagination\"\n    className={cn('mx-auto flex w-full justify-center', className)}\n    {...props}\n  />\n);\nPagination.displayName = 'Pagination';\n\nconst PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(\n  ({ className, ...props }, ref) => (\n    <ul ref={ref} className={cn('flex flex-row items-center gap-1', className)} {...props} />\n  ),\n);\nPaginationContent.displayName = 'PaginationContent';\n\nconst PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(\n  ({ className, ...props }, ref) => <li ref={ref} className={cn('', className)} {...props} />,\n);\nPaginationItem.displayName = 'PaginationItem';\n\ntype PaginationLinkProps = {\n  isActive?: boolean;\n} & Pick<ButtonProps, 'size'> &\n  React.ComponentProps<'a'>;\n\nconst PaginationLink = ({ className, isActive, size = 'icon', ...props }: PaginationLinkProps) => (\n  <a\n    aria-current={isActive ? 'page' : undefined}\n    className={cn(\n      buttonVariants({\n        variant: isActive ? 'outline' : 'ghost',\n        size,\n      }),\n      className,\n    )}\n    {...props}\n  />\n);\nPaginationLink.displayName = 'PaginationLink';\n\nconst PaginationPrevious = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to previous page\"\n    size=\"default\"\n    className={cn('gap-1 pl-2.5', className)}\n    {...props}\n  >\n    <ChevronLeft className=\"size-4\" />\n    <span>Previous</span>\n  </PaginationLink>\n);\nPaginationPrevious.displayName = 'PaginationPrevious';\n\nconst PaginationNext = ({ className, ...props }: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to next page\"\n    size=\"default\"\n    className={cn('gap-1 pr-2.5', className)}\n    {...props}\n  >\n    <span>Next</span>\n    <ChevronRight className=\"size-4\" />\n  </PaginationLink>\n);\nPaginationNext.displayName = 'PaginationNext';\n\nconst PaginationEllipsis = ({ className, ...props }: React.ComponentProps<'span'>) => (\n  <span\n    aria-hidden\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"size-4\" />\n    <span className=\"sr-only\">More pages</span>\n  </span>\n);\nPaginationEllipsis.displayName = 'PaginationEllipsis';\n\nexport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "percent-bar", "type": "component", "files": [{"path": "components/ui/percent-bar.tsx", "content": "// A reusable component to display a percentage bar with a label and color.\nexport function PercentBar({\n  name,\n  percentage,\n  color,\n}: {\n  /** Label shown on the left side */\n  name: string;\n  /** e.g. \"42%\" – the CSS width of the coloured bar */\n  percentage: string;\n  /** A Tailwind bg-class (e.g. \"bg-brand\") **OR** a CSS colour string (e.g. \"#22c55e\") */\n  color: string;\n}) {\n  const isClass = color.startsWith('bg-');\n\n  return (\n    <div className=\"space-y-1.5\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          {isClass ? (\n            <span className={`size-3 ${color} rounded-full`} />\n          ) : (\n            <span className=\"size-3 rounded-full\" style={{ backgroundColor: color }} />\n          )}\n          <span className=\"text-sm text-muted-foreground\">{name}</span>\n        </div>\n        <span className=\"font-medium text-foreground\">{percentage}</span>\n      </div>\n      <div className=\"h-2 w-full overflow-hidden rounded-full bg-muted\">\n        {isClass ? (\n          <div className={`${color} h-2`} style={{ width: percentage }} />\n        ) : (\n          <div className=\"h-2\" style={{ width: percentage, backgroundColor: color }} />\n        )}\n      </div>\n    </div>\n  );\n}\n", "type": "tsx"}], "dependencies": []}, {"name": "popover", "type": "component", "files": [{"path": "components/ui/popover.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\n\nimport { cn } from '@/lib/utils';\n\nconst Popover = PopoverPrimitive.Root;\n\nconst PopoverTrigger = PopoverPrimitive.Trigger;\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = 'center', sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className,\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n));\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\n\nexport { Popover, PopoverTrigger, PopoverContent };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "profile-completion-banner", "type": "component", "files": [{"path": "components/ui/profile-completion-banner.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nexport interface ProfileCompletionBannerProps {\n  title: string;\n  description: string;\n  progress: number; // 0-100\n  actionText?: string;\n  onAction?: () => void;\n  icon?: LucideIcon;\n  className?: string;\n  variant?: 'info' | 'warning' | 'success';\n}\n\nconst variantStyles = {\n  info: {\n    bg: 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-200/50 dark:border-blue-800/50',\n    progress: 'bg-blue-500',\n    button: 'bg-blue-500 hover:bg-blue-600 text-white',\n    icon: 'text-blue-500',\n  },\n  warning: {\n    bg: 'bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-200/50 dark:border-orange-800/50',\n    progress: 'bg-orange-500',\n    button: 'bg-orange-500 hover:bg-orange-600 text-white',\n    icon: 'text-orange-500',\n  },\n  success: {\n    bg: 'bg-gradient-to-r from-green-500/10 to-green-600/10 border-green-200/50 dark:border-green-800/50',\n    progress: 'bg-green-500',\n    button: 'bg-green-500 hover:bg-green-600 text-white',\n    icon: 'text-green-500',\n  },\n};\n\nexport function ProfileCompletionBanner({\n  title,\n  description,\n  progress,\n  actionText,\n  onAction,\n  icon: Icon,\n  className,\n  variant = 'info',\n}: ProfileCompletionBannerProps) {\n  const styles = variantStyles[variant];\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={cn(\n        'rounded-xl border p-6 backdrop-blur-sm',\n        styles.bg,\n        className\n      )}\n    >\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1 space-y-4\">\n          <div className=\"flex items-center space-x-3\">\n            {Icon && <Icon className={cn('size-5', styles.icon)} />}\n            <h3 className=\"text-lg font-semibold text-foreground\">{title}</h3>\n          </div>\n          \n          <p className=\"text-muted-foreground\">{description}</p>\n          \n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-muted-foreground\">Profile Completion</span>\n              <span className=\"font-medium text-foreground\">{progress}%</span>\n            </div>\n            <div className=\"h-2 overflow-hidden rounded-full bg-muted\">\n              <motion.div\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.8, ease: 'easeOut' }}\n                className={cn('h-full rounded-full', styles.progress)}\n              />\n            </div>\n          </div>\n        </div>\n        \n        {actionText && onAction && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3, delay: 0.3 }}\n            className=\"ml-6\"\n          >\n            <Button\n              onClick={onAction}\n              className={cn('shadow-lg transition-all duration-200 hover:scale-105', styles.button)}\n            >\n              {actionText}\n            </Button>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button"]}, {"name": "progress", "type": "component", "files": [{"path": "components/ui/progress.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\n\nimport { cn } from '@/lib/utils';\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn('relative h-2 w-full overflow-hidden rounded-full bg-primary/20', className)}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"size-full flex-1 bg-gradient-to-r from-indigo-500 to-purple-500 transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n));\nProgress.displayName = ProgressPrimitive.Root.displayName;\n\nexport { Progress };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "radio-group", "type": "component", "files": [{"path": "components/ui/radio-group.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as RadioGroupPrimitive from '@radix-ui/react-radio-group';\nimport { Circle } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return <RadioGroupPrimitive.Root className={cn('grid gap-2', className)} {...props} ref={ref} />;\n});\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName;\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        'aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n        className,\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"size-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  );\n});\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;\n\nexport { RadioGroup, RadioGroupItem };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "select", "type": "component", "files": [{"path": "components/ui/select.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"size-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"size-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"size-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"size-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "separator", "type": "component", "files": [{"path": "components/ui/separator.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "sheet", "type": "component", "files": [{"path": "components/ui/sheet.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"size-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "sidebar-nav", "type": "component", "files": [{"path": "components/ui/sidebar-nav.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport interface SidebarNavItem {\n  id: string;\n  label: string;\n  description?: string;\n  icon: LucideIcon;\n  disabled?: boolean;\n  badge?: string | number;\n}\n\nexport interface SidebarNavProps {\n  items: SidebarNavItem[];\n  activeItemId?: string;\n  onItemSelect?: (itemId: string) => void;\n  className?: string;\n  variant?: 'default' | 'pills';\n}\n\nexport function SidebarNav({\n  items,\n  activeItemId,\n  onItemSelect,\n  className,\n  variant = 'default',\n}: SidebarNavProps) {\n  if (variant === 'pills') {\n    return (\n      <nav className={cn('space-y-1', className)}>\n        {items.map((item, index) => {\n          const Icon = item.icon;\n          const isActive = activeItemId === item.id;\n          \n          return (\n            <motion.button\n              key={item.id}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n              onClick={() => !item.disabled && onItemSelect?.(item.id)}\n              disabled={item.disabled}\n              className={cn(\n                'group relative w-full rounded-lg px-4 py-3 text-left transition-all duration-200',\n                isActive\n                  ? 'bg-brand text-brand-foreground shadow-lg'\n                  : 'text-muted-foreground hover:bg-muted hover:text-foreground',\n                item.disabled && 'cursor-not-allowed opacity-50'\n              )}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <Icon\n                    className={cn(\n                      'size-5 transition-colors duration-200',\n                      isActive ? 'text-brand-foreground' : 'text-muted-foreground group-hover:text-foreground'\n                    )}\n                  />\n                  <div>\n                    <div className=\"font-medium\">{item.label}</div>\n                    {item.description && (\n                      <div className=\"text-xs text-muted-foreground group-hover:text-foreground/80\">\n                        {item.description}\n                      </div>\n                    )}\n                  </div>\n                </div>\n                {item.badge && (\n                  <span\n                    className={cn(\n                      'rounded-full px-2 py-1 text-xs font-medium',\n                      isActive\n                        ? 'bg-brand-foreground/20 text-brand-foreground'\n                        : 'bg-muted text-muted-foreground'\n                    )}\n                  >\n                    {item.badge}\n                  </span>\n                )}\n              </div>\n            </motion.button>\n          );\n        })}\n      </nav>\n    );\n  }\n\n  return (\n    <nav className={cn('space-y-1', className)}>\n      {items.map((item, index) => {\n        const Icon = item.icon;\n        const isActive = activeItemId === item.id;\n        \n        return (\n          <motion.button\n            key={item.id}\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3, delay: index * 0.1 }}\n            onClick={() => !item.disabled && onItemSelect?.(item.id)}\n            disabled={item.disabled}\n            className={cn(\n              'group w-full rounded-lg px-4 py-3 text-left transition-all duration-200',\n              isActive\n                ? 'border-l-4 border-brand bg-gradient-to-r from-brand/20 to-brand/30 text-brand'\n                : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground',\n              item.disabled && 'cursor-not-allowed opacity-50'\n            )}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <Icon\n                  className={cn(\n                    'size-5 transition-colors duration-200',\n                    isActive ? 'text-brand' : 'text-muted-foreground group-hover:text-foreground'\n                  )}\n                />\n                <div>\n                  <div className=\"font-medium\">{item.label}</div>\n                  {item.description && (\n                    <div className=\"text-xs text-muted-foreground group-hover:text-foreground\">\n                      {item.description}\n                    </div>\n                  )}\n                </div>\n              </div>\n              {item.badge && (\n                <span\n                  className={cn(\n                    'rounded-full px-2 py-1 text-xs font-medium',\n                    isActive\n                      ? 'bg-brand/20 text-brand'\n                      : 'bg-muted text-muted-foreground'\n                  )}\n                >\n                  {item.badge}\n                </span>\n              )}\n            </div>\n          </motion.button>\n        );\n      })}\n    </nav>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "sidebar", "type": "component", "files": [{"path": "components/ui/sidebar.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { VariantProps, cva } from 'class-variance-authority';\nimport { PanelLeft } from 'lucide-react';\n\nimport { useIsMobile } from '@/hooks/use-mobile';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Separator } from '@/components/ui/separator';\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from '@/components/ui/sheet';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\n\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = '16rem';\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\nconst SIDEBAR_WIDTH_ICON = '3rem';\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\n\ntype SidebarContextProps = {\n  state: 'expanded' | 'collapsed';\n  open: boolean;\n  setOpen: (open: boolean) => void;\n  openMobile: boolean;\n  setOpenMobile: (open: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n};\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext);\n  if (!context) {\n    throw new Error('useSidebar must be used within a SidebarProvider.');\n  }\n\n  return context;\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    defaultOpen?: boolean;\n    open?: boolean;\n    onOpenChange?: (open: boolean) => void;\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref,\n  ) => {\n    const isMobile = useIsMobile();\n    const [openMobile, setOpenMobile] = React.useState(false);\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen);\n    const open = openProp ?? _open;\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === 'function' ? value(open) : value;\n        if (setOpenProp) {\n          setOpenProp(openState);\n        } else {\n          _setOpen(openState);\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n      },\n      [setOpenProp, open],\n    );\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\n    }, [isMobile, setOpen, setOpenMobile]);\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n          event.preventDefault();\n          toggleSidebar();\n        }\n      };\n\n      globalThis.addEventListener('keydown', handleKeyDown);\n      return () => globalThis.removeEventListener('keydown', handleKeyDown);\n    }, [toggleSidebar]);\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? 'expanded' : 'collapsed';\n\n    const contextValue = React.useMemo<SidebarContextProps>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar],\n    );\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                '--sidebar-width': SIDEBAR_WIDTH,\n                '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              'group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar',\n              className,\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    );\n  },\n);\nSidebarProvider.displayName = 'SidebarProvider';\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    side?: 'left' | 'right';\n    variant?: 'sidebar' | 'floating' | 'inset';\n    collapsible?: 'offcanvas' | 'icon' | 'none';\n  }\n>(\n  (\n    {\n      side = 'left',\n      variant = 'sidebar',\n      collapsible = 'offcanvas',\n      className,\n      children,\n      ...props\n    },\n    ref,\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n\n    if (collapsible === 'none') {\n      return (\n        <div\n          className={cn(\n            'flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground',\n            className,\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      );\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                '--sidebar-width': SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <SheetHeader className=\"sr-only\">\n              <SheetTitle>Sidebar</SheetTitle>\n              <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n            </SheetHeader>\n            <div className=\"flex size-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      );\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden text-sidebar-foreground md:block\"\n        data-state={state}\n        data-collapsible={state === 'collapsed' ? collapsible : ''}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            'relative w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear',\n            'group-data-[collapsible=offcanvas]:w-0',\n            'group-data-[side=right]:rotate-180',\n            variant === 'floating' || variant === 'inset'\n              ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]'\n              : 'group-data-[collapsible=icon]:w-[--sidebar-width-icon]',\n          )}\n        />\n        <div\n          className={cn(\n            'fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex',\n            side === 'left'\n              ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'\n              : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\n            // Adjust the padding for floating and inset variants.\n            variant === 'floating' || variant === 'inset'\n              ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]'\n              : 'group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l',\n            className,\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex size-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    );\n  },\n);\nSidebar.displayName = 'Sidebar';\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar();\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn('h-7 w-7', className)}\n      onClick={(event) => {\n        onClick?.(event);\n        toggleSidebar();\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  );\n});\nSidebarTrigger.displayName = 'SidebarTrigger';\n\nconst SidebarRail = React.forwardRef<HTMLButtonElement, React.ComponentProps<'button'>>(\n  ({ className, ...props }, ref) => {\n    const { toggleSidebar } = useSidebar();\n\n    return (\n      <button\n        ref={ref}\n        data-sidebar=\"rail\"\n        aria-label=\"Toggle Sidebar\"\n        tabIndex={-1}\n        onClick={toggleSidebar}\n        title=\"Toggle Sidebar\"\n        className={cn(\n          'absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex',\n          '[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize',\n          '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize',\n          'group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar',\n          '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2',\n          '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2',\n          className,\n        )}\n        {...props}\n      />\n    );\n  },\n);\nSidebarRail.displayName = 'SidebarRail';\n\nconst SidebarInset = React.forwardRef<HTMLDivElement, React.ComponentProps<'main'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <main\n        ref={ref}\n        className={cn(\n          'relative flex w-full flex-1 flex-col bg-background',\n          'md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow',\n          className,\n        )}\n        {...props}\n      />\n    );\n  },\n);\nSidebarInset.displayName = 'SidebarInset';\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        'h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring',\n        className,\n      )}\n      {...props}\n    />\n  );\n});\nSidebarInput.displayName = 'SidebarInput';\n\nconst SidebarHeader = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-sidebar=\"header\"\n        className={cn('flex flex-col gap-2 p-2', className)}\n        {...props}\n      />\n    );\n  },\n);\nSidebarHeader.displayName = 'SidebarHeader';\n\nconst SidebarFooter = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-sidebar=\"footer\"\n        className={cn('flex flex-col gap-2 p-2', className)}\n        {...props}\n      />\n    );\n  },\n);\nSidebarFooter.displayName = 'SidebarFooter';\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn('mx-2 w-auto bg-sidebar-border', className)}\n      {...props}\n    />\n  );\n});\nSidebarSeparator.displayName = 'SidebarSeparator';\n\nconst SidebarContent = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-sidebar=\"content\"\n        className={cn(\n          'flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden',\n          className,\n        )}\n        {...props}\n      />\n    );\n  },\n);\nSidebarContent.displayName = 'SidebarContent';\n\nconst SidebarGroup = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-sidebar=\"group\"\n        className={cn('relative flex w-full min-w-0 flex-col p-2', className)}\n        {...props}\n      />\n    );\n  },\n);\nSidebarGroup.displayName = 'SidebarGroup';\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'div';\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        'flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n        'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0',\n        className,\n      )}\n      {...props}\n    />\n  );\n});\nSidebarGroupLabel.displayName = 'SidebarGroupLabel';\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<'button'> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        'absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n        // Increases the hit area of the button on mobile.\n        'after:absolute after:-inset-2 after:md:hidden',\n        'group-data-[collapsible=icon]:hidden',\n        className,\n      )}\n      {...props}\n    />\n  );\n});\nSidebarGroupAction.displayName = 'SidebarGroupAction';\n\nconst SidebarGroupContent = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      data-sidebar=\"group-content\"\n      className={cn('w-full text-sm', className)}\n      {...props}\n    />\n  ),\n);\nSidebarGroupContent.displayName = 'SidebarGroupContent';\n\nconst SidebarMenu = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(\n  ({ className, ...props }, ref) => (\n    <ul\n      ref={ref}\n      data-sidebar=\"menu\"\n      className={cn('flex w-full min-w-0 flex-col gap-1', className)}\n      {...props}\n    />\n  ),\n);\nSidebarMenu.displayName = 'SidebarMenu';\n\nconst SidebarMenuItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(\n  ({ className, ...props }, ref) => (\n    <li\n      ref={ref}\n      data-sidebar=\"menu-item\"\n      className={cn('group/menu-item relative', className)}\n      {...props}\n    />\n  ),\n);\nSidebarMenuItem.displayName = 'SidebarMenuItem';\n\nconst sidebarMenuButtonVariants = cva(\n  'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\n  {\n    variants: {\n      variant: {\n        default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\n        outline:\n          'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]',\n      },\n      size: {\n        default: 'h-8 text-sm',\n        sm: 'h-7 text-xs',\n        lg: 'h-12 text-sm group-data-[collapsible=icon]:!p-0',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  },\n);\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<'button'> & {\n    asChild?: boolean;\n    isActive?: boolean;\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>;\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = 'default',\n      size = 'default',\n      tooltip,\n      className,\n      ...props\n    },\n    ref,\n  ) => {\n    const Comp = asChild ? Slot : 'button';\n    const { isMobile, state } = useSidebar();\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    );\n\n    if (!tooltip) {\n      return button;\n    }\n\n    if (typeof tooltip === 'string') {\n      tooltip = {\n        children: tooltip,\n      };\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== 'collapsed' || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    );\n  },\n);\nSidebarMenuButton.displayName = 'SidebarMenuButton';\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<'button'> & {\n    asChild?: boolean;\n    showOnHover?: boolean;\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        'absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0',\n        // Increases the hit area of the button on mobile.\n        'after:absolute after:-inset-2 after:md:hidden',\n        'peer-data-[size=sm]/menu-button:top-1',\n        'peer-data-[size=default]/menu-button:top-1.5',\n        'peer-data-[size=lg]/menu-button:top-2.5',\n        'group-data-[collapsible=icon]:hidden',\n        showOnHover &&\n          'group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0',\n        className,\n      )}\n      {...props}\n    />\n  );\n});\nSidebarMenuAction.displayName = 'SidebarMenuAction';\n\nconst SidebarMenuBadge = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        'pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground',\n        'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground',\n        'peer-data-[size=sm]/menu-button:top-1',\n        'peer-data-[size=default]/menu-button:top-1.5',\n        'peer-data-[size=lg]/menu-button:top-2.5',\n        'group-data-[collapsible=icon]:hidden',\n        className,\n      )}\n      {...props}\n    />\n  ),\n);\nSidebarMenuBadge.displayName = 'SidebarMenuBadge';\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    showIcon?: boolean;\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`;\n  }, []);\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)}\n      {...props}\n    >\n      {showIcon && <Skeleton className=\"size-4 rounded-md\" data-sidebar=\"menu-skeleton-icon\" />}\n      <Skeleton\n        className=\"h-4 max-w-[--skeleton-width] flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            '--skeleton-width': width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  );\n});\nSidebarMenuSkeleton.displayName = 'SidebarMenuSkeleton';\n\nconst SidebarMenuSub = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(\n  ({ className, ...props }, ref) => (\n    <ul\n      ref={ref}\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        'mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5',\n        'group-data-[collapsible=icon]:hidden',\n        className,\n      )}\n      {...props}\n    />\n  ),\n);\nSidebarMenuSub.displayName = 'SidebarMenuSub';\n\nconst SidebarMenuSubItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(\n  ({ ...props }, ref) => <li ref={ref} {...props} />,\n);\nSidebarMenuSubItem.displayName = 'SidebarMenuSubItem';\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<'a'> & {\n    asChild?: boolean;\n    size?: 'sm' | 'md';\n    isActive?: boolean;\n  }\n>(({ asChild = false, size = 'md', isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'a';\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        'flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground',\n        'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground',\n        size === 'sm' && 'text-xs',\n        size === 'md' && 'text-sm',\n        'group-data-[collapsible=icon]:hidden',\n        className,\n      )}\n      {...props}\n    />\n  );\n});\nSidebarMenuSubButton.displayName = 'SidebarMenuSubButton';\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["button", "input", "separator", "sheet", "skeleton", "tooltip"]}, {"name": "skeleton", "type": "component", "files": [{"path": "components/ui/skeleton.tsx", "content": "import React from 'react';\nimport { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "slider", "type": "component", "files": [{"path": "components/ui/slider.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "sonner", "type": "component", "files": [{"path": "components/ui/sonner.tsx", "content": "'use client';\n\nimport React from 'react';\nimport { Toaster as Sonner } from 'sonner';\nimport { useThemeHydrated } from '@/hooks/use-theme';\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>;\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { currentTheme } = useThemeHydrated();\n\n  return (\n    <Sonner\n      theme={currentTheme as ToasterProps['theme']}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',\n          description: 'group-[.toast]:text-muted-foreground',\n          actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',\n          cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',\n        },\n      }}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n", "type": "tsx"}], "dependencies": []}, {"name": "stats-card", "type": "component", "files": [{"path": "components/ui/stats-card.tsx", "content": "'use client';\n\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport interface StatsCardProps {\n  title: string;\n  value: string | number;\n  description?: string;\n  icon: LucideIcon;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  colorVariant?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'brand';\n  className?: string;\n  loading?: boolean;\n}\n\nconst colorVariants = {\n  blue: {\n    border: 'border-blue-200 dark:border-blue-800',\n    gradient: 'from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20',\n    title: 'text-blue-700 dark:text-blue-300',\n    value: 'text-blue-700 dark:text-blue-300',\n    description: 'text-blue-600 dark:text-blue-400',\n    iconBg: 'bg-blue-100 dark:bg-blue-900/30',\n    icon: 'text-blue-600 dark:text-blue-400',\n  },\n  green: {\n    border: 'border-green-200 dark:border-green-800',\n    gradient: 'from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20',\n    title: 'text-green-700 dark:text-green-300',\n    value: 'text-green-700 dark:text-green-300',\n    description: 'text-green-600 dark:text-green-400',\n    iconBg: 'bg-green-100 dark:bg-green-900/30',\n    icon: 'text-green-600 dark:text-green-400',\n  },\n  orange: {\n    border: 'border-orange-200 dark:border-orange-800',\n    gradient: 'from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20',\n    title: 'text-orange-700 dark:text-orange-300',\n    value: 'text-orange-700 dark:text-orange-300',\n    description: 'text-orange-600 dark:text-orange-400',\n    iconBg: 'bg-orange-100 dark:bg-orange-900/30',\n    icon: 'text-orange-600 dark:text-orange-400',\n  },\n  red: {\n    border: 'border-red-200 dark:border-red-800',\n    gradient: 'from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20',\n    title: 'text-red-700 dark:text-red-300',\n    value: 'text-red-700 dark:text-red-300',\n    description: 'text-red-600 dark:text-red-400',\n    iconBg: 'bg-red-100 dark:bg-red-900/30',\n    icon: 'text-red-600 dark:text-red-400',\n  },\n  purple: {\n    border: 'border-purple-200 dark:border-purple-800',\n    gradient: 'from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20',\n    title: 'text-purple-700 dark:text-purple-300',\n    value: 'text-purple-700 dark:text-purple-300',\n    description: 'text-purple-600 dark:text-purple-400',\n    iconBg: 'bg-purple-100 dark:bg-purple-900/30',\n    icon: 'text-purple-600 dark:text-purple-400',\n  },\n  brand: {\n    border: 'border-brand/20 dark:border-brand/30',\n    gradient: 'from-brand/5 to-brand/10 dark:from-brand/10 dark:to-brand/20',\n    title: 'text-brand dark:text-brand/80',\n    value: 'text-brand dark:text-brand/80',\n    description: 'text-brand/60 dark:text-brand/60',\n    iconBg: 'bg-brand/10 dark:bg-brand/20',\n    icon: 'text-brand dark:text-brand/80',\n  },\n};\n\nexport function StatsCard({\n  title,\n  value,\n  description,\n  icon: Icon,\n  trend,\n  colorVariant = 'blue',\n  className,\n  loading = false,\n}: StatsCardProps) {\n  const colors = colorVariants[colorVariant];\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n      className={cn(\n        'group rounded-xl border p-6 transition-all duration-300 hover:shadow-lg',\n        colors.border,\n        colors.gradient,\n        className\n      )}\n    >\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className={cn('text-sm font-medium', colors.title)}>{title}</p>\n          <p className={cn('text-3xl font-bold', colors.value)}>\n            {loading ? '...' : value}\n          </p>\n          {trend && (\n            <div className=\"mt-1 flex items-center gap-1\">\n              <span\n                className={cn(\n                  'text-xs font-medium',\n                  trend.isPositive ? 'text-green-600' : 'text-red-600'\n                )}\n              >\n                {trend.isPositive ? '+' : ''}{trend.value}%\n              </span>\n              <span className=\"text-xs text-muted-foreground\">from last week</span>\n            </div>\n          )}\n        </div>\n        <div\n          className={cn(\n            'rounded-lg p-2 transition-transform duration-300 group-hover:scale-110',\n            colors.iconBg\n          )}\n        >\n          <Icon className={cn('size-4', colors.icon)} />\n        </div>\n      </div>\n      {description && (\n        <p className={cn('mt-1 text-xs', colors.description)}>{description}</p>\n      )}\n    </motion.div>\n  );\n}", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "stepper", "type": "component", "files": [{"path": "components/ui/stepper.tsx", "content": "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { LoaderCircle } from 'lucide-react';\nimport * as React from 'react';\nimport { createContext, useContext } from 'react';\nimport { CheckIcon } from '@radix-ui/react-icons';\n\n// Types\ntype StepperContextValue = {\n  activeStep: number;\n  setActiveStep: (step: number) => void;\n  orientation: 'horizontal' | 'vertical';\n};\n\ntype StepItemContextValue = {\n  step: number;\n  state: StepState;\n  isDisabled: boolean;\n  isLoading: boolean;\n};\n\ntype StepState = 'active' | 'completed' | 'inactive' | 'loading';\n\n// Contexts\nconst StepperContext = createContext<StepperContextValue | undefined>(undefined);\nconst StepItemContext = createContext<StepItemContextValue | undefined>(undefined);\n\nconst useStepper = () => {\n  const context = useContext(StepperContext);\n  if (!context) {\n    throw new Error('useStepper must be used within a Stepper');\n  }\n  return context;\n};\n\nconst useStepItem = () => {\n  const context = useContext(StepItemContext);\n  if (!context) {\n    throw new Error('useStepItem must be used within a StepperItem');\n  }\n  return context;\n};\n\n// Components\ninterface StepperProps extends React.HTMLAttributes<HTMLDivElement> {\n  defaultValue?: number;\n  value?: number;\n  onValueChange?: (value: number) => void;\n  orientation?: 'horizontal' | 'vertical';\n}\n\nconst Stepper = React.forwardRef<HTMLDivElement, StepperProps>(\n  (\n    { defaultValue = 0, value, onValueChange, orientation = 'horizontal', className, ...props },\n    ref,\n  ) => {\n    const [activeStep, setInternalStep] = React.useState(defaultValue);\n\n    const setActiveStep = React.useCallback(\n      (step: number) => {\n        if (value === undefined) {\n          setInternalStep(step);\n        }\n        onValueChange?.(step);\n      },\n      [value, onValueChange],\n    );\n\n    const currentStep = value ?? activeStep;\n\n    return (\n      <StepperContext.Provider\n        value={{\n          activeStep: currentStep,\n          setActiveStep,\n          orientation,\n        }}\n      >\n        <div\n          ref={ref}\n          className={cn(\n            'group/stepper inline-flex data-[orientation=horizontal]:w-full data-[orientation=horizontal]:flex-row data-[orientation=vertical]:flex-col',\n            className,\n          )}\n          data-orientation={orientation}\n          {...props}\n        />\n      </StepperContext.Provider>\n    );\n  },\n);\nStepper.displayName = 'Stepper';\n\n// StepperItem\ninterface StepperItemProps extends React.HTMLAttributes<HTMLDivElement> {\n  step: number;\n  completed?: boolean;\n  disabled?: boolean;\n  loading?: boolean;\n}\n\nconst StepperItem = React.forwardRef<HTMLDivElement, StepperItemProps>(\n  (\n    { step, completed = false, disabled = false, loading = false, className, children, ...props },\n    ref,\n  ) => {\n    const { activeStep } = useStepper();\n\n    const state: StepState =\n      completed || step < activeStep ? 'completed' : (activeStep === step ? 'active' : 'inactive');\n\n    const isLoading = loading && step === activeStep;\n\n    return (\n      <StepItemContext.Provider value={{ step, state, isDisabled: disabled, isLoading }}>\n        <div\n          ref={ref}\n          className={cn(\n            'group/step flex items-center group-data-[orientation=horizontal]/stepper:flex-row group-data-[orientation=vertical]/stepper:flex-col',\n            className,\n          )}\n          data-state={state}\n          {...(isLoading ? { 'data-loading': true } : {})}\n          {...props}\n        >\n          {children}\n        </div>\n      </StepItemContext.Provider>\n    );\n  },\n);\nStepperItem.displayName = 'StepperItem';\n\n// StepperTrigger\ninterface StepperTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  asChild?: boolean;\n}\n\nconst StepperTrigger = React.forwardRef<HTMLButtonElement, StepperTriggerProps>(\n  ({ asChild = false, className, children, ...props }, ref) => {\n    const { setActiveStep } = useStepper();\n    const { step, isDisabled } = useStepItem();\n\n    if (asChild) {\n      return <div className={className}>{children}</div>;\n    }\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          'inline-flex items-center gap-3 disabled:pointer-events-none disabled:opacity-50',\n          className,\n        )}\n        onClick={() => setActiveStep(step)}\n        disabled={isDisabled}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  },\n);\nStepperTrigger.displayName = 'StepperTrigger';\n\n// StepperIndicator\ninterface StepperIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {\n  asChild?: boolean;\n}\n\nconst StepperIndicator = React.forwardRef<HTMLDivElement, StepperIndicatorProps>(\n  ({ asChild = false, className, children, ...props }, ref) => {\n    const { state, step, isLoading } = useStepItem();\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'relative flex size-6 shrink-0 items-center justify-center rounded-full bg-muted text-xs font-medium text-muted-foreground data-[state=active]:bg-primary data-[state=completed]:bg-primary data-[state=active]:text-primary-foreground data-[state=completed]:text-primary-foreground',\n          className,\n        )}\n        data-state={state}\n        {...props}\n      >\n        {asChild ? (\n          children\n        ) : (\n          <>\n            <span className=\"transition-all group-data-[loading=true]/step:scale-0 group-data-[state=completed]/step:scale-0 group-data-[loading=true]/step:opacity-0 group-data-[state=completed]/step:opacity-0 group-data-[loading=true]/step:transition-none\">\n              {step}\n            </span>\n            <CheckIcon\n              className=\"absolute scale-0 opacity-0 transition-all group-data-[state=completed]/step:scale-100 group-data-[state=completed]/step:opacity-100\"\n              strokeWidth={2}\n              width={16}\n              height={16}\n              aria-hidden=\"true\"\n            />\n            {isLoading && (\n              <span className=\"absolute transition-all\">\n                <LoaderCircle\n                  className=\"animate-spin\"\n                  size={14}\n                  strokeWidth={2}\n                  aria-hidden=\"true\"\n                />\n              </span>\n            )}\n          </>\n        )}\n      </div>\n    );\n  },\n);\nStepperIndicator.displayName = 'StepperIndicator';\n\n// StepperTitle\nconst StepperTitle = React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3 ref={ref} className={cn('text-sm font-medium', className)} {...props} />\n  ),\n);\nStepperTitle.displayName = 'StepperTitle';\n\n// StepperDescription\nconst StepperDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />\n));\nStepperDescription.displayName = 'StepperDescription';\n\n// StepperSeparator\nconst StepperSeparator = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'm-0.5 bg-muted group-data-[orientation=horizontal]/stepper:h-0.5 group-data-[orientation=vertical]/stepper:h-12 group-data-[orientation=horizontal]/stepper:w-full group-data-[orientation=vertical]/stepper:w-0.5 group-data-[orientation=horizontal]/stepper:flex-1 group-data-[state=completed]/step:bg-primary',\n          className,\n        )}\n        {...props}\n      />\n    );\n  },\n);\nStepperSeparator.displayName = 'StepperSeparator';\n\nexport {\n  Stepper,\n  StepperDescription,\n  StepperIndicator,\n  StepperItem,\n  StepperSeparator,\n  StepperTitle,\n  StepperTrigger,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "switch", "type": "component", "files": [{"path": "components/ui/switch.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "table-skeleton", "type": "component", "files": [{"path": "components/ui/table-skeleton.tsx", "content": "import { Skeleton } from '@/components/ui/skeleton';\n\nexport type TableSkeletonProps = {\n  rows?: number;\n  cols?: number;\n};\n\nexport function TableSkeleton({ rows = 5, cols = 6 }: TableSkeletonProps) {\n  return (\n    <div className=\"space-y-3\">\n      {Array.from({ length: rows }).map((_, i) => (\n        <div key={i} className=\"flex space-x-4\">\n          {Array.from({ length: cols }).map((_, j) => (\n            <Skeleton key={j} className=\"h-12 flex-1 bg-[#3b3b3b]\" />\n          ))}\n        </div>\n      ))}\n    </div>\n  );\n}\n", "type": "tsx"}], "dependencies": ["skeleton"]}, {"name": "table", "type": "component", "files": [{"path": "components/ui/table.tsx", "content": "import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement>>(\n  ({ className, ...props }, ref) => (\n    <div className=\"relative w-full overflow-auto\">\n      <table ref={ref} className={cn('w-full caption-bottom text-sm', className)} {...props} />\n    </div>\n  ),\n);\nTable.displayName = 'Table';\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn('[&_tr]:border-b', className)} {...props} />\n));\nTableHeader.displayName = 'TableHeader';\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody ref={ref} className={cn('[&_tr:last-child]:border-0', className)} {...props} />\n));\nTableBody.displayName = 'TableBody';\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn('border-t bg-muted/50 font-medium [&>tr]:last:border-b-0', className)}\n    {...props}\n  />\n));\nTableFooter.displayName = 'TableFooter';\n\nconst TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(\n  ({ className, ...props }, ref) => (\n    <tr\n      ref={ref}\n      className={cn(\n        'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\n        className,\n      )}\n      {...props}\n    />\n  ),\n);\nTableRow.displayName = 'TableRow';\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      'h-12 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\n      className,\n    )}\n    {...props}\n  />\n));\nTableHead.displayName = 'TableHead';\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\n    {...props}\n  />\n));\nTableCell.displayName = 'TableCell';\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption ref={ref} className={cn('mt-4 text-sm text-muted-foreground', className)} {...props} />\n));\nTableCaption.displayName = 'TableCaption';\n\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "tabs", "type": "component", "files": [{"path": "components/ui/tabs.tsx", "content": "\"use client\";\n\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center rounded-lg bg-muted p-0.5 text-muted-foreground/70\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium outline-offset-2 transition-all hover:text-muted-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:shadow-black/5\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 outline-offset-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsContent, TabsList, TabsTrigger };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "textarea", "type": "component", "files": [{"path": "components/ui/textarea.tsx", "content": "import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nTextarea.displayName = 'Textarea';\n\nexport { Textarea };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "theme-toggle", "type": "component", "files": [{"path": "components/ui/theme-toggle.tsx", "content": "'use client';\n\nimport { <PERSON>, Sun } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useThemeHydrated } from '@/hooks/use-theme';\n\nexport function ThemeToggle() {\n  const { isDark, setTheme, mounted } = useThemeHydrated();\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"icon\" aria-label=\"Toggle theme\" disabled>\n        <Moon className=\"size-5\" />\n        <span className=\"sr-only\">Toggle theme</span>\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      aria-label=\"Toggle theme\"\n      onClick={() => setTheme(isDark ? 'light' : 'dark')}\n    >\n      {isDark ? <Sun className=\"size-5\" /> : <Moon className=\"size-5\" />}\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n", "type": "tsx"}], "dependencies": ["button"]}, {"name": "toast", "type": "component", "files": [{"path": "components/ui/toast.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as ToastPrimitives from '@radix-ui/react-toast';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { X } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst ToastProvider = ToastPrimitives.Provider;\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      'fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]',\n      className,\n    )}\n    {...props}\n  />\n));\nToastViewport.displayName = ToastPrimitives.Viewport.displayName;\n\nconst toastVariants = cva(\n  'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full',\n  {\n    variants: {\n      variant: {\n        default: 'border bg-background text-foreground',\n        destructive:\n          'destructive group border-destructive bg-destructive text-destructive-foreground',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  },\n);\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> & VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  );\n});\nToast.displayName = ToastPrimitives.Root.displayName;\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive',\n      className,\n    )}\n    {...props}\n  />\n));\nToastAction.displayName = ToastPrimitives.Action.displayName;\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      'absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600',\n      className,\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"size-4\" />\n  </ToastPrimitives.Close>\n));\nToastClose.displayName = ToastPrimitives.Close.displayName;\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title ref={ref} className={cn('text-sm font-semibold', className)} {...props} />\n));\nToastTitle.displayName = ToastPrimitives.Title.displayName;\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn('text-sm opacity-90', className)}\n    {...props}\n  />\n));\nToastDescription.displayName = ToastPrimitives.Description.displayName;\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>;\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n};\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "toaster", "type": "component", "files": [{"path": "components/ui/toaster.tsx", "content": "'use client';\n\nimport { useToast } from '@/components/ui/use-toast';\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from '@/components/ui/toast';\n\nexport function Toaster() {\n  const { toasts } = useToast();\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && <ToastDescription>{description}</ToastDescription>}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        );\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  );\n}\n", "type": "tsx"}], "dependencies": ["toast", "use-toast"]}, {"name": "toggle-group", "type": "component", "files": [{"path": "components/ui/toggle-group.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { toggleVariants } from \"@/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nconst ToggleGroup = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, children, ...props }, ref) => (\n  <ToggleGroupPrimitive.Root\n    ref={ref}\n    className={cn(\"flex items-center justify-center gap-1\", className)}\n    {...props}\n  >\n    <ToggleGroupContext.Provider value={{ variant, size }}>\n      {children}\n    </ToggleGroupContext.Provider>\n  </ToggleGroupPrimitive.Root>\n))\n\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName\n\nconst ToggleGroupItem = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &\n    VariantProps<typeof toggleVariants>\n>(({ className, children, variant, size, ...props }, ref) => {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n})\n\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName\n\nexport { ToggleGroup, ToggleGroupItem }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": ["toggle"]}, {"name": "toggle", "type": "component", "files": [{"path": "components/ui/toggle.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-10 min-w-10 px-3\",\n        sm: \"h-9 min-w-9 px-2.5\",\n        lg: \"h-11 min-w-11 px-5\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Toggle = React.forwardRef<\n  React.ElementRef<typeof TogglePrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, ...props }, ref) => (\n  <TogglePrimitive.Root\n    ref={ref}\n    className={cn(toggleVariants({ variant, size, className }))}\n    {...props}\n  />\n))\n\nToggle.displayName = TogglePrimitive.Root.displayName\n\nexport { Toggle, toggleVariants }\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "tooltip", "type": "component", "files": [{"path": "components/ui/tooltip.tsx", "content": "'use client';\n\nimport * as React from 'react';\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\n\nimport { cn } from '@/lib/utils';\n\nconst TooltipProvider = TooltipPrimitive.Provider;\n\nconst Tooltip = TooltipPrimitive.Root;\n\nconst TooltipTrigger = TooltipPrimitive.Trigger;\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Portal>\n    <TooltipPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]',\n        className,\n      )}\n      {...props}\n    />\n  </TooltipPrimitive.Portal>\n));\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\n", "type": "tsx"}, {"path": "lib/utils.ts", "content": "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n", "type": "ts"}], "dependencies": []}, {"name": "use-toast", "type": "component", "files": [{"path": "components/ui/use-toast.ts", "content": "'use client';\n\n// Inspired by react-hot-toast library\nimport * as React from 'react';\n\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast';\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1_000_000;\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n  action?: ToastActionElement;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, TOAST_REMOVE_DELAY);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST': {\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n    }\n\n    case 'UPDATE_TOAST': {\n      return {\n        ...state,\n        toasts: state.toasts.map((t) => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\n      };\n    }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        for (const toast of state.toasts) {\n          addToRemoveQueue(toast.id);\n        }\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t,\n        ),\n      };\n    }\n    case 'REMOVE_TOAST': {\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n    }\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  for (const listener of listeners) {\n    listener(memoryState);\n  }\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index !== -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "type": "ts"}], "dependencies": ["toast"]}]}