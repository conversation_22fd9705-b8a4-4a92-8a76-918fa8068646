# QBraid Partner Dashboard

A Next.js 14+ application for managing quantum devices and partner organizations with enterprise-grade authentication and role-based access control.

## 🚀 Features

- **🔐 AWS Cognito Authentication** - Enterprise authentication with MFA support
- **🔄 Redis-Enhanced Sessions** - Scalable session management with automatic JWT fallback
- **🛡️ Role-Based Access Control (RBAC)** - Organization-aware permissions system
- **⚡ TanStack Query** - Optimized data fetching with intelligent caching
- **📊 Real-time Updates** - Multi-tab synchronization for permissions and session state
- **🎯 Type-safe Development** - Comprehensive TypeScript support throughout
- **🎨 Modern UI** - Tailwind CSS with shadcn/ui components
- **📱 Responsive Design** - Mobile-first approach with adaptive layouts

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Redis server (for session storage)
- AWS Cognito User Pool
- QBraid API access

## 🚀 Quick Start

1. **Clone the repository:**

   ```bash
   git clone https://github.com/qbraid/partner-dashboard.git
   cd partner-dashboard
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Configure environment variables:**

   ```bash
   cp env.example .env.local
   ```

   Required environment variables:

   ```env
   # AWS Cognito
   NEXT_PUBLIC_USER_POOL_ID=your-user-pool-id
   NEXT_PUBLIC_USER_POOL_CLIENT_ID=your-client-id
   NEXT_PUBLIC_COGNITO_DOMAIN=your-cognito-domain

   # Redis
   REDIS_URL=redis://localhost:6379

   # QBraid API
   NEXT_PUBLIC_QBRAID_API_URL=https://api.qbraid.com

   # Session Configuration
   SESSION_DURATION_HOURS=24
   NODE_ENV=development
   ```

4. **Start development server:**

   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🏗️ Architecture

### Technology Stack

- **Frontend Framework**: Next.js 14 with App Router
- **Authentication**: AWS Cognito with custom session management
- **Session Storage**: Redis with automatic JWT fallback
- **State Management**: TanStack Query v5
- **Authorization**: Custom RBAC with QBraid organizations
- **Styling**: Tailwind CSS + shadcn/ui
- **Type Safety**: TypeScript 5+

### Key Components

```
app/
├── (auth)/          # Authentication routes
├── (dashboard)/     # Protected dashboard routes
├── api/             # API routes
│   ├── auth/        # Authentication endpoints
│   ├── orgs/        # Organization management
│   └── team/        # Team management
components/
├── auth/            # Authentication components
├── dashboard/       # Dashboard UI components
├── devices/         # Device management
└── team/            # Team management
lib/
├── auth.ts          # Core authentication logic
├── session.ts       # Session management
├── rbac.ts          # RBAC implementation
└── redis.ts         # Redis client configuration
```

## 🔐 Authentication & Authorization

### Authentication Flow

1. **Sign In**: Email/password or OAuth (Google)
2. **Session Creation**: Redis-backed sessions with secure cookies
3. **Token Management**: Cognito tokens stored in Redis
4. **Auto-refresh**: Seamless token refresh without user intervention

### Organization Roles

- **`owner`** - Full organization control
- **`admin`** - Administrative access
- **`member`** - Standard member access
- **`viewer`** - Read-only access

### Permissions

Permissions are automatically derived from roles:

```typescript
// Server-side permission check
import { requirePermission } from '@/lib/rbac';

export async function deleteDevice(deviceId: string) {
  await requirePermission(Permission.ManageDevices);
  // Implementation
}

// Client-side permission check
import { PermissionGuard } from '@/hooks/use-permissions';

<PermissionGuard permission={Permission.ViewDevices}>
  <DeviceList />
</PermissionGuard>
```

## 📚 Documentation

- [Authentication Complete Guide](./docs/auth/AUTH_COMPLETE_GUIDE.md) - Comprehensive authentication documentation
- [API Documentation](./docs/api/API_DOCUMENTATION.md) - Complete API endpoint reference
- [Teams Page Guide](./docs/teams/TEAMS_PAGE_COMPREHENSIVE_GUIDE.md) - Team management implementation

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run E2E tests with Cypress
npm run cypress:open

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Configuration

Ensure all required environment variables are set in your production environment:

- Configure AWS Cognito for production domain
- Set up Redis with proper security and persistence
- Update API URLs to production endpoints
- Enable secure session cookies

## 🛠️ Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript compiler
npm test            # Run tests
```

### Code Style

- ESLint configuration for consistent code style
- Prettier for code formatting
- TypeScript strict mode enabled
- Git hooks with Husky for pre-commit checks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Contribution Guidelines

- Follow the existing code style
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support, please contact the QBraid team or open an issue in the repository.

---

Built with ❤️ by the QBraid Team
