import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';
import eslintPluginUnicorn from 'eslint-plugin-unicorn';
import tailwind from 'eslint-plugin-tailwindcss';
import { FlatCompat } from '@eslint/eslintrc';

const compat = new FlatCompat({
  // import.meta.dirname is available after Node.js v20.11.0
  baseDirectory: import.meta.dirname,
});

/** @type {import('eslint').Linter.Config[]} */
const config = [
  { ignores: ['.next/**', 'public/**', 'next.config.js', 'postcss.config.js'] },
  { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  { languageOptions: { globals: { ...globals.browser, ...globals.node } } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  eslintPluginUnicorn.configs['flat/recommended'],
  ...tailwind.configs['flat/recommended'],
  ...compat.config({
    extends: ['next'],
    settings: {
      next: {
        rootDir: '.',
      },
    },
  }),
  ...compat.config({
    extends: ['next/core-web-vitals'],
  }),
  ...compat.config({
    extends: ['plugin:drizzle/all'],
  }),
  {
    rules: {
      // Core JavaScript/TypeScript rules - more relaxed
      'no-undef': 'warn', // Downgraded from error
      '@typescript-eslint/no-unused-vars': 'warn', // Downgraded from error
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-empty-object-type': 'off',
      'no-unused-vars': 'off',
      'no-console': 'off',

      // React rules - more permissive
      'react/react-in-jsx-scope': 'off',
      'react-hooks/rules-of-hooks': 'warn', // Downgraded from error
      'react-hooks/exhaustive-deps': 'off', // Turned off completely
      'react/prop-types': 'off', // Added - turn off prop-types in TS projects
      'react/display-name': 'off', // Added - allow anonymous components

      // Tailwind rules - relaxed
      'tailwindcss/no-custom-classname': 'off',
      'tailwindcss/classnames-order': 'off', // Added - turn off class ordering
      'tailwindcss/enforces-negative-arbitrary-values': 'off', // Added - allow negative values

      // Unicorn rules - much more relaxed
      'unicorn/prevent-abbreviations': 'off',
      'unicorn/filename-case': 'off', // Added - allow any filename case
      'unicorn/no-array-reduce': 'off', // Added - allow reduce
      'unicorn/no-null': 'off', // Added - allow null
      'unicorn/prefer-module': 'off', // Added - allow require()
      'unicorn/prefer-node-protocol': 'off', // Added - don't require node: protocol
      'unicorn/no-array-for-each': 'off', // Added - allow forEach
      'unicorn/consistent-destructuring': 'off', // Added - don't enforce destructuring
      'unicorn/no-await-expression-member': 'off', // Added - allow await chains
      'unicorn/prefer-spread': 'off', // Added - allow Array.from()

      // Drizzle rules - relaxed
      'drizzle/enforce-delete-with-where': 'off',
      'drizzle/enforce-update-with-where': 'off', // Added - allow updates without where

      // Import/export rules - relaxed
      'import/no-anonymous-default-export': 'off', // Added - allow anonymous exports
      'import/prefer-default-export': 'off', // Added - don't require default exports

      // Next.js specific - more permissive
      '@next/next/no-html-link-for-pages': 'warn', // Downgraded from error
      '@next/next/no-img-element': 'off', // Added - allow <img> tags
    },
  },
  {
    files: ['**/*.{jsx,tsx}'],
    rules: {
      'no-console': 'off', // Changed from warn to off for JSX/TSX files
    },
  },
  // Cypress E2E tests
  {
    files: ['cypress/**/*.{js,ts,jsx,tsx}'],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.mocha, // describe, it, beforeEach, afterEach
        cy: 'readonly',
        Cypress: 'readonly',
        expect: 'readonly',
      },
    },
    rules: {
      'no-undef': 'off',
    },
  },
];

export default config;
