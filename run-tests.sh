#!/bin/bash

# Cypress test runner script for different user roles

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if a role was specified
if [ -z "$1" ]; then
    print_error "No role specified. Please specify a role: owner, admin, manager, viewer, or all"
    echo "Usage: ./run-tests.sh <role>"
    echo "Example: ./run-tests.sh owner"
    echo "Example: ./run-tests.sh all"
    exit 1
fi

ROLE=$1

# Start the development server if not already running
print_status "Checking if development server is running..."
if ! curl -s http://localhost:3000 > /dev/null; then
    print_warning "Development server not found. Starting it..."
    npm run dev &
    SERVER_PID=$!
    print_status "Waiting for server to start..."
    sleep 10
else
    print_success "Development server is running"
fi

# Function to run tests for a specific role
run_role_tests() {
    local role=$1
    print_status "Running tests for $role role..."
    
    # Run role-specific tests
    npx cypress run --spec "cypress/e2e/${role}-role.cy.ts" --env "ROLE=$role"
    
    if [ $? -eq 0 ]; then
        print_success "$role role tests passed"
    else
        print_error "$role role tests failed"
        return 1
    fi
}

# Run tests based on the specified role
case $ROLE in
    "owner")
        run_role_tests "owner"
        ;;
    "admin")
        run_role_tests "admin"
        ;;
    "manager")
        run_role_tests "manager"
        ;;
    "viewer")
        run_role_tests "viewer"
        ;;
    "all")
        print_status "Running tests for all roles..."
        run_role_tests "owner" && \
        run_role_tests "admin" && \
        run_role_tests "manager" && \
        run_role_tests "viewer"
        ;;
    "device")
        print_status "Running device visibility tests..."
        npx cypress run --spec "cypress/e2e/device-visibility.cy.ts"
        ;;
    "signup")
        print_status "Running signup tests..."
        npx cypress run --spec "cypress/e2e/signup.cy.ts"
        ;;
    "signin")
        print_status "Running signin tests..."
        npx cypress run --spec "cypress/e2e/signin.cy.ts"
        ;;
    "auth")
        print_status "Running all authentication tests..."
        npx cypress run --spec "cypress/e2e/signup.cy.ts,cypress/e2e/signin.cy.ts,cypress/e2e/auth-error-handling.cy.ts,cypress/e2e/social-auth.cy.ts"
        ;;
    "auth-error")
        print_status "Running authentication error handling tests..."
        npx cypress run --spec "cypress/e2e/auth-error-handling.cy.ts"
        ;;
    "social")
        print_status "Running social authentication tests..."
        npx cypress run --spec "cypress/e2e/social-auth.cy.ts"
        ;;
    "role-signin")
        print_status "Running role-based signin tests..."
        npx cypress run --spec "cypress/e2e/role-based-signin.cy.ts"
        ;;
    "simple-auth")
        print_status "Running simple role-based authentication tests..."
        npx cypress run --spec "cypress/e2e/simple-role-auth.cy.ts"
        ;;
    *)
        print_error "Invalid role: $ROLE"
        echo "Valid roles are: owner, admin, manager, viewer, all, device, signup, signin, auth, auth-error, social, role-signin, simple-auth"
        exit 1
        ;;
esac

# Clean up
if [ ! -z "$SERVER_PID" ]; then
    print_status "Stopping development server..."
    kill $SERVER_PID
fi

print_success "Test execution completed"