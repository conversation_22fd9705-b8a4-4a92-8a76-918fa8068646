import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, UserPlus, UserX, Shield } from 'lucide-react';

type Stats = {
  totalUsers: number;
  activeUsers: number;
  pendingInvites: number;
  totalRoles: number;
};

type TeamStatsProps = {
  stats: Stats;
  isLoading: boolean;
};

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: any;
  gradient: string;
  isLoading?: boolean;
}

const StatsCard = ({ title, value, icon: Icon, gradient, isLoading }: StatsCardProps) => (
  <Card className="overflow-hidden border-sidebar-border bg-sidebar transition-colors hover:bg-muted">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          {isLoading ? (
            <Skeleton className="mt-3 h-8 w-16 bg-muted" />
          ) : (
            <p className="mt-3 text-2xl font-bold text-foreground">{value}</p>
          )}
        </div>
        <div className={`rounded-full p-4 ${gradient} shadow-lg`}>
          <Icon className="size-7 text-white" />
        </div>
      </div>
    </CardContent>
  </Card>
);

export function TeamStats({ stats, isLoading }: TeamStatsProps) {
  return (
    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      <StatsCard
        title="Total Members"
        value={stats.totalUsers}
        icon={Users}
        gradient="bg-gradient-to-r from-blue-600 to-blue-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Active Members"
        value={stats.activeUsers}
        icon={UserPlus}
        gradient="bg-gradient-to-r from-green-600 to-green-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Pending Invites"
        value={stats.pendingInvites}
        icon={UserX}
        gradient="bg-gradient-to-r from-yellow-600 to-yellow-700"
        isLoading={isLoading}
      />
      <StatsCard
        title="Active Roles"
        value={stats.totalRoles}
        icon={Shield}
        gradient="bg-gradient-to-r from-purple-600 to-purple-700"
        isLoading={isLoading}
      />
    </div>
  );
}
