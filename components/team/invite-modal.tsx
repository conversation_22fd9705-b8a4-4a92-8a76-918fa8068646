'use client';

import React, { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import {
  Users,
  Mail,
  Loader2,
  AlertCircle,
  CheckCircle2,
  Plus,
  Trash2,
  ChevronDown,
  Sparkles,
  Upload,
  XCircle,
  RotateCcw,
  Ban,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import {
  getAssignableRoles,
  getRoleDisplayName,
  getRoleDescription,
  externalRoleToPermissions,
} from '@/lib/permissions';
import { useReinviteUser, useCancelInvite } from '@/hooks/use-api';
import { useOrgContext } from '@/components/org/org-context-provider';
import {
  ALL_ROLES,
  ROLE_ICONS,
  ROLE_GRADIENTS,
  ROLE_BORDERS,
  ROLE_SELECTED_BG,
} from '@/lib/constant';

const ROLES = ALL_ROLES.map((role) => ({
  name: getRoleDisplayName(role),
  description: getRoleDescription(role),
  icon: ROLE_ICONS[role] || '👤',
  gradient: ROLE_GRADIENTS[role] || 'from-gray-500/20 to-slate-500/20',
  border: ROLE_BORDERS[role] || 'border-gray-500/30',
  selectedBg: ROLE_SELECTED_BG[role] || 'bg-gray-500/10',
  permissions: externalRoleToPermissions[role] || [],
}));
// Form schemas
const multipleInviteSchema = z.object({
  invites: z
    .array(
      z.object({
        email: z.string().email('Invalid email address'),
        role: z.string().min(1, 'Please select a role'),
      }),
    )
    .min(1, 'Add at least one user'),
});

// Update the bulk invite schema to remove role field
const bulkInviteSchema = z.object({
  file: z.any().optional(), // For file upload
});

type MultipleInviteForm = z.infer<typeof multipleInviteSchema>;
type BulkInviteForm = z.infer<typeof bulkInviteSchema>;

interface InviteModalProps {
  open: boolean;
  onClose: () => void;
  onInvite: (
    email: string,
    role: string,
  ) => Promise<{ message: string; currentUserRole?: string | null; error?: boolean }>;
  orgName?: string;
  orgId?: string;
  isLoading?: boolean;
}

export function InviteModal({
  open,
  onClose,
  onInvite,
  orgName,
  orgId,
  isLoading = false,
}: InviteModalProps) {
  const { currentOrg } = useOrgContext();
  const assignableRoles = getAssignableRoles(currentOrg?.role || '');

  const [activeTab, setActiveTab] = useState('multiple');
  // State for tracking invite results (audit logging handled by API)
  const [inviteResults, setInviteResults] = useState<
    Array<{
      email: string;
      status: 'pending' | 'success' | 'error';
      message?: string;
      role: string;
      timestamp: string;
    }>
  >([]);
  const [showRoleDetails, setShowRoleDetails] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // API mutations for reinvite and cancel
  const { mutate: reinviteUser, isPending: isReinviting } = useReinviteUser();
  const { mutate: cancelInvite, isPending: isCancelling } = useCancelInvite();

  // Use all available roles
  const availableRoles = ALL_ROLES;

  // Multiple invite form with dynamic fields
  const multipleForm = useForm<MultipleInviteForm>({
    resolver: zodResolver(multipleInviteSchema),
    defaultValues: {
      invites: [{ email: '', role: '' }],
    },
    mode: 'onChange', // Enable real-time validation
  });

  const { fields, append, remove } = useFieldArray({
    control: multipleForm.control,
    name: 'invites',
  });

  // Bulk invite form
  const bulkForm = useForm<BulkInviteForm>({
    resolver: zodResolver(bulkInviteSchema),
    defaultValues: {},
  });

  // Add state for uploaded invites
  const [uploadedInvites, setUploadedInvites] = useState<Array<{ email: string; role: string }>>(
    [],
  );

  // Check if multiple form is valid for submission
  const isMultipleFormValid = () => {
    const watchedValues = multipleForm.watch('invites');
    const isValid = watchedValues.every(
      (invite) =>
        invite.email.trim() !== '' && invite.role.trim() !== '' && invite.email.includes('@'),
    );
    return isValid;
  };

  // Check if bulk form is valid for submission
  const isBulkFormValid = () => {
    return uploadedInvites.length > 0;
  };

  // Parse CSV/Excel file content
  const parseFileContent = async (file: File) => {
    const text = await file.text();
    const lines = text.split(/[\r\n]+/).filter((line) => line.trim());
    const results: Array<{ email: string; role: string }> = [];
    const validRoles = assignableRoles;

    // Try to detect header row
    const firstLine = lines[0].toLowerCase();
    const hasHeader = firstLine.includes('email') || firstLine.includes('role');
    const startIndex = hasHeader ? 1 : 0;

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Split by comma or tab
      const parts = line.split(/[,\t]+/).map((p) => p.trim());

      // Try to identify email and role columns
      let email = '',
        role = '';

      if (hasHeader) {
        // Use header information to find columns
        const headers = lines[0]
          .toLowerCase()
          .split(/[,\t]+/)
          .map((h) => h.trim());
        const emailIndex = headers.findIndex((h) => h.includes('email'));
        const roleIndex = headers.findIndex((h) => h.includes('role'));

        email = parts[emailIndex] || '';
        role = parts[roleIndex] || '';
      } else {
        // Assume first column is email, second is role
        [email, role] = parts;
      }

      // Validate email
      if (!email || !email.includes('@')) continue;

      // Validate and normalize role
      role = role?.toLowerCase() || '';
      if (!role || !validRoles.includes(role)) {
        role = 'member'; // Default role if not specified or invalid
      }

      results.push({ email, role });
    }

    return results;
  };

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    setUploadError(null);
    try {
      const results = await parseFileContent(file);

      if (results.length === 0) {
        setUploadError('No valid entries found in file');
        return;
      }

      setUploadedInvites(results);
    } catch (error) {
      setUploadError('Failed to read file');
      console.error('File upload error:', error);
    }
  };

  // Multiple invite handler - API handles audit logging
  const handleMultipleInvite = async (data: MultipleInviteForm) => {
    const results: typeof inviteResults = [];
    let successCount = 0;

    for (const invite of data.invites) {
      // Add pending status
      results.push({
        email: invite.email,
        status: 'pending',
        role: invite.role,
        timestamp: new Date().toISOString(),
      });
      setInviteResults([...results]);

      try {
        const res = await onInvite(invite.email, invite.role);

        // Update status (audit logging handled by API)
        const index = results.findIndex((r) => r.email === invite.email);
        if (index !== -1) {
          if (!res.error) {
            successCount++;
          }

          results[index] = {
            ...results[index],
            status: res.error ? 'error' : 'success',
            message: res.message,
            timestamp: new Date().toISOString(),
          };
          setInviteResults([...results]);
        }
      } catch {
        // Update status for error
        const index = results.findIndex((r) => r.email === invite.email);
        if (index !== -1) {
          results[index] = {
            ...results[index],
            status: 'error',
            message: 'Invitation failed',
            timestamp: new Date().toISOString(),
          };
          setInviteResults([...results]);
        }
      }
    }

    // Clear successful entries from form and reset if all successful
    if (successCount > 0) {
      if (successCount === data.invites.length) {
        // All successful - reset to single empty form
        multipleForm.reset({ invites: [{ email: '', role: '' }] });
      } else {
        // Partial success - remove successful entries from form
        const failedInvites = data.invites.filter((invite) => {
          const result = results.find((r) => r.email === invite.email);
          return result?.status === 'error';
        });

        if (failedInvites.length > 0) {
          multipleForm.reset({ invites: failedInvites });
        } else {
          multipleForm.reset({ invites: [{ email: '', role: '' }] });
        }
      }
    }
  };

  // Bulk invite handler - API handles audit logging
  const handleBulkInvite = async () => {
    if (uploadedInvites.length === 0) {
      setUploadError('Please upload a CSV file with invites');
      return;
    }

    const results: typeof inviteResults = [];
    let successCount = 0;

    for (const invite of uploadedInvites) {
      // Add pending status
      results.push({
        email: invite.email,
        status: 'pending',
        role: invite.role,
        timestamp: new Date().toISOString(),
      });
      setInviteResults([...results]);

      try {
        const res = await onInvite(invite.email, invite.role);

        // Update status (audit logging handled by API)
        const index = results.findIndex((r) => r.email === invite.email);
        if (index !== -1) {
          if (!res.error) {
            successCount++;
          }

          results[index] = {
            ...results[index],
            status: res.error ? 'error' : 'success',
            message: res.message,
            timestamp: new Date().toISOString(),
          };
          setInviteResults([...results]);
        }
      } catch {
        // Update status for error
        const index = results.findIndex((r) => r.email === invite.email);
        if (index !== -1) {
          results[index] = {
            ...results[index],
            status: 'error',
            message: 'Invitation failed',
            timestamp: new Date().toISOString(),
          };
          setInviteResults([...results]);
        }
      }
    }

    // Reset form and clear uploads if all invites were successful
    if (successCount === uploadedInvites.length) {
      bulkForm.reset();
      setUploadedInvites([]);
    }
  };

  // Reset forms and results when closing
  const handleClose = () => {
    multipleForm.reset();
    bulkForm.reset();
    setInviteResults([]);
    setUploadedInvites([]);
    setShowRoleDetails(false);
    setUploadError(null);
    onClose();
  };

  // Generate sample CSV content
  const generateSampleCSV = useCallback(() => {
    const content = [
      'email,role',
      '<EMAIL>,admin',
      '<EMAIL>,member',
      '<EMAIL>,viewer',
    ].join('\n');

    const blob = new Blob([content], { type: 'text/csv' });
    const url = globalThis.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample-invite-template.csv';
    document.body.append(a);
    a.click();
    a.remove();
    globalThis.URL.revokeObjectURL(url);
  }, []);

  // Handler for reinviting a user
  const handleReinvite = useCallback(
    (email: string, role: string) => {
      if (!orgName) {
        toast.error('Organization name not available');
        return;
      }

      reinviteUser(
        {
          email,
          role,
          name: orgName,
          referer: process.env.NEXT_PUBLIC_REFERRER || 'qbraid.com',
          orgId: orgId || '',
        },
        {
          onSuccess: (data) => {
            toast.success(`Reinvite sent to ${email}`);
            // Update the invite result to show it was resent
            setInviteResults((prev) =>
              prev.map((result) =>
                result.email === email
                  ? {
                      ...result,
                      status: 'success' as const,
                      message: 'Reinvite sent',
                      timestamp: new Date().toISOString(),
                    }
                  : result,
              ),
            );
          },
          onError: (error: any) => {
            toast.error(`Failed to reinvite ${email}: ${error.message || 'Unknown error'}`);
          },
        },
      );
    },
    [reinviteUser, orgName],
  );

  // Handler for cancelling an invitation
  const handleCancelInvite = useCallback(
    (email: string) => {
      if (!orgId) {
        toast.error('Organization ID not available');
        return;
      }

      cancelInvite(
        {
          orgId,
          email,
        },
        {
          onSuccess: (data) => {
            toast.success(`Invitation cancelled for ${email}`);
            // Remove the cancelled invitation from results
            setInviteResults((prev) => prev.filter((result) => result.email !== email));
          },
          onError: (error: any) => {
            toast.error(
              `Failed to cancel invite for ${email}: ${error.message || 'Unknown error'}`,
            );
          },
        },
      );
    },
    [cancelInvite, orgId],
  );

  // Separate completed invitations from pending ones
  const completedInvitations = inviteResults.filter((r) => r.status !== 'pending');
  const pendingInvitations = inviteResults.filter((r) => r.status === 'pending');

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="max-h-[90vh] max-w-[95vw] overflow-y-auto border border-border bg-background/95 shadow-2xl backdrop-blur-sm dark:border-gray-800 dark:bg-gray-900/95 md:max-w-3xl lg:max-w-4xl"
        data-testid="invite-member-modal"
      >
        {/* Header with gradient background */}
        <div className="pointer-events-none absolute inset-x-0 top-0 h-24 bg-gradient-to-b from-brand/10 via-brand/5 to-transparent dark:from-brand/20 dark:via-brand/10" />

        <DialogHeader className="relative pb-4">
          <div className="mb-4 flex items-center gap-3">
            <div className="animate-pulse rounded-xl bg-gradient-to-br from-brand to-brand/80 p-2.5 shadow-lg shadow-brand/20 dark:shadow-brand/30">
              <Users className="size-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <DialogTitle className="truncate text-xl font-bold text-foreground dark:text-white md:text-2xl">
                Invite Team Members
              </DialogTitle>
              <DialogDescription className="mt-1 text-sm text-muted-foreground dark:text-gray-300">
                Add new members to{' '}
                <span className="truncate font-medium text-brand">{orgName}</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* Role Details Toggle */}
        <button
          onClick={() => setShowRoleDetails(!showRoleDetails)}
          className="group mb-4 flex w-full items-center gap-2 rounded-lg px-3 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:bg-muted/50 hover:text-foreground dark:text-gray-400 dark:hover:bg-gray-800/50 dark:hover:text-white"
        >
          <Sparkles className="size-4 shrink-0 text-brand transition-transform group-hover:rotate-12" />
          <span className="flex-1 text-left">Role Permissions</span>
          <ChevronDown
            className={cn(
              'w-4 h-4 transition-transform duration-200 flex-shrink-0',
              showRoleDetails && 'rotate-180',
            )}
          />
        </button>

        {/* Role Details Cards */}
        {showRoleDetails && (
          <div className="mb-6 grid grid-cols-1 gap-4 animate-in fade-in slide-in-from-top-2 md:grid-cols-2">
            {ROLES.map((role) => (
              <Card
                key={role.name}
                className={cn(
                  'p-3 border transition-all duration-200 hover-lift',
                  role.border,
                  role.selectedBg,
                  'dark:border-gray-700 dark:bg-gray-800/50',
                )}
              >
                <div className="flex items-start gap-2">
                  <div
                    className={cn(
                      'p-1.5 rounded-md bg-gradient-to-br shadow-md flex-shrink-0',
                      `shadow-${role.gradient.split('-')[1]}/20`,
                      role.gradient,
                    )}
                  >
                    {typeof role.icon === 'string' ? (
                      <span className="flex size-4 items-center justify-center">{role.icon}</span>
                    ) : (
                      React.createElement(role.icon, { className: 'w-4 h-4' })
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="text-sm font-semibold text-foreground dark:text-white">
                      {role.name}
                    </h4>
                    <p className="mt-0.5 text-xs text-muted-foreground dark:text-gray-400">
                      {role.description}
                    </p>
                  </div>
                </div>
                <ul className="mt-2 space-y-0.5">
                  {role.permissions.slice(0, 3).map((permission, i) => (
                    <li
                      key={i}
                      className="flex items-center gap-1 text-xs text-muted-foreground dark:text-gray-400"
                    >
                      <CheckCircle2 className="size-2.5 shrink-0 text-emerald-500 dark:text-emerald-400" />
                      <span className="truncate">{permission}</span>
                    </li>
                  ))}
                  {role.permissions.length > 3 && (
                    <li className="text-xs text-muted-foreground dark:text-gray-500">
                      +{role.permissions.length - 3} more permissions
                    </li>
                  )}
                </ul>
              </Card>
            ))}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid h-10 w-full grid-cols-2 border border-border bg-muted/30 p-1 backdrop-blur-sm dark:border-gray-700 dark:bg-gray-800/50">
            <TabsTrigger
              value="multiple"
              className="tab-indicator text-xs text-muted-foreground transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-violet-500/25 dark:text-gray-400 md:text-sm"
            >
              <Users className="mr-1 size-3 md:mr-2 md:size-4" />
              Multiple
            </TabsTrigger>
            <TabsTrigger
              value="bulk"
              className="tab-indicator text-xs text-muted-foreground transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-violet-500/25 dark:text-gray-400 md:text-sm"
            >
              <Mail className="mr-1 size-3 md:mr-2 md:size-4" />
              Bulk
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="multiple"
            className="space-y-6 duration-300 animate-in fade-in slide-in-from-bottom-2"
          >
            <Form {...multipleForm}>
              <form
                onSubmit={multipleForm.handleSubmit(handleMultipleInvite)}
                className="space-y-6"
              >
                <div className="space-y-3">
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className="group flex items-start gap-2"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <div className="grid flex-1 grid-cols-1 gap-2 md:grid-cols-2">
                        <FormField
                          control={multipleForm.control}
                          name={`invites.${index}.email`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && (
                                <FormLabel className="text-sm font-medium text-foreground dark:text-gray-200">
                                  Email Address
                                </FormLabel>
                              )}
                              <FormControl>
                                <Input
                                  type="email"
                                  data-testid="invite-email-input"
                                  placeholder="<EMAIL>"
                                  {...field}
                                  disabled={isLoading}
                                  className="h-10 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-500"
                                />
                              </FormControl>
                              <FormMessage className="text-xs text-destructive dark:text-red-400" />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={multipleForm.control}
                          name={`invites.${index}.role`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && (
                                <FormLabel className="text-sm font-medium text-foreground dark:text-gray-200">
                                  Role
                                </FormLabel>
                              )}
                              <FormControl>
                                <Select
                                  value={field.value}
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                    multipleForm.trigger(`invites.${index}.role`);
                                  }}
                                  disabled={isLoading}
                                >
                                  <SelectTrigger
                                    data-testid="invite-role-select-trigger"
                                    className="h-10 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-500"
                                  >
                                    <SelectValue
                                      placeholder="Select role"
                                      data-testid="invite-role-select"
                                    />
                                  </SelectTrigger>
                                  <SelectContent className="max-h-60 border-border bg-background text-foreground dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100">
                                    {availableRoles.map((roleValue) => {
                                      const role = ROLES.find(
                                        (r) => getRoleDisplayName(roleValue) === r.name,
                                      );
                                      if (!role) return null;
                                      return (
                                        <SelectItem
                                          data-testid="invite-role-select-item"
                                          data-value={roleValue}
                                          key={roleValue}
                                          value={roleValue}
                                          className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-700/50 dark:focus:bg-gray-700/50"
                                        >
                                          <div className="flex items-center gap-2">
                                            {typeof role.icon === 'string' ? (
                                              <span className="flex size-4 items-center justify-center">
                                                {role.icon}
                                              </span>
                                            ) : (
                                              React.createElement(role.icon, {
                                                className: 'w-4 h-4',
                                              })
                                            )}
                                            <div>
                                              <div className="font-semibold">{role.name}</div>
                                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                                {role.description}
                                              </div>
                                            </div>
                                          </div>
                                        </SelectItem>
                                      );
                                    })}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage className="text-xs text-destructive dark:text-red-400" />
                            </FormItem>
                          )}
                        />
                      </div>

                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => remove(index)}
                          disabled={isLoading}
                          className="mt-6 size-10 text-muted-foreground hover:bg-red-500/10 hover:text-red-500 dark:text-gray-400 dark:hover:bg-red-500/20 dark:hover:text-red-400"
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={() => append({ email: '', role: '' })}
                  disabled={isLoading}
                  className="group h-11 w-full border-dashed border-border text-sm text-muted-foreground transition-all duration-200 hover:border-border hover:bg-muted/50 hover:text-foreground dark:border-gray-700 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-800/50 dark:hover:text-gray-200"
                  data-testid="add-invite-row-btn"
                >
                  <Plus className="mr-2 size-4 transition-transform duration-200 group-hover:rotate-90" />
                  Add Another User
                </Button>

                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={isLoading || !isMultipleFormValid()}
                    className={cn(
                      'w-full h-11 px-4 bg-gradient-to-r from-violet-600 to-purple-700 hover:from-violet-700 hover:to-purple-800 text-white shadow-lg shadow-violet-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover-lift text-sm',
                      isLoading && 'shimmer',
                    )}
                    data-testid="send-invite-btn"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 size-4" />
                        Send {fields.length} Invitation{fields.length === 1 ? '' : 's'}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent
            value="bulk"
            className="space-y-6 duration-300 animate-in fade-in slide-in-from-bottom-2"
          >
            <Form {...bulkForm}>
              <form onSubmit={bulkForm.handleSubmit(handleBulkInvite)} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-foreground dark:text-gray-300">
                        Upload Invites
                      </Label>
                      <div className="mt-2 flex flex-col items-center justify-center gap-3 rounded-lg border-2 border-dashed border-border bg-muted/20 p-6 transition-colors duration-200 hover:border-border dark:border-gray-700 dark:bg-gray-800/30 dark:hover:border-gray-600">
                        <Input
                          data-testid="bulk-invite-file-input"
                          type="file"
                          accept=".csv,.txt,.xlsx,.xls"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload(file);
                          }}
                          className="hidden"
                          id="file-upload"
                        />
                        <div className="flex flex-col items-center gap-3">
                          <div className="rounded-xl bg-gradient-to-br from-violet-500 to-purple-600 p-2.5 shadow-lg shadow-violet-500/20">
                            <Upload className="size-5 text-white" />
                          </div>
                          <div className="text-center">
                            <Label
                              htmlFor="file-upload"
                              className="flex cursor-pointer items-center gap-2 rounded-lg border border-violet-400/20 px-3 py-2 text-sm font-medium text-violet-500 transition-colors hover:bg-violet-400/10 hover:text-violet-400 dark:text-violet-400 dark:hover:text-violet-300"
                            >
                              Choose CSV File
                            </Label>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={generateSampleCSV}
                              className="mt-2 h-auto px-2 py-1 text-xs text-violet-500 hover:bg-violet-400/10 hover:text-violet-400 dark:text-violet-400 dark:hover:text-violet-300"
                            >
                              Download Sample Template
                            </Button>
                          </div>
                        </div>
                        <FormDescription className="max-w-xs text-center text-xs text-muted-foreground dark:text-gray-500">
                          Format: email,role (e.g., <EMAIL>,admin)
                          <br />
                          Supported roles: {assignableRoles.map((r) => r).join(', ')}
                        </FormDescription>
                      </div>
                    </div>
                    {uploadError && (
                      <div className="flex items-center gap-2 text-sm text-destructive dark:text-red-400">
                        <AlertCircle className="size-4" />
                        {uploadError}
                      </div>
                    )}
                    {uploadedInvites.length > 0 && (
                      <div className="rounded-lg border border-border bg-muted/20 p-3 dark:border-gray-700 dark:bg-gray-800/30">
                        <div className="mb-2 flex items-center gap-2">
                          <Users className="size-4 text-violet-500 dark:text-violet-400" />
                          <span className="text-sm text-foreground dark:text-gray-300">
                            {uploadedInvites.length} invite{uploadedInvites.length === 1 ? '' : 's'}{' '}
                            ready to send
                          </span>
                        </div>
                        <div className="grid max-h-20 grid-cols-1 gap-1.5 overflow-y-auto">
                          {uploadedInvites.map((invite, i) => (
                            <div
                              key={i}
                              className="flex items-center gap-2 text-xs text-muted-foreground dark:text-gray-400"
                            >
                              <Mail className="size-3 shrink-0" />
                              <span className="flex-1 truncate">{invite.email}</span>
                              <Badge
                                variant="outline"
                                className="shrink-0 text-xs dark:border-gray-600 dark:text-gray-300"
                              >
                                {invite.role}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={isLoading || !isBulkFormValid()}
                    className={cn(
                      'w-full h-11 px-4 bg-gradient-to-r from-violet-600 to-purple-700 hover:from-violet-700 hover:to-purple-800 text-white shadow-lg shadow-violet-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover-lift text-sm',
                      isLoading && 'shimmer',
                    )}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 size-4" />
                        Send {uploadedInvites.length} Invitation
                        {uploadedInvites.length === 1 ? '' : 's'}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>
        </Tabs>

        {/* Active Invitations (Pending) */}
        {pendingInvitations.length > 0 && (
          <div
            className="mt-6 rounded-lg border border-blue-500/30 bg-blue-500/10 p-4 dark:border-blue-500/20 dark:bg-blue-500/5"
            data-testid="recent-pending-invitations"
          >
            <div className="mb-3 flex items-center gap-2">
              <div className="size-3 animate-spin rounded-full border-2 border-blue-500 border-t-transparent dark:border-blue-400" />
              <h4 className="text-sm font-medium text-blue-600 dark:text-blue-400">
                Sending ({pendingInvitations.length})
              </h4>
            </div>
            <div className="max-h-32 space-y-2 overflow-y-auto">
              {pendingInvitations.map((result) => (
                <div
                  key={`${result.email}-${result.timestamp}`}
                  className="flex items-center justify-between rounded border border-blue-500/20 bg-blue-500/10 p-1.5 text-xs dark:border-blue-500/10 dark:bg-blue-500/5"
                >
                  <div className="flex min-w-0 flex-1 items-center gap-2">
                    <div className="size-2 shrink-0 animate-spin rounded-full border border-blue-500 border-t-transparent dark:border-blue-400" />
                    <div className="min-w-0 flex-1">
                      <p className="truncate font-medium text-blue-600 dark:text-blue-300">
                        {result.email}
                      </p>
                      <p className="text-xs text-blue-500 dark:text-blue-400">
                        {getRoleDisplayName(result.role)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Invitations (Completed) */}
        {completedInvitations.length > 0 && (
          <div className="mt-6 border-t border-border pt-4 dark:border-gray-700">
            <div className="mb-3 flex items-center gap-2">
              <CheckCircle2 className="size-4 text-muted-foreground dark:text-gray-400" />
              <h4 className="text-sm font-medium text-foreground dark:text-gray-300">
                Recent ({completedInvitations.length})
              </h4>
            </div>
            <div
              className="max-h-40 space-y-2 overflow-y-auto"
              data-testid="recent-completed-invitations"
            >
              {completedInvitations.map((result) => (
                <div
                  key={`${result.email}-${result.timestamp}`}
                  className={cn(
                    'flex items-start gap-2 p-3 rounded-lg border text-xs transition-all duration-200',
                    result.status === 'success' &&
                      'border-green-500/30 dark:border-green-500/20 bg-green-500/10 dark:bg-green-500/5',
                    result.status === 'error' &&
                      'border-red-500/30 dark:border-red-500/20 bg-red-500/10 dark:bg-red-500/5',
                  )}
                >
                  <div className="flex min-w-0 flex-1 items-start gap-2">
                    {result.status === 'success' && (
                      <CheckCircle2 className="mt-0.5 size-3 shrink-0 text-green-600 dark:text-green-500" />
                    )}
                    {result.status === 'error' && (
                      <XCircle className="mt-0.5 size-3 shrink-0 text-red-600 dark:text-red-500" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="truncate font-medium text-foreground dark:text-white">
                        {result.email}
                      </p>
                      <p className="text-xs text-muted-foreground dark:text-gray-400">
                        {getRoleDisplayName(result.role)}
                        {result.message && ` • ${result.message}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex shrink-0 items-center gap-1">
                    {result.status === 'success' && (
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleReinvite(result.email, result.role)}
                          disabled={isReinviting || isCancelling}
                          className="h-6 px-2 text-xs text-blue-600 hover:bg-blue-500/10 hover:text-blue-500 dark:text-blue-400 dark:hover:bg-blue-500/20 dark:hover:text-blue-300"
                          title="Resend invitation"
                        >
                          <RotateCcw className="size-2.5" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCancelInvite(result.email)}
                          disabled={isReinviting || isCancelling}
                          className="h-6 px-2 text-xs text-red-600 hover:bg-red-500/10 hover:text-red-500 dark:text-red-400 dark:hover:bg-red-500/20 dark:hover:text-red-300"
                          title="Cancel invitation"
                        >
                          <Ban className="size-2.5" />
                        </Button>
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground dark:text-gray-500">
                      {new Date(result.timestamp).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Close Button */}
        <div className="mt-6 border-t border-border pt-4 dark:border-gray-700">
          <Button
            data-testid="close-invite-modal-btn"
            type="button"
            variant="outline"
            onClick={handleClose}
            className="h-9 w-full border-red-500/30 px-4 text-sm text-red-600 transition-all duration-200 hover:border-red-500/40 hover:bg-red-500/10 hover:text-red-500 dark:border-red-500/20 dark:text-red-400 dark:hover:border-red-500/30 dark:hover:bg-red-500/20 dark:hover:text-red-300"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
