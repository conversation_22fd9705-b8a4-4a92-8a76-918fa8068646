import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, Filter, ChevronDown, Users } from 'lucide-react';
import { TeamMemberRow } from '@/components/team/team-member-row';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { TableSkeleton } from '@/components/ui/table-skeleton';
import type { TeamMember } from '@/types/team';

type TeamMembersTabProps = {
  filteredUsers: TeamMember[];
  usersLoading: boolean;
  totalUserPages: number;
  pagination: {
    page: number;
    pageSize: number;
    goToPrevious: () => void;
    goToNext: (maxPages: number) => void;
    changePageSize: (size: number) => void;
  };
  roleFilter: string;
  setRoleFilter: (role: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  roles: Array<{ name: string }>;
  totalUsers: number;
  currentUserRole?: string | null;
  onChangeRole: (member: TeamMember) => void;
  onRemove: (member: TeamMember) => void;
  onReinvite?: (member: TeamMember) => void;
  onCancelInvite?: (member: TeamMember) => void;
  isReinviting?: boolean;
  isCancelling?: boolean;
};

export function TeamMembersTab({
  filteredUsers,
  usersLoading,
  totalUserPages,
  pagination,
  roleFilter,
  setRoleFilter,
  searchTerm,
  setSearchTerm,
  roles,
  totalUsers,
  currentUserRole,
  onChangeRole,
  onRemove,
  onReinvite,
  onCancelInvite,
  isReinviting,
  isCancelling,
}: TeamMembersTabProps) {
  return (
    <Card className="border-sidebar-border bg-sidebar shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex flex-col justify-between gap-4 sm:flex-row">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search members by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border-sidebar-border bg-muted pl-11 text-base font-medium text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-brand"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="min-w-[140px] border-sidebar-border bg-muted font-semibold text-muted-foreground hover:bg-muted/70 hover:text-foreground"
              >
                <Filter className="mr-2 size-5" />
                {roleFilter}
                <ChevronDown className="ml-2 size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="border-sidebar-border bg-sidebar">
              <DropdownMenuItem
                className="font-medium text-muted-foreground hover:bg-muted hover:text-foreground"
                onClick={() => setRoleFilter('All Roles')}
              >
                All Roles
              </DropdownMenuItem>
              {roles.map((role) => (
                <DropdownMenuItem
                  key={role.name}
                  className="font-medium text-muted-foreground hover:bg-muted hover:text-foreground"
                  onClick={() => setRoleFilter(role.name)}
                >
                  {role.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-sidebar-border bg-muted">
                {['Member', 'Role', 'Credits', 'Status', 'Last Active', 'Actions'].map((header) => (
                  <th
                    key={header}
                    className="px-6 py-5 text-left text-base font-semibold text-muted-foreground"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {usersLoading ? (
                <tr>
                  <td colSpan={6} className="p-6">
                    <TableSkeleton rows={pagination.pageSize} />
                  </td>
                </tr>
              ) : (filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="py-12 text-center">
                    <div className="text-muted-foreground">
                      <Users className="mx-auto mb-6 size-16 opacity-60" />
                      <p className="mb-3 text-xl font-bold">No team members found</p>
                      <p className="text-base font-medium">
                        Try adjusting your search or filter criteria
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredUsers.map((member) => (
                  <TeamMemberRow
                    key={member.email}
                    user={{
                      ...member,
                      userCredits: member.userCredits ?? 0,
                      lastActive: member.lastActive ?? '',
                    }}
                    currentUserRole={currentUserRole}
                    onChangeRole={() => onChangeRole(member)}
                    onRemove={() => onRemove(member)}
                    onReinvite={onReinvite ? () => onReinvite(member) : undefined}
                    onCancelInvite={onCancelInvite ? () => onCancelInvite(member) : undefined}
                    isReinviting={isReinviting}
                    isCancelling={isCancelling}
                  />
                ))
              ))}
            </tbody>
          </table>
        </div>

        <div className="px-6 pb-4">
          <PaginationControls
            page={pagination.page}
            totalPages={totalUserPages}
            onPrevious={pagination.goToPrevious}
            onNext={() => pagination.goToNext(totalUserPages)}
            pageSize={pagination.pageSize}
            onPageSizeChange={pagination.changePageSize}
            disabled={usersLoading}
            totalItems={totalUsers}
          />
        </div>
      </CardContent>
    </Card>
  );
}
