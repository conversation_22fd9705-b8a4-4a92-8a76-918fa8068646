import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Download, Activity, Clock, Users } from 'lucide-react';
import { ActionLogRow } from '@/components/team/action-log-row';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { TableSkeleton } from '@/components/ui/table-skeleton';
import { useDebounce } from '@/hooks/use-debounce';

// Type definitions
type ActionLog = {
  _id: string;
  userId: string;
  userEmail: string;
  userName: string;
  organizationId: string;
  userRole: string | null;
  action: string;
  actionDisplay: string;
  resourceType: string;
  resourceTypeDisplay: string;
  resourceId: string;
  resourceName: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  status: string;
  statusDisplay: string;
};

type ActivityLogData = {
  logs: ActionLog[];
  pagination: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
};

type ActivityLogTabProps = {
  data?: ActivityLogData;
  isLoading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    goToPrevious: () => void;
    goToNext: (maxPages: number) => void;
    changePageSize: (size: number) => void;
  };
  actions: readonly string[];
};

// Transform API log to display format
const transformLogForDisplay = (log: ActionLog) => ({
  timestamp: log.timestamp,
  user: log.userName || log.userEmail?.split('@')[0] || 'Unknown',
  userEmail: log.userEmail,
  userRole: log.userRole || 'User',
  action: log.actionDisplay || log.action.replaceAll('_', ' '),
  actionDescription: log.description,
  createdAt: log.timestamp,
  metadata: log.metadata,
});

export function ActivityLogTab({ data, isLoading, pagination, actions }: ActivityLogTabProps) {
  // Local state for search
  const [searchQuery, setSearchQuery] = useState('');

  // Debounced search for better performance
  const debouncedSearch = useDebounce(searchQuery, 300);

  // Transform and filter logs
  const { filteredLogs, totalLogs } = useMemo(() => {
    if (!data?.logs) {
      return { filteredLogs: [], totalLogs: 0 };
    }

    const transformedLogs = data.logs.map((log) => transformLogForDisplay(log));

    const filtered = transformedLogs.filter((log) => {
      // Search filter
      const searchLower = debouncedSearch.toLowerCase();
      const matchesSearch =
        !searchLower ||
        log.userEmail.toLowerCase().includes(searchLower) ||
        log.actionDescription?.toLowerCase().includes(searchLower) ||
        log.userRole?.toLowerCase().includes(searchLower) ||
        log.user.toLowerCase().includes(searchLower);

      return matchesSearch;
    });

    return {
      filteredLogs: filtered,
      totalLogs: transformedLogs.length,
    };
  }, [data?.logs, debouncedSearch]);

  // Export functionality
  const handleExport = () => {
    if (filteredLogs.length === 0) {
      return;
    }

    const csvContent = [
      ['User', 'Role', 'Action', 'Description', 'Timestamp'],
      ...filteredLogs.map((log) => [
        log.user,
        log.userRole,
        log.action,
        log.actionDescription,
        new Date(log.timestamp).toLocaleString(),
      ]),
    ]
      .map((row) => row.map((cell) => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = globalThis.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activity-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    globalThis.URL.revokeObjectURL(url);
  };

  return (
    <Card className="border-sidebar-border bg-gradient-to-br from-sidebar to-sidebar/80 shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          {/* Header with Stats */}
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-gradient-to-br from-brand/20 to-brand/10 p-2">
              <Activity className="size-5 text-brand" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">Activity Log</h3>
              <p className="text-sm text-muted-foreground">Track team actions and system events</p>
            </div>
          </div>

          {/* Stats Badge and Download Button */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 rounded-full border border-blue-500/20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 px-3 py-1">
              <Clock className="size-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                {data?.pagination?.total || totalLogs} activities
              </span>
            </div>

            {/* Download Button */}
            <Button
              onClick={handleExport}
              variant="outline"
              size="sm"
              disabled={filteredLogs.length === 0}
              className="border-border/50 bg-background/50 text-foreground transition-colors hover:bg-background/70"
              title="Export filtered logs to CSV"
            >
              <Download className="mr-2 size-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Search Input */}
        <div className="mt-4 flex flex-col gap-3 sm:flex-row">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              data-testid="activity-search-input"
              placeholder="Search activities, users, or descriptions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border-border/50 bg-background/50 pl-10 transition-colors focus:border-brand/50"
            />
          </div>
        </div>

        {/* Enhanced Results Summary */}
        {debouncedSearch && (
          <div className="mt-3 rounded-lg border border-blue-500/10 bg-gradient-to-r from-blue-500/5 to-purple-500/5 p-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="size-4" />
              <span>
                {filteredLogs.length === totalLogs
                  ? `Showing all ${totalLogs} activities`
                  : `Showing ${filteredLogs.length} of ${totalLogs} activities`}
                {debouncedSearch && (
                  <span className="text-blue-500"> matching &quot;{debouncedSearch}&quot;</span>
                )}
              </span>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-sidebar-border bg-gradient-to-r from-muted/50 to-muted/30">
                <th className="w-[220px] px-6 py-4 text-left text-sm font-semibold text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Users className="size-4" />
                    User
                  </div>
                </th>
                <th className="w-[120px] px-6 py-4 text-left text-sm font-semibold text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Activity className="size-4" />
                    Action
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="size-4" />
                    Description
                  </div>
                </th>
                <th className="w-[180px] px-6 py-4 text-left text-sm font-semibold text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="size-4" />
                    Time
                  </div>
                </th>
              </tr>
            </thead>
            <tbody data-testid="activity-log-table">
              {isLoading ? (
                <tr>
                  <td colSpan={4} className="p-6">
                    <TableSkeleton rows={pagination.pageSize} />
                  </td>
                </tr>
              ) : (filteredLogs.length === 0 ? (
                <tr>
                  <td colSpan={4} className="py-16 text-center">
                    <div className="text-muted-foreground">
                      <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-gradient-to-br from-muted/20 to-muted/10">
                        <Activity className="size-8 opacity-50" />
                      </div>
                      <p className="mb-2 text-lg font-medium">
                        {debouncedSearch
                          ? 'No matching activities found'
                          : 'No activity logs found'}
                      </p>
                      <p className="mx-auto max-w-md text-sm">
                        {debouncedSearch
                          ? 'Try adjusting your search criteria'
                          : 'Activity will appear here as team members perform actions'}
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredLogs.map((log, index) => (
                  <ActionLogRow
                    key={`${log.timestamp}-${log.action}-${log.userEmail}-${index}`}
                    {...log}
                  />
                ))
              ))}
            </tbody>
          </table>
        </div>

        {/* Enhanced Pagination */}
        {!isLoading && filteredLogs.length > 0 && (
          <div className="border-t border-sidebar-border/50 bg-gradient-to-r from-muted/10 to-muted/5 px-6 pb-4">
            <PaginationControls
              page={pagination.page}
              totalPages={data?.pagination?.pages || 1}
              onPrevious={pagination.goToPrevious}
              onNext={() => pagination.goToNext(data?.pagination?.pages || 1)}
              pageSize={pagination.pageSize}
              onPageSizeChange={pagination.changePageSize}
              pageSizeOptions={[10, 15, 25, 50, 100]}
              disabled={isLoading}
              totalItems={data?.pagination?.total || totalLogs}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
