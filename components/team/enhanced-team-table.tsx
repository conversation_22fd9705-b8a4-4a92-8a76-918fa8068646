'use client';

import React, { useState, useMemo, useCallback } from 'react';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import {
  ArrowUpDown,
  Search,
  Users,
  RotateCcw,
  Ban,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { canChangeUserRole, canRemoveUser } from '@/lib/permissions';
import type { TeamMember } from '@/types/team';
import { cn } from '@/lib/utils';

interface EnhancedTeamTableProps {
  data: TeamMember[];
  currentUserRole?: string | null;
  onChangeRole?: (member: TeamMember) => void;
  onRemove?: (member: TeamMember) => void;
  onReinvite?: (member: TeamMember) => void;
  onCancelInvite?: (member: TeamMember) => void;
  isReinviting?: boolean;
  isCancelling?: boolean;
  isLoading?: boolean;
}

const columnHelper = createColumnHelper<TeamMember>();

export function EnhancedTeamTable({
  data,
  currentUserRole,
  onChangeRole,
  onRemove,
  onReinvite,
  onCancelInvite,
  isLoading,
}: EnhancedTeamTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');

  // Track which rows are loading
  const [loadingRows, setLoadingRows] = useState<Set<string>>(new Set());

  const getInitials = useCallback((name: string) => {
    if (!name || name.trim() === '') return '??';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }, []);

  const formatLastActive = useCallback((date: string) => {
    if (!date) return 'Never';
    try {
      const d = new Date(date);
      if (Number.isNaN(d.getTime())) return 'Never';
      return d.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Never';
    }
  }, []);

  const getStatusBadge = (status: string) => {
    const baseClasses = 'font-semibold px-3 py-1 text-xs rounded-full';
    switch (status) {
      case 'Active': {
        return (
          <Badge className={cn(baseClasses, 'bg-green-500/10 text-green-400 border-green-500/20')}>
            Active
          </Badge>
        );
      }
      case 'Deactivated': {
        return (
          <Badge className={cn(baseClasses, 'bg-red-500/10 text-red-400 border-red-500/20')}>
            Deactivated
          </Badge>
        );
      }
      case 'Invited': {
        return (
          <Badge className={cn(baseClasses, 'bg-blue-500/10 text-blue-400 border-blue-500/20')}>
            Invited
          </Badge>
        );
      }
      default: {
        return (
          <Badge className={cn(baseClasses, 'bg-muted/50 text-muted-foreground')}>Unknown</Badge>
        );
      }
    }
  };

  // Simple action handlers - React Query handles optimistic updates
  const handleReinvite = async (member: TeamMember) => {
    const memberKey = member.email;
    setLoadingRows((prev) => new Set(prev).add(memberKey));

    try {
      await onReinvite?.(member);
    } catch {
      // Error handled by the parent component
    } finally {
      setLoadingRows((prev) => {
        const newSet = new Set(prev);
        newSet.delete(memberKey);
        return newSet;
      });
    }
  };

  const handleCancelInvite = async (member: TeamMember) => {
    const memberKey = member.email;
    setLoadingRows((prev) => new Set(prev).add(memberKey));

    try {
      await onCancelInvite?.(member);
    } catch {
      // Error handled by the parent component
    } finally {
      setLoadingRows((prev) => {
        const newSet = new Set(prev);
        newSet.delete(memberKey);
        return newSet;
      });
    }
  };

  const handleChangeRole = (member: TeamMember) => {
    // No loading state needed - opens a modal
    onChangeRole?.(member);
  };

  const handleRemove = (member: TeamMember) => {
    // No loading state needed - opens a modal
    onRemove?.(member);
  };

  const columns = useMemo(
    () => [
      columnHelper.accessor('name', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold text-muted-foreground hover:text-foreground"
          >
            Member
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-4" data-testid="member-row-user">
            <Avatar className="size-10 ring-2 ring-sidebar-border">
              <AvatarImage src={row.original.avatar || '/placeholder.svg'} />
              <AvatarFallback className="bg-brand text-xs font-semibold text-brand-foreground">
                {getInitials(row.original.name || row.original.email)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-semibold text-foreground">
                {row.original.name || row.original.email}
              </p>
              <p className="text-xs text-muted-foreground">{row.original.email}</p>
            </div>
          </div>
        ),
      }),
      columnHelper.accessor('role', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold text-muted-foreground hover:text-foreground"
          >
            Role
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        ),
        cell: ({ getValue }) => (
          <Badge variant="outline" className="font-medium capitalize">
            {getValue()}
          </Badge>
        ),
      }),
      columnHelper.accessor('userCredits', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold text-muted-foreground hover:text-foreground"
          >
            Credits
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        ),
        cell: ({ getValue }) => (
          <span className="font-medium text-foreground">{getValue()?.toLocaleString() || 0}</span>
        ),
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: ({ getValue }) => getStatusBadge(getValue()),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      }),
      columnHelper.accessor('lastActive', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold text-muted-foreground hover:text-foreground"
          >
            Last Active
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        ),
        cell: ({ getValue }) => (
          <span className="text-sm font-medium text-foreground">
            {formatLastActive(getValue() || '')}
          </span>
        ),
      }),
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => {
          const member = row.original;
          const isInvited = member.status === 'Invited';
          const isRowLoading = loadingRows.has(member.email);

          const canChangeRole = currentUserRole
            ? canChangeUserRole(currentUserRole, member.role, 'member').canChange
            : false;
          const canRemove = currentUserRole
            ? canRemoveUser(currentUserRole, member.role).canRemove
            : false;

          const hasInviteActions = isInvited && (onReinvite || onCancelInvite);
          const hasAnyPermission = canChangeRole || canRemove || hasInviteActions;

          if (!hasAnyPermission) {
            return <span className="text-xs text-muted-foreground">No actions</span>;
          }

          const buttons = [];

          // For invited users - show reinvite and cancel buttons
          if (isInvited) {
            if (onReinvite) {
              buttons.push(
                <Button
                  key="reinvite"
                  variant="outline"
                  size="sm"
                  onClick={() => handleReinvite(member)}
                  disabled={isRowLoading}
                  className="h-7 border-blue-500/20 px-2 py-1 text-xs text-blue-400 transition-all duration-200 hover:border-blue-500/30 hover:bg-blue-500/10 hover:text-blue-300"
                  data-testid="resend-invite-btn"
                >
                  {isRowLoading ? (
                    <Loader2 className="mr-1 size-3 animate-spin" />
                  ) : (
                    <RotateCcw className="mr-1 size-3" />
                  )}
                  {isRowLoading ? 'Resending...' : 'Resend'}
                </Button>,
              );
            }

            if (onCancelInvite) {
              buttons.push(
                <Button
                  key="cancel"
                  variant="outline"
                  size="sm"
                  onClick={() => handleCancelInvite(member)}
                  disabled={isRowLoading}
                  className="h-7 border-red-500/20 px-2 py-1 text-xs text-red-400 transition-all duration-200 hover:border-red-500/30 hover:bg-red-500/10 hover:text-red-300"
                  data-testid="cancel-invite-btn"
                >
                  {isRowLoading ? (
                    <Loader2 className="mr-1 size-3 animate-spin" />
                  ) : (
                    <Ban className="mr-1 size-3" />
                  )}
                  {isRowLoading ? 'Cancelling...' : 'Cancel'}
                </Button>,
              );
            }
          } else {
            // For active users - show change role and remove buttons
            if (canChangeRole) {
              buttons.push(
                <Button
                  data-testid="change-role-btn"
                  key="change-role"
                  variant="outline"
                  size="sm"
                  onClick={() => handleChangeRole(member)}
                  disabled={isRowLoading}
                  className="h-7 border-muted px-2 py-1 text-xs text-muted-foreground transition-all duration-200 hover:bg-muted hover:text-foreground"
                >
                  Change Role
                </Button>,
              );
            }

            if (canRemove) {
              buttons.push(
                <Button
                  data-testid="remove-user-btn"
                  key="remove"
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemove(member)}
                  disabled={isRowLoading}
                  className="h-7 border-red-500/20 px-2 py-1 text-xs text-red-400 transition-all duration-200 hover:border-red-500/30 hover:bg-red-500/10 hover:text-red-300"
                >
                  Remove
                </Button>,
              );
            }
          }

          return <div className="flex items-center gap-2">{buttons}</div>;
        },
      }),
    ],
    [
      currentUserRole,
      onChangeRole,
      onRemove,
      onReinvite,
      onCancelInvite,
      loadingRows,
      handleReinvite,
      handleCancelInvite,
      handleChangeRole,
      handleRemove,
    ],
  );

  const table = useReactTable({
    data: data, // Use props data directly
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: 'includesString',
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  const uniqueRoles = [...new Set(data.map((item) => item.role))];
  const uniqueStatuses = [...new Set(data.map((item) => item.status))];

  return (
    <Card className="border-sidebar-border bg-sidebar shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex flex-col justify-between gap-4 sm:flex-row">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground" />
            <Input
              data-testid="team-search-input"
              placeholder="Search members by name or email..."
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="focus-glow border-sidebar-border bg-muted pl-11 text-base font-medium text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-brand"
            />
          </div>
          <div className="flex gap-2">
            <Select
              data-testid="role-filter-select"
              value={(table.getColumn('role')?.getFilterValue() as string) ?? 'all'}
              onValueChange={(value) => {
                table.getColumn('role')?.setFilterValue(value === 'all' ? undefined : value);
              }}
            >
              <SelectTrigger
                data-testid="role-filter-select-trigger"
                className="w-[150px] border-sidebar-border bg-muted"
              >
                <SelectValue data-testid="role-filter-select-value" placeholder="All Roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {uniqueRoles.map((role) => (
                  <SelectItem key={role} value={role} className="capitalize">
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={(table.getColumn('status')?.getFilterValue() as string) ?? 'all'}
              onValueChange={(value) => {
                table.getColumn('status')?.setFilterValue(value === 'all' ? undefined : value);
              }}
            >
              <SelectTrigger className="w-[150px] border-sidebar-border bg-muted">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {uniqueStatuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="relative overflow-auto">
          <Table data-testid="team-members-table">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b border-sidebar-border bg-muted hover:bg-muted"
                >
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className="px-6 py-4 text-sm font-bold text-muted-foreground"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {(() => {
                if (isLoading) {
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        <div className="flex items-center justify-center">
                          <div className="size-8 animate-spin rounded-full border-b-2 border-brand" />
                          <span className="ml-2 text-muted-foreground">
                            Loading team members...
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                }

                const rows = table.getRowModel().rows;

                if (rows?.length) {
                  return rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className="group border-b border-sidebar-border transition-colors hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="px-6 py-4">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ));
                }

                return (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="text-muted-foreground">
                        <Users className="mx-auto mb-4 size-16 opacity-60" />
                        <p className="mb-2 text-lg font-semibold">No team members found</p>
                        <p className="text-sm">Try adjusting your search or filter criteria</p>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })()}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between space-x-2 border-t border-sidebar-border p-6">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-muted-foreground">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => table.setPageSize(Number(value))}
            >
              <SelectTrigger className="h-8 w-[70px] border-sidebar-border bg-muted">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[5, 10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium text-muted-foreground">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden size-8 border-sidebar-border bg-muted p-0 hover:bg-muted/70 lg:flex"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="size-4" />
              </Button>
              <Button
                variant="outline"
                className="size-8 border-sidebar-border bg-muted p-0 hover:bg-muted/70"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="size-4" />
              </Button>
              <Button
                variant="outline"
                className="size-8 border-sidebar-border bg-muted p-0 hover:bg-muted/70"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="size-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden size-8 border-sidebar-border bg-muted p-0 hover:bg-muted/70 lg:flex"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
