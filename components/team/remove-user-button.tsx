'use client';

import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface RemoveUserButtonProps {
  member: { name: string; email: string };
  onRemove: (email: string) => Promise<void>;
  disabled: boolean;
}

export function RemoveUserButton({ member, onRemove, disabled }: RemoveUserButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleRemove = async () => {
    setIsLoading(true);
    try {
      // API handles audit logging automatically
      await onRemove(member.email);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to remove user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const closeAll = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Initial confirmation dialog */}
      {isOpen && (
        <AlertDialog open onOpenChange={closeAll}>
          <AlertDialogContent className="max-w-xl border-gray-800 bg-[#121212] text-white">
            <AlertDialogHeader>
              <AlertDialogTitle>Remove User</AlertDialogTitle>
              <AlertDialogDescription className="text-center text-gray-300">
                Are you sure you want to remove{' '}
                <span className="font-bold text-white">{member.email}</span> from the team? <br />
                <span className="font-bold text-red-500">This action cannot be undone.</span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="border-gray-700 bg-transparent hover:bg-gray-800 hover:text-white">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction onClick={handleRemove} className="bg-red-600 hover:bg-red-700">
                Remove
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
}
