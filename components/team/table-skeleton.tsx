import { Skeleton } from '@/components/ui/skeleton';
import type { TableSkeletonProps } from '@/types/ui';

export function TableSkeleton({ rows = 5, columns = 6 }: TableSkeletonProps) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, j) => (
            <Skeleton key={j} className="h-12 flex-1 bg-[#3b3b3b]" />
          ))}
        </div>
      ))}
    </div>
  );
}
