import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import {
  User,
  Shield,
  UserPlus,
  UserX,
  Settings,
  Database,
  FileText,
  Activity,
} from 'lucide-react';

type ActionLogRowProps = {
  timestamp: string;
  user: string;
  userEmail: string;
  userRole: string;
  action: string;
  actionDescription: string;
  metadata?: Record<string, any>;
};

// Action icon mapping
const getActionIcon = (action: string) => {
  const actionLower = action.toLowerCase();

  if (actionLower.includes('invite') || actionLower.includes('add user')) {
    return <UserPlus className="size-4 text-green-500" />;
  }
  if (actionLower.includes('remove') || actionLower.includes('delete user')) {
    return <UserX className="size-4 text-red-500" />;
  }
  if (actionLower.includes('role') || actionLower.includes('permission')) {
    return <Shield className="size-4 text-blue-500" />;
  }
  if (actionLower.includes('device') || actionLower.includes('resource')) {
    return <Database className="size-4 text-purple-500" />;
  }
  if (actionLower.includes('settings') || actionLower.includes('config')) {
    return <Settings className="size-4 text-orange-500" />;
  }
  if (actionLower.includes('document') || actionLower.includes('file')) {
    return <FileText className="size-4 text-indigo-500" />;
  }

  return <Activity className="size-4 text-gray-500" />;
};

// Role color mapping
const getRoleColor = (role: string) => {
  const roleLower = role.toLowerCase();

  switch (roleLower) {
    case 'owner': {
      return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20';
    }
    case 'admin': {
      return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
    }
    case 'member': {
      return 'bg-green-500/10 text-green-400 border-green-500/20';
    }
    case 'viewer': {
      return 'bg-gray-500/10 text-gray-400 border-gray-500/20';
    }
    default: {
      return 'bg-slate-500/10 text-slate-400 border-slate-500/20';
    }
  }
};

// Format timestamp
const formatTimestamp = (timestamp: string) => {
  try {
    const date = new Date(timestamp);
    const timeAgo = formatDistanceToNow(date, { addSuffix: true });
    const fullDate = date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });

    return { timeAgo, fullDate };
  } catch {
    return { timeAgo: 'Unknown', fullDate: timestamp };
  }
};

export function ActionLogRow({
  timestamp,
  user,
  userEmail,
  userRole,
  action,
  actionDescription,
  metadata,
}: ActionLogRowProps) {
  const { timeAgo, fullDate } = formatTimestamp(timestamp);
  const actionIcon = getActionIcon(action);
  const roleColorClass = getRoleColor(userRole);

  // Get user initials for avatar fallback
  const userInitials = user
    .split(' ')
    .map((name) => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <tr className="border-b border-sidebar-border/30 transition-all duration-200 hover:bg-gradient-to-r hover:from-muted/20 hover:to-muted/10">
      {/* Enhanced User Column */}
      <td className="px-6 py-4">
        <div className="flex items-center gap-3">
          <Avatar className="size-9 ring-2 ring-sidebar-border/50">
            <AvatarImage src={metadata?.avatar} alt={user} />
            <AvatarFallback className="bg-gradient-to-br from-brand/20 to-brand/10 text-sm font-medium text-brand">
              {userInitials}
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <p className="truncate font-medium text-foreground">{user}</p>
              <Badge
                variant="outline"
                className={`text-xs font-medium capitalize ${roleColorClass}`}
              >
                {userRole}
              </Badge>
            </div>
            <p className="truncate text-sm text-muted-foreground">{userEmail}</p>
          </div>
        </div>
      </td>

      {/* Enhanced Action Column */}
      <td className="px-6 py-4">
        <div className="flex items-center gap-2">
          <div className="rounded-md bg-gradient-to-br from-muted/30 to-muted/10 p-1.5">
            {actionIcon}
          </div>
          <span className="text-sm font-medium capitalize text-foreground">{action}</span>
        </div>
      </td>

      {/* Enhanced Description Column */}
      <td className="px-6 py-4">
        <div className="max-w-md">
          <p className="text-sm leading-relaxed text-muted-foreground">{actionDescription}</p>
          {metadata && Object.keys(metadata).length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {Object.entries(metadata)
                .filter(
                  ([key, value]) =>
                    !['avatar', 'userRole', 'submittedAt'].includes(key) &&
                    value !== null &&
                    value !== undefined,
                )
                .slice(0, 3) // Limit to 3 metadata items
                .map(([key, value]) => (
                  <span
                    key={key}
                    className="inline-flex items-center rounded-md border border-border/30 bg-gradient-to-r from-background/50 to-background/30 px-2 py-1 text-xs text-muted-foreground"
                  >
                    {key}: {String(value)}
                  </span>
                ))}
            </div>
          )}
        </div>
      </td>

      {/* Enhanced Timestamp Column */}
      <td className="px-6 py-4">
        <div className="text-right">
          <p className="text-sm font-medium text-foreground">{timeAgo}</p>
          <p className="text-xs text-muted-foreground" title={fullDate}>
            {fullDate}
          </p>
        </div>
      </td>
    </tr>
  );
}
