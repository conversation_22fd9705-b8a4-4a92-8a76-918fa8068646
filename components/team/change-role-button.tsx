'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { TeamMember } from '@/types/team';
import { getAssignableRoles, canChangeUserRole } from '@/lib/permissions';
import { Crown, Shield, User, Users, ArrowRight, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Add Select imports
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useOrgContext } from '@/components/org/org-context-provider';

const ROLES = [
  {
    id: 'owner',
    name: 'Owner',
    description: 'Full control over organization',
    icon: Crown,
    color: 'from-yellow-500/20 to-orange-500/20',
    borderColor: 'border-yellow-500/30',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
  },
  {
    id: 'admin',
    name: 'Admin',
    description: 'Manage team and resources',
    icon: Shield,
    color: 'from-blue-500/20 to-purple-500/20',
    borderColor: 'border-blue-500/30',
    iconColor: 'text-blue-600 dark:text-blue-400',
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Limited management access',
    icon: Users,
    color: 'from-green-500/20 to-emerald-500/20',
    borderColor: 'border-green-500/30',
    iconColor: 'text-green-600 dark:text-green-400',
  },
  {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only access',
    icon: Eye,
    color: 'from-gray-500/20 to-slate-500/20',
    borderColor: 'border-gray-500/30',
    iconColor: 'text-gray-600 dark:text-gray-400',
  },
];

interface ChangeRoleButtonProps {
  user: TeamMember;
  roles: string[];
  currentUserRole: string;
  onRoleChange: (userEmail: string, oldRole: string, newRole: string) => Promise<void>;
  onClose: () => void;
}

export function ChangeRoleButton({
  user,
  onRoleChange,
  onClose,
  roles,
  currentUserRole,
}: ChangeRoleButtonProps) {
  const { currentOrg } = useOrgContext();
  const [selectedRole, setSelectedRole] = useState(user.role);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Get roles that the current user can assign
  const assignableRoleNames = currentOrg?.role ? getAssignableRoles(currentOrg.role) : [];
  const availableRoles = assignableRoleNames;

  // Check if current user can change this specific user's role
  const canChangeThisUserRole =
    currentOrg?.role && canChangeUserRole(currentOrg.role, user.role, selectedRole).canChange;

  const handleConfirm = async () => {

    if (!canChangeThisUserRole) {
      console.warn('❌ [CHANGE-ROLE] Permission denied for role change');
      setErrorMessage(
        "You do not have permission to change this user's role to the selected role.",
      );
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      await onRoleChange(user.email, user.role, selectedRole);
      toast.success(`Successfully changed ${user.email}'s role to ${selectedRole}`);
      // Reset state before closing
      setSelectedRole(user.role);
      setConfirmOpen(false);
      onClose();
    } catch (error: any) {
      console.error('❌ [CHANGE-ROLE] Role change failed:', error);
      const errorMsg = error?.message || 'Failed to change user role';
      setErrorMessage(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
      setConfirmOpen(false);
    }
  };

  const hasChanges = selectedRole !== user.role;
  const isValidChange = hasChanges && canChangeThisUserRole;

  const getChangeDescription = () => {
    if (!hasChanges) return 'No changes selected';
    if (!canChangeThisUserRole) return 'Insufficient permissions for this change';

    const fromRole =
      ROLES.find((r) => r.id === user.role.toLowerCase()) ||
      ROLES.find((r) => r.name.toLowerCase().includes(user.role.toLowerCase()));
    const toRole = ROLES.find((r) => r.id === selectedRole);

    const fromRoleName = fromRole?.name || user.role;
    const toRoleName = toRole?.name || selectedRole;

    return `${user.email} will be changed from ${fromRoleName} to ${toRoleName}`;
  };

  return (
    <>
      <Dialog open onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="rounded-xl border border-border bg-muted p-2.5">
                <Shield className="size-5 text-muted-foreground" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold">Change User Role</DialogTitle>
                <DialogDescription className="text-sm text-muted-foreground">
                  Update role for {user.email}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Current Role Display */}
            <div className="rounded-lg border border-border bg-muted/50 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="mb-1 text-sm text-muted-foreground">Current Role</p>
                  <div className="flex items-center gap-2">
                    {(() => {
                      const currentRole =
                        ROLES.find((r) => r.id === user.role.toLowerCase()) ||
                        ROLES.find((r) => r.name.toLowerCase().includes(user.role.toLowerCase()));
                      if (currentRole) {
                        const Icon = currentRole.icon;
                        return (
                          <div className="flex items-center gap-2">
                            <div
                              className={cn(
                                'p-1.5 rounded-md bg-gradient-to-br',
                                currentRole.color,
                                'border',
                                currentRole.borderColor,
                              )}
                            >
                              <Icon className={cn('w-3.5 h-3.5', currentRole.iconColor)} />
                            </div>
                            <span className="font-medium">{currentRole.name}</span>
                          </div>
                        );
                      }
                      return (
                        <Badge variant="outline" className="font-medium capitalize">
                          {user.role}
                        </Badge>
                      );
                    })()}
                  </div>
                </div>
                {hasChanges && <ArrowRight className="size-4 text-muted-foreground" />}
              </div>
            </div>

            {/* Role Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Select New Role</label>
              <Select value={selectedRole} onValueChange={setSelectedRole} disabled={isLoading}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="Select new role">
                    {selectedRole ? (
                      <span className="flex items-center gap-2">
                        {(() => {
                          const role = ROLES.find((r) => r.id === selectedRole);
                          if (role) {
                            const RoleIcon = role.icon;
                            return (
                              <>
                                <div
                                  className={cn(
                                    'p-1 rounded bg-gradient-to-br',
                                    role.color,
                                    'border',
                                    role.borderColor,
                                  )}
                                >
                                  <RoleIcon className={cn('w-3.5 h-3.5', role.iconColor)} />
                                </div>
                                <span>{role.name}</span>
                              </>
                            );
                          }
                          return 'Select new role';
                        })()}
                      </span>
                    ) : null}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {ROLES.map((role) => {
                    // Only show roles that the current user can assign
                    if (!availableRoles.includes(role.id)) return null;
                    const RoleIcon = role.icon;
                    return (
                      <SelectItem key={role.id} value={role.id} className="cursor-pointer">
                        <div className="flex items-start gap-3 p-1">
                          <div
                            className={cn(
                              'p-1.5 rounded-md bg-gradient-to-br',
                              role.color,
                              'border',
                              role.borderColor,
                            )}
                          >
                            <RoleIcon className={cn('w-4 h-4', role.iconColor)} />
                          </div>
                          <div>
                            <h4 className="font-semibold">{role.name}</h4>
                            <p className="text-xs text-muted-foreground">{role.description}</p>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Change Description */}
            <div className="rounded-lg border border-border bg-muted/50 p-4">
              <p className="mb-2 text-sm text-muted-foreground">Change Summary</p>
              <p className="text-sm font-medium">{getChangeDescription()}</p>
            </div>

            {/* Error Message */}
            {errorMessage && (
              <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-500/20 dark:bg-red-500/10">
                <p className="text-sm text-red-600 dark:text-red-400">{errorMessage}</p>
              </div>
            )}

            {/* Permissions Info */}
            {availableRoles.length === 0 && (
              <div className="rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-500/20 dark:bg-amber-500/10">
                <p className="text-sm text-amber-600 dark:text-amber-400">
                  You don&apos;t have permission to change roles for this user.
                </p>
              </div>
            )}
          </div>

          <DialogFooter className="flex flex-col gap-2 sm:flex-row">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button onClick={() => setConfirmOpen(true)} disabled={!isValidChange || isLoading}>
              {isLoading ? 'Changing...' : 'Change Role'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold">Confirm Role Change</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change {user.email}&apos;s role from{' '}
              <span className="font-medium text-foreground">
                {ROLES.find((r) => r.id === user.role.toLowerCase())?.name || user.role}
              </span>{' '}
              to{' '}
              <span className="font-medium text-foreground">
                {ROLES.find((r) => r.id === selectedRole)?.name || selectedRole}
              </span>
              ?
              <br />
              <br />
              This will immediately update their permissions and access levels.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? 'Changing...' : 'Confirm Change'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
