import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Crown, Shield, Users, Eye, Check, X, Key, Sparkles, User } from 'lucide-react';
import { useMemo } from 'react';

type Role = {
  name: string;
  description: string;
  permissions: string[];
  color: string;
  border: string;
  icon: string;
  members: Array<{ name: string; avatar: string | undefined; email: string }>;
  count: number;
};

type RolesTabProps = {
  rolesWithMembers: Role[];
};

const getRoleIcon = (icon: string, size: 'sm' | 'md' | 'lg' = 'sm') => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const iconClass = sizeClasses[size];

  switch (icon) {
    case '👑': {
      return <Crown className={iconClass} />;
    }
    case '🛡️': {
      return <Shield className={iconClass} />;
    }
    case '👤': {
      return <Users className={iconClass} />;
    }
    case '🙋': {
      return <Users className={iconClass} />;
    }
    case '👁️': {
      return <Eye className={iconClass} />;
    }
    default: {
      return <Users className={iconClass} />;
    }
  }
};

const getInitials = (name: string) => {
  if (!name) return '??';
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

export function RolesTab({ rolesWithMembers }: RolesTabProps) {
  // Handle empty state
  if (!rolesWithMembers || rolesWithMembers.length === 0) {
    return (
      <Card className="border-border bg-gradient-to-br from-sidebar via-sidebar to-sidebar/80 shadow-xl">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Users className="mx-auto mb-4 size-12 text-muted-foreground" />
            <h3 className="mb-2 text-lg font-semibold text-foreground">No Roles Found</h3>
            <p className="text-sm text-muted-foreground">
              No roles have been defined for this organization yet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Extract all unique permissions from all roles with useMemo for performance
  const allPermissions = useMemo(
    () => [...new Set(rolesWithMembers.flatMap((role) => role.permissions))].sort(),
    [rolesWithMembers],
  );

  // Sort roles by priority (owner first, then by name) with useMemo
  const sortedRoles = useMemo(
    () =>
      [...rolesWithMembers].sort((a, b) => {
        const aIsOwner = a.name.toLowerCase().includes('owner');
        const bIsOwner = b.name.toLowerCase().includes('owner');

        if (aIsOwner && !bIsOwner) return -1;
        if (!aIsOwner && bIsOwner) return 1;

        return a.name.localeCompare(b.name);
      }),
    [rolesWithMembers],
  );

  return (
    <TooltipProvider>
      <Card className="border-border bg-card shadow-lg dark:shadow-2xl">
        <CardHeader className="space-y-4 pb-8">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3 text-2xl font-bold">
              <div className="rounded-xl border border-brand/20 bg-gradient-to-br from-brand/10 to-brand/5 p-2.5 dark:from-brand/20 dark:to-brand/10">
                <Sparkles className="size-6 text-brand" />
              </div>
              <span className="text-foreground">Role Permissions Matrix</span>
            </CardTitle>
            <Badge variant="secondary" className="bg-muted text-muted-foreground">
              {sortedRoles.reduce((acc, role) => acc + role.count, 0)} Total Members
            </Badge>
          </div>
          <p className="max-w-2xl text-sm text-muted-foreground">
            Visualize role permissions and team member assignments across your organization
          </p>
        </CardHeader>

        <CardContent className="overflow-x-auto pb-8">
          <div className="min-w-full" role="table" aria-label="Role permissions comparison">
            {/* Header row with role names */}
            <div className="sticky top-0 z-10 bg-card backdrop-blur-sm dark:bg-background/95">
              <div className="mb-6 flex items-start border-b-2 border-border/50 pb-4" role="row">
                <div className="w-52 shrink-0 pr-4" role="columnheader">
                  <h3 className="flex items-center gap-2 text-sm font-semibold uppercase tracking-wider text-muted-foreground">
                    <Key className="size-4" />
                    Permissions
                  </h3>
                </div>
                <div className="flex flex-1 flex-wrap justify-around gap-4" role="row">
                  {sortedRoles.map((role, index) => (
                    <div
                      key={role.name}
                      className="flex min-w-[180px] max-w-[200px] flex-col items-center"
                      role="columnheader"
                      aria-label={`Role: ${role.name}`}
                    >
                      <div className="relative mb-3">
                        <div
                          className={`
                          size-12 rounded-xl bg-gradient-to-br ${role.color} 
                          flex items-center justify-center border
                          border-border/30 shadow-sm dark:border-transparent
                        `}
                        >
                          <div className="text-foreground/80 dark:text-foreground">
                            {getRoleIcon(role.icon, 'md')}
                          </div>
                        </div>
                        {role.name.toLowerCase().includes('owner') && (
                          <div className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-yellow-500 shadow-sm dark:bg-yellow-400">
                            <Crown className="size-3 text-white dark:text-yellow-900" />
                          </div>
                        )}
                      </div>
                      <h4 className="mb-1 text-center text-sm font-bold leading-tight text-foreground">
                        {role.name}
                      </h4>
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {role.count} {role.count === 1 ? 'member' : 'members'}
                      </Badge>

                      {/* Member list */}
                      <div className="mt-3 w-full">
                        {role.members.length > 0 ? (
                          <div className="space-y-1.5">
                            {role.members.slice(0, 2).map((member) => (
                              <div key={member.email} className="flex items-center gap-2 px-2">
                                <Avatar className="size-5 border border-border">
                                  <AvatarImage src={member.avatar} alt={member.name} />
                                  <AvatarFallback className="bg-muted text-[9px] text-muted-foreground">
                                    {getInitials(member.name)}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="max-w-[120px] truncate text-xs text-muted-foreground">
                                  {member.name}
                                </span>
                              </div>
                            ))}
                            {role.members.length > 2 && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button className="px-2 text-xs text-muted-foreground hover:text-foreground">
                                    +{role.members.length - 2} more
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div className="space-y-1">
                                    <p className="mb-1 text-sm font-semibold">All Members:</p>
                                    {role.members.map((member) => (
                                      <p key={member.email} className="text-xs">
                                        {member.name} - {member.email}
                                      </p>
                                    ))}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        ) : (
                          <p className="text-center text-xs text-muted-foreground">No members</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Permission rows */}
            <div className="space-y-3" role="rowgroup">
              {allPermissions.map((permission, permissionIndex) => (
                <div
                  key={permission}
                  className={`
                    flex items-center rounded-lg border
                    border-border/50 bg-muted/20
                    p-3 transition-colors
                    duration-200 hover:bg-muted/30
                    dark:bg-card/30 dark:hover:bg-card/50
                  `}
                  role="row"
                  aria-label={`Permission: ${permission.replace('_', ' ')}`}
                >
                  {/* Permission info column */}
                  <div className="w-52 shrink-0 pr-4">
                    <div className="flex items-center gap-3">
                      <div className="flex size-9 items-center justify-center rounded-lg border border-border bg-muted dark:border-brand/20 dark:bg-brand/10">
                        <Key className="size-4 text-muted-foreground dark:text-brand" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-semibold text-foreground">
                          {permission.replace('_', ' ').replaceAll(/\b\w/g, (l) => l.toUpperCase())}
                        </p>
                        <p className="mt-0.5 truncate text-xs text-muted-foreground">
                          Access control
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Role assignment columns */}
                  <div className="flex flex-1 flex-wrap justify-around gap-4" role="row">
                    {sortedRoles.map((role, roleIndex) => {
                      const hasPermission = role.permissions.includes(permission);

                      return (
                        <div
                          key={`${permission}-${role.name}`}
                          className="flex min-w-[180px] max-w-[200px] items-center justify-center"
                          role="cell"
                          aria-label={`${role.name} ${hasPermission ? 'has' : 'does not have'} ${permission.replace('_', ' ')} permission`}
                        >
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div
                                className={`
                                  cursor-pointer rounded-lg p-2.5
                                  transition-colors duration-200
                                  ${
                                    hasPermission
                                      ? 'border border-green-200 bg-green-50 hover:bg-green-100 dark:border-green-500/30 dark:bg-green-500/20 dark:hover:bg-green-500/30'
                                      : 'border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-500/20 dark:bg-gray-500/10 dark:hover:bg-gray-500/20'
                                  }
                                `}
                              >
                                {hasPermission ? (
                                  <div className="flex items-center gap-1.5">
                                    <Check className="size-4 text-green-600 dark:text-green-400" />
                                    <span className="text-xs font-medium text-green-700 dark:text-green-300">
                                      Granted
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-1.5">
                                    <X className="size-4 text-gray-400 dark:text-gray-500" />
                                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                                      Denied
                                    </span>
                                  </div>
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="font-semibold">{role.name}</p>
                              <p className="text-xs">
                                {hasPermission
                                  ? `Has ${permission.replace('_', ' ').toLowerCase()} permission`
                                  : `No ${permission.replace('_', ' ').toLowerCase()} permission`}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Summary Statistics */}
            <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-3">
              {/* Roles Summary Card */}
              <Card className="border-border bg-muted/50 dark:border-brand/20 dark:bg-gradient-to-br dark:from-brand/10 dark:to-brand/5">
                <CardContent className="p-6">
                  <div className="mb-4 flex items-center justify-between">
                    <h4 className="flex items-center gap-2 text-sm font-semibold text-foreground">
                      <Shield className="size-4" />
                      Roles Overview
                    </h4>
                    <Badge
                      variant="secondary"
                      className="bg-muted text-foreground dark:bg-brand/20 dark:text-brand"
                    >
                      {sortedRoles.length}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {sortedRoles.map((role) => (
                      <div key={role.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className={`dark: size-8 rounded-lg bg-muted dark:bg-gradient-to-br${role.color} border border-border p-1.5 dark:border-transparent`}
                          >
                            <div className="text-muted-foreground dark:text-foreground">
                              {getRoleIcon(role.icon, 'sm')}
                            </div>
                          </div>
                          <span className="text-sm font-medium">{role.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {role.permissions.length} perms
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {role.count} users
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Permissions Summary Card */}
              <Card className="border-border bg-card">
                <CardContent className="p-6">
                  <h4 className="mb-4 flex items-center gap-2 text-sm font-semibold text-foreground">
                    <Key className="size-4" />
                    Permissions Distribution
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Total Permissions</span>
                      <span className="font-bold">{allPermissions.length}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Most Privileged</span>
                      <span className="font-bold">
                        {
                          sortedRoles.reduce((max, role) =>
                            role.permissions.length > max.permissions.length ? role : max,
                          ).name
                        }
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Least Privileged</span>
                      <span className="font-bold">
                        {
                          sortedRoles.reduce((min, role) =>
                            role.permissions.length < min.permissions.length ? role : min,
                          ).name
                        }
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Members Summary Card */}
              <Card className="border-border bg-muted/30 dark:bg-card/50">
                <CardContent className="p-6">
                  <h4 className="mb-4 flex items-center gap-2 text-sm font-semibold text-foreground">
                    <Users className="size-4" />
                    Team Composition
                  </h4>
                  <div className="space-y-3">
                    {sortedRoles.map((role) => (
                      <div key={role.name}>
                        <div className="mb-1 flex items-center justify-between">
                          <span className="text-sm font-medium">{role.name}</span>
                          <span className="text-sm text-muted-foreground">
                            {role.count > 0
                              ? `${Math.round((role.count / sortedRoles.reduce((acc, r) => acc + r.count, 0)) * 100)}%`
                              : '0%'}
                          </span>
                        </div>
                        <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                          <div
                            className={`dark: h-2 bg-foreground/20 dark:bg-gradient-to-r${role.color}`}
                            style={{
                              width: `${role.count > 0 ? (role.count / sortedRoles.reduce((acc, r) => acc + r.count, 0)) * 100 : 0}%`,
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
