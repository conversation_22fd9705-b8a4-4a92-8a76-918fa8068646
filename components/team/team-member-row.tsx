'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>otateCcw, Ban } from 'lucide-react';
import { CheckCircle2, XCircle, Mail } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { canChangeUserRole, canRemoveUser } from '@/lib/permissions';

interface TeamMemberRowProps {
  user: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
    status: 'Active' | 'Deactivated' | 'Invited';
    userCredits: number;
    lastActive: string;
  };
  currentUserRole?: string | null; // Role of the user viewing this row
  onChangeRole?: () => void;
  onRemove?: () => void;
  onReinvite?: () => void;
  onCancelInvite?: () => void;
  isReinviting?: boolean;
  isCancelling?: boolean;
}

export function TeamMemberRow({
  user,
  currentUserRole,
  onChangeRole,
  onRemove,
  onReinvite,
  onCancelInvite,
  isReinviting,
  isCancelling,
}: TeamMemberRowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active': {
        return (
          <Badge
            variant="secondary"
            className="bg-green-500 px-2 py-0.5 font-semibold text-green-50"
          >
            Active
          </Badge>
        );
      }
      case 'Deactivated': {
        return (
          <Badge variant="secondary" className="bg-red-500 px-2 py-0.5 font-semibold text-red-50">
            Deactivated
          </Badge>
        );
      }
      case 'Invited': {
        return (
          <Badge variant="secondary" className="bg-blue-500 px-2 py-0.5 font-semibold text-blue-50">
            Invited
          </Badge>
        );
      }
      default: {
        return (
          <Badge
            variant="secondary"
            className="bg-muted px-2 py-0.5 font-semibold text-muted-foreground"
          >
            Unknown
          </Badge>
        );
      }
    }
  };

  const getInitials = (name: string) => {
    if (!name || name.trim() === '') {
      return '??';
    }
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2); // Limit to 2 characters
  };

  const formatLastActive = (date: string) => {
    if (!date) return 'Never';
    try {
      const d = new Date(date);
      if (isNaN(d.getTime())) return 'Never';
      return d.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Never';
    }
  };

  // Check permissions for role management
  const canChangeRole = currentUserRole
    ? canChangeUserRole(currentUserRole, user.role, 'member').canChange
    : false;

  const canRemove = currentUserRole ? canRemoveUser(currentUserRole, user.role).canRemove : false;

  // For invited users, show reinvite/cancel options regardless of role permissions
  const isInvited = user.status === 'Invited';
  const hasInviteActions = isInvited && (onReinvite || onCancelInvite);

  // Don't show actions if user has no permissions
  const hasAnyPermission = canChangeRole || canRemove || hasInviteActions;

  return (
    <tr className="border-b border-sidebar-border transition-colors hover:bg-muted/50">
      <td className="px-6 py-5">
        <div className="flex items-center space-x-4">
          <Avatar className="size-10">
            <AvatarImage src={user.avatar || '/placeholder.svg'} />
            <AvatarFallback className="bg-brand text-xs font-semibold text-brand-foreground">
              {getInitials(user.name || user.email)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-semibold text-foreground">{user.name || user.email}</p>
            <p className="text-xs text-muted-foreground">{user.email}</p>
          </div>
        </div>
      </td>
      <td className="px-6 py-5">
        <span className="text-sm font-medium capitalize text-foreground">{user.role}</span>
      </td>
      <td className="px-6 py-5">
        <span className="text-sm font-medium text-foreground">{user.userCredits || 0}</span>
      </td>
      <td className="px-6 py-5">{getStatusBadge(user.status)}</td>
      <td className="px-6 py-5">
        <span className="text-sm font-medium text-foreground">
          {formatLastActive(user.lastActive)}
        </span>
      </td>
      <td className="px-6 py-5">
        {hasAnyPermission ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 text-muted-foreground hover:text-foreground"
              >
                <MoreHorizontal className="size-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="border-sidebar-border bg-sidebar">
              {/* Standard role management actions for active/deactivated users */}
              {!isInvited && canChangeRole && (
                <DropdownMenuItem
                  className="font-medium text-muted-foreground hover:bg-muted hover:text-foreground"
                  onClick={onChangeRole}
                >
                  Change Role
                </DropdownMenuItem>
              )}

              {!isInvited && canRemove && (
                <DropdownMenuItem
                  className="font-medium text-red-500 hover:bg-muted hover:text-red-500"
                  onClick={onRemove}
                >
                  Remove Member
                </DropdownMenuItem>
              )}

              {/* Invited user specific actions */}
              {isInvited && onReinvite && (
                <DropdownMenuItem
                  className="font-medium text-blue-500 hover:bg-muted hover:text-blue-400"
                  onClick={onReinvite}
                  disabled={isReinviting || isCancelling}
                >
                  <RotateCcw className="mr-2 size-4" />
                  {isReinviting ? 'Resending...' : 'Resend Invite'}
                </DropdownMenuItem>
              )}

              {isInvited && onCancelInvite && (
                <DropdownMenuItem
                  className="font-medium text-red-500 hover:bg-muted hover:text-red-400"
                  onClick={onCancelInvite}
                  disabled={isReinviting || isCancelling}
                >
                  <Ban className="mr-2 size-4" />
                  {isCancelling ? 'Cancelling...' : 'Cancel Invite'}
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <span className="text-xs text-muted-foreground">No actions</span>
        )}
      </td>
    </tr>
  );
}
