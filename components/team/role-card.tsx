import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface RoleCardProps {
  role: {
    name: string;
    description: string;
    permissions: string[];
    members: Array<{
      name: string;
      email: string;
      avatar?: string;
    }>;
  };
}

export function RoleCard({ role }: RoleCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Card className="border-[#3b3b3b] bg-[#262131]">
      <CardHeader>
        <h3 className="text-lg font-semibold text-white">{role.name}</h3>
        <p className="text-sm text-[#94a3b8]">{role.description}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="mb-2 text-sm font-medium text-white">Permissions</h4>
          <ul className="space-y-1">
            {role.permissions.map((permission) => (
              <li key={permission} className="flex items-start space-x-2">
                <div className="mt-2 size-1.5 shrink-0 rounded-full bg-[#94a3b8]"></div>
                <span className="text-sm text-[#94a3b8]">{permission}</span>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="mb-2 text-sm font-medium text-white">Current Members</h4>
          <div className="flex -space-x-2">
            {role.members.slice(0, 3).map((member, index) => (
              <Avatar
                key={member.email}
                className="size-8 border-2 border-[#262131]"
                title={member.email}
              >
                <AvatarImage src={member.avatar} title={member.email} />
                <AvatarFallback className="bg-[#8a2be2] text-xs text-white">
                  {getInitials(member.name)}
                </AvatarFallback>
              </Avatar>
            ))}
            {role.members.length > 3 && (
              <div className="flex size-8 items-center justify-center rounded-full border-2 border-[#262131] bg-[#3b3b3b]">
                <span className="text-xs text-[#94a3b8]">+{role.members.length - 3}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
