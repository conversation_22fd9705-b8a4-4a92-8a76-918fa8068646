'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useDashboardStore } from '@/lib/stores/dashboard-store';
import {
  <PERSON><PERSON>,
  Zap,
  Clock,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Layers,
  Server,
} from 'lucide-react';

// Format execution time helper function
const formatExecutionTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  } else if (seconds < 3600) {
    const minutes = seconds / 60;
    return `${minutes.toFixed(1)}m`;
  } else {
    const hours = seconds / 3600;
    return `${hours.toFixed(1)}h`;
  }
};

// Optimized progress bar component with animation
const AnimatedProgress: React.FC<{ value: number; className?: string; delay?: number }> =
  React.memo(({ value, className = 'h-3', delay = 0 }) => (
    <div className={`relative overflow-hidden rounded-full bg-muted/30 ${className}`}>
      <div
        className="h-full rounded-full bg-gradient-to-r from-brand to-brand/80 shadow-sm transition-all duration-1000 ease-out"
        style={{
          width: `${Math.min(Math.max(value, 0), 100)}%`,
          transitionDelay: `${delay}ms`,
        }}
      />
      <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-transparent via-white/10 to-transparent" />
    </div>
  ));

// Status configuration with improved styling
const STATUS_CONFIG = {
  completed: {
    label: 'Completed',
    icon: TrendingUp,
    bgColor: 'bg-emerald-50 dark:bg-emerald-950/20',
    textColor: 'text-emerald-700 dark:text-emerald-400',
    borderColor: 'border-emerald-200 dark:border-emerald-800',
    progressColor: 'from-emerald-500 to-emerald-600',
  },
  failed: {
    label: 'Failed',
    icon: TrendingDown,
    bgColor: 'bg-rose-50 dark:bg-rose-950/20',
    textColor: 'text-rose-700 dark:text-rose-400',
    borderColor: 'border-rose-200 dark:border-rose-800',
    progressColor: 'from-rose-500 to-rose-600',
  },
  running: {
    label: 'Running',
    icon: Activity,
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    textColor: 'text-blue-700 dark:text-blue-400',
    borderColor: 'border-blue-200 dark:border-blue-800',
    progressColor: 'from-blue-500 to-blue-600',
  },
  pending: {
    label: 'Initialized',
    icon: Clock,
    bgColor: 'bg-amber-50 dark:bg-amber-950/20',
    textColor: 'text-amber-700 dark:text-amber-400',
    borderColor: 'border-amber-200 dark:border-amber-800',
    progressColor: 'from-amber-500 to-amber-600',
  },
} as const;

// Enhanced metric card component
const MetricCard: React.FC<{
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  className?: string;
}> = React.memo(({ title, icon: Icon, children, className }) => (
  <Card
    className={`group relative overflow-hidden border-border/50 bg-gradient-to-t from-sidebar
    to-sidebar/80 shadow-lg transition-all duration-300 hover:scale-[1.02]
    hover:border-brand/20 hover:shadow-xl ${className}`}
  >
    <div
      className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-transparent opacity-0 
      transition-opacity duration-500 group-hover:opacity-100"
    />
    <CardHeader className="relative pb-4">
      <CardTitle className="flex items-center gap-3 text-foreground transition-colors duration-200 group-hover:text-brand">
        <div className="rounded-lg bg-brand/10 p-2 transition-colors duration-200 group-hover:bg-brand/20">
          <Icon className="size-5 text-brand" />
        </div>
        <span className="font-semibold">{title}</span>
      </CardTitle>
    </CardHeader>
    <CardContent className="relative space-y-4">{children}</CardContent>
  </Card>
));

// Status item component with enhanced styling
const StatusItem: React.FC<{
  status: keyof typeof STATUS_CONFIG;
  count: number;
  total: number;
  index: number;
}> = React.memo(({ status, count, total, index }) => {
  const config = STATUS_CONFIG[status];
  const Icon = config.icon;
  const percentage = total > 0 ? (count / total) * 100 : 0;

  return (
    <div className="group space-y-3 rounded-lg border border-border/50 bg-card/50 p-4 transition-all duration-200 hover:bg-muted/20 hover:border-border">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`rounded-lg p-2 ${config.bgColor} border ${config.borderColor}`}>
            <Icon className={`size-5 ${config.textColor}`} />
          </div>
          <div className="flex flex-col">
            <Badge
              variant="outline"
              className={`${config.bgColor} ${config.textColor} ${config.borderColor} font-medium text-xs`}
            >
              {config.label}
            </Badge>
            <span className="text-lg font-bold text-foreground">{count.toLocaleString()}</span>
          </div>
        </div>
        <div className="flex flex-col items-end gap-1">
          <span className="rounded-md bg-muted/50 px-2 py-1 text-xs font-semibold text-muted-foreground">
            {percentage.toFixed(1)}%
          </span>
        </div>
      </div>
      <div className="relative">
        <div className="h-2 w-full overflow-hidden rounded-full bg-muted/30">
          <div
            className={`h-full rounded-full bg-gradient-to-r ${config.progressColor} shadow-sm transition-all duration-1000 ease-out`}
            style={{
              width: `${Math.min(Math.max(percentage, 0), 100)}%`,
              transitionDelay: `${index * 100}ms`,
            }}
          />
        </div>
      </div>
    </div>
  );
});

// Distribution item component
const DistributionItem: React.FC<{
  label: string;
  count: number;
  total: number;
  index: number;
  showIcon?: boolean;
}> = React.memo(({ label, count, total, index, showIcon = false }) => {
  const percentage = total > 0 ? (count / total) * 100 : 0;

  return (
    <div className="group space-y-3 rounded-lg border border-border/50 bg-card/50 p-4 transition-all duration-200 hover:bg-muted/20 hover:border-border">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {showIcon && (
            <div className="rounded-lg p-2 bg-muted/20 border border-border">
              <Server className="size-5 text-muted-foreground" />
            </div>
          )}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground truncate" title={label}>
              {label}
            </span>
            <span className="text-lg font-bold text-foreground">{count.toLocaleString()}</span>
          </div>
        </div>
        <div className="flex flex-col items-end gap-1">
          <span className="rounded-md bg-muted/50 px-2 py-1 text-xs font-semibold text-muted-foreground">
            {percentage.toFixed(1)}%
          </span>
        </div>
      </div>
      <div className="relative">
        <div className="h-2 w-full overflow-hidden rounded-full bg-muted/30">
          <div
            className="h-full rounded-full bg-gradient-to-r from-brand to-brand/80 shadow-sm transition-all duration-1000 ease-out"
            style={{
              width: `${Math.min(Math.max(percentage, 0), 100)}%`,
              transitionDelay: `${index * 50}ms`,
            }}
          />
        </div>
      </div>
    </div>
  );
});

export function QuantumJobMetrics() {
  const { getJobStats, filteredJobs } = useDashboardStore();
  const jobStats = getJobStats();

  // Memoized calculations with better error handling
  const distributions = React.useMemo(() => {
    if (!filteredJobs?.length) {
      return {
        qubit: [],
        vendor: [],
        device: [],
        experimentType: [],
      };
    }

    // Qubit distribution
    const qubitMap = new Map<number, number>();
    const vendorMap = new Map<string, number>();
    const deviceMap = new Map<string, number>();
    const experimentMap = new Map<string, number>();

    for (const job of filteredJobs) {
      // Qubit distribution
      const qubits = job.circuitNumQubits || 0;
      qubitMap.set(qubits, (qubitMap.get(qubits) || 0) + 1);

      // Vendor distribution - use the actual vendor from the job
      const vendor = job.vendor || job.provider || 'Unknown';
      vendorMap.set(vendor, (vendorMap.get(vendor) || 0) + 1);

      // Device distribution - use qbraidDeviceId if available
      const device = job.qbraidDeviceId || job.device || 'Unknown';
      deviceMap.set(device, (deviceMap.get(device) || 0) + 1);

      // Experiment type distribution
      const type = job.experimentType || 'Unknown';
      experimentMap.set(type, (experimentMap.get(type) || 0) + 1);
    }

    return {
      qubit: [...qubitMap.entries()]
        .map(([qubits, count]) => ({ label: `${qubits} Qubits`, count, key: qubits }))
        .sort((a, b) => a.key - b.key),

      vendor: [...vendorMap.entries()]
        .map(([vendor, count]) => ({ label: vendor, count }))
        .sort((a, b) => b.count - a.count),

      device: [...deviceMap.entries()]
        .map(([device, count]) => ({ label: device, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),

      experimentType: [...experimentMap.entries()]
        .map(([type, count]) => ({ label: type, count }))
        .sort((a, b) => b.count - a.count),
    };
  }, [filteredJobs]);

  const EmptyState: React.FC<{ message: string }> = React.memo(({ message }) => (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <div className="mb-3 rounded-full bg-muted/20 p-3">
        <Layers className="size-6 text-muted-foreground" />
      </div>
      <p className="text-sm font-medium text-muted-foreground">{message}</p>
    </div>
  ));

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Job Status Distribution */}
      <MetricCard title="Job Status Overview" icon={Activity} className="lg:col-span-2">
        {jobStats.total > 0 ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
            <StatusItem
              status="completed"
              count={jobStats.completed}
              total={jobStats.total}
              index={0}
            />
            <StatusItem
              status="running"
              count={jobStats.running}
              total={jobStats.total}
              index={1}
            />
            <StatusItem status="failed" count={jobStats.failed} total={jobStats.total} index={2} />
            <StatusItem
              status="pending"
              count={jobStats.pending}
              total={jobStats.total}
              index={3}
            />
            {/* Average Execution Time */}
            <div className="group space-y-3 rounded-lg border border-border/50 bg-card/50 p-4 transition-all duration-200 hover:bg-muted/20 hover:border-border">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg p-2 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                    <Clock className="size-5 text-blue-700 dark:text-blue-400" />
                  </div>
                  <div className="flex flex-col">
                    <Badge
                      variant="outline"
                      className="bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800 font-medium text-xs"
                    >
                      Avg Exec Time
                    </Badge>
                    <span className="text-lg font-bold text-foreground">
                      {formatExecutionTime(jobStats.avgExecutionTime)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <EmptyState message="No job data available" />
        )}
      </MetricCard>

      {/* Qubit Distribution */}
      <MetricCard title="Qubit Distribution" icon={Zap}>
        {distributions.qubit.length > 0 ? (
          <div className="max-h-80 space-y-3 overflow-y-auto">
            {distributions.qubit.map(({ label, count }, index) => (
              <DistributionItem
                key={label}
                label={label}
                count={count}
                total={jobStats.total}
                index={index}
              />
            ))}
          </div>
        ) : (
          <EmptyState message="No qubit data available" />
        )}
      </MetricCard>

      {/* Vendor Distribution */}
      <MetricCard title="Vendor Distribution" icon={DollarSign}>
        {distributions.vendor.length > 0 ? (
          <div className="max-h-80 space-y-3 overflow-y-auto">
            {distributions.vendor.map(({ label, count }, index) => (
              <DistributionItem
                key={label}
                label={label}
                count={count}
                total={jobStats.total}
                index={index}
                showIcon
              />
            ))}
          </div>
        ) : (
          <EmptyState message="No vendor data available" />
        )}
      </MetricCard>

      {/* Top Devices */}
      <MetricCard title="Top Quantum Devices" icon={Cpu}>
        {distributions.device.length > 0 ? (
          <div className="space-y-3">
            {distributions.device.map(({ label, count }, index) => (
              <DistributionItem
                key={label}
                label={label}
                count={count}
                total={jobStats.total}
                index={index}
                showIcon
              />
            ))}
          </div>
        ) : (
          <EmptyState message="No device data available" />
        )}
      </MetricCard>

      {/* Experiment Types */}
      <MetricCard title="Experiment Types" icon={Layers}>
        {distributions.experimentType.length > 0 ? (
          <div className="max-h-80 space-y-3 overflow-y-auto">
            {distributions.experimentType.map(({ label, count }, index) => (
              <DistributionItem
                key={label}
                label={label}
                count={count}
                total={jobStats.total}
                index={index}
              />
            ))}
          </div>
        ) : (
          <EmptyState message="No experiment data available" />
        )}
      </MetricCard>
    </div>
  );
}
