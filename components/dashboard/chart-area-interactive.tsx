'use client';

import * as React from 'react';
import {
  Area,
  AreaChart,
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  YAxis,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

import { useIsMobile } from '@/hooks/use-mobile';
import { useQuantumDataWithStore } from '@/hooks/use-api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

import { generateChartConfig, buildDynamicStyles } from '@/components/dashboard/chart-utils';

import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { LoaderThree, LoaderTwo } from '@/components/ui/loader';
import { useOrgContext } from '../org/org-context-provider';
import { useDashboardStore } from '@/lib/stores/dashboard-store';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  RotateCcw,
  BarChart3,
  TrendingUp,
  Zap,
  Clock,
  ScatterChart as ScatterChartIcon,
} from 'lucide-react';
import { QuantumJob } from '@/types/quantum-job';

// Utilities moved to `chart-utils.ts` for reuse across dashboard charts.

export function ChartAreaInteractive() {
  const isMobile = useIsMobile();
  const [chartType, setChartType] = React.useState<'area' | 'bar'>('area');
  const [showFilters, setShowFilters] = React.useState(false);
  const [selectedChart, setSelectedChart] = React.useState<
    'jobs' | 'performance' | 'qubits' | 'execution'
  >('jobs');
  const { currentOrgId, currentOrg } = useOrgContext();
  const { data: quantumData, isLoading: quantumDataLoading } = useQuantumDataWithStore(
    currentOrgId || '',
  );

  // Zustand store for filters
  const {
    filters,
    setFilters,
    resetFilters,
    filteredJobs,
    chartData: storeChartData,
    devicePerformanceData,
    qubitUsageData,
    isLoading: storeLoading,
  } = useDashboardStore();

  // Aggregate jobsArray into chart data format: [{ date, [deviceKey]: count }]
  const chartData = React.useMemo(() => {
    if (!filteredJobs || filteredJobs.length === 0) return [];
    // Group jobs by date and device
    const jobsByDate: Record<string, Record<string, number>> = {};
    const deviceNames = new Set<string>();
    filteredJobs.forEach((job: QuantumJob) => {
      const createdAt = job.createdAt;
      if (!createdAt) return;
      const dateKey = new Date(createdAt).toISOString().split('T')[0];
      // Use qbraidDeviceId
      const deviceKey = job.qbraidDeviceId;
      if (!deviceKey) return;
      deviceNames.add(deviceKey);
      if (!jobsByDate[dateKey]) jobsByDate[dateKey] = {};
      jobsByDate[dateKey][deviceKey] = (jobsByDate[dateKey][deviceKey] || 0) + 1;
    });
    // Convert to chart data array
    const sortedDates = Object.keys(jobsByDate).sort();
    return sortedDates.map((date) => {
      const entry: Record<string, any> = { date };
      for (const deviceKey of deviceNames) {
        entry[deviceKey] = jobsByDate[date][deviceKey] || 0;
      }
      return entry;
    });
  }, [filteredJobs]);

  // Get unique device keys from the data
  const deviceKeys = React.useMemo(() => {
    if (!chartData || chartData.length === 0) return [];
    const keys = new Set<string>();
    for (const item of chartData) {
      for (const key of Object.keys(item)) {
        if (key !== 'date') {
          keys.add(key);
        }
      }
    }
    return [...keys];
  }, [chartData]);

  // Generate chart config dynamically
  const chartConfig = React.useMemo(() => generateChartConfig(deviceKeys), [deviceKeys]);

  // Dynamically generate CSS variables for device colors
  const CHART_COLOR_CLASS = 'chart-area-dynamic-colors';
  const dynamicStyles = `${buildDynamicStyles(
    deviceKeys,
    chartConfig,
    CHART_COLOR_CLASS,
  )} .${CHART_COLOR_CLASS} .recharts-tooltip-wrapper .text-muted-foreground { max-width: 100px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }`;

  // Filter data based on time range
  const filteredData = React.useMemo(() => {
    if (!chartData || chartData.length === 0) {
      return [];
    }

    const referenceDate = new Date();
    if (filters.timeRange === 'all') {
      return chartData;
    }
    let daysToSubtract = 90;
    if (filters.timeRange === '30d') {
      daysToSubtract = 30;
    } else if (filters.timeRange === '7d') {
      daysToSubtract = 7;
    }
    const startDate = new Date(referenceDate);
    startDate.setDate(startDate.getDate() - daysToSubtract);

    return chartData.filter((item) => {
      const date = new Date(item.date);
      return date >= startDate;
    });
  }, [chartData, filters.timeRange]);

  // Custom tooltip content that hides zero values
  const FilteredTooltipContent = React.useCallback((props: any) => {
    const filtered = props.payload?.filter(
      (item: any) => typeof item.value === 'number' && item.value > 0,
    );
    return <ChartTooltipContent {...props} payload={filtered} />;
  }, []);

  // Determine if chart type selector should be shown
  const shouldShowChartTypeSelector = selectedChart !== 'qubits';

  // Show loading state
  if (quantumDataLoading) {
    return (
      <Card
        className={`@container/card bg-gradient-to-t from-sidebar to-sidebar/80 ${CHART_COLOR_CLASS} h-full`}
      >
        <style>{dynamicStyles}</style>
        <CardHeader className="relative">
          <CardTitle>Quantum Jobs</CardTitle>
          <CardDescription>Loading {currentOrg?.orgName} device data...</CardDescription>
        </CardHeader>
        <CardContent className="mx-10 mb-10 mt-2 rounded-xl border border-sidebar-border px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="flex aspect-auto h-[250px] w-full items-center justify-center">
            <LoaderTwo />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show no-data state (when fetch succeeded but nothing to display)
  if (!quantumDataLoading && filteredData.length === 0) {
    return (
      <Card
        className={`@container/card bg-gradient-to-t from-sidebar to-sidebar/80 ${CHART_COLOR_CLASS} h-full`}
      >
        <style>{dynamicStyles}</style>
        <CardHeader className="relative">
          <CardTitle>Quantum Jobs</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="flex aspect-auto h-[250px] w-full items-center justify-center">
            <div className="text-muted-foreground">—</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state - we need to check if quantumData is null or undefined
  if (!quantumDataLoading && (quantumData === null || quantumData === undefined)) {
    return (
      <Card
        className={`@container/card bg-gradient-to-t from-sidebar to-sidebar/80 ${CHART_COLOR_CLASS} h-full`}
      >
        <style>{dynamicStyles}</style>
        <CardHeader className="relative">
          <CardTitle>Quantum Jobs</CardTitle>
          <CardDescription>Error loading data</CardDescription>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="flex aspect-auto h-[250px] w-full items-center justify-center">
            <div className="text-destructive">Failed to load quantum jobs data</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      {/*
        Inject the dynamically generated CSS variables & scoping class directly into the DOM.
        Recharts renders its SVG elements outside React's scope, so traditional CSS-in-JS
        solutions (e.g. `style` props) don't cascade. With this <style> block we ensure the
        variables are available to the SVG tree at runtime.
      */}
      <style>{dynamicStyles}</style>
      <Card
        className={`@container/card bg-gradient-to-t from-sidebar to-sidebar/80 ${CHART_COLOR_CLASS} h-full`}
      >
        <CardHeader className="relative">
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col space-y-2">
              <CardTitle>Quantum Jobs</CardTitle>
              <CardDescription>
                <span className="@[540px]/card:block hidden">
                  {currentOrg?.orgName} devices - Job activity over time
                </span>
                <span className="@[540px]/card:hidden">{currentOrg?.orgName} devices</span>
              </CardDescription>
            </div>

            {/* Chart type selector */}
            <div className="flex flex-wrap items-center gap-3">
              <ToggleGroup
                type="single"
                value={selectedChart}
                onValueChange={(value) =>
                  setSelectedChart(value as 'jobs' | 'performance' | 'qubits' | 'execution')
                }
                variant="outline"
                className="flex-wrap"
              >
                <ToggleGroupItem value="jobs" className="flex h-8 items-center gap-1 px-2.5">
                  <BarChart3 className="size-4" />
                  Jobs
                </ToggleGroupItem>
                <ToggleGroupItem value="performance" className="flex h-8 items-center gap-1 px-2.5">
                  <TrendingUp className="size-4" />
                  Performance
                </ToggleGroupItem>
                <ToggleGroupItem value="qubits" className="flex h-8 items-center gap-1 px-2.5">
                  <Zap className="size-4" />
                  Qubits
                </ToggleGroupItem>
                <ToggleGroupItem value="execution" className="flex h-8 items-center gap-1 px-2.5">
                  <Clock className="size-4" />
                  Execution
                </ToggleGroupItem>
              </ToggleGroup>

              {/* Only show chart type selector for non-qubits charts */}
              {shouldShowChartTypeSelector && (
                <ToggleGroup
                  type="single"
                  value={chartType}
                  onValueChange={(value) => setChartType(value as 'area' | 'bar')}
                  variant="outline"
                >
                  <ToggleGroupItem value="area" className="h-8 px-2.5">
                    Area
                  </ToggleGroupItem>
                  <ToggleGroupItem value="bar" className="h-8 px-2.5">
                    Bar
                  </ToggleGroupItem>
                </ToggleGroup>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          {selectedChart === 'jobs' && (
            <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
              {chartType === 'area' ? (
                <AreaChart data={filteredData}>
                  <defs>
                    {deviceKeys.map((deviceKey) => (
                      <linearGradient
                        key={`fill-${deviceKey}`}
                        id={`fill-${deviceKey}`}
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop offset="5%" stopColor={`var(--color-${deviceKey})`} stopOpacity={1} />
                        <stop
                          offset="95%"
                          stopColor={`var(--color-${deviceKey})`}
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    ))}
                  </defs>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{
                      value: 'Number of Jobs',
                      angle: -90,
                      position: 'insideLeft',
                      offset: 10,
                    }}
                  />
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    minTickGap={32}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      });
                    }}
                    label={{ value: 'Date', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={(tooltipProps) => (
                      <FilteredTooltipContent
                        {...tooltipProps}
                        labelFormatter={(value: any) =>
                          new Date(value).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                          })
                        }
                        indicator="dot"
                      />
                    )}
                  />
                  {deviceKeys.map((deviceKey) => (
                    <Area
                      key={deviceKey}
                      dataKey={deviceKey}
                      type="monotone"
                      fill={`url(#fill-${deviceKey})`}
                      stroke={chartConfig[deviceKey]?.color}
                      strokeWidth={1}
                      activeDot={(dotProps: any) => {
                        // Show a dot only when the datapoint value is > 0
                        const value = dotProps.payload?.[deviceKey];
                        if (typeof value !== 'number' || value <= 0) return <g />;
                        const color = chartConfig[deviceKey]?.color;
                        return (
                          <circle
                            cx={dotProps.cx}
                            cy={dotProps.cy}
                            r={3}
                            fill={color}
                            stroke={color}
                            strokeWidth={1}
                          />
                        );
                      }}
                    />
                  ))}
                </AreaChart>
              ) : (
                <BarChart data={filteredData}>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{
                      value: 'Number of Jobs',
                      angle: -90,
                      position: 'insideLeft',
                      offset: 10,
                    }}
                  />
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    minTickGap={32}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      });
                    }}
                    label={{ value: 'Date', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={(tooltipProps) => (
                      <FilteredTooltipContent
                        {...tooltipProps}
                        labelFormatter={(value: any) =>
                          new Date(value).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                          })
                        }
                        indicator="dot"
                      />
                    )}
                  />
                  {deviceKeys.map((deviceKey) => (
                    <Bar
                      key={deviceKey}
                      dataKey={deviceKey}
                      fill={chartConfig[deviceKey]?.color}
                      strokeWidth={1}
                    />
                  ))}
                </BarChart>
              )}
            </ChartContainer>
          )}

          {selectedChart === 'performance' && (
            <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
              {chartType === 'area' ? (
                <AreaChart data={devicePerformanceData}>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{ value: 'Jobs', angle: -90, position: 'insideLeft', offset: 10 }}
                  />
                  <XAxis
                    dataKey="device"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    label={{ value: 'Device', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                  <Area
                    dataKey="jobs"
                    type="monotone"
                    fill="url(#fill-jobs)"
                    stroke={chartConfig['jobs']?.color || '#6366f1'}
                    strokeWidth={1}
                  />
                  <defs>
                    <linearGradient id="fill-jobs" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#6366f1" stopOpacity={1} />
                      <stop offset="95%" stopColor="#6366f1" stopOpacity={0.1} />
                    </linearGradient>
                  </defs>
                </AreaChart>
              ) : (
                <BarChart data={devicePerformanceData}>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{ value: 'Jobs', angle: -90, position: 'insideLeft', offset: 10 }}
                  />
                  <XAxis
                    dataKey="device"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    label={{ value: 'Device', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                  <Bar
                    dataKey="jobs"
                    fill={chartConfig['jobs']?.color || '#6366f1'}
                    strokeWidth={1}
                  />
                </BarChart>
              )}
            </ChartContainer>
          )}

          {selectedChart === 'qubits' && (
            <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
              <BarChart data={qubitUsageData}>
                <CartesianGrid vertical={false} />
                <YAxis
                  domain={[0, 'dataMax']}
                  label={{ value: 'Jobs', angle: -90, position: 'insideLeft', offset: 10 }}
                />
                <XAxis
                  dataKey="qubits"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  label={{ value: 'Number of Qubits', position: 'insideBottom', offset: -5 }}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                <Bar
                  dataKey="jobs"
                  fill={chartConfig['qubits']?.color || '#8b5cf6'}
                  strokeWidth={1}
                />
              </BarChart>
            </ChartContainer>
          )}

          {selectedChart === 'execution' && (
            <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
              {chartType === 'area' ? (
                <AreaChart data={storeChartData}>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{
                      value: 'Avg Execution Time (s)',
                      angle: -90,
                      position: 'insideLeft',
                      offset: 10,
                    }}
                  />
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    minTickGap={32}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      });
                    }}
                    label={{ value: 'Date', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                  <Area
                    dataKey="avgExecutionTime"
                    type="monotone"
                    fill="url(#fill-execution)"
                    stroke={chartConfig['avgExecutionTime']?.color || '#f97316'}
                    strokeWidth={1}
                  />
                  <defs>
                    <linearGradient id="fill-execution" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f97316" stopOpacity={1} />
                      <stop offset="95%" stopColor="#f97316" stopOpacity={0.1} />
                    </linearGradient>
                  </defs>
                </AreaChart>
              ) : (
                <BarChart data={storeChartData}>
                  <CartesianGrid vertical={false} />
                  <YAxis
                    domain={[0, 'dataMax']}
                    label={{
                      value: 'Avg Execution Time (s)',
                      angle: -90,
                      position: 'insideLeft',
                      offset: 10,
                    }}
                  />
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    minTickGap={32}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      });
                    }}
                    label={{ value: 'Date', position: 'insideBottom', offset: -5 }}
                  />
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                  <Bar
                    dataKey="avgExecutionTime"
                    fill={chartConfig['avgExecutionTime']?.color || '#f97316'}
                    strokeWidth={1}
                  />
                </BarChart>
              )}
            </ChartContainer>
          )}
        </CardContent>
      </Card>
    </>
  );
}
