import { ChartConfig } from '@/components/ui/chart';

// qBraid-inspired color palette for devices
export const deviceColorPalette: string[] = [
  '#6366f1', // Indigo
  '#8b5cf6', // Violet
  '#a855f7', // Purple
  '#c084fc', // <PERSON>chsia
  '#ec4899', // Pink
  '#f43f5e', // <PERSON>
  '#f97316', // Orange
  '#f59e0b', // Amber
  '#eab308', // Yellow
  '#84cc16', // Lime
  '#10b981', // Emerald
  '#22c55e', // Green
  '#14b8a6', // Teal
  '#06b6d4', // <PERSON>an
  '#0ea5e9', // Sky
  '#3b82f6', // Blue
  '#2563eb', // Indigo Dark
  '#6d28d9', // Violet Deep
  '#c026d3', // Purple Deep
  '#ef4444', // Red
];

/**
 * Build the chart configuration object which maps each device key to a colour and label.
 * The output feeds directly into the `<ChartContainer>` so it can resolve colours & labels in the
 * tooltip and legend.
 */
export const generateChartConfig = (deviceKeys: string[]): ChartConfig => {
  const config: ChartConfig = {};

  for (const [index, key] of deviceKeys.entries()) {
    const color = deviceColorPalette[index % deviceColorPalette.length];

    // Convert raw device ID to a human-readable label (e.g. "ionq_harmony" -> "Ionq Harmony")
    const label = key.replaceAll('_', ' ').replaceAll(/\b\w/g, (l) => l.toUpperCase());

    config[key] = { label, color };
  }

  return config;
};

/**
 * Generate the CSS variable declarations needed by Recharts (e.g. `--color-<device>`).
 * Returns the final scoped style string: `.<className> { --color-a: #fff; ... }`.
 */
export const buildDynamicStyles = (
  deviceKeys: string[],
  chartConfig: ChartConfig,
  className: string,
) => {
  const cssVars = deviceKeys.map((key) => `--color-${key}: ${chartConfig[key]?.color};`).join(' ');

  return `.${className} { ${cssVars} }`;
};
