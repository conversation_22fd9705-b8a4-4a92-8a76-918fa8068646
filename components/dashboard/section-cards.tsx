import React from 'react';
import { <PERSON>ren<PERSON><PERSON>p<PERSON>con, Loader2, <PERSON><PERSON>, <PERSON><PERSON>, Clock } from 'lucide-react';

// Format execution time helper function
const formatExecutionTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  } else if (seconds < 3600) {
    const minutes = seconds / 60;
    return `${minutes.toFixed(1)}m`;
  } else {
    const hours = seconds / 3600;
    return `${hours.toFixed(1)}h`;
  }
};

import { Badge } from '@/components/ui/badge';
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useDashboardStats } from '@/hooks/use-api';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useDashboardStore } from '@/lib/stores/dashboard-store';
import { QuantumJob } from '@/types/quantum-job';

export function SectionCards() {
  const { currentOrgId } = useOrgContext();
  const { data: statsData, isLoading: statsLoading } = useDashboardStats(currentOrgId || undefined);
  const { getJobStats, filteredJobs } = useDashboardStore();

  const jobStats = getJobStats();

  // Calculate total revenue from filtered jobs
  const filteredRevenue = React.useMemo(() => {
    return filteredJobs.reduce((sum, job) => sum + ((job.cost || 0) / 100), 0);
  }, [filteredJobs]);

  const activeAccounts = React.useMemo(() => {
    return statsData?.uniqueUsers || 0;
  }, [statsData]);

  return (
    <div className="*:data-[slot=card]:shadow-xs grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:from-sidebar *:data-[slot=card]:to-sidebar/80 dark:*:data-[slot=card]:bg-gradient-to-t dark:*:data-[slot=card]:from-sidebar dark:*:data-[slot=card]:to-sidebar/80 md:grid-cols-4 lg:px-6">
      <Card className="@container/card bg-gradient-to-t from-sidebar to-sidebar/80">
        <CardHeader className="relative">
          <CardDescription>Total Revenue</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="size-4 animate-spin" />
                <span>Loading...</span>
              </div>
            ) : (
              `$${filteredRevenue.toFixed(2)}`
            )}
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">
              <TrendingUpIcon className="size-3" />
              Revenue
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {statsLoading ? 'Calculating revenue...' : 'Revenue from quantum jobs'}
            <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {statsLoading
              ? 'Loading organization data...'
              : `Revenue from ${filteredJobs.length} filtered jobs`}
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card bg-gradient-to-t from-sidebar to-sidebar/80">
        <CardHeader className="relative">
          <CardDescription>Quantum Jobs</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="size-4 animate-spin" />
                <span>Loading...</span>
              </div>
            ) : (
              jobStats.total.toLocaleString() || '0'
            )}
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">
              <Cpu className="size-3" />
              Jobs
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {statsLoading ? 'Calculating jobs...' : 'Success rate'}
            <Zap className="size-4" />
            <span className="text-muted-foreground">{jobStats.successRate.toFixed(1)}%</span>
          </div>
          <div className="text-muted-foreground">
            {statsLoading
              ? 'Loading job data...'
              : `${jobStats.completed} completed, ${jobStats.failed} failed`}
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card bg-gradient-to-t from-sidebar to-sidebar/80">
        <CardHeader className="relative">
          <CardDescription>Avg Execution Time</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="size-4 animate-spin" />
                <span>Loading...</span>
              </div>
            ) : (
              formatExecutionTime(jobStats.avgExecutionTime)
            )}
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">
              <Clock className="size-3" />
              Time
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {statsLoading ? 'Calculating time...' : 'Average execution time'}
            <Clock className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {statsLoading ? 'Loading time data...' : `Across ${jobStats.total} quantum jobs`}
          </div>
        </CardFooter>
      </Card>
      {/* active accounts */}
      <Card className="@container/card bg-gradient-to-t from-sidebar to-sidebar/80">
        <CardHeader className="relative">
          <CardDescription>Active Accounts</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {statsData?.uniqueUsers?.toLocaleString() || '0'}
          </CardTitle>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {statsLoading ? 'Calculating users...' : 'Active accounts'}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
