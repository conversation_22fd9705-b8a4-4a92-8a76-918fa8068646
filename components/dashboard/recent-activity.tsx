'use client';

import React, { use<PERSON>emo } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Clock,
  Activity,
  Database,
  User,
  Settings,
  UserPlus,
  UserX,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Loader2,
  ExternalLink,
} from 'lucide-react';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useQuantumJobsChartData } from '@/hooks/use-api';
import { useActivityLogs } from '@/hooks/use-activity-logs';
import { formatDistanceToNow } from 'date-fns';
import { QuantumJob, JobStatus } from '@/types/quantum-job';

// Type for recent job
interface RecentJob {
  id: string;
  deviceId: string;
  status: JobStatus;
  cost: number;
  shots: number;
  experimentType: string;
  createdAt?: string;
  provider: string;
  circuitNumQubits: number;
  executionDuration: number;
  vendor: string;
  qbraidStatus: JobStatus;
}

// Type for recent activity
interface RecentActivity {
  id: string;
  user: string;
  action: string;
  description: string;
  timestamp: string;
  resourceType: string;
}

export function RecentActivity() {
  const { currentOrgId } = useOrgContext();

  // Fetch recent jobs
  const { data: jobsData, isLoading: jobsLoading } = useQuantumJobsChartData(currentOrgId || '');

  // Fetch recent activity logs
  const { data: activityData, isLoading: activityLoading } = useActivityLogs({
    organizationId: currentOrgId || '',
    page: 0,
    resultsPerPage: 10,
    enabled: true,
  });

  // Process recent jobs
  const recentJobs = useMemo(() => {
    if (!jobsData?.jobsArray) return [];

    return jobsData.jobsArray
      .filter((job: QuantumJob) => {
        // Filter out invalid jobs
        if (job.qbraidDeviceId === 'null-device') {
          return false;
        }
        return true;
      })
      .sort((a: QuantumJob, b: QuantumJob) => {
        // Sort by creation date, newest first
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 5) // Show only the 5 most recent
      .map((job: QuantumJob, index: number) => ({
        id: job._id || `job-${index}`,
        deviceId: job.qbraidDeviceId,
        status: job.qbraidStatus || job.status,
        cost: job.cost || 0,
        shots: job.shots || 0,
        experimentType: job.experimentType,
        createdAt: job.createdAt,
        provider: job.provider,
        circuitNumQubits: job.circuitNumQubits,
        executionDuration: job.timeStamps?.executionDuration || 0,
        vendor: job.vendor,
        qbraidStatus: job.qbraidStatus || job.status,
      }));
  }, [jobsData]);

  // Process recent activities
  const recentActivities = useMemo(() => {
    if (!activityData?.data?.logs) return [];

    return activityData.data.logs
      .slice(0, 5) // Show only the 5 most recent
      .map((log: any) => ({
        id: log._id,
        user: log.userName || log.userEmail?.split('@')[0] || 'Unknown',
        action: log.actionDisplay || log.action.replaceAll('_', ' '),
        description: log.description,
        timestamp: log.timestamp,
        resourceType: log.resourceTypeDisplay || log.resourceType,
      }));
  }, [activityData]);

  // Get status icon and color
  const getStatusInfo = (status: JobStatus) => {
    switch (status) {
      case 'COMPLETED': {
        return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-100' };
      }
      case 'RUNNING': {
        return { icon: Play, color: 'text-blue-500', bgColor: 'bg-blue-100' };
      }
      case 'INITIALIZED': {
        return { icon: Pause, color: 'text-yellow-500', bgColor: 'bg-yellow-100' };
      }
      case 'FAILED': {
        return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-100' };
      }
      case 'CANCELLED': {
        return { icon: XCircle, color: 'text-gray-500', bgColor: 'bg-gray-100' };
      }
      default: {
        return { icon: Loader2, color: 'text-gray-500', bgColor: 'bg-gray-100' };
      }
    }
  };

  // Get activity icon
  const getActivityIcon = (action: string) => {
    if (action.toLowerCase().includes('invite')) return UserPlus;
    if (action.toLowerCase().includes('remove')) return UserX;
    if (action.toLowerCase().includes('role')) return Settings;
    if (action.toLowerCase().includes('device')) return Database;
    if (action.toLowerCase().includes('job')) return Activity;
    return User;
  };

  const isLoading = jobsLoading || activityLoading;

  return (
    <Card className="border-sidebar-border bg-gradient-to-br from-sidebar to-sidebar/80 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-foreground">
          <Activity className="size-5 text-brand" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="jobs" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="jobs" className="flex items-center gap-2">
              <Database className="size-4" />
              Recent Jobs
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <Clock className="size-4" />
              Team Activity
            </TabsTrigger>
          </TabsList>

          <TabsContent value="jobs" className="mt-4">
            {isLoading ? (
              <div className="space-y-3">
                {Array.from({length: 3}).map((_, i) => (
                  <div key={i} className="flex items-center gap-3 rounded-lg border p-3">
                    <Skeleton className="size-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (recentJobs.length === 0 ? (
              <div className="py-8 text-center">
                <Database className="mx-auto mb-4 size-12 text-muted-foreground" />
                <p className="text-muted-foreground">No recent jobs found</p>
                <p className="mt-1 text-sm text-muted-foreground">
                  Jobs will appear here as they are submitted
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentJobs.map((job) => {
                  const statusInfo = getStatusInfo(job.status);
                  const StatusIcon = statusInfo.icon;

                  return (
                    <div
                      key={job.id}
                      className="flex items-center gap-3 rounded-lg border border-sidebar-border p-3 transition-colors hover:bg-muted/20"
                    >
                      <div className={`rounded-full p-2 ${statusInfo.bgColor}`}>
                        <StatusIcon className={`size-4 ${statusInfo.color}`} />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <p className="truncate text-sm font-medium">{job.deviceId}</p>
                          <Badge variant="outline" className="text-xs">
                            {job.experimentType}
                          </Badge>
                          {job.circuitNumQubits > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {job.circuitNumQubits} qubits
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{job.vendor}</span>
                          <span>{job.shots} shots</span>
                          {job.executionDuration > 0 && <span>{job.executionDuration}s</span>}
                          <span>${(job.cost / 100).toFixed(2)}</span>
                          {job.createdAt && (
                            <span>
                              {formatDistanceToNow(new Date(job.createdAt), { addSuffix: true })}
                            </span>
                          )}
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {job.status}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            ))}
          </TabsContent>

          <TabsContent value="activity" className="mt-4">
            {isLoading ? (
              <div className="space-y-3">
                {Array.from({length: 3}).map((_, i) => (
                  <div key={i} className="flex items-center gap-3 rounded-lg border p-3">
                    <Skeleton className="size-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (recentActivities.length === 0 ? (
              <div className="py-8 text-center">
                <Clock className="mx-auto mb-4 size-12 text-muted-foreground" />
                <p className="text-muted-foreground">No recent activity</p>
                <p className="mt-1 text-sm text-muted-foreground">Team activity will appear here</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentActivities.map((activity) => {
                  const ActivityIcon = getActivityIcon(activity.action);

                  return (
                    <div
                      key={activity.id}
                      className="flex items-center gap-3 rounded-lg border border-sidebar-border p-3 transition-colors hover:bg-muted/20"
                    >
                      <div className="rounded-full bg-brand/10 p-2">
                        <ActivityIcon className="size-4 text-brand" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <p className="text-sm font-medium">{activity.user}</p>
                          <span className="text-xs text-muted-foreground">•</span>
                          <p className="text-sm text-muted-foreground">{activity.action}</p>
                        </div>
                        <p className="line-clamp-1 text-xs text-muted-foreground">
                          {activity.description}
                        </p>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </TabsContent>
        </Tabs>

        <div className="mt-4 border-t border-sidebar-border pt-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {recentJobs.length} recent jobs and {recentActivities.length} activities
            </p>
            <Button variant="outline" size="sm" className="text-xs">
              View All
              <ExternalLink className="ml-1 size-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
