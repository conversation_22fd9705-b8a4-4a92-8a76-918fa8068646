'use client';
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, X, Filter, Clock, ChevronDown, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useDashboardStore } from '@/lib/stores/dashboard-store';
import { JobStatus, Provider } from '@/types/quantum-job';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Calendar } from '@/components/ui/calendar';
import { DateRangePicker } from '@/components/ui/date-range-picker';

export function FilterPanel() {
  const { filters, setFilters, resetFilters, filteredJobs } = useDashboardStore();

  // Get unique values for filter options
  const uniqueStatuses = React.useMemo(() => {
    const statuses = new Set<JobStatus>();
    for (const job of filteredJobs) {
      if (job.status) {
        statuses.add(job.status);
      }
    }
    return [...statuses].filter(Boolean);
  }, [filteredJobs]);

  const uniqueVendors = React.useMemo(() => {
    const vendors = new Set<Provider>();
    for (const job of filteredJobs) {
      if (job.vendor) {
        vendors.add(job.vendor);
      }
    }
    return [...vendors].filter(Boolean);
  }, [filteredJobs]);

  const uniqueDevices = React.useMemo(() => {
    const devices = new Set<string>();
    for (const job of filteredJobs) {
      if (job.qbraidDeviceId) {
        devices.add(job.qbraidDeviceId);
      }
    }
    return [...devices].filter(Boolean);
  }, [filteredJobs]);

  const handleStatusChange = (status: JobStatus, checked: boolean) => {
    const currentStatuses = filters.status || [];
    const newStatuses = checked
      ? [...currentStatuses, status]
      : currentStatuses.filter((s) => s !== status);
    setFilters({ status: newStatuses });
  };

  const handleVendorChange = (vendor: Provider, checked: boolean) => {
    const currentVendors = filters.vendor || [];
    const newVendors = checked
      ? [...currentVendors, vendor]
      : currentVendors.filter((v) => v !== vendor);
    setFilters({ vendor: newVendors });
  };

  const handleDeviceChange = (device: string, checked: boolean) => {
    const currentDevices = filters.device || [];
    const newDevices = checked
      ? [...currentDevices, device]
      : currentDevices.filter((d) => d !== device);
    setFilters({ device: newDevices });
  };

  const handleQubitsMinChange = (value: string) => {
    const min = value ? Number.parseInt(value) : null;
    setFilters({ qubitsRange: { ...filters.qubitsRange, min } });
  };

  const handleQubitsMaxChange = (value: string) => {
    const max = value ? Number.parseInt(value) : null;
    setFilters({ qubitsRange: { ...filters.qubitsRange, max } });
  };

  const handleCostMinChange = (value: string) => {
    const min = value ? Number.parseFloat(value) : null;
    setFilters({ costRange: { ...filters.costRange, min } });
  };

  const handleCostMaxChange = (value: string) => {
    const max = value ? Number.parseFloat(value) : null;
    setFilters({ costRange: { ...filters.costRange, max } });
  };

  const handleTimeRangeChange = (value: '7d' | '30d' | '60d' | '90d' | 'all') => {
    setFilters({ timeRange: value });
  };

  // Get active filters count
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;
    if (filters.status.length > 0) count++;
    if (filters.vendor.length > 0) count++;
    if (filters.device.length > 0) count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.qubitsRange.min !== null || filters.qubitsRange.max !== null) count++;
    if (filters.costRange.min !== null || filters.costRange.max !== null) count++;
    if (filters.timeRange !== '90d') count++; // '90d' is the default
    return count;
  }, [filters]);

  const [expandedSections, setExpandedSections] = React.useState({
    timeRange: true,
    device: true,
    status: true,
    vendor: false,
    dateRange: false,
    rangeFilters: false,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  return (
    <Card className="h-full rounded-lg border border-sidebar-border bg-gradient-to-t from-sidebar to-sidebar/80 shadow-sm mr-8">
      <CardHeader className="border-b border-sidebar-border pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-foreground">
            <Filter className="size-5 text-primary" />
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge
                variant="secondary"
                className="ml-2 h-5 border-primary/20 bg-primary/10 px-1.5 text-xs text-primary"
              >
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="h-8 px-2 text-xs text-muted-foreground hover:bg-sidebar-border hover:text-foreground"
          >
            <X className="mr-1 size-3" />
            Reset
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        {/* Time Range Filter */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('timeRange')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <Clock className="size-4 text-muted-foreground" />
              <span>Time Range</span>
            </div>
            {expandedSections.timeRange ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.timeRange && (
            <div className="pl-6 pt-2">
              <ToggleGroup
                type="single"
                value={filters.timeRange}
                onValueChange={handleTimeRangeChange}
                variant="outline"
                className="hidden md:flex"
              >
                <ToggleGroupItem value="all" className="h-8 px-3 text-xs font-medium">
                  All time
                </ToggleGroupItem>
                <ToggleGroupItem value="90d" className="h-8 px-3 text-xs font-medium">
                  90 days
                </ToggleGroupItem>
                <ToggleGroupItem value="60d" className="h-8 px-3 text-xs font-medium">
                  60 days
                </ToggleGroupItem>
                <ToggleGroupItem value="30d" className="h-8 px-3 text-xs font-medium">
                  30 days
                </ToggleGroupItem>
                <ToggleGroupItem value="7d" className="h-8 px-3 text-xs font-medium">
                  7 days
                </ToggleGroupItem>
              </ToggleGroup>
              <Select value={filters.timeRange} onValueChange={handleTimeRangeChange}>
                <SelectTrigger
                  className="flex h-9 w-full border-sidebar-border text-xs md:hidden"
                  aria-label="Select a time range"
                >
                  <SelectValue placeholder="Last 30 days" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border-sidebar-border">
                  <SelectItem value="all" className="rounded-lg text-xs">
                    All time
                  </SelectItem>
                  <SelectItem value="90d" className="rounded-lg text-xs">
                    Last 90 days
                  </SelectItem>
                  <SelectItem value="60d" className="rounded-lg text-xs">
                    Last 60 days
                  </SelectItem>
                  <SelectItem value="30d" className="rounded-lg text-xs">
                    Last 30 days
                  </SelectItem>
                  <SelectItem value="7d" className="rounded-lg text-xs">
                    Last 7 days
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Device Filter */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('device')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <span>Device</span>
              {filters.device.length > 0 && (
                <Badge
                  variant="secondary"
                  className="h-5 border-primary/20 bg-primary/10 px-1.5 text-xs text-primary"
                >
                  {filters.device.length}
                </Badge>
              )}
            </div>
            {expandedSections.device ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.device && (
            <div className="pl-6 pt-2">
              <div className="grid grid-cols-2 gap-1.5">
                {uniqueDevices.slice(0, 20).map((device, index) => (
                  <Badge
                    key={device || `device-${index}`}
                    variant={filters.device?.includes(device) ? 'default' : 'outline'}
                    className={cn(
                      'cursor-pointer text-xs h-7 px-2 truncate transition-colors',
                      filters.device?.includes(device)
                        ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                        : 'bg-sidebar hover:bg-sidebar-border text-muted-foreground border-sidebar-border',
                    )}
                    onClick={() => handleDeviceChange(device, !filters.device?.includes(device))}
                    title={device}
                  >
                    {device}
                  </Badge>
                ))}
                {uniqueDevices.length > 20 && (
                  <Badge
                    variant="outline"
                    className="col-span-2 flex h-7 items-center justify-center border-sidebar-border bg-sidebar text-xs text-muted-foreground"
                  >
                    +{uniqueDevices.length - 20} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('status')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <span>Status</span>
            </div>
            {expandedSections.status ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.status && (
            <div className="pl-6 pt-2">
              <div className="flex flex-wrap gap-1.5">
                {uniqueStatuses.map((status, index) => (
                  <Badge
                    key={status || `status-${index}`}
                    variant={filters.status?.includes(status) ? 'default' : 'outline'}
                    className={cn(
                      'cursor-pointer text-xs h-7 px-2 transition-colors',
                      filters.status?.includes(status)
                        ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                        : 'bg-sidebar hover:bg-sidebar-border text-muted-foreground border-sidebar-border',
                    )}
                    onClick={() => handleStatusChange(status, !filters.status?.includes(status))}
                  >
                    {status}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Vendor Filter */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('vendor')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <span>Vendor</span>
            </div>
            {expandedSections.vendor ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.vendor && (
            <div className="pl-6 pt-2">
              <div className="flex flex-wrap gap-1.5">
                {uniqueVendors.map((vendor, index) => (
                  <Badge
                    key={vendor || `vendor-${index}`}
                    variant={filters.vendor?.includes(vendor) ? 'default' : 'outline'}
                    className={cn(
                      'cursor-pointer text-xs h-7 px-2 transition-colors',
                      filters.vendor?.includes(vendor)
                        ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                        : 'bg-sidebar hover:bg-sidebar-border text-muted-foreground border-sidebar-border',
                    )}
                    onClick={() => handleVendorChange(vendor, !filters.vendor?.includes(vendor))}
                  >
                    {vendor}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Date Range Filter */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('dateRange')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <span>Date Range</span>
              {(filters.dateRange.start || filters.dateRange.end) && (
                <Badge
                  variant="secondary"
                  className="h-5 border-primary/20 bg-primary/10 px-1.5 text-xs text-primary"
                >
                  Set
                </Badge>
              )}
            </div>
            {expandedSections.dateRange ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.dateRange && (
            <div className="pl-6 pt-2">
              <DateRangePicker
                dateRange={{
                  from: filters.dateRange.start || undefined,
                  to: filters.dateRange.end || undefined,
                }}
                onDateRangeChange={(range) => {
                  setFilters({
                    dateRange: {
                      start: range?.from || null,
                      end: range?.to || null,
                    },
                  });
                }}
                className="w-full"
              />
              <div className="mt-3 flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setFilters({
                      dateRange: {
                        start: null,
                        end: null,
                      },
                    });
                  }}
                  className="h-8 px-2 text-xs text-muted-foreground hover:text-foreground"
                >
                  Clear
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Range Filters */}
        <div className="space-y-2">
          <button
            onClick={() => toggleSection('rangeFilters')}
            className="flex w-full items-center justify-between rounded-md p-2 text-sm font-medium text-foreground transition-colors hover:bg-sidebar-border hover:text-foreground"
          >
            <div className="flex items-center gap-2">
              <span>Range Filters</span>
              {(filters.qubitsRange.min !== null ||
                filters.qubitsRange.max !== null ||
                filters.costRange.min !== null ||
                filters.costRange.max !== null) && (
                <Badge
                  variant="secondary"
                  className="h-5 border-primary/20 bg-primary/10 px-1.5 text-xs text-primary"
                >
                  Set
                </Badge>
              )}
            </div>
            {expandedSections.rangeFilters ? (
              <ChevronDown className="size-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="size-4 text-muted-foreground" />
            )}
          </button>
          {expandedSections.rangeFilters && (
            <div className="space-y-4 pl-6 pt-2">
              {/* Qubits Range Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-foreground">Qubits Range</Label>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                  <div>
                    <Label className="mb-1 block text-xs text-muted-foreground">Min</Label>
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.qubitsRange.min || ''}
                      onChange={(e) => handleQubitsMinChange(e.target.value)}
                      className="h-9 border-sidebar-border bg-sidebar text-xs"
                    />
                  </div>
                  <div>
                    <Label className="mb-1 block text-xs text-muted-foreground">Max</Label>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.qubitsRange.max || ''}
                      onChange={(e) => handleQubitsMaxChange(e.target.value)}
                      className="h-9 border-sidebar-border bg-sidebar text-xs"
                    />
                  </div>
                </div>
              </div>

              {/* Cost Range Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-foreground">Cost Range ($)</Label>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                  <div>
                    <Label className="mb-1 block text-xs text-muted-foreground">Min</Label>
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.costRange.min || ''}
                      onChange={(e) => handleCostMinChange(e.target.value)}
                      className="h-9 border-sidebar-border bg-sidebar text-xs"
                    />
                  </div>
                  <div>
                    <Label className="mb-1 block text-xs text-muted-foreground">Max</Label>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.costRange.max || ''}
                      onChange={(e) => handleCostMaxChange(e.target.value)}
                      className="h-9 border-sidebar-border bg-sidebar text-xs"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
