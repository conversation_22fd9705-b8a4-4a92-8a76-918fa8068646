'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Sparkles, ChevronDown } from 'lucide-react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card } from '@/components/ui/card';
import { ImageUpload } from '@/components/providers/image-upload';
import { cn } from '@/lib/utils';
import {
  AddProviderFormProps,
  AddProviderFormData,
  addProviderSchema,
  useAddProvider,
  createProviderFormData,
} from '@/hooks/use-providers';

export function AddProviderForm({ onSuccess, trigger, orgId }: AddProviderFormProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);
  const { mutateAsync: addProvider } = useAddProvider();
  const form = useForm<AddProviderFormData>({
    resolver: zodResolver(addProviderSchema),
    defaultValues: {
      orgId: orgId,
      name: '',
      description: '',
      website: '',
      supportEmail: '',
      documentation: '',
      sdkLink: '',
      logo: null,
      logoDark: null,
    },
  });

  const { watch, setValue } = form;
  const logoValue = watch('logo');
  const logoDarkValue = watch('logoDark');

  const onSubmit = async (data: AddProviderFormData) => {

    setIsSubmitting(true);

    try {
      // Use the helper to create FormData with proper validation
      const formData = createProviderFormData(data);


      const response = await addProvider({ formData });


      toast.success('Provider added successfully');
      handleClose();
      onSuccess?.(response.data);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to add provider');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setOpen(false);
    setShowDetails(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      setOpen(true);
    } else {
      handleClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="bg-brand font-semibold text-brand-foreground shadow-lg hover:bg-brand/90">
            <Plus className="mr-2 size-4" />
            Add Custom Provider
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-2xl overflow-y-auto border border-border bg-background/95 shadow-2xl backdrop-blur-sm dark:border-white/10 dark:bg-[#0a0a0f]">
        {/* Header with gradient background */}
        <div className="pointer-events-none absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-brand/10 via-brand/5 to-transparent" />

        <DialogHeader className="relative pb-2">
          <div className="mb-4 flex items-center gap-4">
            <div className="animate-pulse rounded-2xl bg-gradient-to-br from-brand to-brand/80 p-3 shadow-lg shadow-brand/20">
              <Plus className="size-6 text-white" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-foreground dark:text-white">
                Add Custom Provider
              </DialogTitle>
              <DialogDescription className="mt-1 text-sm text-muted-foreground dark:text-gray-400">
                Add your own quantum computing provider to{' '}
                <span className="font-medium text-brand">your fleet</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* Details Toggle */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="group mb-4 flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:bg-muted/50 hover:text-foreground dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-white"
        >
          <Sparkles className="size-4 text-brand transition-transform group-hover:rotate-12" />
          <span>Why Add a Custom Provider?</span>
          <ChevronDown
            className={cn('w-4 h-4 transition-transform duration-200', showDetails && 'rotate-180')}
          />
        </button>

        {/* Details Cards */}
        {showDetails && (
          <div className="mb-6 grid grid-cols-1 gap-4 animate-in fade-in slide-in-from-top-2">
            <Card className="border border-brand/20 bg-gradient-to-br from-brand/5 to-brand/10 p-4 dark:from-brand/10 dark:to-brand/5">
              <div className="flex items-start gap-3">
                <div className="rounded-lg bg-brand/10 p-2 dark:bg-brand/20">
                  <Plus className="size-4 text-brand" />
                </div>
                <div>
                  <h4 className="mb-1 font-semibold text-foreground dark:text-white">
                    Custom Integration
                  </h4>
                  <p className="text-sm text-muted-foreground dark:text-gray-400">
                    Connect your own quantum computing infrastructure or private providers not
                    available in our marketplace.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-5 duration-300 animate-in fade-in slide-in-from-bottom-2"
          >
            {/* Provider Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    htmlFor="provider-name"
                    className="text-sm font-medium text-foreground dark:text-gray-300"
                  >
                    Provider Name *
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="provider-name"
                      placeholder="e.g. Quantum Corp"
                      autoComplete="organization"
                      {...field}
                      disabled={isSubmitting}
                      className="h-12 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-destructive dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Provider Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    htmlFor="provider-description"
                    className="text-sm font-medium text-foreground dark:text-gray-300"
                  >
                    Description *
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      id="provider-description"
                      placeholder="Describe what this provider offers..."
                      autoComplete="off"
                      className="min-h-[100px] resize-none border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-destructive dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Website URL */}
            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    htmlFor="provider-website"
                    className="text-sm font-medium text-foreground dark:text-gray-300"
                  >
                    Website URL *
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="provider-website"
                      type="url"
                      placeholder="https://example.com"
                      autoComplete="url"
                      {...field}
                      disabled={isSubmitting}
                      className="h-12 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-destructive dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Additional Fields Toggle */}
            <button
              type="button"
              onClick={() => setShowAdditionalFields(!showAdditionalFields)}
              className="group mb-4 flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:bg-muted/50 hover:text-foreground dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-white"
            >
              <Plus className="size-4 text-brand transition-transform group-hover:rotate-12" />
              <span>Additional Information (Optional)</span>
              <ChevronDown
                className={cn(
                  'w-4 h-4 transition-transform duration-200',
                  showAdditionalFields && 'rotate-180'
                )}
              />
            </button>

            {/* Additional Fields Section */}
            {showAdditionalFields && (
              <div className="space-y-5 animate-in fade-in slide-in-from-top-2">
                {/* Support Email */}
                <FormField
                  control={form.control}
                  name="supportEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor="provider-support-email"
                        className="text-sm font-medium text-foreground dark:text-gray-300"
                      >
                        Support Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="provider-support-email"
                          type="email"
                          placeholder="<EMAIL>"
                          autoComplete="email"
                          {...field}
                          value={field.value || ''}
                          disabled={isSubmitting}
                          className="h-12 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                        />
                      </FormControl>
                      <FormMessage className="text-sm text-destructive dark:text-red-400" />
                    </FormItem>
                  )}
                />

                {/* Documentation URL */}
                <FormField
                  control={form.control}
                  name="documentation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor="provider-documentation"
                        className="text-sm font-medium text-foreground dark:text-gray-300"
                      >
                        Documentation URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="provider-documentation"
                          type="url"
                          placeholder="https://docs.example.com"
                          autoComplete="url"
                          {...field}
                          value={field.value || ''}
                          disabled={isSubmitting}
                          className="h-12 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                        />
                      </FormControl>
                      <FormMessage className="text-sm text-destructive dark:text-red-400" />
                    </FormItem>
                  )}
                />

                {/* SDK Link */}
                <FormField
                  control={form.control}
                  name="sdkLink"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        htmlFor="provider-sdk-link"
                        className="text-sm font-medium text-foreground dark:text-gray-300"
                      >
                        SDK Link
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="provider-sdk-link"
                          type="url"
                          placeholder="https://github.com/example/sdk"
                          autoComplete="url"
                          {...field}
                          value={field.value || ''}
                          disabled={isSubmitting}
                          className="h-12 border border-border bg-background text-foreground transition-all duration-200 placeholder:text-muted-foreground focus:border-brand focus:ring-1 focus:ring-brand/20 dark:border-white/5 dark:bg-[#0a0a0f] dark:text-white dark:placeholder:text-gray-600"
                        />
                      </FormControl>
                      <FormMessage className="text-sm text-destructive dark:text-red-400" />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Logo Upload Section */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Logo Upload (Light) */}
              <div className="space-y-2">
                <Label
                  htmlFor="provider-logo-light"
                  className="text-sm font-medium text-foreground dark:text-gray-300"
                >
                  Logo (Light Theme)
                </Label>
                <ImageUpload
                  id="provider-logo-light"
                  currentUrl={logoValue instanceof File ? undefined : undefined}
                  onUpload={(url) => {
                    // url is the preview URL, we keep the file in form state
                  }}
                  onFileSelect={(file) => {
                    setValue('logo', file);
                  }}
                  onRemove={() => {
                    setValue('logo', null);
                  }}
                  placeholder="Upload light theme logo"
                  disabled={isSubmitting}
                />
              </div>

              {/* Logo Upload (Dark) */}
              <div className="space-y-2">
                <Label
                  htmlFor="provider-logo-dark"
                  className="text-sm font-medium text-foreground dark:text-gray-300"
                >
                  Logo (Dark Theme) - Optional
                </Label>
                <ImageUpload
                  id="provider-logo-dark"
                  currentUrl={logoDarkValue instanceof File ? undefined : undefined}
                  onUpload={(url) => {
                    // url is the preview URL, we keep the file in form state
                  }}
                  onFileSelect={(file) => {
                    setValue('logoDark', file);
                  }}
                  onRemove={() => {
                    setValue('logoDark', null);
                  }}
                  placeholder="Upload dark theme logo"
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-3 border-t border-border pt-6 dark:border-white/10">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
                className="flex-1 border-border text-muted-foreground transition-all duration-200 hover:bg-muted hover:text-foreground dark:border-white/10 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-white"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-gradient-to-r from-brand to-brand/80 font-semibold text-white shadow-lg shadow-brand/25 transition-all duration-200 hover:scale-[1.02] hover:from-brand/90 hover:to-brand/70 disabled:hover:scale-100"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Adding Provider...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 size-4" />
                    Add Provider
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
