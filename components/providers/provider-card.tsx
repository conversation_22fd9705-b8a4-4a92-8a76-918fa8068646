'use client';

import { useState } from 'react';
import { ExternalLink, Plus, Trash2, Globe, Check } from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';
import { useThemeHydrated } from '@/hooks/use-theme';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import type { ProviderResponseType } from '@/types/provider';
import { OrgPermissionGuard } from '../auth/org-permission-guard';
import { Permission } from '@/types/auth';
interface ProviderCardProps {
  provider: ProviderResponseType;
  isAdded?: boolean;
  onAdd?: (providerId: string) => void;
  onSelect?: () => void;
  variant?: 'available' | 'added' | 'pending' | 'selectable';
}

export function ProviderCard({
  provider,
  isAdded = false,
  onAdd,
  onSelect,
  variant = 'available',
}: ProviderCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { currentTheme } = useThemeHydrated();

  const handleAdd = async () => {
    if (!onAdd) return;

    setIsLoading(true);
    try {
      await onAdd(provider._id);
    } catch {
      toast.error('Failed to add provider');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelect = async () => {
    if (!onSelect) return;

    setIsLoading(true);
    try {
      await onSelect();
    } catch {
      toast.error('Failed to select provider');
    } finally {
      setIsLoading(false);
    }
  };

  // Determine if provider is popular based on status or metadata
  const isPopular = provider.status === 'public';

  // Select appropriate logo based on theme - use resolvedTheme for more reliable detection
  const logoUrl =
    currentTheme === 'dark' && provider.logoUrlDark ? provider.logoUrlDark : provider.logoUrl;

  return (
    <Card className="group relative h-full bg-gradient-to-t from-sidebar to-sidebar/80 transition-shadow duration-200 hover:shadow-lg">
      {/* Status Badge */}
      {variant === 'added' && (
        <div className="absolute right-3 top-3 z-10">
          <Badge
            variant="secondary"
            className="border-green-500/30 bg-green-500/20 text-green-600 dark:text-green-400"
          >
            <Check className="mr-1 size-3" />
            Added
          </Badge>
        </div>
      )}

      {/* Status Badge for Pending */}
      {provider.status === 'pending_approval' && (
        <div className="absolute right-3 top-3 z-10">
          <Badge
            variant="secondary"
            className="border-yellow-500/30 bg-yellow-500/20 text-yellow-600"
          >
            Pending
          </Badge>
        </div>
      )}

      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute left-3 top-3 z-10">
          <Badge className="border-0 bg-brand text-brand-foreground">Popular</Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        {/* Logo */}
        <div className="mx-auto mb-3 flex size-16 items-center justify-center overflow-hidden rounded-lg border border-border bg-muted/50">
          {logoUrl ? (
            <Image
              src={logoUrl}
              alt={`${provider.provider || 'Provider'} logo`}
              width={64}
              height={64}
              className="size-full object-contain"
              onError={(e) => {
                // Fallback to text if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = document.createElement('div');
                fallback.className =
                  'w-full h-full flex items-center justify-center text-lg font-bold text-brand';
                fallback.textContent = provider.provider?.charAt(0) || '?';
                target.parentElement?.append(fallback);
              }}
            />
          ) : (
            <h3 className="flex size-full items-center justify-center text-lg font-bold text-brand">
              {provider.provider?.charAt(0) || '?'}
            </h3>
          )}
        </div>
      </CardHeader>

      {/* Name */}
      <h5 className="text-center text-sm font-semibold leading-tight text-foreground">
        {provider.provider || 'Unnamed Provider'}
      </h5>

      <CardContent className="flex-1 pt-0">
        <p className="mb-4 line-clamp-3 text-center text-sm text-muted-foreground">
          {provider.providerDescription || 'No description available'}
        </p>

        {/* Status and Device Count */}
        <div className="mb-2 flex justify-center gap-2">
          <Badge
            variant="outline"
            className={`capitalize ${
              provider.status === 'public'
                ? 'border-green-500/30 text-green-600'
                : (provider.status === 'pending_approval'
                  ? 'border-yellow-500/30 text-yellow-600'
                  : 'border-red-500/30 text-red-600')
            }`}
          >
            {provider.status?.replace('_', ' ') || 'Unknown'}
          </Badge>
          {provider.devices && provider.devices.length > 0 && (
            <Badge variant="outline">
              {provider.devices.length} Device{provider.devices.length === 1 ? '' : 's'}
            </Badge>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 pt-2">
        {/* Website Link */}
        <Button
          variant="ghost"
          size="sm"
          className="w-full text-muted-foreground hover:text-foreground"
          onClick={() => provider.about && window.open(provider.about, '_blank')}
        >
          <Globe className="mr-2 size-4" />
          Visit Website
          <ExternalLink className="ml-1 size-3" />
        </Button>

        {/* Action Button */}
        {/* Hide it for users who are not allowed to add providers */}
        <OrgPermissionGuard permission={Permission.ManageDevices} fallback={null}>
          {variant === 'available' && !isAdded && provider.status === 'public' && (
            <Button
              onClick={handleAdd}
              disabled={isLoading}
              className="w-full bg-brand text-brand-foreground hover:bg-brand/90"
              size="sm"
            >
              <Plus className="mr-2 size-4" />
              {isLoading ? 'Adding...' : 'Add to Fleet'}
            </Button>
          )}
        </OrgPermissionGuard>

        {variant === 'available' && !isAdded && provider.status === 'pending_approval' && (
          <Button
            variant="outline"
            disabled
            className="w-full border-yellow-500/30 bg-yellow-500/10 text-yellow-600"
            size="sm"
          >
            Pending Approval
          </Button>
        )}

        {variant === 'available' && isAdded && (
          <Button
            variant="outline"
            disabled
            className="w-full border-green-500/30 bg-green-500/10 text-green-600 dark:text-green-400"
            size="sm"
          >
            <Check className="mr-2 size-4" />
            Already Added
          </Button>
        )}

        {variant === 'selectable' && provider.status === 'public' && (
          <Button
            onClick={handleSelect}
            disabled={isLoading}
            className="w-full bg-brand text-brand-foreground hover:bg-brand/90"
            size="sm"
          >
            <Check className="mr-2 size-4" />
            {isLoading ? 'Selecting...' : 'Select Provider'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
