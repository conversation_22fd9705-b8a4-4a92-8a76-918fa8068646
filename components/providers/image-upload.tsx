'use client';

import { useState, useRef } from 'react';
import { Upload, X, Loader2, Image as ImageIcon, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import Image from 'next/image';
import {
  getFilePreviewUrl,
  cleanupPreviewUrl,
  validateClientFile,
  getAllowedTypesClient,
  getMaxFileSizeClient,
  formatFileSize,
} from '@/lib/image/image-upload-client';
interface ImageUploadProps {
  id?: string;
  onUpload: (url: string) => void;
  onFileSelect?: (file: File) => void;
  onRemove?: () => void;
  currentUrl?: string;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
}

export function ImageUpload({
  id,
  onUpload,
  onFileSelect,
  onRemove,
  currentUrl,
  className = '',
  disabled = false,
  placeholder = 'Upload image',
}: ImageUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const resetUploadState = () => {
    setError(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileSelect = async (file: File) => {

    // Use client-side validation utilities
    const validation = validateClientFile(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid file');
      return;
    }


    // Reset error state
    setError(null);

    // Create preview using utility function
    const preview = getFilePreviewUrl(file);
    setPreviewUrl(preview);


    // Store the file for later upload
    onFileSelect?.(file);
    onUpload(preview); // Use preview URL temporarily
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    if (!disabled) {
      event.preventDefault();
    }
  };

  const triggerFileSelect = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemove = () => {

    if (previewUrl && previewUrl.startsWith('blob:')) {
      cleanupPreviewUrl(previewUrl);
    }
    setPreviewUrl(null);

    onRemove?.();
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <input
        id={id}
        ref={fileInputRef}
        type="file"
        accept="image/png,image/jpeg,image/gif,image/webp"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled}
      />

      {previewUrl ? (
        <div className="group relative">
          <div className="relative h-32 w-full overflow-hidden rounded-lg border bg-muted">
            <Image
              src={previewUrl}
              alt="Uploaded image"
              className="size-full object-cover"
              onError={() => setPreviewUrl(null)}
              width={128}
              height={128}
            />

            {/* Error overlay */}
            {error && (
              <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 bg-destructive/90 backdrop-blur-sm">
                <AlertCircle className="size-6 text-destructive-foreground" />
                <p className="px-2 text-center text-sm text-destructive-foreground">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetUploadState}
                  className="bg-background/50"
                >
                  Dismiss
                </Button>
              </div>
            )}

            {/* Remove button */}
            {!disabled && !error && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                onClick={handleRemove}
              >
                <X className="size-4" />
              </Button>
            )}
          </div>
        </div>
      ) : (
        <div
          className={`rounded-lg border-2 border-dashed border-border p-6 text-center transition-colors hover:border-primary/50 ${
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={triggerFileSelect}
          role="button"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={(e) => {
            if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
              e.preventDefault();
              triggerFileSelect();
            }
          }}
        >
          <div className="flex flex-col items-center gap-2">
            {error ? (
              <div className="flex flex-col items-center gap-2">
                <AlertCircle className="size-8 text-destructive" />
                <span className="text-sm text-destructive">{error}</span>
              </div>
            ) : (
              <ImageIcon className="size-8 text-muted-foreground" />
            )}

            {!error && (
              <div className="text-sm">
                <span className="font-medium text-foreground">{placeholder}</span>
                <p className="mt-1 text-muted-foreground">Drag and drop or click to browse</p>
              </div>
            )}

            <p className="text-xs text-muted-foreground">
              {getAllowedTypesClient().join(', ').toUpperCase()} up to{' '}
              {formatFileSize(getMaxFileSizeClient())}
            </p>
          </div>
        </div>
      )}

      {!previewUrl && (
        <Button
          variant="outline"
          onClick={triggerFileSelect}
          disabled={disabled}
          className="w-full"
        >
          <Upload className="mr-2 size-4" />
          Choose File
        </Button>
      )}

      {/* Retry button for errors */}
      {error && !previewUrl && (
        <Button
          variant="outline"
          onClick={() => {
            resetUploadState();
            triggerFileSelect();
          }}
          className="w-full"
        >
          Try Again
        </Button>
      )}
    </div>
  );
}
