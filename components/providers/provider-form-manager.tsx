'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Edit, ChevronDown, Globe, Building2 } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageUpload } from '@/components/providers/image-upload';
import { ProviderCard } from '@/components/providers/provider-card';
import { cn } from '@/lib/utils';
import {
  AddProviderFormData,
  addProviderSchema,
  useAddProvider,
  useUpdateProvider,
  createProviderFormData,
} from '@/hooks/use-provider-registration';
import { z } from 'zod';
import {
  useProviders,
  useOrgProvider,
  useAssignProviderToOrg,
} from '@/hooks/use-provider-management';
import { useOrgContext } from '@/components/org/org-context-provider';
import { ProviderResponseType } from '@/types/provider';
import { queryClient } from '@/lib/query-client';
interface ProviderFormManagerProps {
  onSuccess?: (provider: any) => void;
  trigger?: React.ReactNode;
}

// Edit existing provider form
function EditProviderForm({
  provider,
  onSuccess,
}: {
  provider: ProviderResponseType;
  onSuccess?: () => void;
}) {
  const { currentOrgId } = useOrgContext();
  const { mutateAsync: updateProvider } = useUpdateProvider();
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);

  // Use the inferred type from the schema
  type EditProviderFormData = z.infer<typeof addProviderSchema> & { orgId?: never };
  const editProviderSchema = addProviderSchema.omit({ orgId: true });

  const form = useForm<z.infer<typeof editProviderSchema>>({
    resolver: zodResolver(editProviderSchema),
    defaultValues: {
      name: provider.provider || '',
      description: provider.providerDescription || '',
      website: provider.about || '',
      supportEmail: '',
      documentation: '',
      sdkLink: '',
      logo: null,
      logoDark: null,
    },
  });

  const {
    watch,
    setValue,
    formState: { isSubmitting },
  } = form;
  const logoValue = watch('logo');
  const logoDarkValue = watch('logoDark');

  const onSubmit = async (data: z.infer<typeof editProviderSchema>) => {
    try {
      // Create form data with orgId included and ensure null instead of undefined
      const formDataWithOrgId: AddProviderFormData = {
        ...data,
        orgId: currentOrgId || '',
        supportEmail: data.supportEmail ?? null,
        documentation: data.documentation ?? null,
        sdkLink: data.sdkLink ?? null,
      };
      const formData = createProviderFormData(formDataWithOrgId);

      await updateProvider({
        providerId: provider._id,
        formData,
      });
      queryClient.invalidateQueries({ queryKey: ['orgProvider', currentOrgId] });
      toast.success('Provider updated successfully');
      onSuccess?.();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to update provider');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
        <div className="space-y-4">
          <div className="flex items-center gap-4 rounded-lg border border-brand/20 bg-brand/5 p-4 dark:border-brand/30 dark:bg-brand/10">
            <div className="rounded-lg bg-brand/10 p-2 dark:bg-brand/20">
              <Building2 className="size-5 text-brand dark:text-brand" />
            </div>
            <div>
              <h4 className="font-semibold text-brand dark:text-brand">Editing Your Provider</h4>
              <p className="text-sm text-brand/80 dark:text-brand/70">
                You have full control to modify your provider information
              </p>
            </div>
          </div>
        </div>

        {/* Provider Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Provider Name *</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g. Quantum Corp"
                  {...field}
                  disabled={isSubmitting}
                  className="h-12"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Provider Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description *</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe what this provider offers..."
                  className="min-h-[100px] resize-none"
                  {...field}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Website URL */}
        <FormField
          control={form.control}
          name="website"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Website URL *</FormLabel>
              <FormControl>
                <Input
                  type="url"
                  placeholder="https://example.com"
                  {...field}
                  disabled={isSubmitting}
                  className="h-12"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Additional Fields Toggle */}
        <button
          type="button"
          onClick={() => setShowAdditionalFields(!showAdditionalFields)}
          className="group mb-4 flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:bg-muted/50 hover:text-foreground"
        >
          <Plus className="size-4 text-brand transition-transform group-hover:rotate-12" />
          <span>Additional Information (Optional)</span>
          <ChevronDown
            className={cn(
              'w-4 h-4 transition-transform duration-200',
              showAdditionalFields && 'rotate-180',
            )}
          />
        </button>

        {/* Additional Fields Section */}
        {showAdditionalFields && (
          <div className="space-y-4 animate-in fade-in slide-in-from-top-2">
            {/* Support Email */}
            <FormField
              control={form.control}
              name="supportEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Support Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                      value={field.value || ''}
                      disabled={isSubmitting}
                      className="h-12"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Documentation URL */}
            <FormField
              control={form.control}
              name="documentation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Documentation URL</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://docs.example.com"
                      {...field}
                      value={field.value || ''}
                      disabled={isSubmitting}
                      className="h-12"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* SDK Link */}
            <FormField
              control={form.control}
              name="sdkLink"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SDK Link</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://github.com/example/sdk"
                      {...field}
                      value={field.value || ''}
                      disabled={isSubmitting}
                      className="h-12"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Logo Upload Section */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label>Logo (Light Theme)</Label>
            <ImageUpload
              currentUrl={provider.logoUrl}
              onUpload={() => {}}
              onFileSelect={(file) => setValue('logo', file)}
              onRemove={() => setValue('logo', null)}
              placeholder="Upload light theme logo"
              disabled={isSubmitting}
            />
          </div>

          <div className="space-y-2">
            <Label>Logo (Dark Theme) - Optional</Label>
            <ImageUpload
              currentUrl={provider.logoUrlDark}
              onUpload={() => {}}
              onFileSelect={(file) => setValue('logoDark', file)}
              onRemove={() => setValue('logoDark', null)}
              placeholder="Upload dark theme logo"
              disabled={isSubmitting}
            />
          </div>
        </div>

        <div className="flex gap-3 border-t pt-6">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-brand text-brand-foreground hover:bg-brand/90"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 size-4 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Edit className="mr-2 size-4" />
                Update Provider
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Select or create provider form
function ProviderSelectionForm({ onSuccess }: { onSuccess?: () => void }) {
  const { currentOrgId } = useOrgContext();
  const { data: providers, isLoading } = useProviders();
  const { mutate: assignProvider } = useAssignProviderToOrg();
  const { mutateAsync: addProvider } = useAddProvider();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);

  const form = useForm<z.infer<typeof addProviderSchema>>({
    resolver: zodResolver(addProviderSchema),
    defaultValues: {
      orgId: currentOrgId || '',
      name: '',
      description: '',
      website: '',
      supportEmail: '',
      documentation: '',
      sdkLink: '',
      logo: null,
      logoDark: null,
    },
  });

  const { watch, setValue } = form;
  const logoValue = watch('logo');
  const logoDarkValue = watch('logoDark');

  const handleSelectProvider = (provider: ProviderResponseType) => {
    if (!currentOrgId) return;

    assignProvider(
      {
        orgId: currentOrgId,
        providerId: provider._id,
        providerName: provider.provider,
      },
      {
        onSuccess: () => {
          onSuccess?.();
        },
      },
    );
  };

  const onSubmit = async (data: z.infer<typeof addProviderSchema>) => {
    setIsSubmitting(true);
    try {
      // Ensure null instead of undefined for optional fields
      const cleanedData: AddProviderFormData = {
        ...data,
        supportEmail: data.supportEmail ?? null,
        documentation: data.documentation ?? null,
        sdkLink: data.sdkLink ?? null,
      };
      const formData = createProviderFormData(cleanedData);
      const response = await addProvider({ formData });
      toast.success('Provider created successfully');
      onSuccess?.();
    } catch {
      toast.error('Failed to create provider');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Tabs defaultValue="create" className="space-y-6">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="create" className="flex items-center gap-2">
          <Plus className="size-4" />
          Create Custom
        </TabsTrigger>
        <TabsTrigger value="select" className="flex items-center gap-2">
          <Globe className="size-4" />
          Select Existing
        </TabsTrigger>
      </TabsList>

      <TabsContent value="select" className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-center gap-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
            <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/30">
              <Globe className="size-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-300">
                Associate with Existing Provider
              </h4>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                Connect with an established quantum computing provider
              </p>
            </div>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-48 animate-pulse rounded-lg bg-muted" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {providers
                ?.filter((p) => p.status === 'public')
                .map((provider) => (
                  <ProviderCard
                    key={provider._id}
                    provider={provider}
                    variant="selectable"
                    onSelect={() => handleSelectProvider(provider)}
                  />
                ))}
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value="create" className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-center gap-4 rounded-lg border border-purple-200 bg-purple-50 p-4 dark:border-purple-800 dark:bg-purple-950/20">
            <div className="rounded-lg bg-purple-100 p-2 dark:bg-purple-900/30">
              <Plus className="size-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h4 className="font-semibold text-purple-800 dark:text-purple-300">
                Create Custom Provider
              </h4>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                Register your own quantum computing infrastructure
              </p>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              {/* Provider Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Provider Name *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g. Quantum Corp"
                        {...field}
                        disabled={isSubmitting}
                        className="h-12"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Provider Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what this provider offers..."
                        className="min-h-[100px] resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Website URL */}
              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website URL *</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        {...field}
                        disabled={isSubmitting}
                        className="h-12"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Additional Fields Toggle */}
              <button
                type="button"
                onClick={() => setShowAdditionalFields(!showAdditionalFields)}
                className="group mb-4 flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:bg-muted/50 hover:text-foreground"
              >
                <Plus className="size-4 text-brand transition-transform group-hover:rotate-12" />
                <span>Additional Information (Optional)</span>
                <ChevronDown
                  className={cn(
                    'w-4 h-4 transition-transform duration-200',
                    showAdditionalFields && 'rotate-180',
                  )}
                />
              </button>

              {/* Additional Fields Section */}
              {showAdditionalFields && (
                <div className="space-y-5 animate-in fade-in slide-in-from-top-2">
                  {/* Support Email */}
                  <FormField
                    control={form.control}
                    name="supportEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Support Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            value={field.value || ''}
                            disabled={isSubmitting}
                            className="h-12"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Documentation URL */}
                  <FormField
                    control={form.control}
                    name="documentation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Documentation URL</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://docs.example.com"
                            {...field}
                            value={field.value || ''}
                            disabled={isSubmitting}
                            className="h-12"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* SDK Link */}
                  <FormField
                    control={form.control}
                    name="sdkLink"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SDK Link</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://github.com/example/sdk"
                            {...field}
                            value={field.value || ''}
                            disabled={isSubmitting}
                            className="h-12"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Logo Upload Section */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Logo (Light Theme)</Label>
                  <ImageUpload
                    onUpload={() => {}}
                    onFileSelect={(file) => setValue('logo', file)}
                    onRemove={() => setValue('logo', null)}
                    placeholder="Upload light theme logo"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Logo (Dark Theme) - Optional</Label>
                  <ImageUpload
                    onUpload={() => {}}
                    onFileSelect={(file) => setValue('logoDark', file)}
                    onRemove={() => setValue('logoDark', null)}
                    placeholder="Upload dark theme logo"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div className="flex gap-3 border-t pt-6">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-brand text-brand-foreground hover:bg-brand/90"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 size-4" />
                      Create Provider
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </TabsContent>
    </Tabs>
  );
}

export function ProviderFormManager({ onSuccess, trigger }: ProviderFormManagerProps) {
  const [open, setOpen] = useState(false);
  const { currentOrgId } = useOrgContext();
  const { data: orgProvider } = useOrgProvider(currentOrgId || '');

  const handleClose = () => {
    setOpen(false);
  };

  const handleSuccess = (data?: any) => {
    onSuccess?.(data);
    handleClose();
  };
  //if orgProvider could be an empty {} or null
  const hasProvider = orgProvider && Object.keys(orgProvider).length > 0;
  // Determine the mode based on provider status
  const isOwner = true; // All assigned providers can be managed

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="bg-brand text-brand-foreground hover:bg-brand/90">
            {hasProvider ? (
              <>
                <Edit className="mr-2 size-4" />
                {isOwner ? 'Edit Provider' : 'Change Provider'}
              </>
            ) : (
              <>
                <Plus className="mr-2 size-4" />
                Add Provider
              </>
            )}
          </Button>
        )}
      </DialogTrigger>

      <DialogContent
        className="max-h-[90vh] max-w-4xl overflow-y-auto"
        onFocusOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <div className="flex items-center gap-4">
            <div className="rounded-2xl bg-gradient-to-br from-brand to-brand/80 p-3">
              {hasProvider && isOwner ? (
                <Edit className="size-6 text-white" />
              ) : (
                <Plus className="size-6 text-white" />
              )}
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">
                {hasProvider && isOwner
                  ? 'Edit Your Provider'
                  : (hasProvider
                    ? 'Change Provider Association'
                    : 'Setup Your Provider')}
              </DialogTitle>
              <DialogDescription className="mt-1">
                {hasProvider && isOwner
                  ? 'Modify your provider information and settings'
                  : (hasProvider
                    ? 'Associate with a different provider or create your own'
                    : 'Get started by selecting an existing provider or creating your own')}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="mt-6">
          {hasProvider && isOwner && orgProvider ? (
            <EditProviderForm provider={orgProvider} onSuccess={handleSuccess} />
          ) : (
            <ProviderSelectionForm onSuccess={handleSuccess} />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
