'use client';

import * as React from 'react';
import { useState } from 'react';
import { ChevronsUpDown, Loader2 } from 'lucide-react';
import { CreateOrgModal } from './create-org-modal';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';

import { usePermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';

export function TeamSwitcher() {
  const { isMobile } = useSidebar();
  const { loading: permissionsLoading, refreshPermissions } = usePermissions();
  const { currentOrg, organizations, switchOrganization, isLoading: orgLoading } = useOrgContext();
  const [createOrgOpen, setCreateOrgOpen] = useState(false);

  const loading = permissionsLoading || orgLoading;
  // Consider component still initializing until we have an active organization
  const activeOrg = currentOrg || organizations[0];
  const initializing = loading || !activeOrg;

  // Listen for organizations-updated event to refresh data
  React.useEffect(() => {
    const handleOrganizationsUpdated = () => {
      // Refresh permissions to get the latest organizations data
      refreshPermissions();
    };

    globalThis.addEventListener('organizations-updated', handleOrganizationsUpdated);
    return () => {
      globalThis.removeEventListener('organizations-updated', handleOrganizationsUpdated);
    };
  }, [refreshPermissions]);

  // Handle organization switching
  const handleOrgSwitch = async (orgId: string) => {
    if (orgId === currentOrg?.orgId) return; // No change needed

    try {
      await switchOrganization(orgId);
    } catch {
      // Do nothing
    }
  };

  if (initializing) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" className="animate-pulse">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-gray-200" />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <div className="h-4 w-24 rounded bg-gray-200" />
              <div className="mt-1 h-3 w-16 rounded bg-gray-200" />
            </div>
            <ChevronsUpDown className="ml-auto size-4" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }
  // If the user truly has zero organizations (rare), render explicit empty state
  if (organizations.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <span className="text-sm font-semibold">?</span>
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">No Organization</span>
              <span className="truncate text-xs">Not assigned</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-[#8a2be2] text-sidebar-primary-foreground">
                  <span className="text-sm font-semibold">
                    {activeOrg?.orgName?.charAt(0).toUpperCase() || 'O'}
                  </span>
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {activeOrg?.orgName || 'Select Organization'}
                  </span>
                  <span className="truncate text-xs">
                    {activeOrg
                      ? `${activeOrg.role} • ${organizations.length} org${organizations.length === 1 ? '' : 's'}`
                      : 'No access'}
                  </span>
                </div>
                {orgLoading ? (
                  <Loader2 className="ml-auto size-4 animate-spin" />
                ) : (
                  <ChevronsUpDown className="ml-auto size-4" />
                )}
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              align="start"
              side={isMobile ? 'bottom' : 'right'}
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                Organizations ({organizations.length})
              </DropdownMenuLabel>
              {organizations.map((org) => (
                <DropdownMenuItem
                  key={org.orgId}
                  onClick={() => handleOrgSwitch(org.orgId)}
                  className="gap-2 p-2"
                  disabled={orgLoading}
                >
                  <div className="flex size-6 items-center justify-center rounded-sm bg-[#8a2be2] text-xs font-medium text-sidebar-primary-foreground">
                    {org.orgName.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex flex-1 flex-col">
                    <span className="font-medium">{org.orgName}</span>
                    <span className="text-xs text-muted-foreground">
                      {org.role} • Updated {new Date().toLocaleDateString()}
                    </span>
                  </div>
                  {org.orgId === activeOrg?.orgId && <DropdownMenuShortcut>✓</DropdownMenuShortcut>}
                </DropdownMenuItem>
              ))}
              {/* Add organization moved to admin panel */}
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>

      {/* Create Organization Modal */}
      <CreateOrgModal
        open={createOrgOpen}
        onOpenChange={setCreateOrgOpen}
        onSuccess={() => {
          // The mutation will automatically invalidate and refetch the organizations list
        }}
      />
    </>
  );
}
