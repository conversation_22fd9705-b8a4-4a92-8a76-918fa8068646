'use client';

import * as React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { LucideIcon } from 'lucide-react';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';

export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon;
    target?: string;
    rel?: string;
  }[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const pathname = usePathname();

  return (
    <SidebarGroup data-testid="nav-secondary" {...props}>
      <SidebarGroupContent>
        <Separator className="my-2" />
        <SidebarMenu>
          {items.map((item) => {
            // For external links, don't apply active state
            const isExternal = item.target === '_blank' || item.url.startsWith('http');
            const isActive =
              !isExternal &&
              (pathname === item.url || (item.url !== '/' && pathname.startsWith(item.url)));

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  className={isActive ? 'bg-accent text-accent-foreground' : ''}
                >
                  {isExternal ? (
                    <a href={item.url} target={item.target ?? '_self'} rel={item.rel}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  ) : (
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
