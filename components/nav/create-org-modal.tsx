'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Building2, Shield, Users, Eye, Crown, Loader2 } from 'lucide-react';
import { useCreateOrganization } from '@/hooks/use-create-organization';
import { useIsQbraidAdmin } from '@/hooks/use-qbraid-admin-role';

interface CreateOrgModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

interface CreateOrgFormData {
  name: string;
  description: string;
  marketing: boolean;
  owner: string;
  image?: File;
  darkImage?: File;
  qbraidGitHubAssistance: boolean;
  orgGitHubUrl: string;
  orgGitHubToken: string;
  qBookDomain: 'quera' | 'qbraid';
}

export function CreateOrgModal({ open, onOpenChange, onSuccess }: CreateOrgModalProps) {
  const { isQbraidAdmin } = useIsQbraidAdmin();
  const createOrganizationMutation = useCreateOrganization();
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const isLoading = createOrganizationMutation.isPending;

  const [formData, setFormData] = useState<CreateOrgFormData>({
    name: '',
    description: '',
    marketing: false,
    owner: '',
    qbraidGitHubAssistance: false,
    orgGitHubUrl: '',
    orgGitHubToken: '',
    qBookDomain: 'qbraid',
  });

  // Check if user has permission to create organizations
  const canCreateOrg = isQbraidAdmin;

  const handleInputChange = (field: keyof CreateOrgFormData, value: string | boolean | File) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!isQbraidAdmin) {
      setErrorMessage('Only qbraid administrators can create organizations.');
      return;
    }

    if (!canCreateOrg) {
      setErrorMessage('You do not have permission to create organizations.');
      return;
    }

    if (!formData.name.trim() || !formData.owner.trim()) {
      setErrorMessage('Organization name and owner email are required.');
      return;
    }

    setErrorMessage(null);

    try {
      await createOrganizationMutation.mutateAsync(formData);

      // Reset form
      setFormData({
        name: '',
        description: '',
        marketing: false,
        owner: '',
        qbraidGitHubAssistance: false,
        orgGitHubUrl: '',
        orgGitHubToken: '',
        qBookDomain: 'qbraid',
      });

      // Force a refresh of permissions data to ensure organizations list is updated
      // This ensures the team switcher gets the latest organizations data
      setTimeout(() => {
        // Dispatch a custom event to notify components that organizations have changed
        globalThis.dispatchEvent(
          new CustomEvent('organizations-updated', {
            detail: { timestamp: Date.now(), action: 'organization_created' },
          }),
        );
      }, 100);

      onSuccess?.();
      onOpenChange(false);
      setConfirmOpen(false);
    } catch (error: any) {
      console.error('❌ [CREATE-ORG] Failed to create organization:', error);
      const errorMsg = error?.message || 'Failed to create organization';
      setErrorMessage(errorMsg);
    }
  };

  const handleFileChange = (field: 'image' | 'darkImage', file: File | null) => {
    if (file) {
      handleInputChange(field, file);
    }
  };

  if (!canCreateOrg) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold">
              <Building2 className="size-5" />
              Create Organization
            </DialogTitle>
            <DialogDescription>
              You need administrator permissions to create organizations.
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 text-center">
            <div className="rounded-lg border border-border bg-muted/50 p-4">
              <Shield className="mx-auto mb-3 size-12 text-muted-foreground" />
              <h3 className="mb-2 font-semibold">Permission Required</h3>
              <p className="text-sm text-muted-foreground">
                Only qbraid administrators can create new organizations.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          className="modal-scrollbar max-h-[90vh] max-w-3xl overflow-y-auto"
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader className="border-b border-border/50 pb-6">
            <div className="mb-2 flex items-center gap-3">
              <div className="rounded-xl bg-[hsl(var(--brand))] p-2">
                <Building2 className="size-6 text-[hsl(var(--brand-foreground))]" />
              </div>
              <DialogTitle className="text-2xl font-bold text-foreground">
                Create New Organization
              </DialogTitle>
            </div>
            <DialogDescription className="text-base text-muted-foreground">
              Set up a new organization with customized settings and team management.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-6">
            {/* Basic Information */}
            <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
              <div className="mb-6 flex items-center gap-3">
                <div className="rounded-lg bg-[hsl(var(--brand))] p-2">
                  <Building2 className="size-5 text-[hsl(var(--brand-foreground))]" />
                </div>
                <h3 className="text-lg font-semibold text-foreground">Basic Information</h3>
              </div>

              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-medium text-foreground">
                    Organization Name *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter organization name"
                    disabled={isLoading}
                    className="border-input bg-background"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="owner" className="text-sm font-medium text-foreground">
                    Owner Email *
                  </Label>
                  <Input
                    id="owner"
                    type="email"
                    value={formData.owner}
                    onChange={(e) => handleInputChange('owner', e.target.value)}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                    className="border-input bg-background"
                  />
                </div>
              </div>

              <div className="mt-6 space-y-3">
                <Label htmlFor="description" className="text-sm font-medium text-foreground">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter organization description"
                  rows={3}
                  disabled={isLoading}
                  className="resize-none border-input bg-background"
                />
              </div>
            </div>

            {/* GitHub Integration */}
            <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
              <div className="mb-6 flex items-center gap-3">
                <div className="rounded-lg bg-muted p-2">
                  <svg className="size-5 text-foreground" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground">GitHub Integration</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-center rounded-lg border border-border bg-muted/50 p-4">
                  <Checkbox
                    id="qbraidGitHubAssistance"
                    checked={formData.qbraidGitHubAssistance}
                    onCheckedChange={(checked) =>
                      handleInputChange('qbraidGitHubAssistance', checked as boolean)
                    }
                    disabled={isLoading}
                    className="data-[state=checked]:border-[hsl(var(--brand))] data-[state=checked]:bg-[hsl(var(--brand))]"
                  />
                  <Label
                    htmlFor="qbraidGitHubAssistance"
                    className="ml-3 text-sm font-medium text-foreground"
                  >
                    Enable qbraid GitHub setup assistance
                  </Label>
                </div>

                {formData.qbraidGitHubAssistance && (
                  <div className="ml-8 space-y-4 rounded-lg border-l-4 border-[hsl(var(--brand))]/30 bg-muted/30 p-4">
                    <div className="space-y-3">
                      <Label htmlFor="orgGitHubUrl" className="text-sm font-medium text-foreground">
                        GitHub Organization URL
                      </Label>
                      <Input
                        id="orgGitHubUrl"
                        value={formData.orgGitHubUrl}
                        onChange={(e) => handleInputChange('orgGitHubUrl', e.target.value)}
                        placeholder="https://github.com/your-org"
                        disabled={isLoading}
                        className="border-input bg-background"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label
                        htmlFor="orgGitHubToken"
                        className="text-sm font-medium text-foreground"
                      >
                        GitHub Token
                      </Label>
                      <Input
                        id="orgGitHubToken"
                        type="password"
                        value={formData.orgGitHubToken}
                        onChange={(e) => handleInputChange('orgGitHubToken', e.target.value)}
                        placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                        disabled={isLoading}
                        className="border-input bg-background"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* qBook Domain */}
            <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
              <div className="mb-6 flex items-center gap-3">
                <div className="rounded-lg bg-muted p-2">
                  <svg
                    className="size-5 text-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground">qBook Domain</h3>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div
                  className={`cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ${
                    formData.qBookDomain === 'qbraid'
                      ? 'border-[hsl(var(--brand))] bg-[hsl(var(--brand))]/10 shadow-sm'
                      : 'border-border bg-background hover:border-[hsl(var(--brand))]/50'
                  }`}
                  onClick={() => !isLoading && handleInputChange('qBookDomain', 'qbraid')}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`flex size-5 items-center justify-center rounded-full border-2 transition-colors ${
                        formData.qBookDomain === 'qbraid'
                          ? 'border-[hsl(var(--brand))] bg-[hsl(var(--brand))]'
                          : 'border-border'
                      }`}
                    >
                      {formData.qBookDomain === 'qbraid' && (
                        <div className="size-2 rounded-full bg-background" />
                      )}
                    </div>
                    <div>
                      <Label
                        htmlFor="qbraid"
                        className="cursor-pointer text-sm font-medium text-foreground"
                      >
                        qbraid
                      </Label>
                      <p className="mt-1 text-xs text-muted-foreground">Default domain</p>
                    </div>
                  </div>
                </div>

                <div
                  className={`cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ${
                    formData.qBookDomain === 'quera'
                      ? 'border-[hsl(var(--brand))] bg-[hsl(var(--brand))]/10 shadow-sm'
                      : 'border-border bg-background hover:border-[hsl(var(--brand))]/50'
                  }`}
                  onClick={() => !isLoading && handleInputChange('qBookDomain', 'quera')}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`flex size-5 items-center justify-center rounded-full border-2 transition-colors ${
                        formData.qBookDomain === 'quera'
                          ? 'border-[hsl(var(--brand))] bg-[hsl(var(--brand))]'
                          : 'border-border'
                      }`}
                    >
                      {formData.qBookDomain === 'quera' && (
                        <div className="size-2 rounded-full bg-background" />
                      )}
                    </div>
                    <div>
                      <Label
                        htmlFor="quera"
                        className="cursor-pointer text-sm font-medium text-foreground"
                      >
                        quera
                      </Label>
                      <p className="mt-1 text-xs text-muted-foreground">Alternative domain</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Marketing Consent */}
            <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
              <div className="mb-4 flex items-center gap-3">
                <div className="rounded-lg bg-muted p-2">
                  <svg
                    className="size-5 text-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground">Marketing Preferences</h3>
              </div>

              <div className="flex items-center rounded-lg border border-border bg-muted/50 p-4">
                <Checkbox
                  id="marketing"
                  checked={formData.marketing}
                  onCheckedChange={(checked) => handleInputChange('marketing', checked as boolean)}
                  disabled={isLoading}
                  className="data-[state=checked]:border-[hsl(var(--brand))] data-[state=checked]:bg-[hsl(var(--brand))]"
                />
                <div className="ml-3">
                  <Label
                    htmlFor="marketing"
                    className="cursor-pointer text-sm font-medium text-foreground"
                  >
                    Marketing consent (optional)
                  </Label>
                  <p className="mt-1 text-xs text-muted-foreground">
                    Receive updates about new features, events, and qbraid news
                  </p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {errorMessage && (
              <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-500/20 dark:bg-red-500/10">
                <p className="text-sm text-red-600 dark:text-red-400">{errorMessage}</p>
              </div>
            )}

            {/* Organization Preview */}
            <div className="rounded-lg border border-border bg-muted/50 p-4">
              <h3 className="mb-3 text-sm font-semibold">Organization Preview</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Name:</span>
                  <span className="font-medium">{formData.name || 'Not specified'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Owner:</span>
                  <span className="font-medium">{formData.owner || 'Not specified'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Email:</span>
                  <span className="font-medium">
                    {formData.name
                      ? `${formData.name.toLowerCase().replaceAll(/\s+/g, '')}@qbraid.com`
                      : 'Not specified'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">qBook Domain:</span>
                  <Badge variant="outline">{formData.qBookDomain}</Badge>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col gap-2 sm:flex-row">
            <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              onClick={() => setConfirmOpen(true)}
              disabled={isLoading || !formData.name || !formData.owner}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Organization'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold">
              Confirm Organization Creation
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-3">
                <p>Are you sure you want to create the organization "{formData.name}"?</p>
                <div>
                  <p className="mb-2">This will:</p>
                  <ul className="list-inside list-disc space-y-1 text-sm">
                    <li>Create the organization with owner {formData.owner}</li>
                    <li>Set up billing account with Stripe</li>
                    <li>
                      Generate organization email: {formData.name.toLowerCase().replaceAll(/\s+/g, '')}
                      @qbraid.com
                    </li>
                    {formData.qbraidGitHubAssistance && <li>Configure GitHub integration</li>}
                  </ul>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Organization'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
