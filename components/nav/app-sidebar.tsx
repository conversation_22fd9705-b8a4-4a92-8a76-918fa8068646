'use client';

import * as React from 'react';
import {
  CameraIcon,
  DatabaseIcon,
  FileCodeIcon,
  FileTextIcon,
  LayoutDashboardIcon,
  UsersIcon,
  BookIcon,
  CloudIcon,
  Settings,
  UserCog,
} from 'lucide-react';

import { NavMain } from '@/components/nav/nav-main';
import { NavSecondary } from '@/components/nav/nav-secondary';
import { NavUser } from '@/components/nav/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { useUserProfile } from '@/hooks/use-api';
import { TeamSwitcher } from '@/components/nav/team-switcher';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';
import { useIsQbraidAdmin } from '@/hooks/use-qbraid-admin-role';

const data = {
  user: {
    name: 'User',
    email: '<EMAIL>',
    avatar: '/qbraid_logo.png',
  },
  navMain: [
    {
      title: 'Dashboard',
      url: '/',
      icon: LayoutDashboardIcon,
    },
    {
      title: 'Devices',
      url: '/devices',
      icon: DatabaseIcon,
    },
    {
      title: 'Providers',
      url: '/providers',
      icon: CloudIcon,
    },
    {
      title: 'Device Management',
      url: '/device-management',
      icon: Settings,
    },
    {
      title: 'Team',
      url: '/team',
      icon: UsersIcon,
    },
  ],
  navClouds: [
    {
      title: 'Capture',
      icon: CameraIcon,
      isActive: true,
      url: '#',
      items: [
        {
          title: 'Active Proposals',
          url: '#',
        },
        {
          title: 'Archived',
          url: '#',
        },
      ],
    },
    {
      title: 'Proposal',
      icon: FileTextIcon,
      url: '#',
      items: [
        {
          title: 'Active Proposals',
          url: '#',
        },
        {
          title: 'Archived',
          url: '#',
        },
      ],
    },
    {
      title: 'Prompts',
      icon: FileCodeIcon,
      url: '#',
      items: [
        {
          title: 'Active Proposals',
          url: '#',
        },
        {
          title: 'Archived',
          url: '#',
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: 'Admin',
      url: '/admin',
      icon: UserCog,
    },
    {
      title: 'Documentation',
      url: 'https://docs.qbraid.com/',
      icon: BookIcon,
      target: '_blank',
      rel: 'noopener noreferrer',
    },
  ],
};

function PermissionAwareNavigation() {
  const { hasPermission, hasRole } = useOrgPermissions();
  const { isQbraidAdmin } = useIsQbraidAdmin();
    // Filter navigation items based on permissions
  const filteredNavMain = data.navMain.filter((item) => {
    switch (item.url) {
      case '/devices': {
        return hasPermission(Permission.ViewDevices);
      }
      case '/providers': {
        return hasPermission(Permission.ViewProviders);
      } // Same permission as devices
      case '/team': {
        return hasPermission(Permission.ViewTeam);
      }
      case '/device-management': {
        return hasPermission(Permission.ManageDevices);
      }

      case '/':
      default: {
        return true;
      } // Dashboard is accessible to all authenticated users
    }
  });
  const filteredNavSecondary = data.navSecondary.filter((item) => {
    switch (item.url) {
      case '/admin': {
        return isQbraidAdmin;
      }
      default: {
        return true;
      }
    }
  });

  // Sidebar should no longer show Profile tab; use navUser dropdown instead

  return {
    filteredNavMain,
    enhancedNavSecondary: filteredNavSecondary,
  };
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { filteredNavMain, enhancedNavSecondary } = PermissionAwareNavigation();
  const { data: profile, isLoading: profileLoading } = useUserProfile();

  // Build user data from profile
  const userData = React.useMemo(() => {
    if (!profile) {
      return data.user; // fallback to default
    }

    const firstName = profile.firstName || '';
    const lastName = profile.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();

    // If no name, use email username
    const displayName = fullName || (profile.email ? profile.email.split('@')[0] : 'User');

    return {
      name: displayName,
      email: profile.email || '<EMAIL>',
      avatar: '/qbraid_logo.png', // You can add avatar URL from profile if available
    };
  }, [profile]);

  return (
    <Sidebar  variant="inset" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={filteredNavMain} />
        <NavSecondary items={enhancedNavSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  );
}
