'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SidebarNavItem {
  id: string;
  label: string;
  description?: string;
  icon: LucideIcon;
  disabled?: boolean;
  badge?: string | number;
}

export interface SidebarNavProps {
  items: SidebarNavItem[];
  activeItemId?: string;
  onItemSelect?: (itemId: string) => void;
  className?: string;
  variant?: 'default' | 'pills';
}

export function SidebarNav({
  items,
  activeItemId,
  onItemSelect,
  className,
  variant = 'default',
}: SidebarNavProps) {
  if (variant === 'pills') {
    return (
      <nav className={cn('space-y-1', className)}>
        {items.map((item, index) => {
          const Icon = item.icon;
          const isActive = activeItemId === item.id;
          
          return (
            <motion.button
              key={item.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              onClick={() => !item.disabled && onItemSelect?.(item.id)}
              disabled={item.disabled}
              className={cn(
                'group relative w-full rounded-lg px-4 py-3 text-left transition-all duration-200',
                isActive
                  ? 'bg-brand text-brand-foreground shadow-lg'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground',
                item.disabled && 'cursor-not-allowed opacity-50'
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Icon
                    className={cn(
                      'size-5 transition-colors duration-200',
                      isActive ? 'text-brand-foreground' : 'text-muted-foreground group-hover:text-foreground'
                    )}
                  />
                  <div>
                    <div className="font-medium">{item.label}</div>
                    {item.description && (
                      <div className="text-xs text-muted-foreground group-hover:text-foreground/80">
                        {item.description}
                      </div>
                    )}
                  </div>
                </div>
                {item.badge && (
                  <span
                    className={cn(
                      'rounded-full px-2 py-1 text-xs font-medium',
                      isActive
                        ? 'bg-brand-foreground/20 text-brand-foreground'
                        : 'bg-muted text-muted-foreground'
                    )}
                  >
                    {item.badge}
                  </span>
                )}
              </div>
            </motion.button>
          );
        })}
      </nav>
    );
  }

  return (
    <nav className={cn('space-y-1', className)}>
      {items.map((item, index) => {
        const Icon = item.icon;
        const isActive = activeItemId === item.id;
        
        return (
          <motion.button
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            onClick={() => !item.disabled && onItemSelect?.(item.id)}
            disabled={item.disabled}
            className={cn(
              'group w-full rounded-lg px-4 py-3 text-left transition-all duration-200',
              isActive
                ? 'border-l-4 border-brand bg-gradient-to-r from-brand/20 to-brand/30 text-brand'
                : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground',
              item.disabled && 'cursor-not-allowed opacity-50'
            )}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Icon
                  className={cn(
                    'size-5 transition-colors duration-200',
                    isActive ? 'text-brand' : 'text-muted-foreground group-hover:text-foreground'
                  )}
                />
                <div>
                  <div className="font-medium">{item.label}</div>
                  {item.description && (
                    <div className="text-xs text-muted-foreground group-hover:text-foreground">
                      {item.description}
                    </div>
                  )}
                </div>
              </div>
              {item.badge && (
                <span
                  className={cn(
                    'rounded-full px-2 py-1 text-xs font-medium',
                    isActive
                      ? 'bg-brand/20 text-brand'
                      : 'bg-muted text-muted-foreground'
                  )}
                >
                  {item.badge}
                </span>
              )}
            </div>
          </motion.button>
        );
      })}
    </nav>
  );
}