'use client';

import { useEffect, useState } from 'react';

interface ErrorTrackerProps {
  children: React.ReactNode;
}

export function ErrorTracker({ children }: ErrorTrackerProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Track console errors
    const handleError = (event: ErrorEvent) => {
      console.error('Error caught:', event.error);
      setHasError(true);
      
      // Reset after 5 seconds
      setTimeout(() => setHasError(false), 5000);
    };

    // Track unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled rejection:', event.reason);
      setHasError(true);
      
      // Reset after 5 seconds
      setTimeout(() => setHasError(false), 5000);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <div className={hasError ? 'animate-bounce-slow' : ''}>
      {children}
    </div>
  );
}