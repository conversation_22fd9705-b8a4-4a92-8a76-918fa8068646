'use client';

import * as React from 'react';

import { useState } from 'react';
import Image from 'next/image';

import { LogIn, Lock, Mail } from 'lucide-react';

const SignIn2 = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSignIn = () => {
    if (!email || !password) {
      setError('Please enter both email and password.');
      return;
    }
    if (!validateEmail(email)) {
      setError('Please enter a valid email address.');
      return;
    }
    setError('');
    alert('Sign in successful! (Demo)');
  };

  return (
    <div className="z-1 flex min-h-screen w-full items-center justify-center rounded-xl  bg-white">
      <div className="shadow-opacity-10 flex w-full max-w-sm flex-col  items-center rounded-3xl border border-blue-100 bg-gradient-to-b from-sky-50/50 to-white p-8 text-black shadow-xl">
        <div className="shadow-opacity-5 mb-6 flex size-14 items-center justify-center rounded-2xl bg-white shadow-lg">
          <LogIn className="size-7 text-black" />
        </div>
        <h2 className="mb-2 text-center text-2xl font-semibold">Sign in with email</h2>
        <p className="mb-6 text-center text-sm text-gray-500">
          Make a new doc to bring your words, data, and teams together. For free
        </p>
        <div className="mb-2 flex w-full flex-col gap-3">
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
              <Mail className="size-4" />
            </span>
            <input
              placeholder="Email"
              type="email"
              value={email}
              className="w-full rounded-xl border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-200"
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
              <Lock className="size-4" />
            </span>
            <input
              placeholder="Password"
              type="password"
              value={password}
              className="w-full rounded-xl border border-gray-200 bg-gray-50 px-10 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-200"
              onChange={(e) => setPassword(e.target.value)}
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer select-none text-xs text-gray-400"></span>
          </div>
          <div className="flex w-full justify-end">
            {error && <div className="text-left text-sm text-red-500">{error}</div>}
            <button className="text-xs  font-medium hover:underline">Forgot password?</button>
          </div>
        </div>
        <button
          onClick={handleSignIn}
          className="mb-4 mt-2 w-full cursor-pointer rounded-xl bg-gradient-to-b from-gray-700 to-gray-900 py-2 font-medium text-white shadow transition hover:brightness-105"
        >
          Get Started
        </button>
        <div className="my-2 flex w-full items-center">
          <div className="grow border-t border-dashed border-gray-200"></div>
          <span className="mx-2 text-xs text-gray-400">Or sign in with</span>
          <div className="grow border-t border-dashed border-gray-200"></div>
        </div>
        <div className="mt-2 flex w-full justify-center gap-3">
          <button className="flex size-12 grow items-center justify-center rounded-xl border bg-white transition hover:bg-gray-100">
            <Image
              src="https://www.svgrepo.com/show/475656/google-color.svg"
              alt="Google"
              width={24}
              height={24}
              className="size-6"
            />
          </button>
        </div>
      </div>
    </div>
  );
};

export { SignIn2 };
