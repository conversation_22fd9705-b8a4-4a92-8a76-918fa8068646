'use client';

import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface LoadingStateProps {
  message?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  icon?: React.ReactNode;
}

const sizeClasses = {
  sm: {
    icon: 'size-8',
    title: 'text-lg',
    description: 'text-sm',
  },
  md: {
    icon: 'size-12',
    title: 'text-xl',
    description: 'text-base',
  },
  lg: {
    icon: 'size-16',
    title: 'text-2xl',
    description: 'text-lg',
  },
};

export function LoadingState({
  message = 'Loading...',
  description,
  size = 'md',
  className,
  icon,
}: LoadingStateProps) {
  const sizes = sizeClasses[size];

  return (
    <div className={cn('flex min-h-screen items-center justify-center bg-background', className)}>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="p-8 text-center"
      >
        {icon || (
          <Loader2 className={cn('mx-auto mb-4 animate-spin text-brand', sizes.icon)} />
        )}
        <motion.h2
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className={cn('mb-2 font-semibold text-foreground', sizes.title)}
        >
          {message}
        </motion.h2>
        {description && (
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="text-muted-foreground"
          >
            {description}
          </motion.p>
        )}
      </motion.div>
    </div>
  );
}

// Component for loading cards or sections
export function LoadingCard({
  lines = 3,
  className,
}: {
  lines?: number;
  className?: string;
}) {
  return (
    <div className={cn('animate-pulse space-y-4', className)}>
      <div className="h-4 rounded bg-muted" />
      {Array.from({ length: lines - 1 }).map((_, i) => (
        <div
          key={i}
          className={cn('h-4 rounded bg-muted', i === lines - 2 ? 'w-3/4' : '')}
        />
      ))}
    </div>
  );
}

// Component for loading skeleton grids
export function LoadingSkeletonGrid({
  items = 4,
  className,
}: {
  items?: number;
  className?: string;
}) {
  return (
    <div className={cn('grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <LoadingCard key={i} lines={3} />
      ))}
    </div>
  );
}