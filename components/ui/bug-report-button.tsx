'use client';

import { useEffect, useState } from 'react';
import { MessageCircle, Send, X, CheckCircle2, HelpCircle, Bug, Lightbulb } from 'lucide-react';
import { useUserProfile } from '@/hooks/use-api';
import type { MouseEvent } from 'react';
import { useOrgContext } from '../org/org-context-provider';

export function FeedbackButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formData, setFormData] = useState({
    type: '',
    issue: '',
    details: '',
    steps: '',
    priority: 'medium',
  });

  // Get user profile
  const { data: userProfile } = useUserProfile();
  const { currentOrg } = useOrgContext();
  const [userInfo, setUserInfo] = useState({
    email: '',
    page: '',
    browser: '',
    timestamp: new Date().toISOString(),
  });

  useEffect(() => {
    if (globalThis.window) {
      setUserInfo((prev) => ({
        ...prev,

        email: userProfile?.email || '',
        page: globalThis.window?.location.pathname || '',
        browser: navigator.userAgent,
        orgName: currentOrg?.orgName || '',
        orgId: currentOrg?.orgId || '',
      }));
    }
  }, [userProfile?.email]);

  const placeholder = {
    bug: "e.g., The save button doesn't work",
    feature: 'e.g., Add dark mode option',
    help: 'e.g., How do I export my data?',
    other: 'e.g., General feedback about the app',
  };

  const feedbackTypes = [
    {
      id: 'bug',
      label: 'Bug Report',
      description: "Something isn't working correctly",
      icon: Bug,
      color: 'text-red-600',
      bgColor: 'bg-red-50 hover:bg-red-100',
      borderColor: 'border-red-200',
    },
    {
      id: 'feature',
      label: 'Feature Request',
      description: 'Suggest a new feature or improvement',
      icon: Lightbulb,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50 hover:bg-yellow-100',
      borderColor: 'border-yellow-200',
    },
    {
      id: 'help',
      label: 'Need Help',
      description: "I'm confused about how something works",
      icon: HelpCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      borderColor: 'border-blue-200',
    },
    {
      id: 'other',
      label: 'Other',
      description: 'General feedback or questions',
      icon: MessageCircle,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50 hover:bg-gray-100',
      borderColor: 'border-gray-200',
    },
  ];

  const handleSubmit = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const feedbackData = {
        ...formData,
        userEmail: userInfo.email,
        page: userInfo.page,
        browser: userInfo.browser,
        timestamp: new Date().toISOString(),
        orgName: currentOrg?.orgName || 'Not available',
        orgId: currentOrg?.orgId || 'Not available',
        userRole: currentOrg?.role || 'Not available',
      };

      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });

      if (!response.ok) {
        throw new Error('Failed to send feedback');
      }

      setIsSubmitting(false);
      setIsSuccess(true);
      setTimeout(() => {
        setIsOpen(false);
        setIsSuccess(false);
        setCurrentStep(1);
        setFormData({
          type: '',
          issue: '',
          details: '',
          steps: '',
          priority: 'medium',
        });
      }, 2000);
    } catch (error) {
      console.error('Error sending feedback:', error);
      setIsSubmitting(false);
      alert('Sorry, there was an error sending your message. Please try again.');
    }
  };

  const nextStep = () => {
    if (currentStep === 1 && !formData.type) return;
    if (currentStep === 2 && !formData.issue.trim()) return;
    setCurrentStep((prev) => prev + 1);
  };

  const prevStep = () => setCurrentStep((prev) => prev - 1);

  const getSelectedType = () => feedbackTypes.find((t) => t.id === formData.type);

  if (isSuccess) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <div className="bg-white rounded-2xl shadow-2xl border p-8 max-w-sm animate-in slide-in-from-bottom-4 duration-500">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center size-16 bg-green-100 rounded-full mb-4">
              <CheckCircle2 className="size-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Message Sent!</h3>
            <p className="text-sm text-gray-600">
              Thanks for your feedback! We&apos;ll get back to you within 24 hours.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Floating Action Button */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="group size-12 rounded-full bg-gradient-to-r from-brand to-brand/80 hover:from-brand/90 hover:to-brand/70 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center"
          title="Need help? Send us feedback!"
        >
          <MessageCircle className="size-5 text-brand-foreground transition-transform group-hover:rotate-12" />
        </button>
      )}

      {/* Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 animate-in fade-in duration-200">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[85vh] overflow-hidden animate-in slide-in-from-bottom-8 duration-300">
            {/* Header */}
            <div className="px-4 py-3 border-b bg-gradient-to-r from-brand/10 to-brand/5">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white rounded-full shadow-sm">
                    <MessageCircle className="size-5 text-brand" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">How can we help?</h2>
                    <p className="text-sm text-gray-600">We&apos;re here to make things better</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-white/50 rounded-full transition-colors"
                >
                  <X className="size-5 text-gray-500" />
                </button>
              </div>

              {/* Progress Bar */}
              <div className="mt-4 flex items-center gap-2">
                {[1, 2, 3].map((step) => (
                  <div key={step} className="flex items-center">
                    <div
                      className={`size-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                        step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
                      }`}
                    >
                      {step}
                    </div>
                    {step < 3 && (
                      <div
                        className={`w-12 h-0.5 mx-1 transition-colors duration-200 ${
                          step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="p-4 overflow-y-auto max-h-[55vh]">
              <div>
                {/* Step 1: Choose Type */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        What brings you here today?
                      </h3>
                      <p className="text-sm text-gray-600 mb-6">
                        Choose the option that best describes your situation
                      </p>
                    </div>

                    <div className="grid grid-cols-1 gap-3">
                      {feedbackTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <button
                            key={type.id}
                            type="button"
                            onClick={() => setFormData((prev) => ({ ...prev, type: type.id }))}
                            className={`text-left p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md ${
                              formData.type === type.id
                                ? `${type.borderColor} ${type.bgColor} shadow-md`
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="flex items-start gap-3">
                              <div className={`p-2 rounded-lg ${type.bgColor}`}>
                                <IconComponent className={`size-5 ${type.color}`} />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">{type.label}</h4>
                                <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Step 2: Describe Issue */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="flex items-center gap-3 mb-4">
                      {(() => {
                        const selectedType = getSelectedType();
                        return (
                          selectedType && (
                            <>
                              <div className={`p-2 rounded-lg ${selectedType?.bgColor}`}>
                                {(() => {
                                  const SelectedIcon = selectedType?.icon;
                                  return SelectedIcon ? (
                                    <SelectedIcon className={`size-5 ${selectedType?.color}`} />
                                  ) : null;
                                })()}
                              </div>
                              <div>
                                <h3 className="text-lg font-medium text-gray-900">
                                  {selectedType?.label}
                                </h3>
                                <p className="text-sm text-gray-600">{selectedType?.description}</p>
                              </div>
                            </>
                          )
                        );
                      })()}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        What&apos;s the issue? *
                      </label>
                      <input
                        type="text"
                        value={formData.issue}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, issue: e.target.value }))
                        }
                        placeholder={placeholder[formData.type as keyof typeof placeholder]}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tell us more details *
                      </label>
                      <textarea
                        value={formData.details}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, details: e.target.value }))
                        }
                        placeholder="Describe what happened, what you expected, or provide more context..."
                        rows={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                        required
                      />
                    </div>

                    {formData.type === 'bug' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          How urgent is this? *
                        </label>
                        <div className="flex gap-3">
                          {[
                            { value: 'low', label: 'Low', desc: 'Minor inconvenience' },
                            { value: 'medium', label: 'Medium', desc: 'Affects my work' },
                            { value: 'high', label: 'High', desc: 'Blocking my progress' },
                          ].map((priority) => (
                            <button
                              key={priority.value}
                              type="button"
                              onClick={() =>
                                setFormData((prev) => ({ ...prev, priority: priority.value }))
                              }
                              className={`flex-1 p-3 text-center rounded-lg border-2 transition-all ${
                                formData.priority === priority.value
                                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <div className="font-medium">{priority.label}</div>
                              <div className="text-xs text-gray-600 mt-1">{priority.desc}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Step 3: Additional Info */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Almost done!</h3>
                      <p className="text-sm text-gray-600">
                        Any additional details that might help us?
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Steps to reproduce (optional)
                      </label>
                      <textarea
                        value={formData.steps}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, steps: e.target.value }))
                        }
                        placeholder="1. I clicked on...&#10;2. Then I tried to...&#10;3. The error occurred when..."
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        This helps us recreate and fix the issue faster
                      </p>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <CheckCircle2 className="size-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-1">
                            We&apos;ll include technical details
                          </h4>
                          <p className="text-sm text-gray-600 mb-3">
                            This helps us diagnose issues more effectively
                          </p>
                          <div className="text-xs text-gray-500 space-y-1">
                            <div>Page: {userInfo.page}</div>
                            <div>Browser: {userInfo.browser.split(' ').slice(-2).join(' ')}</div>
                            <div>Email: {userInfo.email || 'Not provided'}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            {/* Footer */}
            <div className="px-4 py-3 border-t bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {currentStep === 1 && 'Step 1 of 3 - Choose your topic'}
                {currentStep === 2 && 'Step 2 of 3 - Describe the issue'}
                {currentStep === 3 && 'Step 3 of 3 - Final details'}
              </div>

              <div className="flex gap-3">
                {currentStep > 1 && (
                  <button
                    type="button"
                    onClick={prevStep}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Back
                  </button>
                )}

                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={currentStep === 1 ? !formData.type : !formData.issue.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    type="submit"
                    onClick={handleSubmit}
                    disabled={isSubmitting || !formData.issue.trim() || !formData.details.trim()}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin size-4 border-2 border-white border-t-transparent rounded-full" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="size-4" />
                        Send Message
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Export with old name for backward compatibility
export { FeedbackButton as BugReportButton };
