'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'card' | 'page';
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  actions,
  className,
  variant = 'default',
}: EmptyStateProps) {
  const baseClasses = 'text-center';
  
  if (variant === 'page') {
    return (
      <div className={cn('flex min-h-screen items-center justify-center bg-background', className)}>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="max-w-md p-8"
        >
          {Icon && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="mb-6 flex justify-center"
            >
              <Icon className="size-16 text-muted-foreground" />
            </motion.div>
          )}
          <motion.h2
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="mb-2 text-xl font-semibold text-foreground"
          >
            {title}
          </motion.h2>
          {description && (
            <motion.p
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="mb-6 text-muted-foreground"
            >
              {description}
            </motion.p>
          )}
          {actions && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="flex justify-center gap-3"
            >
              {actions}
            </motion.div>
          )}
        </motion.div>
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={cn(
          'rounded-xl border border-border bg-card p-8 text-center shadow-sm',
          className
        )}
      >
        {Icon && (
          <div className="mb-4 flex justify-center">
            <Icon className="size-12 text-muted-foreground" />
          </div>
        )}
        <h3 className="mb-2 text-lg font-semibold text-foreground">{title}</h3>
        {description && (
          <p className="mb-4 text-sm text-muted-foreground">{description}</p>
        )}
        {actions && <div className="flex justify-center gap-2">{actions}</div>}
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(baseClasses, 'py-12', className)}
    >
      {Icon && <Icon className="mx-auto mb-4 size-12 text-muted-foreground" />}
      <h3 className="mb-2 text-lg font-semibold text-foreground">{title}</h3>
      {description && (
        <p className="mb-4 text-muted-foreground">{description}</p>
      )}
      {actions && <div className="flex justify-center gap-2">{actions}</div>}
    </motion.div>
  );
}