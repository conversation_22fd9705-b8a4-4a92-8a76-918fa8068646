'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface ProfileCompletionBannerProps {
  title: string;
  description: string;
  progress: number; // 0-100
  actionText?: string;
  onAction?: () => void;
  icon?: LucideIcon;
  className?: string;
  variant?: 'info' | 'warning' | 'success';
}

const variantStyles = {
  info: {
    bg: 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-200/50 dark:border-blue-800/50',
    progress: 'bg-blue-500',
    button: 'bg-blue-500 hover:bg-blue-600 text-white',
    icon: 'text-blue-500',
  },
  warning: {
    bg: 'bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-200/50 dark:border-orange-800/50',
    progress: 'bg-orange-500',
    button: 'bg-orange-500 hover:bg-orange-600 text-white',
    icon: 'text-orange-500',
  },
  success: {
    bg: 'bg-gradient-to-r from-green-500/10 to-green-600/10 border-green-200/50 dark:border-green-800/50',
    progress: 'bg-green-500',
    button: 'bg-green-500 hover:bg-green-600 text-white',
    icon: 'text-green-500',
  },
};

export function ProfileCompletionBanner({
  title,
  description,
  progress,
  actionText,
  onAction,
  icon: Icon,
  className,
  variant = 'info',
}: ProfileCompletionBannerProps) {
  const styles = variantStyles[variant];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        'rounded-xl border p-6 backdrop-blur-sm',
        styles.bg,
        className
      )}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-4">
          <div className="flex items-center space-x-3">
            {Icon && <Icon className={cn('size-5', styles.icon)} />}
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          </div>
          
          <p className="text-muted-foreground">{description}</p>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Profile Completion</span>
              <span className="font-medium text-foreground">{progress}%</span>
            </div>
            <div className="h-2 overflow-hidden rounded-full bg-muted">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                className={cn('h-full rounded-full', styles.progress)}
              />
            </div>
          </div>
        </div>
        
        {actionText && onAction && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="ml-6"
          >
            <Button
              onClick={onAction}
              className={cn('shadow-lg transition-all duration-200 hover:scale-105', styles.button)}
            >
              {actionText}
            </Button>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}