'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface StatsCardProps {
  dataTestId?: string;
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  colorVariant?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'brand';
  className?: string;
  loading?: boolean;
}

const colorVariants = {
  blue: {
    border: 'border-blue-200 dark:border-blue-800',
    gradient: 'from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20',
    title: 'text-blue-700 dark:text-blue-300',
    value: 'text-blue-700 dark:text-blue-300',
    description: 'text-blue-600 dark:text-blue-400',
    iconBg: 'bg-blue-100 dark:bg-blue-900/30',
    icon: 'text-blue-600 dark:text-blue-400',
  },
  green: {
    border: 'border-green-200 dark:border-green-800',
    gradient: 'from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20',
    title: 'text-green-700 dark:text-green-300',
    value: 'text-green-700 dark:text-green-300',
    description: 'text-green-600 dark:text-green-400',
    iconBg: 'bg-green-100 dark:bg-green-900/30',
    icon: 'text-green-600 dark:text-green-400',
  },
  orange: {
    border: 'border-orange-200 dark:border-orange-800',
    gradient: 'from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20',
    title: 'text-orange-700 dark:text-orange-300',
    value: 'text-orange-700 dark:text-orange-300',
    description: 'text-orange-600 dark:text-orange-400',
    iconBg: 'bg-orange-100 dark:bg-orange-900/30',
    icon: 'text-orange-600 dark:text-orange-400',
  },
  red: {
    border: 'border-red-200 dark:border-red-800',
    gradient: 'from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20',
    title: 'text-red-700 dark:text-red-300',
    value: 'text-red-700 dark:text-red-300',
    description: 'text-red-600 dark:text-red-400',
    iconBg: 'bg-red-100 dark:bg-red-900/30',
    icon: 'text-red-600 dark:text-red-400',
  },
  purple: {
    border: 'border-purple-200 dark:border-purple-800',
    gradient: 'from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20',
    title: 'text-purple-700 dark:text-purple-300',
    value: 'text-purple-700 dark:text-purple-300',
    description: 'text-purple-600 dark:text-purple-400',
    iconBg: 'bg-purple-100 dark:bg-purple-900/30',
    icon: 'text-purple-600 dark:text-purple-400',
  },
  brand: {
    border: 'border-brand/20 dark:border-brand/30',
    gradient: 'from-brand/5 to-brand/10 dark:from-brand/10 dark:to-brand/20',
    title: 'text-brand dark:text-brand/80',
    value: 'text-brand dark:text-brand/80',
    description: 'text-brand/60 dark:text-brand/60',
    iconBg: 'bg-brand/10 dark:bg-brand/20',
    icon: 'text-brand dark:text-brand/80',
  },
};

export function StatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  colorVariant = 'blue',
  className,
  loading = false,
}: StatsCardProps) {
  const colors = colorVariants[colorVariant];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'group rounded-xl border p-6 transition-all duration-300 hover:shadow-lg',
        colors.border,
        colors.gradient,
        className,
      )}
    >
      <div
        data-testid={`${title.toLowerCase().replace(' ', '-')}-stats-card`}
        className="flex items-center justify-between "
      >
        <div>
          <p className={cn('text-sm font-medium', colors.title)}>{title}</p>
          <p className={cn('text-3xl font-bold', colors.value)}>{loading ? '...' : value}</p>
          {trend && (
            <div className="mt-1 flex items-center gap-1">
              <span
                className={cn(
                  'text-xs font-medium',
                  trend.isPositive ? 'text-green-600' : 'text-red-600',
                )}
              >
                {trend.isPositive ? '+' : ''}
                {trend.value}%
              </span>
              <span className="text-xs text-muted-foreground">from last week</span>
            </div>
          )}
        </div>
        <div
          className={cn(
            'rounded-lg p-2 transition-transform duration-300 group-hover:scale-110',
            colors.iconBg,
          )}
        >
          <Icon className={cn('size-4', colors.icon)} />
        </div>
      </div>
      {description && <p className={cn('mt-1 text-xs', colors.description)}>{description}</p>}
    </motion.div>
  );
}
