'use client';

import { <PERSON>, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useThemeHydrated } from '@/hooks/use-theme';

export function ThemeToggle() {
  const { isDark, setTheme, mounted } = useThemeHydrated();

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" aria-label="Toggle theme" disabled>
        <Moon className="size-5" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      aria-label="Toggle theme"
      onClick={() => setTheme(isDark ? 'light' : 'dark')}
    >
      {isDark ? <Sun className="size-5" /> : <Moon className="size-5" />}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}
