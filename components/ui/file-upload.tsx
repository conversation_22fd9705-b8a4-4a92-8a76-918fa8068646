'use client';

import React from 'react';
import { useState, useRef } from 'react';
import { Upload, X, File as FileIcon, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { validateClientFile, formatFileSize, getFileIcon } from '@/lib/files/file-upload-client';

interface FileUploadProps {
  id?: string;
  onUpload: (url: string) => void;
  onFileSelect?: (file: File) => void;
  onRemove?: () => void;
  currentFile?: File | string | null;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  accept?: string;
  allowedTypes?: string[];
  maxFileSize?: number;
  showPreview?: boolean;
  compact?: boolean; // New prop for compact mode
}

export function FileUpload({
  id,
  onUpload,
  onFileSelect,
  onRemove,
  currentFile,
  className = '',
  disabled = false,
  placeholder = 'Upload file',
  accept,
  allowedTypes,
  maxFileSize,
  showPreview = true,
  compact = false, // Default to false for backward compatibility
}: FileUploadProps) {
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const resetUploadState = () => {
    setError(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileSelect = async (file: File) => {
    // Use client-side validation utilities
    const validation = validateClientFile(file, maxFileSize, allowedTypes);
    if (!validation.valid) {
      setError(validation.error || 'Invalid file');
      toast.error(validation.error || 'Invalid file');
      return;
    }

    // Reset error state
    setError(null);

    // Store the file for later upload
    onFileSelect?.(file);
    onUpload(file.name); // Use file name as temporary identifier
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    if (!disabled) {
      event.preventDefault();
    }
  };

  const triggerFileSelect = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemove = () => {
    onRemove?.();
  };

  const getDisplayFileName = () => {
    if (!currentFile) return null;
    if (typeof currentFile === 'string') {
      // If it's a URL, extract the filename
      return currentFile.split('/').pop() || currentFile;
    }
    return currentFile.name;
  };

  const getFileTypeIcon = () => {
    if (!currentFile) return <FileIcon className="size-8 text-muted-foreground" />;

    const fileType =
      typeof currentFile === 'string'
        ? 'application/octet-stream' // Default for URL strings
        : currentFile.type;

    const icon = getFileIcon(fileType);
    return <span className="text-2xl">{icon}</span>;
  };

  const formatAcceptedTypes = () => {
    if (!allowedTypes || allowedTypes.length === 0) return 'All file types';

    // Create a more human-readable format
    const formatMap: Record<string, string> = {
      'application/pdf': 'PDF',
      'application/msword': 'Word',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
      'application/vnd.ms-excel': 'Excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
      'text/csv': 'CSV',
      'application/json': 'JSON',
      'text/plain': 'Text',
      'text/x-python': 'Python',
      'text/javascript': 'JavaScript',
      'application/javascript': 'JavaScript',
      'application/x-yaml': 'YAML',
      'text/yaml': 'YAML',
      'application/yaml': 'YAML',
    };

    const formats = allowedTypes.map(
      (type) => formatMap[type] || type.split('/')[1]?.toUpperCase() || type,
    );

    // Remove duplicates and sort
    const uniqueFormats = [...new Set(formats)].sort();

    if (compact) {
      // For compact mode, show fewer formats
      return uniqueFormats.length <= 3 ? uniqueFormats.join(', ') : uniqueFormats.join(', ');
    }

    return uniqueFormats.join(', ');
  };

  const formatMaxSize = () => {
    if (!maxFileSize) return '';
    return ` up to ${formatFileSize(maxFileSize)}`;
  };

  return (
    <div className={`${className}`}>
      <input
        id={id}
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled}
      />

      {currentFile && showPreview ? (
        <div className="group relative">
          <div className="relative h-fit w-full overflow-hidden rounded-md border border-border/50 bg-muted/30 p-3">
            <div className="flex items-center gap-2">
              <div className="text-base opacity-60">{getFileTypeIcon()}</div>
              <div className="min-w-0 flex-1">
                <p className="truncate text-xs font-medium text-foreground">
                  {getDisplayFileName()}
                </p>
                {typeof currentFile !== 'string' && (
                  <p className="text-xs text-muted-foreground/70">
                    {formatFileSize(currentFile.size)}
                  </p>
                )}
              </div>
            </div>

            {/* Error overlay */}
            {error && (
              <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 bg-destructive/90 backdrop-blur-sm">
                <AlertCircle className="size-4 text-destructive-foreground" />
                <p className="px-2 text-center text-xs text-destructive-foreground">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetUploadState}
                  className="h-6 bg-background/50 px-2 text-xs"
                >
                  Dismiss
                </Button>
              </div>
            )}

            {/* Remove button */}
            {!disabled && !error && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 size-5 p-0 opacity-0 transition-opacity hover:bg-destructive/20 hover:text-destructive group-hover:opacity-70"
                onClick={handleRemove}
              >
                <X className="size-3" />
              </Button>
            )}
          </div>
        </div>
      ) : (
        <div
          className={`rounded-lg border-2 border-dashed border-border p-6 text-center transition-colors hover:border-primary/50 ${
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          } ${compact ? 'p-4' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={triggerFileSelect}
          role="button"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={(e) => {
            if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
              e.preventDefault();
              triggerFileSelect();
            }
          }}
        >
          <div className="flex flex-col items-center gap-2">
            {error ? (
              <div className="flex flex-col items-center gap-2">
                <AlertCircle className="size-8 text-destructive" />
                <span className="text-sm text-destructive">{error}</span>
              </div>
            ) : (
              <FileIcon className="size-8 text-muted-foreground" />
            )}

            {!error && (
              <div className="text-sm">
                <span className="font-medium text-foreground">{placeholder}</span>
                {!compact && (
                  <p className="mt-1 text-muted-foreground">Drag and drop or click to browse</p>
                )}
              </div>
            )}

            <p className="text-xs text-muted-foreground">
              {formatAcceptedTypes()}
              {formatMaxSize()}
            </p>
          </div>
        </div>
      )}

      {!currentFile && !compact && (
        <Button
          variant="outline"
          onClick={triggerFileSelect}
          disabled={disabled}
          className="w-full"
        >
          <Upload className="mr-2 size-4" />
          Choose File
        </Button>
      )}

      {/* Retry button for errors */}
      {error && !currentFile && !compact && (
        <Button
          variant="outline"
          onClick={() => {
            resetUploadState();
            triggerFileSelect();
          }}
          className="w-full"
        >
          Try Again
        </Button>
      )}
    </div>
  );
}
