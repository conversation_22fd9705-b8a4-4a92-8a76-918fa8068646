// A reusable component to display a percentage bar with a label and color.
export function PercentBar({
  name,
  percentage,
  color,
}: {
  /** Label shown on the left side */
  name: string;
  /** e.g. "42%" – the CSS width of the coloured bar */
  percentage: string;
  /** A Tailwind bg-class (e.g. "bg-brand") **OR** a CSS colour string (e.g. "#22c55e") */
  color: string;
}) {
  const isClass = color.startsWith('bg-');

  return (
    <div className="space-y-1.5">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isClass ? (
            <span className={`size-3 ${color} rounded-full`} />
          ) : (
            <span className="size-3 rounded-full" style={{ backgroundColor: color }} />
          )}
          <span className="text-sm text-muted-foreground">{name}</span>
        </div>
        <span className="font-medium text-foreground">{percentage}</span>
      </div>
      <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
        {isClass ? (
          <div className={`${color} h-2`} style={{ width: percentage }} />
        ) : (
          <div className="h-2" style={{ width: percentage, backgroundColor: color }} />
        )}
      </div>
    </div>
  );
}
