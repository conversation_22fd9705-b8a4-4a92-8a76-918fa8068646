'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface GradientButtonProps extends ButtonProps {
  gradient?: 'brand' | 'blue' | 'green' | 'orange' | 'red' | 'purple';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  shimmer?: boolean;
}

const gradientVariants = {
  brand: 'bg-brand text-brand-foreground shadow-lg hover:bg-brand/90',
  blue: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:from-blue-600 hover:to-blue-700',
  green: 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:from-green-600 hover:to-green-700',
  orange: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg hover:from-orange-600 hover:to-orange-700',
  red: 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:from-red-600 hover:to-red-700',
  purple: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg hover:from-purple-600 hover:to-purple-700',
};

export function GradientButton({
  children,
  className,
  gradient = 'brand',
  icon: Icon,
  iconPosition = 'left',
  shimmer = false,
  disabled,
  ...props
}: GradientButtonProps) {
  const gradientClass = gradientVariants[gradient];
  
  return (
    <Button
      className={cn(
        'relative overflow-hidden transition-all duration-200 hover:scale-105 disabled:scale-100 disabled:opacity-50',
        gradientClass,
        shimmer && !disabled && 'before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:animate-[shimmer_2s_infinite]',
        className
      )}
      disabled={disabled}
      {...props}
    >
      {Icon && iconPosition === 'left' && (
        <Icon className="mr-2 size-4" />
      )}
      {children}
      {Icon && iconPosition === 'right' && (
        <Icon className="ml-2 size-4" />
      )}
      
      {shimmer && !disabled && (
        <style jsx>{`
          @keyframes shimmer {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(100%);
            }
          }
        `}</style>
      )}
    </Button>
  );
}

// Special variant for the Return to Dashboard button pattern
export function ReturnButton({
  href = '/',
  children = 'Return to Dashboard',
  className,
  ...props
}: Omit<GradientButtonProps, 'gradient'> & { href?: string }) {
  return (
    <motion.a
      href={href}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn('inline-block', className)}
    >
      <GradientButton gradient="brand" {...props}>
        {children}
      </GradientButton>
    </motion.a>
  );
}