'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface AccessDeniedProps {
  title?: string;
  description?: string;
  icon?: LucideIcon;
  backUrl?: string;
  backText?: string;
  className?: string;
  variant?: 'page' | 'modal';
}

export function AccessDenied({
  title = 'Access Denied',
  description = 'You do not have permission to access this resource.',
  icon: Icon,
  backUrl = '/',
  backText = 'Return to Dashboard',
  className,
  variant = 'page',
}: AccessDeniedProps) {
  const containerClasses = variant === 'page' 
    ? 'flex min-h-screen items-center justify-center bg-[#0f0f0f]'
    : 'flex items-center justify-center p-8';

  return (
    <div className={cn(containerClasses, className)}>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={cn(
          'max-w-md rounded-xl border border-border bg-card p-8 text-center shadow-2xl',
          variant === 'modal' && 'max-w-sm'
        )}
      >
        {Icon && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="mb-4 text-red-500"
          >
            <Icon className="mx-auto size-16" />
          </motion.div>
        )}
        
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="mb-2 text-xl font-semibold text-foreground"
        >
          {title}
        </motion.h2>
        
        <motion.p
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="mb-6 text-muted-foreground"
        >
          {description}
        </motion.p>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Link href={backUrl}>
            <Button className="bg-brand font-semibold text-brand-foreground shadow-lg transition-all duration-200 hover:scale-105 hover:bg-brand/90">
              {backText}
            </Button>
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
}