import { Button } from '@/components/ui/button';
import type { PaginationControlsProps } from '@/types/ui';

export function PaginationControls({
  page,
  totalPages,
  onPrevious,
  onNext,
  pageSize,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25, 50],
  disabled = false,
  totalItems = 0,
}: PaginationControlsProps) {
  const startItem = page * pageSize + 1;
  const endItem = Math.min((page + 1) * pageSize, totalItems);

  return (
    <div className="mt-6 flex flex-wrap items-center justify-between gap-4">
      <div className="flex items-center space-x-3">
        <Button
          variant="outline"
          size="sm"
          className="border-border/50 bg-background/50 font-semibold text-foreground transition-colors hover:bg-background/70 disabled:opacity-50"
          onClick={onPrevious}
          disabled={disabled || page === 0}
        >
          Previous
        </Button>
        <span className="px-3 text-base font-medium text-muted-foreground">
          {totalItems > 0 ? `${startItem}-${endItem} of ${totalItems}` : 'No items'}
        </span>
        <Button
          variant="outline"
          size="sm"
          className="border-border/50 bg-background/50 font-semibold text-foreground transition-colors hover:bg-background/70 disabled:opacity-50"
          onClick={onNext}
          disabled={disabled || page >= totalPages - 1}
        >
          Next
        </Button>
      </div>
      <div className="flex items-center space-x-3">
        <span className="text-base font-medium text-muted-foreground">Show:</span>
        <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className="rounded border border-border/50 bg-background/50 px-4 py-2 text-base font-medium text-foreground transition-colors hover:bg-background/70 focus:outline-none focus:ring-2 focus:ring-brand/50"
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
