'use client';

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from 'react';

import { getQueryClient } from '@/lib/query-client';
import { usePermissions } from '@/hooks/use-permissions';
import type { Permission, UserRole } from '@/types/auth';
import { usePerformanceMonitor } from '@/hooks/use-performance-monitor';

// Split contexts for better performance - data vs actions
interface OrgDataContextType {
  currentOrgId: string | undefined;
  currentOrg: Org | undefined;
  organizations: Org[];
  isLoading: boolean;
  error: string | undefined;
}
export type Org = {
  orgId: string;
  orgName: string;
  role: string;
  permissions: Permission[];
};

interface OrgActionsContextType {
  switchOrganization: (orgId: string) => Promise<void>;
  refreshPermissions: () => void;
}

const OrgDataContext = createContext<OrgDataContextType | undefined>(undefined);
const OrgActionsContext = createContext<OrgActionsContextType | undefined>(undefined);

/**
 * Optimized Organization context provider with split contexts and reduced re-renders
 */
export function OrgContextProvider({ children }: { children: React.ReactNode }) {
  // Add performance monitoring with higher threshold for async operations
  usePerformanceMonitor('OrgContextProvider', 200);

  const {
    getOrganizations,
    getCurrentOrgContext,
    setCurrentOrgContext,
    loading,
    error,
    refreshPermissions: baseRefreshPermissions,
  } = usePermissions();

  // Combine related state to reduce setState calls
  const [orgState, setOrgState] = useState<{
    currentOrgId: string | undefined;
    isChanging: boolean;
    error: string | undefined;
  }>(() => {
    // Initialize with localStorage value to prevent flash
    const savedOrgId =
      globalThis.window === undefined ? undefined : localStorage.getItem('currentOrgId');
    return {
      currentOrgId: savedOrgId || undefined,
      isChanging: false,
      error: undefined,
    };
  });

  const queryClient = getQueryClient();
  const isInitialized = useRef(false);

  // Memoize organizations array to prevent unnecessary re-renders
  const organizations = useMemo(() => getOrganizations(), [getOrganizations]);

  // Memoize current org lookup
  const currentOrg = useMemo(() => {
    return orgState.currentOrgId
      ? organizations.find((org: Org) => org.orgId === orgState.currentOrgId) || undefined
      : undefined;
  }, [orgState.currentOrgId, organizations]);

  // Stable reference for organization switching
  const switchOrganization = useCallback(
    async (orgId: string) => {
      if (orgId === orgState.currentOrgId) return;

      setOrgState((prev) => ({ ...prev, isChanging: true, error: undefined }));

      try {
        const targetOrg = organizations.find((org: Org) => org.orgId === orgId);

        // 1. Update context and localStorage atomically
        setCurrentOrgContext(orgId);
        setOrgState((prev) => ({ ...prev, currentOrgId: orgId }));

        // 2. Batch invalidate queries more efficiently
        await queryClient.invalidateQueries({
          queryKey: ['permissions'],
          type: 'all',
          refetchType: 'active',
        });

        // Only invalidate critical org-specific data
        await queryClient.invalidateQueries({
          queryKey: ['devices'],
          refetchType: 'active',
        });

        await queryClient.invalidateQueries({
          queryKey: ['providers'],
          refetchType: 'active',
        });

        // 3. Refresh permissions after a short delay to ensure state consistency
        setTimeout(() => {
          baseRefreshPermissions();
        }, 50);

        // 4. Reset changing state and dispatch event
        setOrgState((prev) => ({ ...prev, isChanging: false }));

        // 5. Single event dispatch with all relevant data
        globalThis.dispatchEvent(
          new CustomEvent('org-context-changed', {
            detail: {
              orgId,
              org: targetOrg,
              previousOrgId: orgState.currentOrgId,
              timestamp: Date.now(),
            },
          }),
        );
      } catch (error: any) {
        setOrgState((prev) => ({ ...prev, error: error.message, isChanging: false }));
      }
    },
    [
      orgState.currentOrgId,
      organizations,
      setCurrentOrgContext,
      queryClient,
      baseRefreshPermissions,
    ],
  );

  // Initialize current org - only run once
  useEffect(() => {
    if (isInitialized.current) return;

    const orgId = getCurrentOrgContext();
    if (orgId !== orgState.currentOrgId) {
      setOrgState((prev) => ({ ...prev, currentOrgId: orgId || undefined }));
    }
    isInitialized.current = true;
  }, [getCurrentOrgContext, orgState.currentOrgId]);

  // Set default org if needed - optimized condition
  useEffect(() => {
    if (organizations.length > 0 && !orgState.currentOrgId && !orgState.isChanging) {
      const defaultOrgId = getCurrentOrgContext();
      if (defaultOrgId && organizations.some((org: Org) => org.orgId === defaultOrgId)) {
        setOrgState((prev) => ({ ...prev, currentOrgId: defaultOrgId }));
      }
    }
  }, [organizations, orgState.currentOrgId, orgState.isChanging, getCurrentOrgContext]);

  // Optimized event listener - single listener with ref for current state
  useEffect(() => {
    const handleOrgChange = (event: CustomEvent) => {
      const { orgId } = event.detail;
      setOrgState((prev) => {
        if (orgId !== prev.currentOrgId) {
          return { ...prev, currentOrgId: orgId };
        }
        return prev;
      });
    };

    globalThis.addEventListener('org-context-changed', handleOrgChange as EventListener);
    return () => {
      globalThis.removeEventListener('org-context-changed', handleOrgChange as EventListener);
    };
  }, []); // Empty deps - using functional setState

  // Stable refresh function
  const refreshPermissions = useCallback(() => {
    baseRefreshPermissions();
  }, [baseRefreshPermissions]);

  // Memoize context values to prevent unnecessary re-renders
  const dataValue = useMemo<OrgDataContextType>(
    () => ({
      currentOrgId: orgState.currentOrgId,
      currentOrg,
      organizations,
      isLoading: loading || orgState.isChanging,
      error: error || orgState.error,
    }),
    [
      orgState.currentOrgId,
      currentOrg,
      organizations,
      loading,
      orgState.isChanging,
      error,
      orgState.error,
    ],
  );

  const actionsValue = useMemo<OrgActionsContextType>(
    () => ({
      switchOrganization,
      refreshPermissions,
    }),
    [switchOrganization, refreshPermissions],
  );

  return (
    <OrgDataContext.Provider value={dataValue}>
      <OrgActionsContext.Provider value={actionsValue}>{children}</OrgActionsContext.Provider>
    </OrgDataContext.Provider>
  );
}

/**
 * Optimized hooks with better error handling and performance
 */
export function useOrgData(): OrgDataContextType {
  const context = useContext(OrgDataContext);
  if (!context) {
    throw new Error('useOrgData must be used within an OrgContextProvider');
  }
  return context;
}

export function useOrgActions(): OrgActionsContextType {
  const context = useContext(OrgActionsContext);
  if (!context) {
    throw new Error('useOrgActions must be used within an OrgContextProvider');
  }
  return context;
}

/**
 * Combined hook for backward compatibility
 */
export function useOrgContext() {
  const data = useOrgData();
  const actions = useOrgActions();

  return {
    ...data,
    ...actions,
  };
}

/**
 * HOC for components that need organization context
 */
export function withOrgContext<P extends object>(Component: React.ComponentType<P>) {
  const displayName = Component.displayName || Component.name || 'Component';

  const OrgContextWrappedComponent = React.memo((props: P) => {
    return (
      <OrgContextProvider>
        <Component {...props} />
      </OrgContextProvider>
    );
  });

  OrgContextWrappedComponent.displayName = `withOrgContext(${displayName})`;
  return OrgContextWrappedComponent;
}

/**
 * Optimized Organization selector component
 */
interface OrgSelectorProps {
  className?: string;
  showRole?: boolean;
  compact?: boolean;
}

export const OrgSelector = React.memo<OrgSelectorProps>(
  ({ className = '', showRole = true, compact = false }) => {
    const { currentOrg, organizations, isLoading } = useOrgData();
    const { switchOrganization } = useOrgActions();

    // Memoize options to prevent recreation on every render
    const options = useMemo(
      () =>
        organizations.map((org) => ({
          value: org.orgId,
          label: `${org.orgName}${showRole ? ` (${org.role})` : ''}`,
        })),
      [organizations, showRole],
    );

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLSelectElement>) => {
        switchOrganization(e.target.value);
      },
      [switchOrganization],
    );

    // Don't render if only one or no organizations
    if (organizations.length <= 1) {
      return null;
    }

    return (
      <div className={`org-selector ${className}`}>
        <select
          value={currentOrg?.orgId || ''}
          onChange={handleChange}
          disabled={isLoading}
          className={`
          rounded-md border border-gray-300 px-3 py-2 text-sm
          ${compact ? 'px-2 py-1 text-xs' : ''}
          ${isLoading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
        `}
        >
          {options.map(({ value, label }) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
        {isLoading && <span className="ml-2 text-xs text-gray-500">Switching...</span>}
      </div>
    );
  },
);

OrgSelector.displayName = 'OrgSelector';

/**
 * Optimized Organization info display component
 */
interface OrgInfoProps {
  className?: string;
  showRole?: boolean;
  showCount?: boolean;
}

export const OrgInfo = React.memo<OrgInfoProps>(
  ({ className = '', showRole = true, showCount = false }) => {
    const { currentOrg, organizations } = useOrgData();

    if (!currentOrg) {
      return <div className={`text-sm text-gray-500 ${className}`}>No organization selected</div>;
    }

    return (
      <div className={`org-info ${className}`}>
        <span className="font-medium">{currentOrg.orgName}</span>
        {showRole && <span className="ml-2 text-gray-500">({currentOrg.role})</span>}
        {showCount && organizations.length > 1 && (
          <span className="ml-2 text-xs text-gray-400">{organizations.length} orgs</span>
        )}
      </div>
    );
  },
);

OrgInfo.displayName = 'OrgInfo';
