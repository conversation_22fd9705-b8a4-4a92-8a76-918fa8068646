'use client';

import React from 'react';
import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Lock, Key, Eye, EyeOff, CheckCircle, AlertCircle, ArrowLeft, Mail } from 'lucide-react';
import Link from 'next/link';
import { completePasswordReset } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';

interface PasswordValidation {
  minLength: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
}

interface FormState {
  email: string;
  code: string;
  password: string;
  confirmPassword: string;
  showPassword: boolean;
  showConfirmPassword: boolean;
  isEmailEditable: boolean;
  initialized: boolean;
}

const INITIAL_FORM_STATE: FormState = {
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
  showPassword: false,
  showConfirmPassword: false,
  isEmailEditable: false,
  initialized: false,
};

export function ResetPasswordForm() {
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const [formState, setFormState] = useState<FormState>(() => {
    // Initialize with stored email if available
    if (globalThis.window !== undefined) {
      const storedEmail = sessionStorage.getItem('reset-email');
      return {
        ...INITIAL_FORM_STATE,
        email: storedEmail || '',
        isEmailEditable: !storedEmail,
        initialized: true,
      };
    }
    return INITIAL_FORM_STATE;
  });

  // React Query mutation for password reset completion
  const resetMutation = useMutation({
    mutationFn: async (formData: {
      email: string;
      code: string;
      newPassword: string;
      confirmPassword: string;
      csrfToken: string;
    }) => {
      const formDataObj = new FormData();
      formDataObj.set('email', formData.email);
      formDataObj.set('code', formData.code);
      formDataObj.set('newPassword', formData.newPassword);
      formDataObj.set('confirmPassword', formData.confirmPassword);
      formDataObj.set('csrfToken', formData.csrfToken);

      const result = await completePasswordReset(null, formDataObj);
      if (!result.success) {
        throw new Error(result.error || 'Failed to reset password');
      }
      return result;
    },
    onSuccess: () => {
      // Clear the stored email from sessionStorage
      if (globalThis.window !== undefined) {
        sessionStorage.removeItem('reset-email');
      }
      // Redirect to sign-in with success message
      router.push(
        '/signin?message=Password reset successful. Please sign in with your new password.',
      );
    },
  });

  // Initialize form on client side
  if (globalThis.window !== undefined && !formState.initialized) {
    const storedEmail = sessionStorage.getItem('reset-email');
    setFormState((prev) => ({
      ...prev,
      email: storedEmail || '',
      isEmailEditable: !storedEmail,
      initialized: true,
    }));
  }

  // Validate password requirements
  const validatePassword = useCallback(
    (pwd: string): PasswordValidation => ({
      minLength: pwd.length >= 8,
      hasNumber: /\d/.test(pwd),
      hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(pwd),
    }),
    [],
  );

  // Update form state helper
  const updateFormState = useCallback((updates: Partial<FormState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Form validation
  const passwordValidation = validatePassword(formState.password);
  const isPasswordValid = Object.values(passwordValidation).every(Boolean);
  const passwordsMatch =
    formState.password === formState.confirmPassword && formState.confirmPassword.length > 0;
  const isEmailValid = formState.email.includes('@') && formState.email.includes('.');
  const canSubmit =
    isPasswordValid &&
    passwordsMatch &&
    formState.code.length === 6 &&
    isEmailValid &&
    !resetMutation.isPending;

  const displayError = resetMutation.error?.message || csrfError;

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!canSubmit || !csrfToken) return;

      resetMutation.mutate({
        email: formState.email,
        code: formState.code,
        newPassword: formState.password,
        confirmPassword: formState.confirmPassword,
        csrfToken,
      });
    },
    [canSubmit, csrfToken, formState, resetMutation],
  );

  // Show loading state during initialization
  if (!formState.initialized) {
    return (
      <div className="h-96 w-full max-w-md animate-pulse rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
        <div className="flex h-full items-center justify-center p-8">
          <div className="size-6 animate-spin rounded-full border-2 border-purple-500/30 border-t-purple-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
      <div className="p-10 lg:p-12">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25">
            <Key className="size-8 text-white" />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">Reset Password</h1>
          <p className="text-sm text-slate-400">
            {formState.email && !formState.isEmailEditable
              ? 'Enter the code and your new password'
              : 'Enter your email, reset code, and new password'}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 rounded-xl border border-red-500/20 bg-red-500/10 p-4 backdrop-blur-sm">
              <AlertCircle className="mt-0.5 size-5 shrink-0 text-red-400" />
              <p className="text-sm text-red-400">{displayError}</p>
            </div>
          )}

          {/* Info Alert for manual email entry */}
          {formState.isEmailEditable && (
            <div className="flex items-start gap-3 rounded-xl border border-blue-500/20 bg-blue-500/10 p-4 backdrop-blur-sm">
              <AlertCircle className="mt-0.5 size-5 shrink-0 text-blue-400" />
              <div className="flex-1">
                <p className="text-sm text-blue-400">
                  Enter the email address where you want to receive the reset code, then the code
                  and new password.
                </p>
                <p className="mt-1 text-xs text-blue-300">
                  If you haven&apos;t requested a reset code yet,{' '}
                  <Link href="/forgot-password" className="underline hover:no-underline">
                    click here to get one
                  </Link>
                  .
                </p>
              </div>
            </div>
          )}

          {/* Email Address */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="email" className="block text-sm font-medium text-slate-300">
                Email Address
              </label>
              {!formState.isEmailEditable && formState.email && (
                <button
                  type="button"
                  onClick={() => updateFormState({ isEmailEditable: true })}
                  className="text-xs text-purple-400 transition-colors hover:text-purple-300 hover:underline"
                  aria-label="Change email address"
                >
                  Change email?
                </button>
              )}
            </div>
            <div className="group relative">
              <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                id="email"
                type="email"
                name="email"
                placeholder={
                  formState.isEmailEditable ? 'Enter your email address' : 'Email address'
                }
                autoComplete="email"
                value={formState.email}
                onChange={(e) => updateFormState({ email: e.target.value })}
                readOnly={!formState.isEmailEditable}
                required
                disabled={resetMutation.isPending}
                className={`w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-4 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 disabled:cursor-not-allowed disabled:opacity-50 ${
                  formState.isEmailEditable ? '' : 'cursor-default bg-slate-700/50'
                }`}
                aria-describedby={!isEmailValid && formState.email ? 'email-error' : undefined}
              />
            </div>

            {/* Email validation feedback */}
            {formState.email && !isEmailValid && formState.isEmailEditable && (
              <p id="email-error" className="flex items-center gap-1 text-xs text-red-400">
                <AlertCircle className="size-3" />
                Please enter a valid email address
              </p>
            )}
          </div>

          {/* Reset Code */}
          <div className="space-y-2">
            <label htmlFor="code" className="block text-sm font-medium text-slate-300">
              6-Digit Reset Code
            </label>
            <div className="flex justify-center">
              <InputOTP
                value={formState.code}
                onChange={(value) => updateFormState({ code: value })}
                maxLength={6}
                disabled={resetMutation.isPending}
                className="gap-2"
              >
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={0}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                  <InputOTPSlot
                    index={1}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                  <InputOTPSlot
                    index={2}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                </InputOTPGroup>
                <div className="flex w-4 justify-center">
                  <div className="h-0.5 w-2 bg-slate-600"></div>
                </div>
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={3}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                  <InputOTPSlot
                    index={4}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                  <InputOTPSlot
                    index={5}
                    className="size-12 rounded-lg border-slate-600/50 bg-slate-800/50 text-lg font-semibold text-white transition-all duration-200 hover:border-slate-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50"
                  />
                </InputOTPGroup>
              </InputOTP>
            </div>

            {/* Code validation feedback */}
            {formState.code.length > 0 && formState.code.length < 6 && (
              <p className="flex items-center justify-center gap-1 text-xs text-amber-400">
                <AlertCircle className="size-3" />
                Enter all 6 digits
              </p>
            )}

            {formState.code.length === 6 && (
              <p className="flex items-center justify-center gap-1 text-xs text-green-400">
                <CheckCircle className="size-3" />
                Code complete
              </p>
            )}
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-slate-300">
              New Password
            </label>
            <div className="group relative">
              <Lock className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                id="password"
                name="password"
                type={formState.showPassword ? 'text' : 'password'}
                placeholder="Enter your new password"
                autoComplete="new-password"
                value={formState.password}
                onChange={(e) => updateFormState({ password: e.target.value })}
                required
                disabled={resetMutation.isPending}
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-12 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 disabled:cursor-not-allowed disabled:opacity-50"
                aria-describedby="password-requirements"
              />
              <button
                type="button"
                onClick={() => updateFormState({ showPassword: !formState.showPassword })}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-purple-400"
                aria-label={formState.showPassword ? 'Hide password' : 'Show password'}
              >
                {formState.showPassword ? (
                  <EyeOff className="size-4" />
                ) : (
                  <Eye className="size-4" />
                )}
              </button>
            </div>

            {/* Password requirements */}
            {formState.password && (
              <div
                id="password-requirements"
                className="mt-2 rounded-lg border border-slate-700/50 bg-slate-800/30 p-3"
              >
                <p className="mb-2 text-xs text-slate-400">Password requirements:</p>
                <div className="space-y-1">
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.minLength ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="size-3" />
                    At least 8 characters
                  </div>
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.hasNumber ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="size-3" />
                    Contains a number
                  </div>
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.hasSpecial ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="size-3" />
                    Contains a special character
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-300">
              Confirm New Password
            </label>
            <div className="group relative">
              <Lock className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={formState.showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your new password"
                autoComplete="new-password"
                value={formState.confirmPassword}
                onChange={(e) => updateFormState({ confirmPassword: e.target.value })}
                required
                disabled={resetMutation.isPending}
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-12 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 disabled:cursor-not-allowed disabled:opacity-50"
                aria-describedby={
                  !passwordsMatch && formState.confirmPassword
                    ? 'confirm-password-error'
                    : undefined
                }
              />
              <button
                type="button"
                onClick={() =>
                  updateFormState({ showConfirmPassword: !formState.showConfirmPassword })
                }
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-purple-400"
                aria-label={
                  formState.showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'
                }
              >
                {formState.showConfirmPassword ? (
                  <EyeOff className="size-4" />
                ) : (
                  <Eye className="size-4" />
                )}
              </button>
            </div>

            {/* Password matching validation */}
            {formState.confirmPassword && !passwordsMatch && (
              <p
                id="confirm-password-error"
                className="flex items-center gap-1 text-xs text-red-400"
              >
                <AlertCircle className="size-3" />
                Passwords do not match
              </p>
            )}

            {formState.confirmPassword && passwordsMatch && (
              <p className="flex items-center gap-1 text-xs text-green-400">
                <CheckCircle className="size-3" />
                Passwords match
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || !canSubmit}
            className="mt-6 w-full rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 py-4 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-[1.01] hover:from-purple-500 hover:to-purple-600 hover:shadow-xl hover:shadow-purple-500/25 active:scale-[0.99] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100"
            aria-label="Reset password"
          >
            {csrfLoading || resetMutation.isPending ? (
              <div className="flex items-center justify-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                {csrfLoading ? 'Loading...' : 'Resetting Password...'}
              </div>
            ) : (
              <>
                <Key className="mr-2 inline size-4" aria-hidden="true" />
                Reset Password
              </>
            )}
          </button>
        </form>

        {/* Navigation Links */}
        <div className="mt-8 space-y-4">
          {/* Back to Sign In */}
          <div className="flex items-center justify-center gap-2 text-center text-sm text-slate-400">
            <ArrowLeft className="size-4" aria-hidden="true" />
            <span>Back to</span>
            <Link
              href="/signin"
              className="font-medium text-purple-400 underline underline-offset-2 transition-colors duration-200 hover:text-purple-300"
            >
              Sign In
            </Link>
          </div>

          {/* Try different email */}
          <div className="text-center text-sm text-slate-500">
            Need to use a different email?{' '}
            <Link
              href="/forgot-password"
              className="font-medium text-purple-400 underline underline-offset-2 transition-colors duration-200 hover:text-purple-300"
            >
              Start over
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
