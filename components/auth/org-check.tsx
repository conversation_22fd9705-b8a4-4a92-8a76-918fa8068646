'use client';

import React from 'react';
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  useUserOrganizations,
  useAcceptInvitation,
  useDeclineInvitation,
  useUserProfile,
} from '@/hooks/use-api';
import { usePermissions } from '@/hooks/use-permissions';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Mail, UserPlus, CheckCircle, XCircle, Loader2, LogOut } from 'lucide-react';
import { logout } from '@/app/(auth)/actions';
import { toast } from 'sonner';
import { getQueryClient } from '@/lib/query-client';

interface Organization {
  orgId: string;
  orgName: string;
  role: string;
  accepted: boolean;
  invited: boolean;
  description?: string;
}

export function OrgCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastEmail, setLastEmail] = useState<string | null>(null);
  const queryClient = getQueryClient();

  // Use cached org data from permissions hook
  const { orgRoles, queryData: permissionsData, loading: permissionsLoading } = usePermissions();

  // Fetch user profile for email (with background updates)
  const {
    data: profile,
    isLoading: profileLoading,
    isFetching: profileFetching,
  } = useUserProfile();

  // Fetch user organizations (with background updates)
  const {
    data: orgsData,
    isLoading: orgsLoading,
    isFetching: orgsFetching,
    refetch: refetchOrganizations,
  } = useUserOrganizations(0, 50);

  const { mutate: acceptInvitation } = useAcceptInvitation();
  const { mutate: declineInvitation } = useDeclineInvitation();

  // Detect user changes and invalidate caches
  useEffect(() => {
    if (profile?.email && lastEmail && profile.email !== lastEmail) {

      // Clear localStorage to ensure no stale org data
      if (globalThis.window !== undefined) {
        const savedOrgId = localStorage.getItem('currentOrgId');
        if (savedOrgId) {
          localStorage.removeItem('currentOrgId');
        }
      }

      // Clear all organization-related queries
      queryClient.invalidateQueries({ queryKey: ['userOrganizations'] });
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      queryClient.invalidateQueries({ queryKey: ['userOrgRoles'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });

      // Remove all query data to force fresh fetch
      queryClient.removeQueries({ queryKey: ['userOrganizations'] });
      queryClient.removeQueries({ queryKey: ['permissions'] });

      // Force refetch organizations
      refetchOrganizations();
    }

    if (profile?.email) {
      setLastEmail(profile.email);
    }
  }, [profile?.email, lastEmail, queryClient, refetchOrganizations]);

  // Create organizations from cached org roles if available, otherwise use API data
  const organizations = useMemo(() => {
    // If we have API data, use it as it contains both accepted and pending invitations
    if (orgsData?.organizations) {
      return orgsData.organizations;
    }

    // Otherwise, use cached org roles (only accepted organizations)
    if (orgRoles && Object.keys(orgRoles).length > 0) {
      return Object.values(orgRoles).map((org) => ({
        orgId: org.orgId,
        orgName: org.orgName,
        role: org.role,
        accepted: true, // If it's in orgRoles cache, it's accepted
        invited: false,
      }));
    }

    return [];
  }, [orgRoles, orgsData]);

  const acceptedOrgs = organizations.filter((org: Organization) => org.accepted);
  const pendingInvitations = organizations.filter(
    (org: Organization) => !org.accepted && org.invited,
  );

  const handleAcceptInvite = async (org: Organization) => {
    if (!profile?.email) {
      toast.error('Unable to get user email');
      return;
    }

    setIsProcessing(true);
    acceptInvitation(
      { orgId: org.orgId, email: profile.email },
      {
        onSuccess: () => {
          toast.success(`Successfully joined ${org.orgName}!`);
          refetchOrganizations();
          // Redirect to dashboard after accepting
          setTimeout(() => {
            router.push('/');
            router.refresh();
          }, 1000);
        },
        onError: (error) => {
          toast.error('Failed to accept invitation');
          console.error('Accept invitation error:', error);
        },
        onSettled: () => {
          setIsProcessing(false);
        },
      },
    );
  };

  const handleDeclineInvite = async (org: Organization) => {
    if (!profile?.email) {
      toast.error('Unable to get user email');
      return;
    }

    setIsProcessing(true);
    declineInvitation(
      { orgName: org.orgName, email: profile.email },
      {
        onSuccess: () => {
          toast.success('Invitation declined');
          refetchOrganizations();
        },
        onError: (error) => {
          toast.error('Failed to decline invitation');
          console.error('Decline invitation error:', error);
        },
        onSettled: () => {
          setIsProcessing(false);
        },
      },
    );
  };

  const handleLogout = async () => {
    try {
      queryClient.removeQueries({ queryKey: ['session'] });
      queryClient.removeQueries({ queryKey: ['permissions', 'current', 'org-aware', 'no-org'] });
      queryClient.removeQueries({ queryKey: ['userProfile'] });
      queryClient.removeQueries({ queryKey: ['userOrganizations'] });
      queryClient.clear();
      queryClient.removeQueries();
      queryClient.cancelQueries();
      await logout();

      if (globalThis.window !== undefined) {
        localStorage.clear();
        sessionStorage.clear();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading only on initial load when we have no data at all
  const isInitialLoad = (orgsLoading || profileLoading) && !orgsData && !profile;

  if (isInitialLoad) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto mb-4 size-8 animate-spin text-purple-500" />
          <p className="text-slate-400">Loading organization data...</p>
        </div>
      </div>
    );
  }

  // User has accepted organizations - show normal content
  if (acceptedOrgs.length > 0) {
    return (
      <div className="relative">
        {/* Subtle background loading indicator */}
        {(orgsFetching || profileFetching) && (
          <div className="fixed inset-x-0 top-0 z-50 h-1 bg-purple-500/20">
            <div className="h-full animate-pulse bg-purple-500" style={{ width: '30%' }} />
          </div>
        )}
        {children}
      </div>
    );
  }

  // User has pending invitations or no organizations
  if (pendingInvitations.length > 0) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Card className="w-full max-w-2xl border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420]">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl bg-purple-600/20">
              <UserPlus className="size-8 text-purple-400" />
            </div>
            <CardTitle className="text-2xl font-bold text-white">
              Welcome! You have organization invitations
            </CardTitle>
            <CardDescription className="text-slate-400">
              Accept an invitation to get started with qBraid
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {pendingInvitations.map((org: Organization) => (
                <Card key={org.orgId} className="border-slate-700 bg-slate-800/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex size-10 items-center justify-center rounded-lg bg-purple-600/20">
                          <Building2 className="size-5 text-purple-400" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">{org.orgName}</h3>
                          <div className="text-sm text-slate-400">
                            Role:{' '}
                            <Badge variant="secondary" className="ml-1">
                              {org.role}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => handleAcceptInvite(org)}
                          disabled={isProcessing}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="mr-1 size-4" />
                          Accept
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeclineInvite(org)}
                          disabled={isProcessing}
                          className="border-red-600 text-red-600 hover:bg-red-600/10"
                        >
                          <XCircle className="mr-1 size-4" />
                          Decline
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="border-t border-slate-700 pt-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-400">
                  Need help? Contact your organization admin
                </div>
                <Button variant="ghost" size="sm" onClick={handleLogout} className="text-slate-400">
                  <LogOut className="mr-2 size-4" />
                  Sign Out
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User has no organizations and no invitations
  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420]">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl bg-purple-600/20">
            <Mail className="size-8 text-purple-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-white">No Organizations Yet</CardTitle>
          <CardDescription className="text-slate-400">
            You need to be invited to an organization to access qBraid
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-slate-800/50 p-4 text-center">
            <p className="mb-2 text-sm text-slate-300">
              Please contact your organization administrator to receive an invitation.
            </p>
            <p className="text-xs text-slate-500">
              Once invited, you&apos;ll receive an email with instructions to join.
            </p>
          </div>

          <Button variant="outline" onClick={handleLogout} className="w-full">
            <LogOut className="mr-2 size-4" />
            Sign out
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
