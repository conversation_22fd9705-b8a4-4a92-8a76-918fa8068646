'use client';

import React from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { LoaderThree } from '@/components/ui/loader';
import { Permission } from '@/types/auth';
import { apiClient } from '@/hooks/use-api';

interface RoleLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showBackgroundUpdates?: boolean; // New prop to optionally show background loading
}

/**
 * Component that ensures roles are loaded before showing children
 * Only shows full loading screen on initial load - subsequent navigations use cached data
 */
export function RoleLoader({ children, fallback, showBackgroundUpdates = false }: RoleLoaderProps) {

  const { loading, error, permissions, queryData, isRefreshing, orgRoles } = usePermissions();


  // Only show full loading screen if we have no data at all (initial load)
  const isInitialLoad = loading && !queryData;


  // Show custom fallback if provided (only for initial load)
  if (fallback && isInitialLoad) {
    return <>{fallback}</>;
  }

  // Show loading state ONLY on initial load when we have no cached data
  if (isInitialLoad) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="space-y-6 text-center">
          {/* Simple animated logo */}
          <div className="flex justify-center">
            <LoaderThree />
          </div>

          {/* Clean text */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-foreground">Loading workspace</h2>
            <p className="text-sm text-muted-foreground">Fetching your permissions</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state only if we have no cached data and an error occurred
  if (error && !orgRoles && !queryData) {

    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="max-w-sm space-y-6 text-center">
          {/* Simple error icon */}
          <div className="flex justify-center">
            <div className="flex size-12 items-center justify-center rounded-full bg-red-500/10">
              <svg
                className="size-6 text-red-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          {/* Error message */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-foreground">Connection failed</h2>
            <p className="text-sm text-muted-foreground">Unable to load your account data</p>
          </div>

          {/* Retry button */}
          <button
            onClick={() => globalThis.location.reload()}
            className="rounded-lg bg-brand px-6 py-2 text-sm text-brand-foreground transition-colors hover:bg-brand/90"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }


  // Always render children if we have any data (cached or fresh)
  // This allows instant navigation between pages using cached permission data
  return (
    <div className="relative">
      {/* Subtle background loading indicator */}
      {showBackgroundUpdates && (loading || isRefreshing) && queryData && (
        <div className="fixed inset-x-0 top-0 z-50 h-1 bg-blue-500/20">
          <div className="h-full animate-pulse bg-blue-500" style={{ width: '30%' }} />
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * Lightweight permission checker for selective use
 * Use this instead of RoleLoader when you only need to protect specific components
 */
interface PermissionWrapperProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredOrgRoles?: { [orgId: string]: string }; // Changed from simple roles to org-specific roles
  fallback?: React.ReactNode;
  showSkeleton?: boolean;
}

export function PermissionWrapper({
  children,
  requiredPermissions = [],
  requiredOrgRoles = {},
  fallback,
  showSkeleton = true,
}: PermissionWrapperProps) {
  const { loading, permissions, queryData, orgRoles } = usePermissions();

  // If we have cached data, proceed immediately without loading state
  if (queryData) {
    const hasRequiredPermissions =
      requiredPermissions.length === 0 || requiredPermissions.some((p) => permissions.includes(p));

    // Check org-specific roles
    const hasRequiredOrgRoles =
      Object.keys(requiredOrgRoles).length === 0 ||
      Object.entries(requiredOrgRoles).every(
        ([orgId, requiredRole]) => orgRoles?.[orgId]?.role === requiredRole,
      );

    if (hasRequiredPermissions && hasRequiredOrgRoles) {
      return <>{children}</>;
    }

    return (
      fallback || (
        <div className="py-4 text-center text-gray-500">
          <p>You don&apos;t have permission to view this content.</p>
        </div>
      )
    );
  }

  // Only show loading for initial fetch
  if (loading && showSkeleton) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 w-3/4 rounded bg-gray-300"></div>
        <div className="h-4 w-1/2 rounded bg-gray-300"></div>
      </div>
    );
  }

  // Default fallback during loading
  return fallback || <div>Loading...</div>;
}

/**
 * Lightweight direct role checker - bypasses complex caching
 * Use this for simple role checks without full RBAC complexity
 */
interface DirectRoleCheckProps {
  children: React.ReactNode;
  requiredRole?: string;
  orgId?: string;
  fallback?: React.ReactNode;
}

export function DirectRoleCheck({ children, requiredRole, orgId, fallback }: DirectRoleCheckProps) {
  const [userRole, setUserRole] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    if (orgId) {
      // Direct API call - bypasses complex system
      apiClient(`/api/user/roles?orgId=${orgId}`)
        .then((data) => {
          setUserRole(data.currentUserRole || null);
          setLoading(false);
        })
        .catch(() => {
          setUserRole(null);
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [orgId]);

  if (loading) {
    return <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />;
  }

  if (requiredRole && userRole !== requiredRole) {
    return fallback || <div className="text-gray-500">Access denied</div>;
  }

  return <>{children}</>;
}
