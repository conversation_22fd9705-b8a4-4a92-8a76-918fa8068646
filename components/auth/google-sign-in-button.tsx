'use client';
import Image from 'next/image';
import { useState } from 'react';
import { signInWithRedirect } from 'aws-amplify/auth';
import { useAuth } from './auth-provider';

export function GoogleSignInButton() {
  const [isLoading, setIsLoading] = useState(false);
  const { isAmplifyConfigured } = useAuth();

  // Check if OAuth environment variables are configured
  const isOAuthConfigured = !!(
    process.env.NEXT_PUBLIC_QBRAID_COGNITO_DOMAIN &&
    process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID &&
    process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID
  );

  const handleGoogleSignIn = async () => {
    if (!isAmplifyConfigured) {
      console.error('Amplify not configured yet');
      return;
    }

    if (!isOAuthConfigured) {
      alert('Google OAuth is not configured. Please contact support.');
      return;
    }

    try {
      setIsLoading(true);
      
      // Redirect to Google OAuth via Cognito hosted UI
      await signInWithRedirect({
        provider: 'Google',
      });
    } catch (error) {
      console.error('❌ [AUTH] Google sign-in error:', error);

      // Show user-friendly error message
      if (error instanceof Error && error.message.includes('oauth param not configured')) {
        alert('Google OAuth is not properly configured. Please contact support.');
      } else {
        alert('Failed to sign in with Google. Please try again.');
      }

      setIsLoading(false);
    }
  };

  // Show disabled state if OAuth is not configured
  const isDisabled = isLoading || !isAmplifyConfigured || !isOAuthConfigured;

  return (
    <button
      type="button"
      onClick={handleGoogleSignIn}
      disabled={isDisabled}
      className="group flex h-12 items-center justify-center rounded-xl border border-slate-600/50 bg-slate-800/30 transition-all duration-200 hover:bg-slate-700/50 disabled:cursor-not-allowed disabled:opacity-50"
      title={isOAuthConfigured ? 'Sign in with Google' : 'Google OAuth not configured'}
    >
      {isLoading ? (
        <div className="size-5 animate-spin rounded-full border-2 border-slate-400/30 border-t-slate-400" />
      ) : (
        <>
          <Image
            src="https://www.svgrepo.com/show/475656/google-color.svg"
            alt="Google"
            className="size-5 transition-transform group-hover:scale-110"
            width={20}
            height={20}
          />
          <span className="ml-2 text-sm font-medium text-slate-300">
            {isOAuthConfigured ? 'Google' : 'Google (Not Configured)'}
          </span>
        </>
      )}
    </button>
  );
}
