'use client';

import { useActionState, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Lock, Mail, User, AlertCircle, Eye, EyeOff, CheckCircle, Circle } from 'lucide-react';
import Link from 'next/link';
import { registerUser } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';
import { AuthResult } from '@/types/auth';
import { GoogleSignInButton } from '@/components/auth/google-sign-in-button';
import { useAuth } from './auth-provider';
import { usePasswordValidation } from '@/hooks/use-password-validation';
import { CardSpotlight } from '@/components/ui/card-spotlight';
import Image from 'next/image';
const initialState: AuthResult = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

export function SignUpForm() {
  const [state, formAction] = useActionState(registerUser, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Use custom hook for password validation (no useEffect)
  const passwordValidation = usePasswordValidation(password, confirmPassword);

  // Handle navigation on success with useEffect to avoid render-time router updates
  useEffect(() => {
    if (state.success && state.nextStep === 'verify' && state.email) {
      const verifyUrl = `/verify?email=${encodeURIComponent(state.email)}`;
      router.push(verifyUrl);
    }
  }, [state.success, state.nextStep, state.email, router]);

  const displayError = state.error || csrfError;

  return (
    <CardSpotlight data-testid="signup-form" className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
      <div className="p-10 lg:p-12">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-6 flex size-16 items-center justify-center   rounded-2xl ">
            <Image
              src="/qbraid_logo.png"
              alt="qBraid"
              className="h-16 w-auto opacity-90"
              width={64}
              height={64}
              quality={100}
              priority
            />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">Join qBraid</h1>
          <p className="text-sm text-slate-400">Start building the future with quantum computing</p>
        </div>

        {/* Form */}
        <form action={formAction} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 rounded-xl border border-red-500/20 bg-red-500/10 p-4 backdrop-blur-sm">
              <AlertCircle className="mt-0.5 size-5 shrink-0 text-red-400" />
              <p className="text-sm text-red-400">{displayError}</p>
            </div>
          )}

          {/* Full Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">Full Name</label>
            <div className="group relative">
              <User className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                data-testid="name-input"
                name="name"
                type="text"
                placeholder="Enter your full name"
                required
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-4 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
            </div>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">Email Address</label>
            <div className="group relative">
              <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                data-testid="email-input"
                name="email"
                type="email"
                placeholder="Enter your email"
                required
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-4 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
            </div>
          </div>

          {/* Password */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">Password</label>
            <div className="group relative">
              <Lock className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                data-testid="password-input"
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Create a strong password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-12 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-300"
              >
                {showPassword ? <EyeOff className="size-4" /> : <Eye className="size-4" />}
              </button>
            </div>

            {/* Password Requirements */}
            {password && (
              <div className="space-y-2 rounded-lg border border-slate-700/50 bg-slate-800/30 p-3">
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.minLength ? (
                    <CheckCircle className="size-3 text-green-400" />
                  ) : (
                    <Circle className="size-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.minLength ? 'text-green-400' : 'text-slate-400'}
                  >
                    At least 8 characters
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.hasNumber ? (
                    <CheckCircle className="size-3 text-green-400" />
                  ) : (
                    <Circle className="size-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.hasNumber ? 'text-green-400' : 'text-slate-400'}
                  >
                    Contains a number
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.hasSpecial ? (
                    <CheckCircle className="size-3 text-green-400" />
                  ) : (
                    <Circle className="size-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.hasSpecial ? 'text-green-400' : 'text-slate-400'}
                  >
                    Contains a special character
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">Confirm Password</label>
            <div className="group relative">
              <Lock className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                data-testid="confirm-password-input"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className={`w-full rounded-xl border bg-slate-800/50 py-3.5 pl-10 pr-12 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 ${
                  confirmPassword && !passwordValidation.match
                    ? 'border-red-500/50 focus:border-red-500'
                    : (confirmPassword && passwordValidation.match
                      ? 'border-green-500/50 focus:border-green-500'
                      : 'border-slate-600/50 focus:border-purple-500')
                }`}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-300"
              >
                {showConfirmPassword ? <EyeOff className="size-4" /> : <Eye className="size-4" />}
              </button>
            </div>
            {confirmPassword && !passwordValidation.match && (
              <p className="flex items-center gap-1 text-xs text-red-400">
                <AlertCircle className="size-3" />
                Passwords don&apos;t match
              </p>
            )}
            {confirmPassword && passwordValidation.match && (
              <p className="flex items-center gap-1 text-xs text-green-400">
                <CheckCircle className="size-3" />
                Passwords match
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            data-testid="signup-submit-button"
            type="submit"
            disabled={csrfLoading || !csrfToken || !passwordValidation.isFormValid}
            className="mt-6 w-full rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 py-4 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-[1.01] hover:from-purple-500 hover:to-purple-600 hover:shadow-xl hover:shadow-purple-500/25 active:scale-[0.99] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100"
          >
            {csrfLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                Loading...
              </div>
            ) : (
              'Create Account'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="my-8 flex items-center">
          <div className="flex-1 border-t border-slate-600/50" />
          <span className="px-4 text-xs font-medium text-slate-500">Or continue with</span>
          <div className="flex-1 border-t border-slate-600/50" />
        </div>

        {/* Social Buttons */}
        <div className="mb-8  grid gap-3">
          <GoogleSignInButton />
        </div>

        {/* Sign In Link */}
        <p className="text-center text-sm text-slate-400">
          Already have an account?{' '}
          <Link
            href="/signin"
            className="font-semibold text-purple-400 transition-colors hover:text-purple-300 hover:underline"
          >
            Sign in
          </Link>
        </p>
        <p className="mt-10 text-center text-sm text-slate-400">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </CardSpotlight>
  );
}
