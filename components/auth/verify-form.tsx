'use client';

import { useEffect, useState, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, Mail, AlertCircle, ExternalLink, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { apiClient } from '@/hooks/use-api';

export function VerifyForm() {
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get email from URL parameters
  const emailParam = searchParams.get('email');
  const email = emailParam ? decodeURIComponent(emailParam) : '';

  const handleVerificationLink = useCallback(
    async (username: string, confirmationCode: string) => {
      try {
        const result = await apiClient('/api/auth/verify-link', {
          method: 'POST',
          body: JSON.stringify({ username, confirmationCode }),
        });

        if (result.success) {
          router.push('/signin?message=Email verified successfully! You can now sign in.');
        } else {
          console.error('❌ [VERIFY] Verification failed:', result.error);
          router.push(
            `/verify?email=${encodeURIComponent(username)}&error=${encodeURIComponent(result.error)}`,
          );
        }
      } catch (error) {
        console.error('❌ [VERIFY] Verification error:', error);
        router.push(`/verify?email=${encodeURIComponent(username)}&error=Verification failed`);
      }
    },
    [router],
  );

  // Handle verification link callback parameters
  useEffect(() => {
    const confirmationCode = searchParams.get('confirmation_code');
    const username = searchParams.get('username');

    if (confirmationCode && username) {
      // This is a verification link callback
      handleVerificationLink(username, confirmationCode);
    }
  }, [searchParams, handleVerificationLink]);

  const handleResendLink = async () => {
    if (!email) return;

    setIsResending(true);
    try {
      const result = await apiClient('/api/auth/resend-verification', {
        method: 'POST',
        body: JSON.stringify({ email }),
      });

      if (result.success) {
        setResendSuccess(true);
        setTimeout(() => setResendSuccess(false), 5000);
      }
    } catch (error) {
      console.error('Failed to resend verification link:', error);
    } finally {
      setIsResending(false);
    }
  };

  const verificationError = searchParams.get('error');

  return (
    <div className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
      <div className="p-10 lg:p-12">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25">
            <Mail className="size-8 text-white" />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">Check Your Email</h1>
          <p className="text-sm text-slate-400">Click the verification link sent to your email</p>
        </div>

        {/* Error Alert */}
        {verificationError && (
          <div className="mb-6 flex items-start gap-3 rounded-xl border border-red-500/20 bg-red-500/10 p-4 backdrop-blur-sm">
            <AlertCircle className="mt-0.5 size-5 shrink-0 text-red-400" />
            <p className="text-sm text-red-400">{verificationError}</p>
          </div>
        )}

        {/* Success message for resend */}
        {resendSuccess && (
          <div className="mb-6 flex items-start gap-3 rounded-xl border border-green-500/20 bg-green-500/10 p-4 backdrop-blur-sm">
            <CheckCircle className="mt-0.5 size-5 shrink-0 text-green-400" />
            <p className="text-sm text-green-400">Verification link sent! Check your email.</p>
          </div>
        )}

        {/* Email Address */}
        {email && (
          <div className="mb-8 space-y-2">
            <label className="block text-sm font-medium text-slate-300">Email Address</label>
            <div className="group relative">
              <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500" />
              <input
                type="email"
                value={email}
                disabled
                className="w-full cursor-not-allowed rounded-xl border border-slate-600/50 bg-slate-800/30 py-3.5 pl-10 pr-4 text-slate-400"
              />
            </div>
            <p className="flex items-center gap-1 text-xs text-slate-500">
              <CheckCircle className="size-3" />
              Verification link sent to this email address
            </p>
          </div>
        )}

        {/* Instructions */}
        <div className="mb-6 rounded-xl border border-slate-700/50 bg-slate-800/30 p-6">
          <div className="flex items-start gap-3">
            <ExternalLink className="mt-0.5 size-5 shrink-0 text-purple-400" />
            <div>
              <h3 className="mb-2 text-sm font-semibold text-white">Next Steps</h3>
              <ol className="list-inside list-decimal space-y-1 text-sm text-slate-400">
                <li>Check your email inbox (and spam folder)</li>
                <li>Look for an email from qBraid</li>
                <li>Click the &quot;Verify Email&quot; link in the email</li>
                <li>You&apos;ll be automatically redirected to sign in</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Resend Link */}
        {email && (
          <div className="mb-6 text-center">
            <button
              onClick={handleResendLink}
              disabled={isResending}
              className="inline-flex items-center gap-2 px-4 py-2 text-sm text-purple-400 transition-colors hover:text-purple-300 disabled:opacity-50"
            >
              <RefreshCw className={`size-4 ${isResending ? 'animate-spin' : ''}`} />
              {isResending ? 'Sending...' : 'Resend verification link'}
            </button>
          </div>
        )}

        {/* Help Text */}
        <div className="mt-8 rounded-xl border border-slate-700/50 bg-slate-800/30 p-4">
          <p className="text-center text-sm text-slate-400">
            Still having trouble?{' '}
            <Link
              href="/signup"
              className="font-semibold text-purple-400 transition-colors hover:text-purple-300 hover:underline"
            >
              Try signing up again
            </Link>
          </p>
        </div>

        {/* Back to Sign In */}
        <p className="mt-6 text-center text-sm text-slate-400">
          Remember your password?{' '}
          <Link
            href="/signin"
            className="font-semibold text-purple-400 transition-colors hover:text-purple-300 hover:underline"
          >
            Sign in instead
          </Link>
        </p>
        <p className="mt-10 text-center text-sm text-slate-400">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </div>
  );
}
