'use client';

import React from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { Loader2, Shield, AlertCircle } from 'lucide-react';

interface OrgRoleGuardProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  orgId?: string; // If not provided, uses current org context
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  showRoleInfo?: boolean; // Show current role info for debugging
  requireAny?: boolean; // If true, user needs ANY of the required roles. If false, needs ALL
}

/**
 * Organization-aware role guard component
 * More performant and cleaner than the existing role checking systems
 * Uses current organization context and provides better UX
 */
export function OrgRoleGuard({
  children,
  requiredRoles = [],
  orgId,
  fallback,
  loadingFallback,
  showRoleInfo = false,
  requireAny = true,
}: OrgRoleGuardProps) {
  const { getOrganizations, getCurrentOrgContext, getUserRoleInOrg, loading, error } =
    usePermissions();

  // Use provided orgId or fall back to current context
  const targetOrgId = orgId || getCurrentOrgContext();
  const organizations = getOrganizations();
  const currentOrg = organizations.find((org) => org.orgId === targetOrgId);
  const userRole = currentOrg?.role || getUserRoleInOrg(targetOrgId || '');

  // Force re-render when organization changes
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);

  React.useEffect(() => {
    const handleOrgChange = () => {
      forceUpdate();
    };

    globalThis.addEventListener('org-context-changed', handleOrgChange);
    return () => {
      globalThis.removeEventListener('org-context-changed', handleOrgChange);
    };
  }, []);

  const hasAccess = React.useMemo(() => {
    return userRole
      ? (requireAny
        ? requiredRoles.length === 0 || requiredRoles.includes(userRole)
        : requiredRoles.every((role) => role === userRole))
      : false;
  }, [userRole, requiredRoles, requireAny]);

  // Show loading state
  if (loading) {
    return (
      loadingFallback || (
        <div className="flex items-center gap-2 p-4 text-sm text-gray-500">
          <Loader2 className="size-4 animate-spin" />
          <span>Checking permissions...</span>
        </div>
      )
    );
  }

  // Show error state
  if (error) {
    return (
      fallback || (
        <div className="flex items-center gap-2 p-4 text-sm text-red-500">
          <AlertCircle className="size-4" />
          <span>Unable to verify permissions</span>
        </div>
      )
    );
  }

  // No organization context
  if (!targetOrgId || !currentOrg) {
    return (
      fallback || (
        <div className="flex items-center gap-2 p-4 text-sm text-yellow-600">
          <Shield className="size-4" />
          <span>No organization selected</span>
        </div>
      )
    );
  }

  if (!hasAccess) {
    return (
      fallback || (
        <div className="flex flex-col gap-2 p-4 text-sm text-gray-500 border border-gray-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Shield className="size-4" />
            <span>Access restricted</span>
          </div>
          {showRoleInfo && (
            <div className="text-xs text-gray-400">
              <p>
                Your role: <span className="font-medium">{userRole || 'none'}</span>
              </p>
              <p>
                Required: <span className="font-medium">{requiredRoles.join(', ') || 'any'}</span>
              </p>
              <p>
                Organization: <span className="font-medium">{currentOrg.orgName}</span>
              </p>
            </div>
          )}
        </div>
      )
    );
  }

  // Render children with optional role info
  return (
    <div className="relative">
      {showRoleInfo && (
        <div className="mb-2 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
          <div className="flex items-center gap-1">
            <Shield className="size-3" />
            <span>
              Access granted as <strong>{userRole}</strong> in <strong>{currentOrg.orgName}</strong>
            </span>
          </div>
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * Hook for checking organization roles programmatically
 */
export function useOrgRole(orgId?: string) {
  const { getOrganizations, getCurrentOrgContext, getUserRoleInOrg, loading, error } =
    usePermissions();

  const targetOrgId = orgId || getCurrentOrgContext();
  const organizations = getOrganizations();
  const currentOrg = organizations.find((org) => org.orgId === targetOrgId);
  const userRole = currentOrg?.role || getUserRoleInOrg(targetOrgId || '');

  // Add a key that changes when organization changes to force re-renders
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);

  React.useEffect(() => {
    // Force update when organization context changes
    const handleOrgChange = () => {
      forceUpdate();
    };

    globalThis.addEventListener('org-context-changed', handleOrgChange);
    return () => {
      globalThis.removeEventListener('org-context-changed', handleOrgChange);
    };
  }, []);

  const hasRole = (role: string): boolean => {
    return userRole === role;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return userRole ? roles.includes(userRole) : false;
  };

  const isAdmin = (): boolean => {
    return hasAnyRole(['admin', 'owner']);
  };

  const canManage = (): boolean => {
    return hasAnyRole(['admin', 'owner']);
  };

  const canView = (): boolean => {
    return hasAnyRole(['admin', 'owner', 'member', 'viewer']);
  };

  return {
    loading,
    error,
    orgId: targetOrgId,
    organization: currentOrg,
    role: userRole,
    hasRole,
    hasAnyRole,
    isAdmin,
    canManage,
    canView,
  };
}

/**
 * Lightweight role checker for conditional rendering
 */
interface RoleCheckProps {
  role?: string;
  roles?: string[];
  orgId?: string;
  requireAny?: boolean;
  children: (hasAccess: boolean, userRole: string | null, org: any) => React.ReactNode;
}

export function RoleCheck({
  role,
  roles = [],
  orgId,
  requireAny = true,
  children,
}: RoleCheckProps) {
  const { role: userRole, organization, loading } = useOrgRole(orgId);

  if (loading) {
    return children(false, null, null);
  }

  const requiredRoles = role ? [role] : roles;
  const hasAccess = Boolean(
    userRole &&
      (requiredRoles.length === 0 ||
        (requireAny
          ? requiredRoles.includes(userRole)
          : requiredRoles.every((r) => r === userRole))),
  );

  return <>{children(hasAccess, userRole, organization)}</>;
}
