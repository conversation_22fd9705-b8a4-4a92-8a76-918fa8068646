'use client';

import React from 'react';
import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Mail, Send, AlertCircle, ArrowLeft, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { initiatePasswordReset } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';

interface FormState {
  email: string;
}

const INITIAL_FORM_STATE: FormState = {
  email: '',
};

export function ForgotPasswordForm() {
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const [formState, setFormState] = useState<FormState>(INITIAL_FORM_STATE);

  // React Query mutation for password reset
  const resetMutation = useMutation({
    mutationFn: async (formData: { email: string; csrfToken: string }) => {
      const formDataObj = new FormData();
      formDataObj.set('email', formData.email);
      formDataObj.set('csrfToken', formData.csrfToken);

      const result = await initiatePasswordReset(null, formDataObj);
      if (!result.success) {
        throw new Error(result.error || 'Failed to send reset code');
      }
      return result;
    },
    onSuccess: (data) => {
      if (data.nextStep === 'reset') {
        // Store email securely in sessionStorage for the reset step
        sessionStorage.setItem('reset-email', formState.email);
        // Navigate to reset password page without email in URL
        router.push('/reset-password');
      }
    },
  });

  // Update form state helper
  const updateFormState = useCallback((updates: Partial<FormState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Form validation
  const isEmailValid =
    formState.email.includes('@') && formState.email.includes('.') && formState.email.length > 5;
  const canSubmit = isEmailValid && !resetMutation.isPending;

  // Handle form submission
  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!canSubmit || !csrfToken) return;

      resetMutation.mutate({
        email: formState.email,
        csrfToken,
      });
    },
    [canSubmit, csrfToken, formState.email, resetMutation],
  );

  const displayError = resetMutation.error?.message || csrfError;
  const isSuccess = resetMutation.isSuccess;

  return (
    <div className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
      <div className="p-10 lg:p-12">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25">
            <Send className="size-8 text-white" />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">Forgot Password</h1>
          <p className="text-sm text-slate-400">
            Enter your email address and we&apos;ll send you a password reset code
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 rounded-xl border border-red-500/20 bg-red-500/10 p-4 backdrop-blur-sm">
              <AlertCircle className="mt-0.5 size-5 shrink-0 text-red-400" />
              <p className="text-sm text-red-400">{displayError}</p>
            </div>
          )}

          {/* Success message */}
          {isSuccess && (
            <div className="flex items-start gap-3 rounded-xl border border-green-500/20 bg-green-500/10 p-4 backdrop-blur-sm">
              <CheckCircle className="mt-0.5 size-5 shrink-0 text-green-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-green-400">Reset code sent!</p>
                <p className="mt-1 text-xs text-green-300">
                  Redirecting you to enter the reset code...
                </p>
              </div>
            </div>
          )}

          {/* Email Address */}
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium text-slate-300">
              Email Address
            </label>
            <div className="group relative">
              <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400" />
              <input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email address"
                autoComplete="email"
                value={formState.email}
                onChange={(e) => updateFormState({ email: e.target.value })}
                required
                disabled={resetMutation.isPending || isSuccess}
                className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-4 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 disabled:cursor-not-allowed disabled:opacity-50"
                aria-describedby={!isEmailValid && formState.email ? 'email-error' : undefined}
              />
            </div>

            {/* Email validation feedback */}
            {formState.email && !isEmailValid && (
              <p id="email-error" className="flex items-center gap-1 text-xs text-red-400">
                <AlertCircle className="size-3" />
                Please enter a valid email address
              </p>
            )}

            {formState.email && isEmailValid && (
              <p className="flex items-center gap-1 text-xs text-green-400">
                <CheckCircle className="size-3" />
                Email format looks good
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || !canSubmit || isSuccess}
            className="mt-6 w-full rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 py-4 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-[1.01] hover:from-purple-500 hover:to-purple-600 hover:shadow-xl hover:shadow-purple-500/25 active:scale-[0.99] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100"
            aria-label="Send password reset code"
          >
            {csrfLoading || resetMutation.isPending ? (
              <div className="flex items-center justify-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                {csrfLoading ? 'Loading...' : 'Sending...'}
              </div>
            ) : (isSuccess ? (
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="size-4" aria-hidden="true" />
                Code Sent!
              </div>
            ) : (
              <>
                <Send className="mr-2 inline size-4" aria-hidden="true" />
                Send Reset Code
              </>
            ))}
          </button>
        </form>

        {/* Help Text */}
        <div className="mt-8 rounded-xl border border-slate-700/50 bg-slate-800/30 p-4">
          <div className="space-y-2 text-center text-sm text-slate-400">
            <p>We&apos;ll send a password reset link to your email address if an account exists.</p>
            <p className="text-xs text-slate-500">
              The code will expire in 15 minutes for security reasons.
            </p>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="mt-6 space-y-4">
          {/* Back to Sign In */}
          <div className="flex items-center justify-center gap-2 text-center text-sm text-slate-400">
            <ArrowLeft className="size-4" aria-hidden="true" />
            <span>Back to</span>
            <Link
              href="/signin"
              className="font-medium text-purple-400 underline underline-offset-2 transition-colors duration-200 hover:text-purple-300"
            >
              Sign In
            </Link>
          </div>

          {/* Create Account */}
          <div className="text-center text-sm text-slate-500">
            Don&apos;t have an account?{' '}
            <Link
              href="/signup"
              className="font-medium text-purple-400 underline underline-offset-2 transition-colors duration-200 hover:text-purple-300"
            >
              Create one here
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
