'use client';

import { useActionState, useState, useEffect, useCallback, useId } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { LogIn, Lock, Mail, AlertCircle, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { authenticateUser } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';
import { GoogleSignInButton } from './google-sign-in-button';
import { useAuth } from './auth-provider';
import { CardSpotlight } from '@/components/ui/card-spotlight';
import Image from 'next/image';
import React from 'react';

const initialState: AuthResult = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

// Custom hook for authentication checking
const useAuthCheck = () => {
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // More robust session checking
        const response = await fetch('/api/auth/session', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.authenticated) {
            router.push('/');
            return;
          }
        }
      } catch (error) {
        console.warn('Auth status check failed:', error);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthStatus();
  }, [router]);

  return isCheckingAuth;
};

// Custom hook for form state
const useSignInForm = () => {
  const [email, setEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  const handleEmailChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEmail(e.target.value);
      if (!hasInteracted) setHasInteracted(true);
    },
    [hasInteracted],
  );

  return {
    email,
    showPassword,
    hasInteracted,
    setEmail,
    togglePasswordVisibility,
    handleEmailChange,
  };
};

// Loading component
const LoadingSpinner = () => (
  <CardSpotlight className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm">
    <div className="p-10 lg:p-12">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="size-8 animate-spin rounded-full border-2 border-purple-500 border-t-transparent mx-auto mb-4" />
          <p className="text-slate-400">Checking authentication status...</p>
        </div>
      </div>
    </div>
  </CardSpotlight>
);

// Error Alert component
interface ErrorAlertProps {
  error: string;
  requiresVerification?: boolean;
  alreadyAuthenticated?: boolean;
  email?: string;
  errorId: string;
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({
  error,
  requiresVerification,
  alreadyAuthenticated,
  email,
  errorId,
}) => {
  const router = useRouter();

  return (
    <div
      id={errorId}
      data-testid="error-alert"
      className="flex items-start gap-3 rounded-xl border border-red-500/20 bg-red-500/10 p-4 backdrop-blur-sm"
      role="alert"
      aria-live="polite"
    >
      <AlertCircle className="mt-0.5 size-5 shrink-0 text-red-400" aria-hidden="true" />
      <div className="flex-1">
        <p className="text-sm text-red-400">
          {error}
          {requiresVerification && (
            <>
              <br />
              <Link
                href={`/verify${email ? `?email=${encodeURIComponent(email)}` : ''}`}
                className="underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
              >
                Go to verification page →
              </Link>
            </>
          )}
          {alreadyAuthenticated && (
            <>
              <br />
              <button
                onClick={() => router.push('/')}
                className="underline hover:no-underline text-red-300 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
              >
                Go to Dashboard →
              </button>
            </>
          )}
        </p>
      </div>
    </div>
  );
};

// Success Message component
const SuccessMessage: React.FC<{ message: string }> = ({ message }) => (
  <div
    className="flex items-start gap-3 rounded-xl border border-green-500/20 bg-green-500/10 p-4 backdrop-blur-sm"
    role="alert"
    aria-live="polite"
  >
    <LogIn className="mt-0.5 size-5 shrink-0 text-green-400" aria-hidden="true" />
    <p className="text-sm text-green-400">{message}</p>
  </div>
);

// Form Field component
interface FormFieldProps {
  id: string;
  label: string;
  type: string;
  placeholder: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  icon: React.ReactNode;
  errorId?: string;
  rightElement?: React.ReactNode;
  'data-testid'?: string;
}

const FormField: React.FC<FormFieldProps> = ({
  id,
  label,
  type,
  placeholder,
  value,
  onChange,
  required = false,
  icon,
  errorId,
  rightElement,
  'data-testid': testId,
}) => (
  <div className="space-y-2">
    <label htmlFor={id} className="block text-sm font-medium text-slate-300">
      {label}
      {required && (
        <span className="text-red-400 ml-1" aria-label="required">
          *
        </span>
      )}
    </label>
    <div className="group relative">
      <div className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-500 transition-colors group-focus-within:text-purple-400">
        {icon}
      </div>
      <input
        id={id}
        data-testid={testId}
        name={id}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        required={required}
        aria-describedby={errorId}
        className="w-full rounded-xl border border-slate-600/50 bg-slate-800/50 py-3.5 pl-10 pr-12 text-white transition-all duration-200 placeholder:text-slate-500 hover:border-slate-500 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
      />
      {rightElement}
    </div>
  </div>
);

// Main component with error boundary
export function SignInForm() {
  const [state, formAction] = useActionState(authenticateUser, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const { handleAuthSuccess } = useAuth();
  const searchParams = useSearchParams();

  const isCheckingAuth = useAuthCheck();
  const { email, showPassword, togglePasswordVisibility, handleEmailChange } = useSignInForm();

  // Generate unique IDs for accessibility
  const errorId = useId();

  const redirect = searchParams.get('redirect') || '/';
  const successMessage = searchParams.get('message');

  // Handle success with useEffect to avoid render-time router updates
  useEffect(() => {
    if (state?.success) {
      // Announce success to screen readers
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      announcement.textContent = 'Sign in successful. Redirecting...';
      document.body.append(announcement);

      setTimeout(() => {
        handleAuthSuccess(state, redirect);
        announcement.remove();
      }, 100);
    }
  }, [state, handleAuthSuccess, redirect]);

  const displayError = state?.error || csrfError;

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return <LoadingSpinner />;
  }

  const handleSubmit = (e: React.FormEvent) => {
    // Let the form action handle submission, but we can add client-side logic here if needed
    if (!csrfToken) {
      e.preventDefault();
      return;
    }
  };

  return (
    <CardSpotlight
      data-testid="signin-form"
      className="w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm"
      colors={[
        [138, 43, 226],
        [75, 0, 130],
      ]}
      lightModeColors={[
        [138, 43, 226],
        [75, 0, 130],
      ]}
    >
      <div className="p-10 lg:p-12">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl">
            <Image
              src="/qbraid_logo.png"
              alt="qBraid"
              className="h-16 w-auto opacity-90"
              width={64}
              height={64}
              priority
            />
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">Sign in to qBraid</h1>
          <p className="text-sm text-slate-400">
            Access your quantum computing dashboard and manage your devices
          </p>
        </div>

        {/* Form */}
        <form action={formAction} onSubmit={handleSubmit} className="space-y-5" noValidate>
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Success Message */}
          {successMessage && <SuccessMessage message={successMessage} />}

          {/* Error Alert */}
          {displayError && (
            <ErrorAlert
              error={displayError}
              requiresVerification={state?.requiresVerification}
              alreadyAuthenticated={state?.alreadyAuthenticated}
              email={email}
              errorId={errorId}
            />
          )}

          {/* Email Field */}
          <FormField
            id="email"
            label="Email Address"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={handleEmailChange}
            required
            icon={<Mail className="size-4" />}
            errorId={displayError ? errorId : undefined}
            data-testid="email-input"
          />

          {/* Password Field */}
          <FormField
            id="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter your password"
            required
            icon={<Lock className="size-4" />}
            errorId={displayError ? errorId : undefined}
            data-testid="password-input"
            rightElement={
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
                data-testid="password-toggle"
              >
                {showPassword ? <EyeOff className="size-4" /> : <Eye className="size-4" />}
              </button>
            }
          />

          {/* Forgot Password Link */}
          <div className="flex justify-end">
            <Link
              href="/forgot-password"
              className="text-sm font-medium text-purple-400 transition-colors hover:text-purple-300 hover:underline focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
            >
              Forgot password?
            </Link>
          </div>

          {/* Submit Button */}
          <button
            data-testid="signin-submit-button"
            type="submit"
            disabled={csrfLoading || !csrfToken}
            className="mt-6 w-full rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 py-4 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-[1.01] hover:from-purple-500 hover:to-purple-600 hover:shadow-xl hover:shadow-purple-500/25 active:scale-[0.99] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-slate-800"
          >
            {csrfLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                <span>Loading...</span>
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="my-8 flex items-center" role="separator">
          <div className="flex-1 border-t border-slate-600/50" />
          <span className="px-4 text-xs font-medium text-slate-500">Or continue with</span>
          <div className="flex-1 border-t border-slate-600/50" />
        </div>

        {/* Social Buttons */}
        <div data-testid="social-signin-buttons" className="mb-8 grid gap-3">
          <GoogleSignInButton />
        </div>

        {/* Sign Up Link */}
        <p className="text-center text-sm text-slate-400">
          Don&apos;t have an account?{' '}
          <Link
            href="/signup"
            className="font-semibold text-purple-400 transition-colors hover:text-purple-300 hover:underline focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
          >
            Sign up
          </Link>
        </p>
        <p className="mt-10 text-center text-sm text-slate-400">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </CardSpotlight>
  );
}
