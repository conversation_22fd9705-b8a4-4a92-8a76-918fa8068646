'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { logout } from '@/app/(auth)/actions';

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

const handleLogout = async () => {
  // Perform the logout, which handles server-side cleanup and redirection
  await logout();
};

export function LogoutButton({
  variant = 'outline',
  size = 'default',
  className,
  children = 'Sign out',
}: LogoutButtonProps) {
  return (
    <Button variant={variant} size={size} className={className} onClick={handleLogout}>
      {children}
    </Button>
  );
}
