import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import type { DeviceData } from '@/types/device';
import { useApproveDevice, useSaveDeviceEdits } from '@/hooks/use-api';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  PROVIDER_OPTIONS,
  RUN_INPUT_TYPE_OPTIONS,
} from '@/components/devices/edit-device-form/constants';
import MultiSelect from '@/components/devices/edit-device-form/multi-select';
import CustomSelect from '@/components/devices/edit-device-form/custom-select';
import { FileText, ExternalLink, Trash2 } from 'lucide-react';
import { apiClient } from '@/hooks/use-api';
import { useOrgContext } from '@/components/org/org-context-provider';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { getQueryClient } from '@/lib/query-client';

interface EditRequestsTabProps {
  editRequests: DeviceData[];
}

export default function EditRequestsTab({ editRequests }: EditRequestsTabProps) {
  const [editEdits, setEditEdits] = useState<Record<string, any> | null>(null);
  const [editingDeviceId, setEditingDeviceId] = useState<string | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState<Record<string, boolean>>({});
  const [pendingApprovals, setPendingApprovals] = useState<Record<string, boolean>>({});
  const [pendingSaves, setPendingSaves] = useState<Record<string, boolean>>({});
  const [pendingDeletes, setPendingDeletes] = useState<Record<string, boolean>>({});
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);

  const { currentOrgId } = useOrgContext();
  const approveDeviceMutation = useApproveDevice();
  const saveDeviceEditsMutation = useSaveDeviceEdits();

  const handleEditFieldChange = (field: string, value: any, device: DeviceData) => {
    if (!editEdits) return;

    // Handle nested pricing fields
    if (field.startsWith('pricing.')) {
      const pricingField = field.split('.')[1];
      const currentPricing = editEdits.pricing || {};
      setEditEdits({
        ...editEdits,
        pricing: {
          ...currentPricing,
          [pricingField]: value,
        },
      });
    } else {
      setEditEdits({ ...editEdits, [field]: value });
    }

    setUnsavedChanges((prev) => ({ ...prev, [device.qrn]: true }));
  };

  const handleSaveChanges = async (device: DeviceData) => {
    if (!editEdits) return;
    setPendingSaves((prev) => ({ ...prev, [device.qrn]: true }));
    try {
      await saveDeviceEditsMutation.mutateAsync({
        deviceId: device.qrn,
        orgId: device.requestedByOrganization?._id || '',
        edits: editEdits,
      });
      toast.success('Changes saved.');
      setUnsavedChanges((prev) => ({ ...prev, [device.qrn]: false }));
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes.');
    } finally {
      setPendingSaves((prev) => ({ ...prev, [device.qrn]: false }));
    }
  };

  const handleEditApprove = async (device: DeviceData) => {
    setPendingApprovals((prev) => ({ ...prev, [device.qrn]: true }));
    try {
      await approveDeviceMutation.mutateAsync(device.qrn);
      toast.success('Edit request approved and applied.');
      setEditingDeviceId(null);
      setEditEdits(null);
      setUnsavedChanges((prev) => ({ ...prev, [device.qrn]: false }));
    } catch (error) {
      console.error('Error approving edit request:', error);
      toast.error('Failed to approve edit request.');
    } finally {
      setPendingApprovals((prev) => ({ ...prev, [device.qrn]: false }));
    }
  };

  const handleDeleteEdit = async (device: DeviceData) => {
    setPendingDeletes((prev) => ({ ...prev, [device.qrn]: true }));
    try {
      await apiClient(`/api/quantum-devices/${device.qrn}/request?orgId=${currentOrgId}`, {
        method: 'DELETE',
      });
      toast.success('Edit request deleted successfully.');
      setShowDeleteDialog(null);

      // Invalidate the adminDevices query to refresh the edit requests list
      const queryClient = getQueryClient();
      queryClient.invalidateQueries({ queryKey: ['adminDevices'] });
    } catch (error) {
      console.error('Error deleting edit request:', error);
      toast.error('Failed to delete edit request.');
    } finally {
      setPendingDeletes((prev) => ({ ...prev, [device.qrn]: false }));
    }
  };

  const renderFieldInput = (field: string, value: any, device: DeviceData) => {
    // Handle nested pricing fields
    let currentValue;
    if (field.startsWith('pricing.')) {
      const pricingField = field.split('.')[1];
      if (editingDeviceId === device.qrn && editEdits && editEdits.pricing) {
        currentValue = editEdits.pricing[pricingField];
      } else if (typeof value === 'object' && value !== null) {
        currentValue = value[pricingField];
      } else {
        currentValue = value;
      }
    } else {
      currentValue = editingDeviceId === device.qrn && editEdits ? editEdits[field] : value;
    }

    const handleChange = (newValue: any) => {
      if (editingDeviceId !== device.qrn) {
        setEditingDeviceId(device.qrn);
        // Initialize editEdits with proper structure for nested objects
        const initialEdits = { ...device.pendingEdits };

        // Ensure pricing object is properly structured
        if (initialEdits.pricing && typeof initialEdits.pricing === 'object') {
          initialEdits.pricing = { ...initialEdits.pricing };
        }

        setEditEdits(initialEdits);
      }
      handleEditFieldChange(field, newValue, device);
    };

    // Device Name - Text input
    if (field === 'name') {
      return (
        <Input
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          placeholder="e.g. SV1"
        />
      );
    }

    // Device Type - Dropdown
    if (field === 'type') {
      return (
        <Select value={currentValue || ''} onValueChange={handleChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select device type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="QPU">QPU</SelectItem>
            <SelectItem value="Simulator">Simulator</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Provider - Dropdown with custom option
    if (field === 'provider') {
      return (
        <CustomSelect
          value={currentValue || ''}
          onValueChange={handleChange}
          placeholder="Select provider"
          options={PROVIDER_OPTIONS.map((provider) => ({ value: provider, label: provider }))}
          customPlaceholder="Enter custom provider..."
          className="flex-1"
        />
      );
    }

    // Device Description - Text input
    if (field === 'deviceDescription') {
      return (
        <Input
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          placeholder="e.g. 11 qubit quantum processor"
        />
      );
    }

    // Device About URL - Text input
    if (field === 'deviceAboutUrl') {
      return (
        <Input
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          placeholder="e.g. https://example.com/quantum-systems/harmony"
        />
      );
    }

    // Run Input Types - MultiSelect
    if (field === 'runInputTypes') {
      const arrayValue = Array.isArray(currentValue)
        ? currentValue
        : typeof currentValue === 'string'
          ? currentValue
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : [];

      // Filter out 'other' from the options for admin editing
      const availableOptions = RUN_INPUT_TYPE_OPTIONS.filter((option) => option !== 'other');

      return (
        <div className="flex-1">
          <MultiSelect
            value={arrayValue}
            onChange={handleChange}
            paradigm={device.pendingEdits?.paradigm || device.paradigm}
          />
        </div>
      );
    }

    // Pricing File - Clickable link (not input box)
    if (field === 'pricingFile') {
      return typeof currentValue === 'string' && currentValue ? (
        <div className="flex items-center gap-2">
          <a
            href={currentValue}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
          >
            <FileText className="size-3" />
            View Pricing File
            <ExternalLink className="size-3" />
          </a>
        </div>
      ) : (
        <span className="text-sm text-muted-foreground">No pricing file</span>
      );
    }

    // Run Input Type File - Clickable link (not input box)
    if (field === 'runInputTypeFile') {
      return typeof currentValue === 'string' && currentValue ? (
        <div className="flex items-center gap-2">
          <a
            href={currentValue}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
          >
            <FileText className="size-3" />
            View Input Type File
            <ExternalLink className="size-3" />
          </a>
        </div>
      ) : (
        <span className="text-sm text-muted-foreground">No input type file</span>
      );
    }

    // Paradigm - Dropdown
    if (field === 'paradigm') {
      return (
        <Select value={currentValue || ''} onValueChange={handleChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select paradigm" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="gate-based">gate-based</SelectItem>
            <SelectItem value="annealing">annealing</SelectItem>
            <SelectItem value="analog">analog</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Architecture - CustomSelect (for QPU)
    if (field === 'architecture') {
      return (
        <CustomSelect
          value={currentValue || ''}
          onValueChange={handleChange}
          placeholder="Select or enter architecture"
          options={[
            { value: 'trapped-ion', label: 'trapped ion' },
            { value: 'superconducting', label: 'superconducting' },
            { value: 'photonic', label: 'photonic' },
            { value: 'neutral-atom', label: 'neutral atom' },
            { value: 'silicon', label: 'silicon' },
          ]}
          customPlaceholder="Enter custom architecture..."
          className="flex-1"
        />
      );
    }

    // Processor Type - CustomSelect (for Simulator)
    if (field === 'processorType') {
      return (
        <CustomSelect
          value={currentValue || ''}
          onValueChange={handleChange}
          placeholder="Select or enter processor type"
          options={[
            { value: 'state vector', label: 'state vector' },
            { value: 'tensor network', label: 'tensor network' },
            { value: 'density matrix', label: 'density matrix' },
          ]}
          customPlaceholder="Enter custom processor type..."
          className="flex-1"
        />
      );
    }

    // Number of Qubits - Number input
    if (field === 'numberQubits') {
      return (
        <Input
          type="number"
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          min="1"
          placeholder="e.g. 32"
        />
      );
    }

    // Noise Models - Text input (comma-separated)
    if (field === 'noiseModels') {
      const arrayValue = Array.isArray(currentValue)
        ? currentValue
        : typeof currentValue === 'string'
          ? currentValue
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : [];

      return (
        <Input
          value={arrayValue.join(', ')}
          onChange={(e) =>
            handleChange(
              e.target.value
                .split(',')
                .map((s) => s.trim())
                .filter(Boolean),
            )
          }
          className="flex-1"
          placeholder="e.g. depolarizing, amplitude damping"
        />
      );
    }

    // Pricing Type - Dropdown
    if (field === 'pricingType') {
      return (
        <Select value={currentValue || ''} onValueChange={handleChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select pricing type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="perMinute">Price per minute</SelectItem>
            <SelectItem value="perTaskPerShot">Price per task/shot</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Pricing fields - Number inputs
    if (field.startsWith('pricing.')) {
      return (
        <Input
          type="number"
          step="0.01"
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          min="0"
          placeholder="0.00"
        />
      );
    }

    // Device Status - Dropdown
    if (field === 'status') {
      return (
        <Select value={currentValue || ''} onValueChange={handleChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select device status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ONLINE">ONLINE</SelectItem>
            <SelectItem value="OFFLINE">OFFLINE</SelectItem>
            <SelectItem value="RETIRED">RETIRED</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Offline Reason - Text input
    if (field === 'statusMsg') {
      return (
        <Input
          value={currentValue || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="flex-1"
          placeholder="e.g. Offline for calibration, retired due to hardware failure, etc."
        />
      );
    }

    // Device Availability - Dropdown
    if (field === 'isAvailable') {
      return (
        <Select value={currentValue?.toString() || ''} onValueChange={handleChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select device availability" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">true</SelectItem>
            <SelectItem value="false">false</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Whitelisted Domains - Array input
    if (field === 'whiteListedDomains') {
      const arrayValue = Array.isArray(currentValue)
        ? currentValue
        : typeof currentValue === 'string'
          ? currentValue
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : [];

      return (
        <Input
          value={arrayValue.join(', ')}
          onChange={(e) =>
            handleChange(
              e.target.value
                .split(',')
                .map((s) => s.trim())
                .filter(Boolean),
            )
          }
          className="flex-1"
          placeholder="e.g. quantumscam.net"
        />
      );
    }

    // Blacklisted Domains - Array input
    if (field === 'blackListedDomains') {
      const arrayValue = Array.isArray(currentValue)
        ? currentValue
        : typeof currentValue === 'string'
          ? currentValue
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : [];

      return (
        <Input
          value={arrayValue.join(', ')}
          onChange={(e) =>
            handleChange(
              e.target.value
                .split(',')
                .map((s) => s.trim())
                .filter(Boolean),
            )
          }
          className="flex-1"
          placeholder="e.g. qnon.io"
        />
      );
    }

    // Default text input for any other fields
    return (
      <Input
        value={currentValue || ''}
        onChange={(e) => handleChange(e.target.value)}
        className="flex-1"
      />
    );
  };

  if (editRequests.length === 0) {
    return <div className="text-muted-foreground">No pending edit device requests.</div>;
  }

  return (
    <div className="space-y-6">
      {editRequests.map((device) => {
        return (
          <div key={device.qrn} className="rounded-lg border bg-muted p-4">
            <div className="flex flex-col gap-2">
              <div className="mb-2 flex items-center justify-between">
                <div className="text-lg font-semibold">{device.name}</div>
                <AlertDialog
                  open={showDeleteDialog === device.qrn}
                  onOpenChange={(open) => setShowDeleteDialog(open ? device.qrn : null)}
                >
                  <AlertDialogTrigger asChild>
                    <Button
                      disabled={pendingDeletes[device.qrn]}
                      variant="destructive"
                      size="sm"
                      className="bg-red-600 text-white hover:bg-red-700"
                    >
                      {pendingDeletes[device.qrn] ? (
                        'Deleting...'
                      ) : (
                        <>
                          <Trash2 className="mr-2 size-4" />
                          Delete Edit
                        </>
                      )}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Edit Request</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this edit request for &quot;{device.name}
                        &quot;? This action cannot be undone and will remove all pending edits.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleDeleteEdit(device)}
                        className="bg-red-600 text-white hover:bg-red-700"
                      >
                        Delete Edit Request
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
              <div className="mb-2">Edit Request Fields:</div>
              {device.pendingEdits && Object.keys(device.pendingEdits).length > 0 ? (
                Object.entries(device.pendingEdits).map(([field, value]) => {
                  // Handle pricing object specially
                  if (field === 'pricing' && typeof value === 'object' && value !== null) {
                    return (
                      <div key={field} className="space-y-2">
                        <div className="mb-2 text-sm font-medium text-muted-foreground">
                          Pricing:
                        </div>
                        {Object.entries(value).map(([pricingField, pricingValue]) => (
                          <div
                            key={`${field}.${pricingField}`}
                            className="ml-4 flex items-center gap-6"
                          >
                            <label className="w-32 text-sm font-medium">
                              {(() => {
                                if (pricingField === 'perMinute') return 'Per Minute ($)';
                                if (pricingField === 'perTask') return 'Per Task ($)';
                                if (pricingField === 'perShot') return 'Per Shot ($)';
                                return pricingField;
                              })()}
                              :
                            </label>
                            {renderFieldInput(`pricing.${pricingField}`, pricingValue, device)}
                          </div>
                        ))}
                      </div>
                    );
                  }

                  // Handle regular fields
                  return (
                    <div key={field} className="mb-2 flex items-center gap-2">
                      <label className="w-40 font-medium">{field}:</label>
                      {renderFieldInput(field, value, device)}
                    </div>
                  );
                })
              ) : (
                <div className="italic text-muted-foreground">
                  No pending edits found for this device.
                </div>
              )}
              <div className="mt-2 flex gap-2">
                <Button
                  disabled={pendingSaves[device.qrn] || !unsavedChanges[device.qrn]}
                  onClick={() => {
                    setEditingDeviceId(device.qrn);
                    setEditEdits({ ...device.pendingEdits, ...editEdits });
                    handleSaveChanges(device);
                  }}
                  className="bg-blue-600 text-white hover:bg-blue-700"
                >
                  {pendingSaves[device.qrn] ? 'Saving...' : 'Save Changes'}
                </Button>
                <Button
                  disabled={pendingApprovals[device.qrn] || unsavedChanges[device.qrn]}
                  onClick={() => {
                    setEditingDeviceId(device.qrn);
                    setEditEdits({ ...device.pendingEdits });
                    handleEditApprove(device);
                  }}
                  className="bg-green-600 text-white hover:bg-green-700"
                >
                  {pendingApprovals[device.qrn] ? 'Approving...' : 'Approve Edits'}
                </Button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
