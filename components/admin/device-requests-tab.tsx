'use client';

import { useState } from 'react';
import {
  CheckCircle,
  XCircle,
  Building,
  Users,
  Calendar,
  Shield,
  Clock,
  Server,
  Zap,
  FileText,
  MoreVertical,
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useDeviceAccessRequests,
  useApproveDeviceAccess,
  DeviceAccessRequest,
} from '@/hooks/use-device-access';

export default function DeviceRequestsTab() {
  const [statusFilter, setStatusFilter] = useState('pending');
  const { data: requestsData, isLoading } = useDeviceAccessRequests(
    statusFilter && statusFilter !== 'all' ? statusFilter : undefined,
  );
  const { mutate: approveRequest } = useApproveDeviceAccess();

  const handleApprove = (requestId: string) => {
    approveRequest({
      requestId,
      action: 'approve',
    });
  };

  const handleDeny = (requestId: string, reason: string) => {
    approveRequest({
      requestId,
      action: 'deny',
      deniedReason: reason,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">Pending Requests</SelectItem>
            <SelectItem value="approved">Approved Requests</SelectItem>
            <SelectItem value="denied">Denied Requests</SelectItem>
            <SelectItem value="all">All Requests</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-48" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {requestsData?.requests.map((request: DeviceAccessRequest) => (
            <Card key={request._id} className="overflow-hidden transition-shadow hover:shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    {/* Device Icon */}
                    <div className="rounded-lg bg-primary/10 p-3">
                      <Server className="size-6 text-primary" />
                    </div>

                    {/* Device Info */}
                    <div className="space-y-1">
                      <div className="flex items-center gap-3">
                        <h3 className="text-lg font-semibold">{request.deviceName}</h3>
                        {request.providerName && (
                          <Badge variant="secondary" className="font-normal">
                            {request.providerName}
                          </Badge>
                        )}
                      </div>

                      {/* Status and Access Type */}
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            request.status === 'approved'
                              ? 'default'
                              : (request.status === 'denied'
                                ? 'destructive'
                                : 'secondary')
                          }
                          className="capitalize"
                        >
                          {request.status}
                        </Badge>
                        <Badge variant="outline" className="capitalize">
                          <Shield className="mr-1 size-3" />
                          {request.requestType} Access
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons or Menu */}
                  <div className="flex items-center gap-2">
                    {request.status === 'pending' ? (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleApprove(request._id)}
                          className="bg-green-600 text-white hover:bg-green-700"
                        >
                          <CheckCircle className="mr-1 size-4" />
                          Approve
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                            >
                              <XCircle className="mr-1 size-4" />
                              Deny
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Deny Access Request</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to deny this device access request? Please
                                provide a reason for the denial.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() =>
                                  handleDeny(request._id, 'Request denied by administrator')
                                }
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Deny Request
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </>
                    ) : (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>View Organization</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4 pb-6 pt-0">
                {/* Request Details Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Building className="mt-0.5 size-4 text-muted-foreground" />
                      <div className="space-y-0.5">
                        <p className="text-xs text-muted-foreground">Organization</p>
                        <p className="text-sm font-medium">{request.organization.name}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Users className="mt-0.5 size-4 text-muted-foreground" />
                      <div className="space-y-0.5">
                        <p className="text-xs text-muted-foreground">Requester</p>
                        <p className="text-sm font-medium">{request.requesterEmail}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Calendar className="mt-0.5 size-4 text-muted-foreground" />
                      <div className="space-y-0.5">
                        <p className="text-xs text-muted-foreground">Requested On</p>
                        <p className="text-sm font-medium">
                          {new Date(request.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          })}
                        </p>
                      </div>
                    </div>

                    {request.metadata?.accessDuration && (
                      <div className="flex items-start gap-2">
                        <Clock className="mt-0.5 size-4 text-muted-foreground" />
                        <div className="space-y-0.5">
                          <p className="text-xs text-muted-foreground">Duration</p>
                          <p className="text-sm font-medium">{request.metadata.accessDuration}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Justification Section */}
                {request.justification && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm font-medium">
                        <FileText className="size-4" />
                        Justification
                      </div>
                      <p className="pl-6 text-sm leading-relaxed text-muted-foreground">
                        {request.justification}
                      </p>
                    </div>
                  </>
                )}

                {/* Request ID */}
                <div className="flex items-center gap-2 pt-2 text-xs text-muted-foreground">
                  <Zap className="size-3" />
                  Request ID: {request._id}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
