import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import type { DeviceData } from '@/types/device';
import { useApproveDevice, useDeleteDeviceRequest, useUpdateDeviceData } from '@/hooks/use-api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Trash2, CheckCircle, Edit3, X, Save, FileText, ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { DeviceFieldInput, PricingFields } from '@/components/admin/device-field-inputs';

interface AddRequestsTabProps {
  addRequests: DeviceData[];
}

/**
 * AddRequestsTab component now supports the correct device schema including:
 * - pricingType field with 'file' option support
 * - pricingFile field for file-based pricing
 * - runInputTypeFiles array for uploaded input type files
 * - Proper nested field handling for pricing object
 * - File upload URL display and editing
 */

// Field requirement indicators
const getFieldLabel = (field: string, isRequired: boolean, isDeprecated?: boolean) => {
  let label = field;
  if (isRequired) {
    label += ' *';
  }
  if (isDeprecated) {
    label += ' (deprecated)';
  }
  return label;
};

// Helper function to construct predictable S3 URLs
const constructS3Url = (
  orgId: string,
  userEmail: string,
  deviceId: string,
  fileType: 'device-images' | 'pricing-files' | 'run-input-type-files',
  fileName?: string,
): string => {
  const bucketName = process.env.NEXT_PUBLIC_S3_BUCKET_NAME || 'qbraid-static';
  const region = process.env.NEXT_PUBLIC_AWS_REGION || 'us-east-1';

  // Encode userEmail for URL safety (handles @ symbols)
  const encodedUserEmail = encodeURIComponent(userEmail);

  // Upload library uses: partner-dashboard/${orgId}/device-images/${userEmail}/${deviceId}/
  const basePath = `partner-dashboard/${orgId}/${fileType}/${encodedUserEmail}`;

  return fileName
    ? `https://${bucketName}.s3.${region}.amazonaws.com/${basePath}/${fileName}`
    : `https://${region}.console.aws.amazon.com/s3/buckets/${bucketName}?region=${region}&bucketType=general&prefix=${basePath}/&showversions=false`;
};

// Component to display file links
const FileDisplay = ({
  device,
  fileType,
  label,
}: {
  device: DeviceData;
  fileType: 'device-images' | 'pricing-files' | 'run-input-type-files';
  label: string;
}) => {
  const userEmail = device.requestedBy;
  const deviceId = device.qrn;
  const orgId = device?.requestedByOrganization?._id;

  // Only show file links if we have user email, device ID, and org ID
  if (!userEmail || !deviceId || !orgId) {
    return (
      <div className="flex items-center gap-2">
        <span className="font-medium">{label}:</span>
        <span className="text-sm text-muted-foreground">No files available</span>
      </div>
    );
  }

  // Construct the base S3 URL
  const baseUrl = constructS3Url(orgId, userEmail, deviceId, fileType);

  return (
    <div className="flex items-center gap-2">
      <span className="font-medium">{label}:</span>
      <a
        href={baseUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
      >
        <FileText className="size-3" />
        View Files
        <ExternalLink className="size-3" />
      </a>
    </div>
  );
};

export default function AddRequestsTab({ addRequests }: AddRequestsTabProps) {
  const [editMode, setEditMode] = useState<string | null>(null);
  const [editData, setEditData] = useState<Record<string, any> | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState<Record<string, boolean>>({});
  const [pendingApprovals, setPendingApprovals] = useState<Record<string, boolean>>({});
  const [pendingDeletions, setPendingDeletions] = useState<Record<string, boolean>>({});

  const approveDeviceMutation = useApproveDevice();
  const deleteDeviceRequestMutation = useDeleteDeviceRequest();
  const updateDeviceDataMutation = useUpdateDeviceData();

  const handleEditFieldChange = (field: string, value: any, device: DeviceData) => {
    if (!editData) return;

    // Handle nested pricing fields properly
    if (field.startsWith('pricing.')) {
      const pricingField = field.split('.')[1];
      const currentPricing = editData.pricing || {};
      setEditData({
        ...editData,
        pricing: {
          ...currentPricing,
          [pricingField]: value,
        },
      });
    } else {
      setEditData({ ...editData, [field]: value });
    }

    setUnsavedChanges((prev) => ({ ...prev, [device.qrn]: true }));
  };

  const handleSaveChanges = async (device: DeviceData) => {
    if (!editData) return;
    try {
      // Prepare edits without approval fields - keep verified as false
      const { verified, ...safeEdits } = editData;
      const editsToSave = {
        ...safeEdits,
        verified: false, // Explicitly keep verified as false until actually approved
      };

      await updateDeviceDataMutation.mutateAsync({
        deviceId: device.qrn,
        postBody: editsToSave,
      });
      toast.success('Changes saved.');
      setUnsavedChanges((prev) => ({ ...prev, [device.qrn]: false }));
      setEditMode(null);
      setEditData(null);
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes.');
    }
  };

  const handleCancelEdit = () => {
    setEditMode(null);
    setEditData(null);
    setUnsavedChanges({});
  };

  const handleStartEdit = (device: DeviceData) => {
    setEditMode(device.qrn);
    setEditData({ ...device });
  };

  const renderFieldInput = (field: string, value: any, device: DeviceData) => {
    const handleFieldChange = (fieldName: string, newValue: any, deviceData: DeviceData) => {
      if (editMode !== deviceData.qrn) {
        setEditMode(deviceData.qrn);
        setEditData({ ...deviceData });
      }
      handleEditFieldChange(fieldName, newValue, deviceData);
    };

    return (
      <DeviceFieldInput
        field={field}
        value={value}
        device={device}
        editMode={editMode}
        editData={editData}
        onChange={handleFieldChange}
      />
    );
  };

  const handleApproveDevice = async (device: DeviceData) => {
    setPendingApprovals((prev) => ({ ...prev, [device.qrn]: true }));
    try {
      await approveDeviceMutation.mutateAsync(device.qrn);
      toast.success('Device approved and published.');
    } catch (error) {
      console.error('Error approving device:', error);
      toast.error('Failed to approve device.');
    } finally {
      setPendingApprovals((prev) => ({ ...prev, [device.qrn]: false }));
    }
  };

  const handleDeleteDeviceRequest = async (device: DeviceData) => {
    setPendingDeletions((prev) => ({ ...prev, [device.qrn]: true }));
    try {
      await deleteDeviceRequestMutation.mutateAsync(device.qrn);
      toast.success('Device request deleted.');
    } catch (error) {
      console.error('Error deleting device request:', error);
      toast.error('Failed to delete device request.');
    } finally {
      setPendingDeletions((prev) => ({ ...prev, [device.qrn]: false }));
    }
  };

  if (addRequests.length === 0) {
    return <div className="text-muted-foreground">No pending add device requests.</div>;
  }

  return (
    <div className="space-y-4">
      {addRequests.map((device) => (
        <div key={device.qrn} className="rounded-lg border bg-sidebar p-6">
          {/* Header */}
          <div className="mb-4 flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold">{device.name}</h3>
              <p className="text-sm text-muted-foreground">{device.qrn}</p>
            </div>
            <div className="flex gap-2">
              <Badge variant={device.type === 'QPU' ? 'default' : 'secondary'}>{device.type}</Badge>
              <Badge variant="outline">
                {typeof device.providerId === 'string'
                  ? device.providerId
                  : device.providerId?.provider || 'N/A'}
              </Badge>
              {editMode !== device.qrn && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleStartEdit(device)}
                  className="size-8 p-0"
                >
                  <Edit3 className="size-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Field Legend */}
          <div className="mb-4 rounded-lg bg-muted/50 p-3">
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Legend:</span> * = Required field, (deprecated) = Field
              required but deprecated
            </div>
          </div>

          {/* All Device Fields */}
          <div className="grid grid-cols-1 gap-x-8 gap-y-3 text-sm md:grid-cols-2">
            {/* Core Required Fields */}
            <div>
              <b>{getFieldLabel('Type', true)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('type', device.type, device)
                : device.type}
            </div>
            <div>
              <b>{getFieldLabel('Provider ID', true)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('providerId', device.providerId, device)
                : typeof device.providerId === 'string'
                  ? device.providerId
                  : device.providerId?.provider || 'N/A'}
            </div>
            <div>
              <b>{getFieldLabel('Paradigm', true)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('paradigm', device.paradigm, device)
                : device.paradigm}
            </div>

            {/* Conditional Fields Based on Device Type */}
            {(editMode === device.qrn && editData?.type === 'QPU') ||
            (!editMode && device.type === 'QPU') ? (
              <div>
                <b>{getFieldLabel('Modality', true)}:</b>{' '}
                {editMode === device.qrn
                  ? renderFieldInput('modality', device.modality, device)
                  : device.modality}
              </div>
            ) : null}

            {(editMode === device.qrn && editData?.type === 'Simulator') ||
            (!editMode && device.type === 'Simulator') ? (
              <div>
                <b>{getFieldLabel('Processor Type', true)}:</b>{' '}
                {editMode === device.qrn
                  ? renderFieldInput('processorType', device.processorType, device)
                  : device.processorType}
              </div>
            ) : null}

            <div>
              <b>{getFieldLabel('Number Qubits', true)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('numberQubits', device.numberQubits, device)
                : device.numberQubits}
            </div>

            {/* Required Technical Fields */}
            <div>
              <b>{getFieldLabel('qBraid ID', true)}:</b>{' '}
              {editMode === device.qrn ? renderFieldInput('qrn', device.qrn, device) : device.qrn}
            </div>
            <div>
              <b>{getFieldLabel('Vendor', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('vendor', device.vendor, device)
                : device.vendor}
            </div>
            <div>
              <b>{getFieldLabel('Object Arg', true)}:</b>{' '}
              {editMode === device.qrn ? renderFieldInput('vrn', device.vrn, device) : device.vrn}
            </div>

            {/* Status and Availability */}
            <div>
              <b>{getFieldLabel('Status', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('status', device.status, device)
                : device.status}
            </div>
            <div>
              <b>{getFieldLabel('Visibility', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('visibility', device.visibility, device)
                : device.visibility}
            </div>
            <div>
              <b>{getFieldLabel('Is Available', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('isAvailable', device.isAvailable, device)
                : device.isAvailable
                  ? 'Yes'
                  : 'No'}
            </div>

            {/* Array fields */}
            <div>
              <b>{getFieldLabel('Run Input Types', true)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('runInputTypes', device.runInputTypes, device)
                : Array.isArray(device.runInputTypes)
                  ? device.runInputTypes.join(', ')
                  : device.runInputTypes}
            </div>
            <div>
              <b>{getFieldLabel('Noise Models', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('noiseModels', device.noiseModels, device)
                : Array.isArray(device.noiseModels)
                  ? device.noiseModels.join(', ')
                  : device.noiseModels}
            </div>
            <div>
              <b>{getFieldLabel('Whitelisted Domains', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('whiteListedDomains', device.whiteListedDomains, device)
                : Array.isArray(device.whiteListedDomains)
                  ? device.whiteListedDomains.join(', ')
                  : device.whiteListedDomains}
            </div>
            <div>
              <b>{getFieldLabel('Blacklisted Domains', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('blackListedDomains', device.blackListedDomains, device)
                : Array.isArray(device.blackListedDomains)
                  ? device.blackListedDomains.join(', ')
                  : device.blackListedDomains}
            </div>

            {/* Pricing Section - Grouped with conditional fields */}
            <PricingFields
              device={device}
              editMode={editMode}
              editData={editData}
              renderFieldInput={renderFieldInput}
              getFieldLabel={getFieldLabel}
            />

            {/* Full-width fields */}
            <div className="md:col-span-2">
              <b>{getFieldLabel('Device Description', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('deviceDescription', device.deviceDescription, device)
                : device.deviceDescription}
            </div>
            <div className="md:col-span-2">
              <b>{getFieldLabel('Device About URL', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('deviceAboutUrl', device.about, device)
                : device.about}
            </div>

            {/* Notes (optional) */}
            <div className="md:col-span-2">
              <b>{getFieldLabel('Notes', false)}:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('notes', (device as any).notes, device)
                : device.notes}
            </div>

            <div className="md:col-span-2">
              <b>Device Image:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput('deviceImage', device.deviceImage, device)
                : device.deviceImage}
            </div>
            <div className="md:col-span-2">
              <b>Technical Specifications:</b>{' '}
              {editMode === device.qrn
                ? renderFieldInput(
                    'technicalSpecifications',
                    device.technicalSpecifications,
                    device,
                  )
                : device.technicalSpecifications}
            </div>

            {/* File Upload Sections */}
            <div className="mt-4 border-t pt-4 md:col-span-2">
              <h4 className="mb-3 text-sm font-semibold">Uploaded Files</h4>
              <div className="space-y-2">
                <FileDisplay device={device} fileType="device-images" label="Device Images" />
                <FileDisplay device={device} fileType="pricing-files" label="Pricing Files" />
                <FileDisplay
                  device={device}
                  fileType="run-input-type-files"
                  label="Input Type Files"
                />
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Bottom Row with Requested By and Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              <b>Requested By:</b> {device.requestedBy}
            </div>
            <div className="flex gap-3">
              {editMode === device.qrn ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                    className="flex items-center gap-2"
                  >
                    <X className="size-4" />
                    Cancel
                  </Button>
                  <Button
                    disabled={updateDeviceDataMutation.isPending || !unsavedChanges[device.qrn]}
                    onClick={() => handleSaveChanges(device)}
                    className="flex items-center gap-2"
                    size="sm"
                  >
                    <Save className="size-4" />
                    {updateDeviceDataMutation.isPending ? 'Saving...' : 'Save'}
                  </Button>
                </>
              ) : (
                <>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={pendingDeletions[device.qrn]}
                        className="flex items-center gap-2"
                      >
                        <Trash2 className="size-4" />
                        {pendingDeletions[device.qrn] ? 'Deleting...' : 'Delete'}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Device Request?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete the device request for &quot;{device.name}
                          &quot;? This action cannot be undone and will permanently remove the
                          request.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteDeviceRequest(device)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete Request
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                  <Button
                    disabled={pendingApprovals[device.qrn]}
                    onClick={() => handleApproveDevice(device)}
                    className="flex items-center gap-2 bg-green-600 text-white hover:bg-green-700"
                    size="sm"
                  >
                    <CheckCircle className="size-4" />
                    {pendingApprovals[device.qrn] ? 'Approving...' : 'Approve'}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
