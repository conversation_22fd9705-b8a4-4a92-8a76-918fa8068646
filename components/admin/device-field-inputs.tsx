import React from 'react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import CustomSelect from '@/components/devices/edit-device-form/custom-select';
import MultiSelect from '@/components/devices/edit-device-form/multi-select';
import { useProviders } from '@/hooks/use-provider-management';
import type { DeviceData } from '@/types/device';

interface FieldInputProps {
  field: string;
  value: any;
  device: DeviceData;
  editMode: string | null;
  editData: Record<string, any> | null;
  onChange: (field: string, value: any, device: DeviceData) => void;
}

export const DeviceFieldInput: React.FC<FieldInputProps> = ({
  field,
  value,
  device,
  editMode,
  editData,
  onChange,
}) => {
  const { data: providers = [] } = useProviders();
  const currentValue = editMode === device.qrn && editData ? editData[field] : value;

  const handleChange = (newValue: any) => {
    onChange(field, newValue, device);
  };

  // Device Name - Text input
  if (field === 'name') {
    return (
      <Input
        value={currentValue || ''}
        onChange={(e) => handleChange(e.target.value)}
        className="flex-1"
        placeholder="e.g. SV1"
      />
    );
  }

  // Device Type - Dropdown
  if (field === 'type') {
    return (
      <Select value={currentValue || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select device type" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="QPU">QPU</SelectItem>
          <SelectItem value="Simulator">Simulator</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Provider ID - Dropdown with providers from API
  if (field === 'providerId') {
    const providerOptions = providers.map((provider) => ({
      value: provider._id,
      label: `${provider.provider} (${provider._id})`,
    }));

    return (
      <CustomSelect
        value={currentValue || ''}
        onValueChange={handleChange}
        placeholder="Select provider"
        options={providerOptions}
        customPlaceholder="Enter custom provider ID..."
        className="flex-1"
      />
    );
  }

  // Paradigm - Dropdown
  if (field === 'paradigm') {
    return (
      <Select value={currentValue || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select paradigm" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="gate-based">gate-based</SelectItem>
          <SelectItem value="annealing">annealing</SelectItem>
          <SelectItem value="analog">analog</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Modality (Architecture) - CustomSelect (QPU only)
  if (field === 'modality') {
    return (
      <CustomSelect
        value={currentValue || ''}
        onValueChange={handleChange}
        placeholder="Select or enter modality"
        options={[
          { value: 'trapped-ion', label: 'trapped ion' },
          { value: 'superconducting', label: 'superconducting' },
          { value: 'photonic', label: 'photonic' },
          { value: 'neutral-atom', label: 'neutral atom' },
          { value: 'silicon', label: 'silicon' },
        ]}
        customPlaceholder="Enter custom modality..."
        className="flex-1"
      />
    );
  }

  // Processor Type - CustomSelect (Simulator only)
  if (field === 'processorType') {
    return (
      <CustomSelect
        value={currentValue || ''}
        onValueChange={handleChange}
        placeholder="Select or enter processor type"
        options={[
          { value: 'state vector', label: 'state vector' },
          { value: 'tensor network', label: 'tensor network' },
          { value: 'density matrix', label: 'density matrix' },
        ]}
        customPlaceholder="Enter custom processor type..."
        className="flex-1"
      />
    );
  }

  // Number of Qubits - Number input
  if (field === 'numberQubits') {
    return (
      <Input
        type="number"
        value={currentValue || ''}
        onChange={(e) => handleChange(e.target.value)}
        className="flex-1"
        min="1"
        placeholder="e.g. 32"
      />
    );
  }

  // Status - Dropdown
  if (field === 'status') {
    return (
      <Select value={currentValue || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select device status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="ONLINE">ONLINE</SelectItem>
          <SelectItem value="OFFLINE">OFFLINE</SelectItem>
          <SelectItem value="RETIRED">RETIRED</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Visibility - Dropdown
  if (field === 'visibility') {
    return (
      <Select value={currentValue || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select visibility" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="public">Public</SelectItem>
          <SelectItem value="private">Private</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Availability - Dropdown
  if (field === 'isAvailable') {
    return (
      <Select value={currentValue?.toString() || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select availability" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="true">true</SelectItem>
          <SelectItem value="false">false</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Run Input Types - MultiSelect (paradigm-based, no file uploads)
  if (field === 'runInputTypes') {
    const arrayValue = Array.isArray(currentValue)
      ? currentValue
      : typeof currentValue === 'string'
        ? currentValue
            .split(',')
            .map((s) => s.trim())
            .filter(Boolean)
        : [];

    // Get paradigm from edit data or original device data
    const deviceParadigm =
      editMode === device.qrn && editData ? editData.paradigm : device.paradigm;

    return <MultiSelect value={arrayValue} onChange={handleChange} paradigm={deviceParadigm} />;
  }

  // Array fields - Noise Models, Whitelisted/Blacklisted Domains
  if (field === 'noiseModels' || field === 'whiteListedDomains' || field === 'blackListedDomains') {
    const arrayValue = Array.isArray(currentValue)
      ? currentValue
      : typeof currentValue === 'string'
        ? currentValue
            .split(',')
            .map((s) => s.trim())
            .filter(Boolean)
        : [];

    return (
      <Input
        value={arrayValue.join(', ')}
        onChange={(e) =>
          handleChange(
            e.target.value
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean),
          )
        }
        className="flex-1"
        placeholder="Enter comma-separated values..."
      />
    );
  }

  // Pricing Type - Dropdown
  if (field === 'pricingType') {
    return (
      <Select value={currentValue || ''} onValueChange={handleChange}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="Select pricing type" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="perMinute">Price per minute</SelectItem>
          <SelectItem value="perTaskPerShot">Price per task/shot</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Pricing fields - Number inputs
  if (field.startsWith('pricing.')) {
    // Handle nested pricing object properly
    const pricingField = field.split('.')[1] as 'perMinute' | 'perTask' | 'perShot';
    let fieldValue = currentValue;

    // If currentValue is from device.pricing object, extract the specific field
    if (field === `pricing.${pricingField}` && !editMode && value && typeof value === 'object') {
      fieldValue = (value as any)[pricingField];
    }
    // If in edit mode and editData has pricing object
    else if (
      editMode === device.qrn &&
      editData &&
      editData.pricing &&
      typeof editData.pricing === 'object'
    ) {
      fieldValue = (editData.pricing as any)[pricingField];
    }
    // If not in edit mode, get from device.pricing
    else if (editMode !== device.qrn && device.pricing && typeof device.pricing === 'object') {
      fieldValue = (device.pricing as any)[pricingField];
    }

    return (
      <Input
        type="number"
        step="0.01"
        value={fieldValue || ''}
        onChange={(e) => handleChange(e.target.value)}
        className="flex-1"
        min="0"
        placeholder="0.00"
      />
    );
  }

  // Device Image - URL input (no file upload)
  if (field === 'deviceImage') {
    const displayValue = currentValue instanceof File ? currentValue.name : currentValue || '';

    return (
      <Input
        value={displayValue}
        onChange={(e) => handleChange(e.target.value)}
        className="flex-1"
        placeholder="Device image URL"
      />
    );
  }

  // Default text input
  return (
    <Input
      value={currentValue || ''}
      onChange={(e) => handleChange(e.target.value)}
      className="flex-1"
    />
  );
};

// Pricing component with conditional fields based on pricing type
interface PricingFieldsProps {
  device: DeviceData;
  editMode: string | null;
  editData: Record<string, any> | null;
  renderFieldInput: (field: string, value: any, device: DeviceData) => React.ReactNode;
  getFieldLabel: (field: string, isRequired: boolean, isDeprecated?: boolean) => string;
}

export const PricingFields: React.FC<PricingFieldsProps> = ({
  device,
  editMode,
  editData,
  renderFieldInput,
  getFieldLabel,
}) => {
  const isInEditMode = editMode === device.qrn;

  // Get current pricing type (from edit data or device)
  const currentPricingType = isInEditMode && editData ? editData.pricingType : device.pricingType;

  // In edit mode, show fields based on pricing type selection
  const shouldShowPerMinute = isInEditMode
    ? !currentPricingType || currentPricingType === 'perMinute'
    : !!device.pricing?.perMinute;
  const shouldShowPerTask = isInEditMode
    ? !currentPricingType || currentPricingType === 'perTaskPerShot'
    : !!device.pricing?.perTask;
  const shouldShowPerShot = isInEditMode
    ? !currentPricingType || currentPricingType === 'perTaskPerShot'
    : !!device.pricing?.perShot;

  // Check if any pricing values exist
  const hasPricingValues =
    device.pricing?.perMinute ||
    device.pricing?.perTask ||
    device.pricing?.perShot ||
    device.pricingType;

  return (
    <div className="rounded-lg border bg-muted/30 p-4 md:col-span-2">
      <h4 className="mb-3 text-sm font-semibold">Pricing Configuration</h4>

      {!isInEditMode && !hasPricingValues ? (
        <div className="text-sm italic text-muted-foreground">
          See S3 pricing files for device pricing information
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Always show pricing type if it exists or in edit mode */}
          {(isInEditMode || device.pricingType) && (
            <div>
              <b>{getFieldLabel('Pricing Type', false)}:</b>{' '}
              {isInEditMode
                ? renderFieldInput('pricingType', device.pricingType, device)
                : device.pricingType}
            </div>
          )}

          {shouldShowPerMinute && (
            <div>
              <b>{getFieldLabel('Per Minute ($)', false)}:</b>{' '}
              {isInEditMode
                ? renderFieldInput('pricing.perMinute', device.pricing?.perMinute, device)
                : device.pricing?.perMinute}
            </div>
          )}

          {shouldShowPerTask && (
            <div>
              <b>{getFieldLabel('Per Task ($)', false)}:</b>{' '}
              {isInEditMode
                ? renderFieldInput('pricing.perTask', device.pricing?.perTask, device)
                : device.pricing?.perTask}
            </div>
          )}

          {shouldShowPerShot && (
            <div>
              <b>{getFieldLabel('Per Shot ($)', false)}:</b>{' '}
              {isInEditMode
                ? renderFieldInput('pricing.perShot', device.pricing?.perShot, device)
                : device.pricing?.perShot}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
