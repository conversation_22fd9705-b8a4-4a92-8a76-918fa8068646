import { z } from 'zod';

// Device Access Request Schema
export const deviceAccessRequestSchema = z.object({
  deviceId: z.string().min(1, 'Device selection is required'),
  requestType: z.enum(['read', 'write'], {
    required_error: 'Access type is required',
  }),
  justification: z
    .string()
    .min(10, 'Justification must be at least 10 characters')
    .max(1000, 'Justification must not exceed 1000 characters'),
  accessDuration: z.string().optional(),
});

export type DeviceAccessRequestInput = z.infer<typeof deviceAccessRequestSchema>;

// Create Device Schema
export const createDeviceSchema = z.object({
  name: z
    .string()
    .min(1, 'Device name is required')
    .max(100, 'Device name must not exceed 100 characters'),
  qrn: z
    .string()
    .min(1, 'QBraid ID is required')
    .regex(
      /^[a-zA-Z0-9\-_]+$/,
      'QBraid ID can only contain letters, numbers, hyphens, and underscores',
    ),
  type: z.enum(['QPU', 'Simulator'], {
    required_error: 'Device type is required',
  }),
  numberQubits: z
    .number()
    .int('Number of qubits must be an integer')
    .min(1, 'Number of qubits must be at least 1')
    .max(10_000, 'Number of qubits seems unreasonably high')
    .optional(),
  architecture: z.string().max(200, 'Architecture description too long').optional(),
  visibility: z.enum(['public', 'private'], {
    required_error: 'Visibility is required',
  }),
  pricing: z
    .object({
      perTask: z.number().min(0, 'Price must be non-negative').optional(),
      perShot: z.number().min(0, 'Price must be non-negative').optional(),
      perMinute: z.number().min(0, 'Price must be non-negative').optional(),
    })
    .optional(),
});

export type CreateDeviceInput = z.infer<typeof createDeviceSchema>;

// Device Filter Schema
export const deviceFilterSchema = z.object({
  type: z.enum(['QPU', 'Simulator', 'all']).default('all'),
  status: z.enum(['ONLINE', 'OFFLINE', 'all']).default('all'),
  searchTerm: z.string().max(100, 'Search term too long').default(''),
});

export type DeviceFilterInput = z.infer<typeof deviceFilterSchema>;

// Available Device Schema (for API responses)
export const availableDeviceSchema = z.object({
  _id: z.string(),
  name: z.string(),
  qrn: z.string(),
  type: z.enum(['QPU', 'Simulator']),
  status: z.enum(['ONLINE', 'OFFLINE']),
  numberQubits: z.number().optional(),
  architecture: z.string().optional(),
  visibility: z.enum(['public', 'private']),
  provider: z.string(),
  providerLogo: z.string().optional(),
  deviceDescription: z.string().optional(),
  processorType: z.string().optional(),
  logo: z
    .object({
      light: z.string().optional(),
      dark: z.string().optional(),
    })
    .optional(),
});

export type AvailableDeviceValidated = z.infer<typeof availableDeviceSchema>;

// Device Access Request Response Schema
export const deviceAccessRequestResponseSchema = z.object({
  _id: z.string(),
  deviceId: z.string(),
  deviceName: z.string(),
  requestType: z.enum(['read', 'write']),
  status: z.enum(['pending', 'approved', 'denied']),
  justification: z.string(),
  requesterEmail: z.string(),
  organization: z.object({
    _id: z.string(),
    name: z.string(),
  }),
  metadata: z
    .object({
      accessDuration: z.string().optional(),
    })
    .optional(),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
});

export type DeviceAccessRequestValidated = z.infer<typeof deviceAccessRequestResponseSchema>;

// Validation helper functions
export const validateDeviceAccessRequest = (data: unknown): DeviceAccessRequestInput => {
  try {
    return deviceAccessRequestSchema.parse(data);
  } catch (error) {
    console.error('❌ [VALIDATION] Device access request validation failed:', error);
    throw error;
  }
};

export const validateDeviceFilters = (data: unknown): DeviceFilterInput => {
  try {
    return deviceFilterSchema.parse(data);
  } catch (error) {
    console.error('❌ [VALIDATION] Device filter validation failed:', error);
    // Return safe defaults on validation error
    return { type: 'all', status: 'all', searchTerm: '' };
  }
};

export const validateAvailableDevices = (data: unknown): AvailableDeviceValidated[] => {
  try {
    const arraySchema = z.array(availableDeviceSchema);
    return arraySchema.parse(data);
  } catch (error) {
    console.error('❌ [VALIDATION] Available devices validation failed:', error);
    throw error;
  }
};

export const validateDeviceAccessRequests = (data: unknown): DeviceAccessRequestValidated[] => {
  try {
    const arraySchema = z.array(deviceAccessRequestResponseSchema);
    return arraySchema.parse(data);
  } catch (error) {
    console.error('❌ [VALIDATION] Device access requests validation failed:', error);
    throw error;
  }
};
