import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { FieldValidation } from '@/lib/device-field-validation';

/**
 * FieldInput Component
 *
 * A dynamic input component that renders different input types based on field validation rules.
 * This component handles all the different input types needed for device field editing:
 * - Text inputs for string fields
 * - Number inputs with min/step validation
 * - Dropdown selects for enum fields
 * - Checkboxes for boolean fields
 * - Array inputs for comma-separated values
 *
 * Props:
 * - fieldConfig: The validation configuration for the field (determines input type)
 * - value: The current value to display/edit
 * - onChange: Callback function when the value changes
 *
 * Input Types Supported:
 * - string: Regular text input (with optional minLength)
 * - string with enum: Dropdown select with predefined options
 * - number: Number input with min/step constraints
 * - boolean: Checkbox with enabled/disabled label
 * - array: Text input with comma-separated placeholder
 */
interface FieldInputProps {
  fieldConfig: FieldValidation | undefined;
  value: any;
  onChange: (value: any) => void;
}

export function FieldInput({ fieldConfig, value, onChange }: FieldInputProps) {
  // Default to text input for unknown fields
  if (!fieldConfig) {
    return (
      <Input
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="border-sidebar-border bg-sidebar text-foreground"
      />
    );
  }

  // Render different input types based on field configuration
  switch (fieldConfig.type) {
    case 'number': {
      return (
        <Input
          type="number"
          min={'min' in fieldConfig ? fieldConfig.min : undefined}
          step={'step' in fieldConfig ? fieldConfig.step : undefined}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="border-sidebar-border bg-sidebar text-foreground"
        />
      );
    }

    case 'boolean': {
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={value}
            onCheckedChange={(checked) => onChange(checked)}
            className="border-sidebar-border bg-sidebar"
          />
          <span className="text-sm text-muted-foreground">{value ? 'Enabled' : 'Disabled'}</span>
        </div>
      );
    }

    case 'string': {
      // Handle enum fields with dropdown select
      if ('enum' in fieldConfig && fieldConfig.enum) {
        return (
          <Select value={value} onValueChange={onChange}>
            <SelectTrigger className="border-sidebar-border bg-sidebar text-foreground">
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent className="border-sidebar-border bg-sidebar text-foreground">
              {fieldConfig.enum.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }

      // Regular string input with optional minLength
      return (
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          minLength={'minLength' in fieldConfig ? fieldConfig.minLength : undefined}
          className="border-sidebar-border bg-sidebar text-foreground"
        />
      );
    }

    case 'array': {
      // Array fields use comma-separated text input
      return (
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Enter comma-separated values"
          className="border-sidebar-border bg-sidebar text-foreground"
        />
      );
    }

    default: {
      // Fallback to text input for unknown types
      return (
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="border-sidebar-border bg-sidebar text-foreground"
        />
      );
    }
  }
}
