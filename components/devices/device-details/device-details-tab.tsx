import {
  Package,
  DollarSign,
  Shield,
  ChevronDown,
  CheckCircle2Icon,
  WifiOff,
  Info,
  Lock,
  Unlock,
  Cpu,
  Building2,
  Type,
  Code,
  Calculator,
  CreditCard,
  Clock,
  Users,
  Eye,
  EyeOff,
  Globe,
  Ban,
  Store,
  Cloud,
  Zap,
  Activity,
  Radio,
  Settings,
  Atom,
  CircleDot,
  Database,
  Hash,
  MoreHorizontal,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { CardSpotlight } from '@/components/ui/card-spotlight';
import type { DeviceCardProps } from '@/types/device';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

/**
 * DeviceDetailsTab Component
 *
 * Main component for displaying device details in a card-based format.
 * Organizes device information into three cards:
 * - System Information: Core device properties like paradigm, type, qubits, etc.
 * - Pricing & Operations: Cost information and operational status
 * - Access: Domain restrictions and availability settings
 *
 * Features:
 * - Card-based layout for better visual organization
 * - Visual status indicators with badges
 * - Responsive design with proper spacing
 */
export function DeviceDetailsTab({ device }: { device: DeviceCardProps }) {
  // Helper function to render status badge
  const renderStatusBadge = () => (
    <div className="flex items-center gap-1" data-testid="detail-queue-status">
      <Badge
        variant="outline"
        className="flex w-fit gap-1 px-2 text-muted-foreground [&_svg]:size-3"
      >
        {['online', 'active'].includes((device.status || '').toLowerCase()) ? (
          <CheckCircle2Icon className="text-green-500" />
        ) : (
          <WifiOff className="text-red-500" />
        )}
        {device.status || 'unknown'}
        {(device.status === 'OFFLINE' || device.status === 'RETIRED') && device.statusMsg && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="ml-1 cursor-pointer align-middle">
                  <Info className="size-4 text-gray-400" />
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="max-w-xs border-sidebar-border bg-sidebar text-xs text-foreground"
              >
                {device.statusMsg}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </Badge>
    </div>
  );

  // Helper function to render availability badge
  const renderAvailabilityBadge = () => {
    return (
      <Badge
        variant={device.isAvailable ? 'default' : 'secondary'}
        className="flex items-center gap-1 px-2 py-0.5 text-xs"
        data-testid="detail-availability"
        data-available={Boolean(device.isAvailable)}
      >
        {device.isAvailable ? (
          <>
            <CircleDot className="size-2.5" />
            Accepting Jobs
          </>
        ) : (
          <>
            <WifiOff className="size-2.5" />
            Not Accepting Jobs
          </>
        )}
      </Badge>
    );
  };

  // Helper function to render domains with tooltip for many domains
  const renderDomains = (
    domains: string | string[] | undefined,
    variant: 'outline' | 'destructive' = 'outline',
    maxVisible: number = 3,
  ) => {
    // Handle case where domains might be a string or array
    const domainsArray = Array.isArray(domains)
      ? domains
      : typeof domains === 'string'
        ? domains
            .split(',')
            .map((d) => d.trim())
            .filter(Boolean)
        : [];

    if (domainsArray.length === 0) {
      return (
        <Badge variant="outline" className="text-xs text-muted-foreground">
          None
        </Badge>
      );
    }

    const visibleDomains = domainsArray.slice(0, maxVisible);
    const hiddenDomains = domainsArray.slice(maxVisible);
    const hasHiddenDomains = hiddenDomains.length > 0;

    return (
      <div
        className="flex items-center gap-1"
        data-testid="detail-domains"
        data-domains-full={domainsArray.join(',')}
        data-has-hidden-domains={hasHiddenDomains}
      >
        {visibleDomains.map((domain: string, index: number) => (
          <Badge key={index} variant={variant} className="text-xs">
            {domain}
          </Badge>
        ))}
        {hasHiddenDomains && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex h-5 w-6 cursor-context-menu items-center justify-center rounded"
                  data-testid="domains-tooltip-trigger"
                  tabIndex={0}
                  role="button"
                  aria-haspopup="dialog"
                >
                  <MoreHorizontal className="size-3 text-muted-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="max-w-xs truncate border-sidebar-border bg-sidebar p-3 text-foreground"
              >
                <div className="space-y-2">
                  <div className="text-xs font-medium text-muted-foreground">
                    All Domains ({domainsArray.length})
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {domainsArray.map((domain: string, index: number) => (
                      <Badge key={index} variant={variant} className="text-xs">
                        {domain}
                      </Badge>
                    ))}
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  };

  return (
    <div className="mt-2 grid grid-cols-1 gap-6 lg:grid-cols-3">
      {/* System Information Card */}
      <CardSpotlight
        radius={200}
        color="rgba(139, 92, 246, 0.15)"
        className="w-full rounded-xl border border-sidebar-border bg-sidebar shadow-sm backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
        showGradient={false}
        lightModeColors={[
          [200, 180, 255],
          [255, 255, 255],
        ]}
      >
        <div className="px-4 py-0">
          <div className="mb-6 flex flex-col items-center gap-1 text-base font-semibold">
            <Info className="size-5 text-brand" />
            System Information
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Hash className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">ID:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-id">
                <code className="rounded bg-muted px-1.5 py-0.5 font-mono text-xs">
                  {device.qrn || 'N/A'}
                </code>
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Cpu className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Paradigm:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-paradigm">
                {device.paradigm || 'N/A'}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Code className="size-3 text-muted-foreground" />
                <span
                  className="text-xs text-muted-foreground"
                  data-testid="detail-modalityOrProcessor-label"
                >
                  {device.type?.toLowerCase() === 'simulator' ? 'Processor Type:' : 'Modality:'}
                </span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-modalityOrProcessor-value">
                {device.modality || device.processorType || 'N/A'}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Atom className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Qubits:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-qubits">
                <code className="rounded bg-muted px-1.5 py-0.5 font-mono text-xs">
                  {device.numberQubits || 'N/A'}
                </code>
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Package className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Run Input Types:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-run-input-types">
                {(() => {
                  const runInputTypes = device.runInputTypes;
                  if (!runInputTypes) return 'N/A';

                  const inputArray = Array.isArray(runInputTypes)
                    ? runInputTypes
                    : runInputTypes.split(',').map((s) => s.trim());

                  // If more than 2 items, show first 2 with tooltip
                  if (inputArray.length > 2) {
                    const displayText = inputArray.slice(0, 2).join(', ') + '...';
                    return (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="cursor-pointer">{displayText}</span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="max-w-xs border-sidebar-border bg-sidebar text-xs text-foreground"
                          >
                            {inputArray.join(', ')}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  }

                  return inputArray.join(', ');
                })()}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Radio className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Noise Models:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-noise-models">
                {Array.isArray(device.noiseModels) && device.noiseModels.length > 0
                  ? device.noiseModels.join(', ')
                  : 'N/A'}
              </div>
            </div>
          </div>
        </div>
      </CardSpotlight>

      {/* Pricing & Operations Card */}
      <CardSpotlight
        radius={200}
        color="rgba(139, 92, 246, 0.15)"
        className="w-full rounded-xl border border-sidebar-border bg-sidebar shadow-sm backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
        showGradient={false}
        lightModeColors={[
          [200, 180, 255],
          [255, 255, 255],
        ]}
      >
        <div className="px-4 py-0">
          <div className="mb-6 flex flex-col items-center gap-1 text-base font-semibold">
            <DollarSign className="size-5 text-brand" />
            Pricing & Operations
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Cloud className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Provider:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-provider">
                {typeof device.providerId === 'string'
                  ? device.providerId
                  : device.providerId?.provider || 'N/A'}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Building2 className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Vendor:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-vendor">
                {device.vendor || 'N/A'}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            {device.pricing?.perMinute ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CreditCard className="size-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Cost / Minute:</span>
                </div>
                <div className="text-sm font-medium" data-testid="detail-cost-per-minute">
                  {device.pricing.perMinute ? `$${device.pricing.perMinute}` : 'Free'}
                </div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="size-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Cost / Task:</span>
                  </div>
                  <div className="text-sm font-medium" data-testid="detail-cost-per-task">
                    {device.pricing?.perTask ? `$${device.pricing.perTask}` : 'Free'}
                  </div>
                </div>
                <div className="my-2 border-b border-border/20"></div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="size-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Cost / Shot:</span>
                  </div>
                  <div className="text-sm font-medium" data-testid="detail-cost-per-shot">
                    {device.pricing?.perShot ? `$${device.pricing.perShot}` : 'Free'}
                  </div>
                </div>
              </>
            )}
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Pending Jobs:</span>
              </div>
              <div className="text-sm font-medium" data-testid="detail-pending-jobs">
                {device.pendingJobs || 0}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle2Icon className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Queue Status:</span>
              </div>
              <div className="mt-1">{renderStatusBadge()}</div>
            </div>
          </div>
        </div>
      </CardSpotlight>

      {/* Access Card */}
      <CardSpotlight
        radius={200}
        color="rgba(139, 92, 246, 0.15)"
        className="w-full rounded-xl border border-sidebar-border bg-sidebar shadow-sm backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
        showGradient={false}
        lightModeColors={[
          [200, 180, 255],
          [255, 255, 255],
        ]}
      >
        <div className="px-4 py-0">
          <div className="mb-6 flex flex-col items-center gap-1 text-base font-semibold">
            <Shield className="size-5 text-brand" />
            Access
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CircleDot className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Availability:</span>
              </div>
              <div className="mt-1">{renderAvailabilityBadge()}</div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Visibility:</span>
              </div>
              <div className="mt-1">
                <Badge
                  variant={device.visibility === 'public' ? 'default' : 'secondary'}
                  className="flex items-center gap-1 px-2 py-0.5 text-xs"
                  data-testid="detail-visibility"
                  data-visibility={device.visibility || 'unknown'}
                >
                  {device.visibility === 'public' ? (
                    <>
                      <Unlock className="size-2.5" />
                      Public
                    </>
                  ) : (
                    <>
                      <Lock className="size-2.5" />
                      Private
                    </>
                  )}
                </Badge>
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Whitelisted Domains:</span>
              </div>
              <div className="mt-1" data-testid="detail-whitelist">
                {renderDomains(device.whiteListedDomains, 'outline', 0)}
              </div>
            </div>
            <div className="my-2 border-b border-border/20"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Ban className="size-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Blacklisted Domains:</span>
              </div>
              <div className="mt-1" data-testid="detail-blacklist">
                {renderDomains(device.blackListedDomains, 'destructive', 0)}
              </div>
            </div>
          </div>
        </div>
      </CardSpotlight>
    </div>
  );
}
