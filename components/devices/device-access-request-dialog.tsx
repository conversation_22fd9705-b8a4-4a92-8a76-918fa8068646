'use client';

import React from 'react';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Key, Eye, Edit, Building, Cpu, Zap, AlertCircle, CheckCircle, Info } from 'lucide-react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useAvailableDevices } from '@/hooks/use-device-management';
import { useRequestDeviceAccess } from '@/hooks/use-device-access';
import { DeviceAccessLevel } from '@/types/provider';
import { validateDeviceAccessRequest } from './device-validation';
import { Zod<PERSON>rror } from 'zod';
import { AvailableDevice } from '@/types/device';

interface DeviceAccessRequestDialogProps {
  orgId: string;
  device?: AvailableDevice;
  trigger?: React.ReactNode;
  defaultRequestType?: DeviceAccessLevel;
}

// Constants for better maintainability
const ACCESS_TYPES = {
  read: {
    icon: Eye,
    color: 'text-green-600',
    title: 'Read Access',
    description: 'View device info and status',
  },
  write: {
    icon: Edit,
    color: 'text-blue-600',
    title: 'Write Access',
    description: 'View +Edit device info and status',
  },
} as const;

const DURATION_SUGGESTIONS = [
  '1 week',
  '2 weeks',
  '1 month',
  '3 months',
  '6 months',
  'Ongoing research',
];

// Validation helper using Zod
const validateForm = (
  selectedDevice: string,
  selectedDeviceName: string,
  justification: string,
  requestType: DeviceAccessLevel,
  accessDuration?: string,
): Record<string, string> => {
  try {
    validateDeviceAccessRequest({
      deviceId: selectedDevice,
      requestType,
      justification: justification.trim(),
      accessDuration,
    });

    return {}; // No errors
  } catch (error) {
    if (error instanceof ZodError) {
      const errors: Record<string, string> = {};

      for (const err of error.errors) {
        const field = err.path[0] as string;
        errors[field] = err.message;
      }

      return errors;
    }

    console.error('❌ [DEVICE-ACCESS-DIALOG] Unexpected validation error:', error);
    return { general: 'Validation failed. Please check your inputs.' };
  }
};

// Device card component for better reusability
const DeviceCard = ({
  device,
  compact = false,
}: {
  device: AvailableDevice;
  compact?: boolean;
}) => (
  <div className={`flex items-center gap-3 ${compact ? '' : 'rounded-lg border bg-muted/30 p-4'}`}>
    {device.logo?.light && (
      <Image
        src={device.logo.light}
        alt={`${device.provider} logo`}
        width={compact ? 20 : 32}
        height={compact ? 20 : 32}
        className={`object-contain ${compact ? 'size-5' : 'size-8'}`}
        onError={(e) => {
          e.currentTarget.style.display = 'none';
        }}
      />
    )}
    <div className="min-w-0 flex-1">
      <div className="flex flex-wrap items-center gap-2">
        <h4 className={`truncate font-semibold ${compact ? 'text-sm' : ''}`}>{device.name}</h4>
        <Badge
          variant={device.status === 'ONLINE' ? 'default' : 'destructive'}
          className="shrink-0 text-xs"
        >
          {device.status}
        </Badge>
      </div>
      <div
        className={`mt-1 flex flex-wrap items-center gap-3 text-muted-foreground ${compact ? 'text-xs' : 'text-sm'}`}
      >
        <div className="flex items-center gap-1">
          <Building className="size-3 shrink-0" />
          <span className="truncate">{device.provider}</span>
        </div>
        <div className="flex items-center gap-1">
          <Cpu className="size-3 shrink-0" />
          <span>{device.numberQubits || 'N/A'} qubits</span>
        </div>
        <div className="flex items-center gap-1">
          <Zap className="size-3 shrink-0" />
          <span>{device.type}</span>
        </div>
      </div>
      {!compact && device.deviceDescription && (
        <p className="mt-2 line-clamp-3 text-xs text-muted-foreground">
          {device.deviceDescription.length > 150
            ? `${device.deviceDescription.slice(0, 150)}...`
            : device.deviceDescription}
        </p>
      )}
    </div>
  </div>
);

export function DeviceAccessRequestDialog({
  orgId,
  device,
  trigger,
  defaultRequestType,
}: DeviceAccessRequestDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<string>(device?._id || '');
  const [selectedDeviceName, setSelectedDeviceName] = useState<string>(device?.name || '');
  const [requestType, setRequestType] = useState<DeviceAccessLevel>(defaultRequestType || 'read');
  const [justification, setJustification] = useState('');
  const [accessDuration, setAccessDuration] = useState('');
  const [showDurationSuggestions, setShowDurationSuggestions] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { mutate: requestAccess, isPending, error } = useRequestDeviceAccess();
  const { data: availableDevices, isLoading: devicesLoading } = useAvailableDevices('', {});

  // Memoize device details lookup
  const selectedDeviceDetails = useMemo(
    () => device || availableDevices?.find((d) => d._id === selectedDevice),
    [device, availableDevices, selectedDevice],
  );

  // Auto-populate when device is provided and set default request type
  useEffect(() => {
    if (device) {
      setSelectedDevice(device._id);
      if (defaultRequestType) {
        setRequestType(defaultRequestType);
      }
    }
  }, [device, defaultRequestType]);

  // Clear errors when form values change
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      setErrors({});
    }
  }, [selectedDevice, justification, errors]);

  // Generate smart placeholder text
  const placeholderText = useMemo(() => {
    if (!selectedDeviceDetails)
      return 'Explain why you need access to this device and how you plan to use it...';

    const deviceType =
      selectedDeviceDetails.type === 'QPU' ? 'quantum processor' : 'quantum simulator';
    const accessTypeText = requestType === 'write' ? 'submit jobs to' : 'monitor';

    return `Explain why you need to ${accessTypeText} the ${selectedDeviceDetails.name} ${deviceType}. Include details about your research, project goals, and expected usage patterns...`;
  }, [selectedDeviceDetails, requestType]);

  // Form validation with debounce
  const validateFormData = useCallback(() => {
    const newErrors = validateForm(
      selectedDevice,
      selectedDeviceDetails?.name || '',
      justification,
      requestType,
      accessDuration,
    );
    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;

    return isValid;
  }, [selectedDevice, justification, requestType, accessDuration, selectedDeviceDetails?.name]);

  // Reset form state
  const resetForm = useCallback(() => {
    setSelectedDevice(device?._id || '');
    setSelectedDeviceName(device?.name || '');
    setRequestType(defaultRequestType || 'read');
    setJustification('');
    setAccessDuration('');
    setErrors({});
    setShowDurationSuggestions(false);
  }, [device?._id, device?.name, defaultRequestType]);

  const handleSubmit = useCallback(() => {
    if (!validateFormData()) {
      return;
    }

    const requestData = {
      deviceId: selectedDevice,
      deviceName: selectedDeviceDetails?.name || '',
      requestType,
      justification: justification.trim(),
      accessDuration: accessDuration || undefined,
      orgId: orgId,
    };

    requestAccess(requestData, {
      onSuccess: () => {
        setIsOpen(false);
        resetForm();
      },
      onError: (error) => {
        console.error('❌ [DEVICE-ACCESS-DIALOG] Failed to submit device access request:', error);
      },
    });
  }, [
    selectedDevice,
    selectedDeviceName,
    requestType,
    justification,
    accessDuration,
    requestAccess,
    orgId,
    selectedDeviceDetails?.name,
    validateFormData,
    resetForm,
  ]);

  const handleDurationSuggestion = useCallback((suggestion: string) => {
    setAccessDuration(suggestion);
    setShowDurationSuggestions(false);
  }, []);

  // Character count for justification
  const justificationLength = justification.length;
  const minJustificationLength = 20;

  // Determine if this is an upgrade request (write access when defaultRequestType is provided)
  const isUpgradeRequest = defaultRequestType === 'write';

  // Debug logging
  console.log('🔍 [DEVICE-ACCESS-DIALOG] Debug info:', {
    defaultRequestType,
    isUpgradeRequest,
    requestType,
    device: device?.name,
    dialogType: isUpgradeRequest ? 'UPGRADE' : 'INITIAL_REQUEST',
  });

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) resetForm();
      }}
    >
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Key className="mr-2 size-4" />
            Request Access
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isUpgradeRequest ? 'Request Write Access' : 'Request Device Access'}
            {/* Debug indicator - remove in production */}
            <span className="ml-2 text-xs text-muted-foreground">
              ({isUpgradeRequest ? 'UPGRADE' : 'INITIAL'})
            </span>
          </DialogTitle>
          <DialogDescription>
            {isUpgradeRequest
              ? 'Submit a request to upgrade your access from read-only to write access. QBraid admins will review your request and respond within 24 hours.'
              : 'Submit a request to access quantum computing devices. QBraid admins will review your request and respond within 1-2 business days.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error banner */}
          {error && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-950/20">
              <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
                <AlertCircle className="size-4" />
                <span className="text-sm font-medium">Failed to submit request</span>
              </div>
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {error.message || 'An unexpected error occurred. Please try again.'}
              </p>
            </div>
          )}

          {/* Device Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Quantum Device <span className="text-red-500">*</span>
            </label>
            {device ? (
              <DeviceCard device={device} />
            ) : (
              <div className="space-y-2">
                <Select
                  value={selectedDevice}
                  onValueChange={setSelectedDevice}
                  disabled={devicesLoading}
                >
                  <SelectTrigger className={errors.device ? 'border-red-500' : ''}>
                    <SelectValue
                      placeholder={
                        devicesLoading ? 'Loading devices...' : 'Select a quantum device'
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {availableDevices?.map((dev) => (
                      <SelectItem key={dev._id} value={dev._id}>
                        <DeviceCard device={dev} compact />
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.device && (
                  <p className="flex items-center gap-1 text-sm text-red-500">
                    <AlertCircle className="size-3" />
                    {errors.device}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Access Type - Only show if not an upgrade request */}
          {!isUpgradeRequest && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Access Type <span className="text-red-500">*</span>
              </label>
              <Select
                value={requestType}
                onValueChange={(value: DeviceAccessLevel) => setRequestType(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(ACCESS_TYPES).map(([key, config]) => {
                    const Icon = config.icon;
                    return (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-start gap-2 py-1">
                          <Icon className={`size-4 ${config.color} mt-0.5 shrink-0`} />
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2 text-sm font-medium">
                              {config.title}
                              <span className="text-xs text-muted-foreground">
                                {config.description}
                              </span>
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Write Access Upgrade Info - Only show for upgrade requests */}
          {isUpgradeRequest && (
            <div className="flex items-center gap-3 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
              {(() => {
                const config = ACCESS_TYPES.write;
                const Icon = config.icon;
                return (
                  <>
                    <div
                      className={`rounded-lg p-2 ${config.color.replace('text-', 'bg-').replace('600', '100')} dark:${config.color.replace('text-', 'bg-').replace('600', '900')}/20`}
                    >
                      <Icon className={`size-6 ${config.color}`} />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold">{config.title}</div>
                      <div className="text-sm text-muted-foreground">{config.description}</div>
                      <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                        Upgrading from read access
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          )}

          {/* Justification */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">
                Justification <span className="text-red-500">*</span>
              </label>
              <span
                className={`text-xs ${
                  justificationLength < minJustificationLength
                    ? 'text-red-500'
                    : 'text-muted-foreground'
                }`}
              >
                {justificationLength}/{minJustificationLength}+ chars
              </span>
            </div>
            <Textarea
              placeholder={placeholderText}
              value={justification}
              onChange={(e) => setJustification(e.target.value)}
              className={`min-h-[120px] resize-none ${errors.justification ? 'border-red-500' : ''}`}
              maxLength={1000}
            />
            {errors.justification ? (
              <p className="flex items-center gap-1 text-sm text-red-500">
                <AlertCircle className="size-3" />
                {errors.justification}
              </p>
            ) : (
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">
                  Provide a detailed explanation to help reviewers understand your use case.
                  {selectedDeviceDetails?.type === 'QPU' && (
                    <span className="font-medium text-amber-600 dark:text-amber-400">
                      {' '}
                      QPU access requires strong justification and may have limited availability.
                    </span>
                  )}
                </p>
                {isUpgradeRequest && (
                  <div className="flex items-start gap-1 rounded border border-blue-200 bg-blue-50 p-2 dark:border-blue-800 dark:bg-blue-950/20">
                    <Info className="mt-0.5 size-3 shrink-0 text-blue-600 dark:text-blue-400" />
                    <p className="text-xs text-blue-600 dark:text-blue-400">
                      <strong>Upgrade Request:</strong> You already have read access to this device.
                      Please explain why you need write access and how it will benefit your
                      research.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Access Duration */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Expected Usage Duration</label>
            <div className="relative">
              <Input
                placeholder="e.g., 1 month, ongoing research project"
                value={accessDuration}
                onChange={(e) => setAccessDuration(e.target.value)}
                onFocus={() => setShowDurationSuggestions(true)}
                onBlur={() => setTimeout(() => setShowDurationSuggestions(false), 200)}
              />
              {showDurationSuggestions && (
                <div className="absolute inset-x-0 top-full z-10 mt-1 rounded-md border bg-background shadow-lg">
                  {DURATION_SUGGESTIONS.map((suggestion) => (
                    <button
                      key={suggestion}
                      className="w-full px-3 py-2 text-left text-sm transition-colors hover:bg-muted"
                      onMouseDown={() => handleDurationSuggestion(suggestion)}
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Optional: Help reviewers understand your timeline and usage needs.
            </p>
          </div>

          {/* Info Panel */}
          {selectedDeviceDetails && (
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
              <div className="flex items-start gap-2">
                <CheckCircle className="mt-0.5 size-4 shrink-0 text-blue-600 dark:text-blue-400" />
                <div>
                  <h4 className="mb-1 font-medium text-blue-800 dark:text-blue-300">
                    Review Process
                  </h4>
                  <div className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                    <p>• Requests are typically reviewed within 1-2 business days</p>
                    <p>• You&apos;ll receive email notifications about status updates</p>
                    <p>
                      • {selectedDeviceDetails.type === 'QPU' ? 'QPU' : 'Simulator'} access
                      permissions will be granted based on your justification
                    </p>
                    {isUpgradeRequest && (
                      <p className="font-medium text-blue-700 dark:text-blue-300">
                        • <strong>Priority Review:</strong> Upgrade requests are prioritized and
                        typically processed within 24 hours
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isPending}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isPending || !selectedDevice || !justification.trim()}
              className="min-w-[140px]"
            >
              {isPending ? (
                <div className="flex items-center gap-2">
                  <div className="size-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  Submitting...
                </div>
              ) : isUpgradeRequest ? (
                'Submit Write Access Request'
              ) : (
                `Submit ${ACCESS_TYPES[requestType].title} Request`
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
