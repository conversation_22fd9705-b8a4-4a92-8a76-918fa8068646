'use client';

import { useState, useMemo } from 'react';
import Image from 'next/image';
import {
  Building,
  Cpu,
  Gauge,
  Search,
  Filter,
  X,
  ChevronDown,
  Activity,
  TrendingUp,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Checkbox } from '@/components/ui/checkbox';
import { useAvailableDevices } from '@/hooks/use-device-management';
import { useOrgDeviceAccess } from '@/hooks/use-device-access';
import { useDeviceAccessRequests } from '@/hooks/use-device-access';
import { useRequestDeviceAccess } from '@/hooks/use-device-access';
import { DeviceAccessRequestDialog } from '@/components/devices/device-access-request-dialog';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Key, Edit, Clock } from 'lucide-react';
import { AvailableDevice } from '@/types/device';

// Device Access Button Component
interface DeviceAccessButtonProps {
  device: AvailableDevice;
  orgId: string;
  hasReadAccess: boolean;
  hasWriteAccess: boolean;
  pendingRequest: any;
  onRequestAccess: (data: any) => void;
  isRequestingAccess: boolean;
}

function DeviceAccessButton({
  device,
  orgId,
  hasReadAccess,
  hasWriteAccess,
  pendingRequest,
}: DeviceAccessButtonProps) {
  // If we have write access, this shouldn't be shown (filtered out)
  if (hasWriteAccess) return null;

  // If there's a pending request
  if (pendingRequest) {
    return (
      <Button className="w-full" variant="outline" disabled>
        <Clock className="mr-2 size-4" />
        Pending Request
        <Badge variant="secondary" className="ml-auto">
          {pendingRequest.requestType === 'write' ? 'Write' : 'Read'}
        </Badge>
      </Button>
    );
  }

  // If we have read access, show upgrade to write access option
  if (hasReadAccess) {
    return (
      <DeviceAccessRequestDialog
        orgId={orgId}
        device={device}
        trigger={
          <Button className="w-full" variant="default">
            <Edit className="mr-2 size-4" />
            Request Write Access
          </Button>
        }
        defaultRequestType="write"
      />
    );
  }

  // No access yet, show request access dialog
  return (
    <DeviceAccessRequestDialog
      orgId={orgId}
      device={device}
      trigger={
        <Button className="w-full" variant="outline">
          <Key className="mr-2 size-4" />
          Request Access
        </Button>
      }
    />
  );
}

// Filter state interface
interface FilterState {
  search: string;
  type: string;
  status: string;
  paradigm: string[];
  architecture: string[];
  provider: string[];
  qubitsRange: [number, number];
  runInputTypes: string[];
  showOnlyAvailable: boolean;
  sortBy: 'name' | 'qubits' | 'provider';
  sortOrder: 'asc' | 'desc';
}

// Helper function to normalize run input types
function normalizeRunInputTypes(runInputTypes: any): string[] {
  if (Array.isArray(runInputTypes)) {
    return runInputTypes;
  }
  if (typeof runInputTypes === 'string') {
    return [runInputTypes];
  }
  return [];
}

export function AvailableDevicesTab() {
  const { currentOrgId } = useOrgContext();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    type: 'all',
    status: 'all',
    paradigm: [],
    architecture: [],
    provider: [],
    qubitsRange: [0, 1000],
    runInputTypes: [],
    showOnlyAvailable: false,
    sortBy: 'name',
    sortOrder: 'asc',
  });

  const {
    data: devices,
    isLoading,
    error,
  } = useAvailableDevices(currentOrgId || '', {
    type: filters.type === 'all' ? undefined : filters.type,
    status: filters.status === 'all' ? undefined : filters.status,
  });

  const { data: deviceAccess } = useOrgDeviceAccess(currentOrgId || '');
  const { data: requestsData } = useDeviceAccessRequests('', currentOrgId || undefined);
  const { mutate: requestAccess, isPending: isRequestingAccess } = useRequestDeviceAccess();

  // Extract unique values for filters
  const filterOptions = useMemo(() => {
    if (!devices)
      return {
        paradigms: [],
        architectures: [],
        providers: [],
        runInputTypes: [],
      };

    const paradigms = [...new Set(devices.map((d) => d.paradigm).filter(Boolean))];
    const architectures = [...new Set(devices.map((d) => d.architecture).filter(Boolean))];
    const providers = [...new Set(devices.map((d) => d.provider).filter(Boolean))];
    const runInputTypes = [
      ...new Set(devices.flatMap((d) => normalizeRunInputTypes(d.runInputTypes)).filter(Boolean)),
    ];

    return { paradigms, architectures, providers, runInputTypes };
  }, [devices]);

  // Helper functions to determine device access status
  const getDeviceAccessStatus = useMemo(() => {
    return (deviceId: string) => {
      if (!deviceAccess) {
        return { hasReadAccess: false, hasWriteAccess: false };
      }

      const hasReadAccess = deviceAccess.read?.some((device) => device._id === deviceId) || false;
      const hasWriteAccess = deviceAccess.write?.some((device) => device._id === deviceId) || false;

      return {
        hasReadAccess,
        hasWriteAccess,
      };
    };
  }, [deviceAccess]);

  const getPendingRequestStatus = useMemo(() => {
    return (deviceId: string) => {
      if (!requestsData?.requests) return null;

      const pendingRequest = requestsData.requests.find(
        (request: any) => request.deviceId === deviceId && request.status === 'pending',
      );

      return pendingRequest;
    };
  }, [requestsData]);

  // Advanced filtering logic with access control
  const filteredDevices = useMemo(() => {
    if (!devices) return [];

    return devices
      .filter((device) => {
        // Skip devices we already have write access to
        const { hasWriteAccess } = getDeviceAccessStatus(device._id);
        if (hasWriteAccess) return false;

        // Search filter
        if (
          filters.search &&
          !(
            device.name.toLowerCase().includes(filters.search.toLowerCase()) ||
            device.qrn.toLowerCase().includes(filters.search.toLowerCase()) ||
            device.provider.toLowerCase().includes(filters.search.toLowerCase()) ||
            device.deviceDescription?.toLowerCase().includes(filters.search.toLowerCase())
          )
        )
          return false;

        // Paradigm filter
        if (filters.paradigm.length > 0 && !filters.paradigm.includes(device.paradigm))
          return false;

        // Architecture filter
        if (filters.architecture.length > 0 && !filters.architecture.includes(device.architecture))
          return false;

        // Provider filter
        if (filters.provider.length > 0 && !filters.provider.includes(device.provider))
          return false;

        // Qubits range filter
        const qubits = device.numberQubits || 0;
        if (qubits < filters.qubitsRange[0] || qubits > filters.qubitsRange[1]) return false;

        // Run input types filter
        if (filters.runInputTypes.length > 0) {
          const deviceTypes = normalizeRunInputTypes(device.runInputTypes);
          if (!filters.runInputTypes.some((type) => deviceTypes.includes(type))) return false;
        }

        // Availability filter
        if (filters.showOnlyAvailable && !device.isAvailable) return false;

        return true;
      })
      .sort((a, b) => {
        let compareValue = 0;

        switch (filters.sortBy) {
          case 'name': {
            compareValue = a.name.localeCompare(b.name);
            break;
          }
          case 'qubits': {
            compareValue = (a.numberQubits || 0) - (b.numberQubits || 0);
            break;
          }
          case 'provider': {
            compareValue = a.provider.localeCompare(b.provider);
            break;
          }
        }

        return filters.sortOrder === 'asc' ? compareValue : -compareValue;
      });
  }, [devices, filters, getDeviceAccessStatus]);

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: '',
      type: 'all',
      status: 'all',
      paradigm: [],
      architecture: [],
      provider: [],
      qubitsRange: [0, 1000],
      runInputTypes: [],
      showOnlyAvailable: false,
      sortBy: 'name',
      sortOrder: 'asc',
    });
  };

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.search) count++;
    if (filters.type !== 'all') count++;
    if (filters.status !== 'all') count++;
    if (filters.paradigm.length > 0) count++;
    if (filters.architecture.length > 0) count++;
    if (filters.provider.length > 0) count++;
    if (filters.qubitsRange[0] > 0 || filters.qubitsRange[1] < 1000) count++;
    if (filters.runInputTypes.length > 0) count++;
    if (filters.showOnlyAvailable) count++;
    return count;
  }, [filters]);

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="py-8 text-center">
          <p className="text-destructive">Failed to load available devices</p>
          <p className="mt-1 text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Available Quantum Devices</h3>
          <p className="text-muted-foreground">
            Browse and request access to quantum computing devices
          </p>
        </div>
      </div>

      {/* Search and Advanced Filters */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search by name, ID, provider, or description..."
              value={filters.search}
              onChange={(e) => setFilters((prev) => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="size-4" />
                Advanced Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {activeFilterCount}
                  </Badge>
                )}
                <ChevronDown className="size-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[420px] p-0" align="end">
              <div className="max-h-[600px] space-y-4 overflow-y-auto p-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Filter Devices</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={resetFilters}
                    className="h-8 px-2 lg:px-3"
                  >
                    Reset all
                  </Button>
                </div>

                <Separator />

                {/* Basic Filters */}
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label>Device Type</Label>
                    <Select
                      value={filters.type}
                      onValueChange={(value) => setFilters((prev) => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="QPU">QPU</SelectItem>
                        <SelectItem value="Simulator">Simulator</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select
                      value={filters.status}
                      onValueChange={(value) => setFilters((prev) => ({ ...prev, status: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="ONLINE">Online</SelectItem>
                        <SelectItem value="OFFLINE">Offline</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                {/* Multi-select Filters */}
                <Collapsible>
                  <CollapsibleTrigger className="flex w-full items-center justify-between rounded p-2 hover:bg-muted/50">
                    <Label className="cursor-pointer">Paradigm</Label>
                    <ChevronDown className="size-4" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <div className="space-y-2">
                      {filterOptions.paradigms.map((paradigm) => (
                        <div key={paradigm} className="flex items-center space-x-2">
                          <Checkbox
                            checked={filters.paradigm.includes(paradigm)}
                            onCheckedChange={(checked) => {
                              setFilters((prev) => ({
                                ...prev,
                                paradigm: checked
                                  ? [...prev.paradigm, paradigm]
                                  : prev.paradigm.filter((p) => p !== paradigm),
                              }));
                            }}
                          />
                          <Label className="cursor-pointer text-sm font-normal">{paradigm}</Label>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible>
                  <CollapsibleTrigger className="flex w-full items-center justify-between rounded p-2 hover:bg-muted/50">
                    <Label className="cursor-pointer">Architecture</Label>
                    <ChevronDown className="size-4" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <div className="space-y-2">
                      {filterOptions.architectures.map((arch) => (
                        <div key={arch} className="flex items-center space-x-2">
                          <Checkbox
                            checked={filters.architecture.includes(arch)}
                            onCheckedChange={(checked) => {
                              setFilters((prev) => ({
                                ...prev,
                                architecture: checked
                                  ? [...prev.architecture, arch]
                                  : prev.architecture.filter((a) => a !== arch),
                              }));
                            }}
                          />
                          <Label className="cursor-pointer text-sm font-normal">{arch}</Label>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible>
                  <CollapsibleTrigger className="flex w-full items-center justify-between rounded p-2 hover:bg-muted/50">
                    <Label className="cursor-pointer">Provider</Label>
                    <ChevronDown className="size-4" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <div className="max-h-[150px] space-y-2 overflow-y-auto">
                      {filterOptions.providers.map((provider) => (
                        <div key={provider} className="flex items-center space-x-2">
                          <Checkbox
                            checked={filters.provider.includes(provider)}
                            onCheckedChange={(checked) => {
                              setFilters((prev) => ({
                                ...prev,
                                provider: checked
                                  ? [...prev.provider, provider]
                                  : prev.provider.filter((p) => p !== provider),
                              }));
                            }}
                          />
                          <Label className="cursor-pointer text-sm font-normal">{provider}</Label>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Separator />

                {/* Range Filters */}
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Cpu className="size-4" />
                      Qubits Range: {filters.qubitsRange[0]} - {filters.qubitsRange[1]}
                    </Label>
                    <Slider
                      value={filters.qubitsRange}
                      onValueChange={(value: number[]) =>
                        setFilters((prev) => ({ ...prev, qubitsRange: value as [number, number] }))
                      }
                      max={1000}
                      step={10}
                      className="w-full"
                    />
                  </div>
                </div>

                <Separator />

                {/* Additional Options */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="available-only" className="cursor-pointer">
                      Show only available devices
                    </Label>
                    <Switch
                      id="available-only"
                      checked={filters.showOnlyAvailable}
                      onCheckedChange={(checked: boolean) =>
                        setFilters((prev) => ({ ...prev, showOnlyAvailable: checked }))
                      }
                    />
                  </div>
                </div>

                <Separator />

                {/* Sort Options */}
                <div className="space-y-2">
                  <Label>Sort By</Label>
                  <div className="flex gap-2">
                    <Select
                      value={filters.sortBy}
                      onValueChange={(value: string) =>
                        setFilters((prev) => ({
                          ...prev,
                          sortBy: value as 'name' | 'qubits' | 'provider',
                        }))
                      }
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Name</SelectItem>
                        <SelectItem value="qubits">Qubits</SelectItem>

                        <SelectItem value="provider">Provider</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() =>
                        setFilters((prev) => ({
                          ...prev,
                          sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc',
                        }))
                      }
                    >
                      <TrendingUp
                        className={`size-4 transition-transform ${filters.sortOrder === 'desc' ? 'rotate-180' : ''}`}
                      />
                    </Button>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Active Filters Display */}
        {activeFilterCount > 0 && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {filters.type !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Type: {filters.type}
                <X
                  className="size-3 cursor-pointer"
                  onClick={() => setFilters((prev) => ({ ...prev, type: 'all' }))}
                />
              </Badge>
            )}
            {filters.status !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Status: {filters.status}
                <X
                  className="size-3 cursor-pointer"
                  onClick={() => setFilters((prev) => ({ ...prev, status: 'all' }))}
                />
              </Badge>
            )}
            {filters.paradigm.length > 0 && (
              <Badge variant="secondary" className="gap-1">
                Paradigm: {filters.paradigm.length}
                <X
                  className="size-3 cursor-pointer"
                  onClick={() => setFilters((prev) => ({ ...prev, paradigm: [] }))}
                />
              </Badge>
            )}
            {filters.provider.length > 0 && (
              <Badge variant="secondary" className="gap-1">
                Provider: {filters.provider.length}
                <X
                  className="size-3 cursor-pointer"
                  onClick={() => setFilters((prev) => ({ ...prev, provider: [] }))}
                />
              </Badge>
            )}
            {(filters.qubitsRange[0] > 0 || filters.qubitsRange[1] < 1000) && (
              <Badge variant="secondary" className="gap-1">
                Qubits: {filters.qubitsRange[0]}-{filters.qubitsRange[1]}
                <X
                  className="size-3 cursor-pointer"
                  onClick={() => setFilters((prev) => ({ ...prev, qubitsRange: [0, 1000] }))}
                />
              </Badge>
            )}
            <Button variant="ghost" size="sm" onClick={resetFilters} className="h-7 text-xs">
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Device Grid */}
      {isLoading && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-8 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!isLoading && filteredDevices.length === 0 && (
        <div className="space-y-3 py-12 text-center">
          <div className="space-y-1 text-muted-foreground">
            <p className="text-lg font-medium">No devices found</p>
            <p className="text-sm">Try adjusting your filters or search terms</p>
          </div>
          {activeFilterCount > 0 && (
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Clear filters
            </Button>
          )}
        </div>
      )}

      {!isLoading && filteredDevices.length > 0 && (
        <>
          <div className="mb-4 flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredDevices.length} of {devices?.length || 0} devices
            </p>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Activity className="size-4" />
              {filteredDevices.filter((d) => d.status === 'ONLINE').length} online
            </div>
          </div>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredDevices.map((device) => (
              <Card key={device.qrn} className="transition-shadow hover:shadow-lg">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex flex-1 items-center gap-3">
                      {device.logo?.light && (
                        <Image
                          src={device.logo.light}
                          alt={`${device.provider} logo`}
                          width={32}
                          height={32}
                          className="size-8 object-contain"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      )}
                      <div className="flex-1">
                        <CardTitle className="text-lg">{device.name}</CardTitle>
                        <CardDescription className="flex items-center gap-1">
                          <Building className="size-3" />
                          {device.provider}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <Badge variant={device.type === 'QPU' ? 'default' : 'secondary'}>
                        {device.type}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <div
                          className={`size-2 rounded-full ${
                            device.status === 'ONLINE' ? 'bg-green-500' : 'bg-red-500'
                          }`}
                        />
                        <span className="text-xs text-muted-foreground">{device.status}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Cpu className="size-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Qubits:</span>
                      <span className="font-medium">
                        {device.numberQubits?.toString() || 'N/A'}
                      </span>
                    </div>
                    {/* Show architecture for QPU, processorType for Simulator */}
                    {device.type === 'QPU' && device.modality && (
                      <div className="flex items-center gap-2">
                        <Gauge className="size-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Modality:</span>
                        <span className="text-xs font-medium">{device.modality}</span>
                      </div>
                    )}
                    {device.type === 'Simulator' && device.processorType && (
                      <div className="flex items-center gap-2">
                        <Gauge className="size-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Type:</span>
                        <span className="text-xs font-medium">{device.processorType}</span>
                      </div>
                    )}
                  </div>

                  {/* Queue information removed - confidential */}

                  {device.deviceDescription && (
                    <div className="rounded bg-muted/30 p-2 text-xs text-muted-foreground">
                      {device.deviceDescription.length > 100
                        ? `${device.deviceDescription.slice(0, 100)}...`
                        : device.deviceDescription}
                    </div>
                  )}

                  <DeviceAccessButton
                    device={device as AvailableDevice}
                    orgId={currentOrgId || ''}
                    hasReadAccess={getDeviceAccessStatus(device._id).hasReadAccess}
                    hasWriteAccess={getDeviceAccessStatus(device._id).hasWriteAccess}
                    pendingRequest={getPendingRequestStatus(device._id)}
                    onRequestAccess={requestAccess}
                    isRequestingAccess={isRequestingAccess}
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
