/**
 * DeviceJobsTab component
 * -----------------------
 * This module defines the Jobs Info tab for the device card.
 * It fetches and displays a summary and table of quantum jobs for a device,
 * including charts, job statistics, and a searchable jobs table.
 */

import React, { useMemo, useState } from 'react';
import { DollarSign, Zap, ListChecks, Banknote, ArrowUpDown, AlertCircle } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip as ReTooltip,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { PercentBar } from '@/components/ui/percent-bar';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useJobsForDevice } from '@/hooks/use-api';
import type { JobsRowProps } from '@/types/jobs';
import { ColumnDef } from '@tanstack/react-table';
// framer-motion no longer used directly in this file
import RecentFailedJobs from '@/components/devices/recent-failed-jobs';

// Constants
const JOB_STATUS_COLORS: Record<string, string> = {
  // Pending statuses - using different shades for similar statuses
  INITIALIZING: 'hsl(var(--brand))', // Purple brand color
  INITIALIZED: 'hsl(var(--brand))', // Purple brand color
  CREATING: '#a855f7', // Lighter purple
  CREATED: '#8b5cf6', // Medium purple
  QUEUED: '#f59e0b', // Orange for queued
  VALIDATING: '#0ea5e9', // Sky blue for validating
  RUNNING: '#2563eb', // Blue for running
  CANCELLING: '#6b7280', // Gray for cancelling
  HOLD: '#7c3aed', // Different purple shade for hold
  UNKNOWN: '#9ca3af', // Light gray for unknown
  // Returned statuses
  COMPLETED: '#059669', // Emerald green for completed
  FAILED: '#6b7280', // Gray for failed
  CANCELLED: '#9ca3af', // Light gray for cancelled
};

// Utility functions
function formatDate(date?: Date | string) {
  if (!date) return 'N/A';
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleString();
}

function getJobStatusDistribution(jobsArray: JobsRowProps[]) {
  const statusCounts: Record<string, number> = {};
  jobsArray.forEach((job) => {
    const status = job.qbraidStatus || 'UNKNOWN';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });
  const total = jobsArray.length;
  return Object.entries(statusCounts).map(([status, count]) => ({
    name: status,
    value: count,
    percent: total > 0 ? ((count / total) * 100).toFixed(2) : '0.00',
  }));
}

function isStatusMatch(status?: string, filter?: string) {
  return (
    filter === 'All Statuses' ||
    status?.toLowerCase() === filter?.toLowerCase() ||
    (filter === 'Unsuccessful' &&
      (status?.toLowerCase() === 'failed' || status?.toLowerCase() === 'cancelled'))
  );
}

// DataTable column definitions
function createTableColumns(): ColumnDef<JobsRowProps & { id: number }>[] {
  return [
    {
      id: 'createdEnded',
      accessorKey: 'timeStamps.createdAt',
      sortUndefined: 'last',
      sortingFn: 'datetime',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="px-1"
        >
          Created At / Ended At
          <ArrowUpDown className="ml-2 size-4 items-center justify-center" />
        </Button>
      ),
      cell: ({ row }) => {
        const createdAt = row.original.timeStamps?.createdAt;
        const endedAt = row.original.timeStamps?.endedAt;
        return (
          <div className="flex flex-col text-xs">
            <span
              className="text-foreground"
              title={typeof createdAt === 'string' ? createdAt : createdAt?.toString()}
            >
              {formatDate(createdAt)}
            </span>
            <span
              className="text-muted-foreground/60"
              title={typeof endedAt === 'string' ? endedAt : endedAt?.toString()}
            >
              {formatDate(endedAt)}
            </span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'queuePosition',
      sortUndefined: 'last',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="px-1"
        >
          Queue Position
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => row.original.queuePosition ?? 'N/A',
      enableSorting: true,
    },
    {
      accessorKey: 'qbraidStatus',
      sortUndefined: 'last',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="px-1"
        >
          Job Status
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.original.qbraidStatus;
        const statusText = row.original.statusText;

        if (status === 'FAILED' && statusText) {
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <span>{status}</span>
                    <AlertCircle className="size-3 text-red-500" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">{statusText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        }

        return <span>{status}</span>;
      },
      enableSorting: true,
    },
    {
      accessorKey: 'shots',
      sortUndefined: 'last',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="px-1"
        >
          Shots
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => row.original.shots?.toLocaleString() ?? 'N/A',
      enableSorting: true,
    },
    {
      accessorKey: 'cost',
      sortUndefined: 'last',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="px-1"
        >
          Cost (Credits)
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => (row.original.cost === undefined ? 'N/A' : row.original.cost?.toFixed(4)),
      enableSorting: true,
    },
    {
      accessorKey: 'experimentType',
      header: ({ column }) => <div className="px-1">Experiment Type</div>,
      enableSorting: false,
    },
  ];
}

// Export functions
async function exportToPDF(tableData: any[], deviceId: string) {
  try {
    // @ts-ignore Dynamic import of ESM module without type declarations
    const jsPDFModule = await import('jspdf');
    // @ts-ignore Dynamic import of commonjs module without type declarations
    const autoTableModule = await import('jspdf-autotable');

    const doc = new jsPDFModule.jsPDF({ orientation: 'landscape' });

    const body = tableData.map((j) => [
      j.qbraidDeviceId,
      formatDate(j.timeStamps?.createdAt),
      formatDate(j.timeStamps?.endedAt),
      j.queuePosition ?? 'N/A',
      j.qbraidStatus ?? 'N/A',
      j.vendor ?? 'N/A',
      typeof j.providerId === 'string' ? j.providerId : (j.providerId?.provider ?? 'N/A'),
      j.shots ?? 'N/A',
      j.cost ?? 'N/A',
      j.experimentType ?? 'N/A',
    ]);

    autoTableModule.default(doc, {
      head: [
        [
          'Device ID (qBraid)',
          'Created At',
          'Ended At',
          'Queue Position',
          'Job Status',
          'Vendor',
          'Provider',
          'Shots',
          'Cost (Credits)',
          'Experiment Type',
        ],
      ],
      body,
      styles: { fontSize: 8 },
    });
    doc.save(`jobs_${deviceId}.pdf`);
  } catch (error) {
    console.error('PDF export failed', error);
  }
}

// Main component
export function DeviceJobsTab({ deviceId = 'qbraid_qir_simulator' }: { deviceId?: string }) {
  const [page, setPage] = useState(0);
  const [resultsPerPage, setResultsPerPage] = useState(5);

  const { data, isLoading } = useJobsForDevice(deviceId, 0, 'all');
  const jobsArray: JobsRowProps[] = Array.isArray((data as any)?.jobsArray)
    ? (data as any).jobsArray
    : [];

  // Filter jobs
  const filteredJobs = useMemo(
    () =>
      jobsArray.filter((job) => {
        if (job.qbraidDeviceId === 'No jobs found' || job.qbraidDeviceId === 'null-device') {
          return false;
        }
        return true; // No status filter needed since we use table filtering
      }),
    [jobsArray],
  );

  // Add IDs to the filtered jobs
  const tableData = useMemo(
    () => filteredJobs.map((job, idx) => ({ id: idx + 1, ...job })),
    [filteredJobs],
  );

  // Calculate statistics
  const totalJobs = filteredJobs.length;
  const averageCost = useMemo(() => {
    if (filteredJobs.length === 0) return 0;
    const sum = filteredJobs.reduce((acc, j) => acc + (j.cost || 0), 0);
    return sum / filteredJobs.length;
  }, [filteredJobs]);

  const averageCostUSD = averageCost / 100;
  const totalCostCredits = filteredJobs.reduce((acc, j) => acc + (j.cost || 0), 0);
  const totalCostUSD = totalCostCredits / 100;

  const averageShots = useMemo(() => {
    if (filteredJobs.length === 0) return 0;
    const sum = filteredJobs.reduce((acc, j) => acc + (j.shots || 0), 0);
    return sum / filteredJobs.length;
  }, [filteredJobs]);

  const jobStatusData = useMemo(() => {
    const data = getJobStatusDistribution(filteredJobs);
    return data.length > 0 ? data : [{ name: 'UNKNOWN', value: 1, percent: '100.00' }];
  }, [filteredJobs]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10 text-muted-foreground">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="border-sidebar-border bg-sidebar transition-colors hover:bg-muted">
          <CardContent className="flex items-center gap-4 p-4">
            <DollarSign className="size-5 text-brand" />
            <div>
              <p className="text-sm text-muted-foreground">Avg Cost per Task (USD)</p>
              <p className="text-xl font-semibold text-foreground">${averageCostUSD.toFixed(4)}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-sidebar-border bg-sidebar transition-colors hover:bg-muted">
          <CardContent className="flex items-center gap-4 p-4">
            <Zap className="size-5 text-brand" />
            <div>
              <p className="text-sm text-muted-foreground">Avg Shots per Task</p>
              <p className="text-xl font-semibold text-foreground">{averageShots.toFixed(2)}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-sidebar-border bg-sidebar transition-colors hover:bg-muted">
          <CardContent className="flex items-center gap-4 p-4">
            <ListChecks className="size-5 text-brand" />
            <div>
              <p className="text-sm text-muted-foreground">Total Tasks</p>
              <p className="text-xl font-semibold text-foreground">{totalJobs}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-sidebar-border bg-sidebar transition-colors hover:bg-muted">
          <CardContent className="flex items-center gap-4 p-4">
            <Banknote className="size-5 text-brand" />
            <div>
              <p className="text-sm text-muted-foreground">Total Job Value (USD)</p>
              <p className="text-xl font-semibold text-foreground">${totalCostUSD.toFixed(2)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <RecentFailedJobs jobs={filteredJobs} />

        {/* Jobs by Status Pie Chart */}
        <Card className="bg-sidebar border-sidebar-border">
          <CardContent className="p-4">
            <h4 className="text-lg font-semibold mb-4">Jobs by Status</h4>
            <div className="flex items-center gap-4 py-8">
              <div
                className="relative mx-auto h-44 w-48 focus:outline-none focus-visible:outline-none select-none chart-nofocus"
                onMouseDown={(e) => e.preventDefault()}
                onFocus={(e) => (e.currentTarget as HTMLElement).blur()}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <RePieChart
                    tabIndex={-1}
                    aria-hidden
                    className="select-none outline-none focus:outline-none focus-visible:outline-none"
                  >
                    <Pie
                      data={jobStatusData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                    >
                      {jobStatusData.map((entry: any, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={JOB_STATUS_COLORS[entry.name] || '#94a3b8'}
                        />
                      ))}
                    </Pie>
                    <ReTooltip
                      formatter={(value: number, name: string, props: any) => [
                        `${value}`,
                        `${props.payload.name}`,
                      ]}
                    />
                  </RePieChart>
                </ResponsiveContainer>
              </div>
              {/* Legend */}
              <div className="space-y-2 text-sm">
                {jobStatusData.map((entry: any) => (
                  <div key={entry.name} className="flex items-center space-x-2">
                    <div
                      className="size-3 rounded-full"
                      style={{ backgroundColor: JOB_STATUS_COLORS[entry.name] || '#8884d8' }}
                    ></div>
                    <span className="text-[#94a3b8]">
                      {entry.name} ({entry.percent}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* DataTable */}
      <div className="space-y-4" data-testid="jobs-table">
        <DataTable
          data={tableData}
          columnsOverride={createTableColumns()}
          isLoading={isLoading}
          hideToolbar={false}
          initialState={{
            pagination: {
              pageSize: 5,
              pageIndex: 0,
            },
          }}
          onPaginationChange={(updater: any) => {
            const newPagination =
              typeof updater === 'function'
                ? updater({
                    pageSize: resultsPerPage,
                    pageIndex: page,
                  })
                : updater;
            setPage(newPagination.pageIndex);
            setResultsPerPage(newPagination.pageSize);
          }}
          onExportPDF={() => exportToPDF(tableData, deviceId)}
        />
      </div>
    </div>
  );
}
