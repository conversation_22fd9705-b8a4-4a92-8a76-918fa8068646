import React from 'react';
import { motion } from 'motion/react';
import { Server } from 'lucide-react';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';

interface QuantumTopologyProps {
  device: any; // expects fields numberQubits or topology
}

export const QuantumTopology: React.FC<QuantumTopologyProps> = ({ device }) => {
  // Determine number of qubits
  const qubitCount = device?.topology?.qubits
    ? device.topology.qubits
    : Number.parseInt(device?.numberQubits || '0', 10);

  // If still 0, no topology to show
  if (!qubitCount || isNaN(qubitCount)) {
    return (
      <div className="flex h-64 items-center justify-center text-muted-foreground">
        <div className="text-center">
          <Server className="mx-auto mb-2 size-12 opacity-50" />
          <p>No topology data available</p>
        </div>
      </div>
    );
  }

  const fakeTopology = device.topology || {
    qubits: qubitCount,
    connectivity: 'grid',
    gateSet: ['cx', 'rz', 'sx'],
  };

  // Determine grid dimensions (rectangular)
  const rows = Math.ceil(Math.sqrt(qubitCount));
  const cols = Math.ceil(qubitCount / rows);

  const generateConnections = (total: number) => {
    const connections: [number, number][] = [];
    for (let i = 0; i < total; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;
      // right neighbor
      if (col < cols - 1 && i + 1 < total) {
        connections.push([i, i + 1]);
      }
      // bottom neighbor
      if (row < rows - 1 && i + cols < total) {
        connections.push([i, i + cols]);
      }
    }
    return connections;
  };

  const gridCols = cols;
  const gridRows = rows;
  const qubits = Array.from({ length: Math.min(fakeTopology.qubits, 64) }, (_, i) => i);
  const connections = generateConnections(qubits.length);

  return (
    <div className="p-6">
      <div className="mb-4">
        <h3 className="mb-2 text-lg font-semibold">Quantum Processor Topology</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Qubits:</span> {fakeTopology.qubits}
          </div>
          <div>
            <span className="text-muted-foreground">Connectivity:</span> {fakeTopology.connectivity}
          </div>
          <div className="col-span-2">
            <span className="text-muted-foreground">Gate Set:</span>{' '}
            {fakeTopology.gateSet?.join(', ')}
          </div>
        </div>
      </div>
      <div className="flex justify-center">
        <div
          className="relative rounded-lg border border-sidebar-border bg-sidebar"
          style={{ width: gridCols * 60, height: gridRows * 60 }}
        >
          <svg width={gridCols * 60} height={gridRows * 60} className="absolute inset-0">
            {connections.map(([from, to], index) => {
              const fromRow = Math.floor(from / gridCols);
              const fromCol = from % gridCols;
              const toRow = Math.floor(to / gridCols);
              const toCol = to % gridCols;
              const x1 = fromCol * 60 + 30;
              const y1 = fromRow * 60 + 30;
              const x2 = toCol * 60 + 30;
              const y2 = toRow * 60 + 30;
              return (
                <motion.line
                  key={index}
                  x1={x1}
                  y1={y1}
                  x2={x2}
                  y2={y2}
                  stroke="hsl(var(--primary))"
                  strokeWidth="2"
                  opacity="0.6"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 2, delay: index * 0.05, ease: 'easeInOut' }}
                />
              );
            })}
          </svg>
          <TooltipProvider delayDuration={100}>
            {qubits.map((qubit) => {
              const row = Math.floor(qubit / gridCols);
              const col = qubit % gridCols;
              const x = col * 60 + 30; // same center as lines
              const y = row * 60 + 30;
              const size = 24; // circle diameter
              return (
                <Tooltip key={qubit}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className="absolute flex items-center justify-center rounded-full border-2 border-background bg-primary/90 font-mono text-[10px] text-primary-foreground shadow-lg"
                      style={{ width: size, height: size, left: x - size / 2, top: y - size / 2 }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{
                        duration: 0.5,
                        delay: qubit * 0.02,
                        type: 'spring',
                        stiffness: 200,
                      }}
                      whileHover={{
                        scale: 1.2,
                        boxShadow: '0 0 20px hsl(var(--primary))',
                        transition: { duration: 0.2 },
                      }}
                    >
                      {qubit}
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>Qubit {qubit}</TooltipContent>
                </Tooltip>
              );
            })}
          </TooltipProvider>
          <motion.div
            className="pointer-events-none absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 0.3, 0] }}
            transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
          >
            <div className="size-full rounded-lg bg-primary/5"></div>
          </motion.div>
        </div>
      </div>
      {fakeTopology.qubits > 64 && (
        <motion.p
          className="mt-4 text-center text-sm text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          Showing first 64 of {fakeTopology.qubits} qubits
        </motion.p>
      )}
    </div>
  );
};
