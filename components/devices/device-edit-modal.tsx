import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { EditDeviceForm } from '@/components/devices/edit-device-form/edit-device-form-refactored';

import { toast } from 'sonner';
import { <PERSON><PERSON>, <PERSON>rk<PERSON>, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface DeviceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isEdit?: boolean;
  deviceData?: any;
  onSuccess?: () => void;
  addLabel?: string;
}

export function DeviceModal({
  open,
  onOpenChange,
  isEdit = false,
  deviceData,
  onSuccess,
  addLabel = 'Add Device',
}: DeviceModalProps) {
  const [showDeviceDetails, setShowDeviceDetails] = useState(false);

  const handleSuccess = () => {
    toast.success(
      isEdit
        ? 'Edit device request submitted successfully'
        : 'Add device request submitted successfully',
    );
    onOpenChange(false);
    onSuccess?.();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="modal-scale-in modal-scrollbar max-h-[95vh] max-w-xl overflow-y-auto border border-sidebar-border bg-sidebar shadow-2xl"
        onPointerDownOutside={(e) => {
          e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          e.preventDefault();
        }}
      >
        {/* Header gradient background */}
        <div className="pointer-events-none absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-violet-400/10 via-violet-300/10 to-transparent dark:from-violet-600/10 dark:via-violet-600/5" />
        <DialogHeader className="relative px-2 pb-2">
          <div className="flex items-center gap-4">
            <div className="pulse-soft rounded-2xl bg-gradient-to-br from-violet-500 to-purple-600 p-3 shadow-lg shadow-violet-500/20">
              <Cpu className="size-6 text-foreground text-white dark:text-white" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-foreground">
                {isEdit ? 'Edit Device' : addLabel}
              </DialogTitle>
              <DialogDescription className="mt-1 text-sm text-muted-foreground">
                {isEdit
                  ? 'Update device information and settings'
                  : 'Submit a request to add a new quantum device'}
              </DialogDescription>
            </div>
          </div>
          {/* Device Details Toggle */}
          <button
            onClick={() => setShowDeviceDetails(!showDeviceDetails)}
            className="group flex items-center gap-2 rounded-lg bg-transparent px-1 py-1.5 text-sm text-muted-foreground transition-all duration-200 hover:text-foreground "
          >
            <Sparkles className="size-4 text-violet-400 transition-transform group-hover:rotate-12" />
            <span>Device Request Details</span>
            <ChevronDown
              className={cn(
                'w-4 h-4 transition-transform duration-200',
                showDeviceDetails && 'rotate-180',
              )}
            />
          </button>
          {/* Device Details Section */}
          {showDeviceDetails && (
            <div className="mb-2 rounded-lg bg-background p-4 animate-in fade-in slide-in-from-top-2">
              <h4 className="mb-2 font-semibold text-foreground">How Device Requests Work</h4>
              <div className="text-foreground-muted space-y-2 text-sm">
                <p>• Fill out the form with your device specifications and requirements</p>
                <p>• Submit your request for admin review and approval</p>
                <p>• Admins will verify device details and configure access settings</p>
                <p>• You&apos;ll be notified once your device is approved and available</p>
              </div>
            </div>
          )}
        </DialogHeader>

        <EditDeviceForm
          deviceData={deviceData}
          isEdit={isEdit}
          onSuccess={handleSuccess}
          navigateOnSuccess={false}
        />
      </DialogContent>
    </Dialog>
  );
}
