import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'motion/react';
import { DeviceJobsTab } from '@/components/devices/device-jobs-tab';
import { CalibrationMetrics } from '@/components/devices/calibration-metrics';
import { Loader2, Settings, Activity, Network } from 'lucide-react';
import { QuantumTopology } from '@/components/devices/quantum-topology';
import type { DeviceCardProps } from '@/types/device';
import { DeviceDetailsTab } from './device-details/device-details-tab';

interface DevicesSectionProps {
  device: DeviceCardProps;
}

// Reusable animated wrapper for tab content
const AnimatedPanel = ({ id, children }: { id: string; children: React.ReactNode }) => (
  <AnimatePresence mode="wait">
    <motion.div
      key={id}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.15 }}
    >
      {children}
    </motion.div>
  </AnimatePresence>
);

export function DevicesSection({ device }: DevicesSectionProps) {
  return (
    <Tabs defaultValue="details" className="w-full space-y-6" data-testid="device-tabs">
      {/* Tab Bar */}
      <TabsList className="grid w-full grid-cols-3 border border-sidebar-border bg-sidebar p-1">
        <TabsTrigger
          value="details"
          data-testid="tab-details"
          className="font-semibold text-muted-foreground transition-all duration-200 data-[state=active]:bg-brand data-[state=active]:font-bold data-[state=active]:text-brand-foreground"
        >
          <Settings className="mr-2 size-4" />
          Device Details
        </TabsTrigger>
        <TabsTrigger
          value="jobs"
          data-testid="tab-jobs"
          className="font-semibold text-muted-foreground transition-all duration-200 data-[state=active]:bg-brand data-[state=active]:font-bold data-[state=active]:text-brand-foreground"
        >
          <Activity className="mr-2 size-4" />
          Jobs Overview
        </TabsTrigger>
        <TabsTrigger
          value="topology"
          disabled
          data-testid="tab-topology"
          className="cursor-not-allowed font-semibold text-muted-foreground opacity-50 transition-all duration-200 data-[state=active]:bg-brand data-[state=active]:font-bold data-[state=active]:text-brand-foreground"
        >
          <Network className="mr-2 size-4" />
          Topology (Coming Soon)
        </TabsTrigger>
      </TabsList>

      {/* Details Tab */}
      <TabsContent value="details" className="mt-0" data-testid="tab-content-details">
        <AnimatedPanel id={`details-${device.qrn}`}>
          <div className="overflow-x-auto">
            <DeviceDetailsTab device={device} />
          </div>
        </AnimatedPanel>
      </TabsContent>

      {/* Jobs Tab */}
      <TabsContent value="jobs" className="mt-0" data-testid="tab-content-jobs">
        <AnimatedPanel id={`jobs-${device.qrn}`}>
          <DeviceJobsTab deviceId={device.qrn} />
        </AnimatedPanel>
      </TabsContent>

      {/* Topology & Calibration Tab */}
      <TabsContent value="topology" className="mt-0 space-y-8" data-testid="tab-content-topology">
        <AnimatedPanel id={`topology-${device.qrn}`}>
          <div className="space-y-8">
            <QuantumTopology device={device as any} />
            <CalibrationMetrics
              metrics={[
                {
                  name: 'Readout Assignment Error',
                  median: '1.587e-2',
                  min: '6.615e-3',
                  max: '2.222e-1',
                  color: '#3b82f6',
                  value: 40,
                },
                {
                  name: 'ECR Error',
                  median: '6.560e-3',
                  min: '3.206e-3',
                  max: '2.481e-1',
                  color: '#3b82f6',
                  value: 30,
                },
                { name: 'SX Error', median: '2.219e-4', color: '#3b82f6', value: 75 },
                { name: 'T1 Relaxation Time', median: '280.82 μs', color: '#8b5cf6', value: 60 },
                { name: 'T2 Coherence Time', median: '214.87 μs', color: '#8b5cf6', value: 50 },
              ]}
            />
          </div>
        </AnimatedPanel>
      </TabsContent>
    </Tabs>
  );
}
