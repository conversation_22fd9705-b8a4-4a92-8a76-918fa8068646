import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, Clock, XCircle } from 'lucide-react';
import type { JobsRowProps } from '@/types/jobs';

const JOB_STATUS_COLORS: Record<string, string> = {
  INITIALIZING: 'hsl(var(--brand))',
  INITIALIZED: 'hsl(var(--brand))',
  CREATING: '#a855f7',
  CREATED: '#8b5cf6',
  QUEUED: '#f59e0b',
  VALIDATING: '#0ea5e9',
  RUNNING: '#2563eb',
  CANCELLING: '#6b7280',
  HOLD: '#7c3aed',
  UNKNOWN: '#9ca3af',
  COMPLETED: '#059669',
  FAILED: '#6b7280',
  CANCELLED: '#9ca3af',
};

function formatDateLocal(date?: Date | string) {
  if (!date) return 'N/A';
  const d = typeof date === 'string' ? new Date(date) : date;
  try {
    return new Intl.DateTimeFormat(undefined, {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    }).format(d);
  } catch {
    return d.toLocaleString();
  }
}

export function RecentFailedJobs({ jobs }: { jobs: JobsRowProps[] }) {
  const failedJobs = jobs
    .filter((job) => ['FAILED', 'CANCELLED'].includes(job.qbraidStatus || ''))
    .sort((a, b) => {
      const dateA = new Date(a.timeStamps?.endedAt || a.timeStamps?.createdAt || 0);
      const dateB = new Date(b.timeStamps?.endedAt || b.timeStamps?.createdAt || 0);
      return dateB.getTime() - dateA.getTime();
    })
    .slice(0, 10);

  return (
    <Card className="bg-sidebar border-sidebar-border">
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <XCircle className="size-5 text-red-500" />
          <h4 className="text-lg font-semibold">Recent Failed Jobs</h4>
        </div>
        <div className="space-y-3 max-h-64 overflow-y-auto scrollbar-hide">
          <AnimatePresence>
            {failedJobs.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-8 text-muted-foreground"
              >
                <AlertCircle className="size-8 mx-auto mb-2 text-muted-foreground/50" />
                <p className="text-sm">No failed jobs found</p>
              </motion.div>
            ) : (
              failedJobs.map((job, index) => (
                <motion.div
                  key={`${job._id || job.qbraidDeviceId}-${index}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                  className="p-3 rounded-lg border border-border/50 hover:bg-muted/30 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-3 min-w-0">
                        <div
                          className="size-2 rounded-full"
                          style={{
                            backgroundColor: JOB_STATUS_COLORS[job.qbraidStatus || 'UNKNOWN'],
                          }}
                        />
                        <p className="text-sm font-medium text-foreground truncate" title={job._id}>
                          {job._id || 'Unknown ID'}
                        </p>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatDateLocal(job.timeStamps?.endedAt || job.timeStamps?.createdAt)}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Clock className="size-3" />
                        {job.qbraidStatus}
                      </span>
                      {job.experimentType && <span>{job.experimentType}</span>}
                      {job.shots && <span>{job.shots.toLocaleString()} shots</span>}
                      {job.cost && <span>${(job.cost / 100).toFixed(4)}</span>}
                    </div>
                    {job.statusText && (
                      <p className="text-xs text-muted-foreground mt-1 italic">{job.statusText}</p>
                    )}
                    {job.provider && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Provider:{' '}
                        {typeof job.providerId === 'string'
                          ? job.providerId
                          : job.providerId?.provider || job.provider}
                      </p>
                    )}
                  </div>
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </div>
      </CardContent>
    </Card>
  );
}

export default RecentFailedJobs;
