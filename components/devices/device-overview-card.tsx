/*
  DeviceOverviewCard
  ------------------
  Compact card to display a brief overview of a quantum device.
  - Shows device name, provider, status, basic specs
  - "View" button opens a modal with full DeviceCard details (fits a fixed size modal)
  - "Edit" button links to edit page for the device
  - Three-dot menu contains Edit, Hide and Delete actions
*/
'use client';

import Link from 'next/link';
import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  MoreVerticalIcon,
  EyeIcon,
  PencilIcon,
  EyeOffIcon,
  WifiIcon,
  WifiOffIcon,
  MonitorIcon,
  ServerIcon,
  CpuIcon,
  Info,
  Settings as GearIcon,
  LockIcon,
  UnlockIcon,
  BarChart3Icon,
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import type { DeviceCardProps } from '@/types/device';
import { toast } from 'sonner';
import { apiClient } from '@/hooks/use-api';
import { DeviceModal } from './device-edit-modal';
import { useOrgContext } from '@/components/org/org-context-provider';

export function DeviceOverviewCard({
  selected = false,
  showDropdownMenu = true,
  deviceAccess,
  ...props
}: DeviceCardProps & {
  selected?: boolean;
  onDeleteRequest?: () => void;
  showDropdownMenu?: boolean;
  deviceAccess?: any;
}) {
  const {
    qrn,
    name,
    type,
    status,
    providerId,
    numberQubits,
    modality,
    pricing,
    pendingJobs,
    verified,
    visibility,
    requestedBy,
    statusMsg,
    pendingEdits,
  } = props;

  const [deleting, setDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const router = useRouter();

  // Get current organization context
  const { currentOrgId } = useOrgContext();

  // Determine access level for this device
  const getDeviceAccessLevel = useMemo(() => {
    if (!deviceAccess) return null;

    // Cast to the actual API response structure
    const accessData = deviceAccess as any;
    const { read = [], write = [] } = accessData;

    // Check write access first (higher priority)
    if (write.some((device: any) => device.qrn === qrn)) {
      return 'write';
    }
    // Then check read access
    else if (read.some((device: any) => device.qrn === qrn)) {
      return 'read';
    }

    return null;
  }, [deviceAccess, qrn]);

  const handleDeleteRequest = async () => {
    setDeleting(true);
    try {
      await apiClient(`/api/quantum-devices/${qrn}/request?orgId=${currentOrgId}`, {
        method: 'DELETE',
      });
      const hasPendingEdits = pendingEdits && Object.keys(pendingEdits).length > 0;
      toast.success(
        hasPendingEdits
          ? 'Pending device request and all pending edits deleted.'
          : 'Pending device request deleted.',
      );
      if (props.onDeleteRequest) props.onDeleteRequest();
    } catch (error_) {
      console.error('Error deleting device request:', error_);
      toast.error('Failed to delete device request.');
    } finally {
      setDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Map status to colour for indicator badge
  const statusColour = (() => {
    if (!status) return '#94a3b8';
    switch (status.toLowerCase()) {
      case 'active':
      case 'online': {
        return '#22c55e';
      }
      case 'offline': {
        return '#ef4444';
      }
      default: {
        return '#94a3b8';
      }
    }
  })();

  // Generates a short subtitle for the device type / modality
  const subtitle = [type, modality].filter(Boolean).join(' • ');

  const isOnline = status && ['online', 'active'].includes(status.toLowerCase());
  const statusStyles = isOnline
    ? 'bg-emerald-800/30 text-emerald-400'
    : 'bg-muted text-muted-foreground';
  const StatusIcon = isOnline ? WifiIcon : WifiOffIcon;

  return (
    <Card
      className={`relative z-0 border bg-transparent ${selected ? 'border-[#4b5563]' : 'border-sidebar-border'} group/card flex flex-col rounded-xl transition-all duration-300 hover:border-[#4b5563] hover:shadow-lg ${verified === false ? 'cursor-default border-muted bg-muted opacity-60 hover:border-muted' : ''} [container-type:inline-size]`}
    >
      {/* Watermark overlay for pending requests */}
      {verified === false && (
        <div className="pointer-events-auto absolute inset-0 z-10 mt-8 flex items-center justify-center">
          <div className="cursor-default rounded-lg border border-red-500/20 bg-red-500/10 px-6 py-4 backdrop-blur-sm">
            <div className="text-center">
              <div className="text-sm font-semibold text-red-600">
                {pendingEdits && Object.keys(pendingEdits).length > 0
                  ? 'EDIT REQUESTED'
                  : 'ADD REQUESTED'}
              </div>
              {requestedBy && (
                <div className="mt-1 text-xs text-red-500/80">Requested by: {requestedBy}</div>
              )}
              <Button
                size="sm"
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={deleting}
                className="relative z-20 mt-2 h-6 px-2 text-xs hover:border-red-700 hover:bg-red-700 hover:text-white"
              >
                {deleting
                  ? 'Deleting...'
                  : pendingEdits && Object.keys(pendingEdits).length > 0
                    ? 'Delete Edit Request'
                    : 'Delete Add Request'}
              </Button>
            </div>
          </div>
        </div>
      )}
      <CardHeader className="relative flex-row items-start pb-2 pt-4">
        <div className="flex w-full items-center">
          {/* Device Icon */}
          <div
            className="mr-2 flex size-9 shrink-0 items-center justify-center rounded-lg"
            style={{ backgroundColor: `${statusColour}1A` }}
          >
            {(() => {
              const lower = (type || '').toLowerCase();
              const IconCmp = lower.includes('qpu')
                ? ServerIcon
                : lower.includes('sim')
                  ? CpuIcon
                  : MonitorIcon;
              return <IconCmp className="size-4" style={{ color: statusColour }} />;
            })()}
          </div>
          {/* Name + subtitle */}
          <div className="min-w-0 flex-1">
            <h3 className="mb-1 flex items-center truncate font-semibold leading-none text-foreground">
              {name}
              {verified === false && (
                <span className="ml-1 animate-pulse rounded-full border border-border bg-muted px-1 py-0.5 text-xs font-medium text-muted-foreground">
                  Reviewing
                </span>
              )}
            </h3>
            <p className="hidden truncate text-xs capitalize leading-none text-muted-foreground sm:block">
              {subtitle}
            </p>
          </div>
          {/* Action menu - only show for verified devices */}
          {verified !== false && showDropdownMenu && (
            <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  size="icon"
                  variant="ghost"
                  className="ml-auto hidden shrink-0 justify-end p-0 text-muted-foreground hover:bg-transparent hover:text-foreground focus:bg-transparent focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 sm:inline-flex"
                  aria-label="Open menu"
                >
                  <MoreVerticalIcon className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="bottom"
                align="end"
                sideOffset={4}
                className="min-w-40 border-sidebar-border bg-sidebar"
              >
                <DropdownMenuItem
                  onClick={() => {
                    if (getDeviceAccessLevel === 'write') {
                      setShowEditModal(true); // Open modal immediately
                      setDropdownOpen(false); // Close dropdown
                    } else {
                      toast.error(
                        'You only have read access to this device. Contact your administrator for edit permissions.',
                      );
                      setDropdownOpen(false); // Close dropdown
                    }
                  }}
                  key="edit-device"
                  disabled={getDeviceAccessLevel !== 'write'}
                  className={
                    getDeviceAccessLevel === 'write' ? '' : 'opacity-50 cursor-not-allowed'
                  }
                >
                  <GearIcon className="size-4" />
                  {getDeviceAccessLevel === 'write' ? 'Edit Device' : 'Edit Device (Read Only)'}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    router.push(`/devices?id=${qrn}`);
                    setDropdownOpen(false);
                  }}
                >
                  <BarChart3Icon className="size-4" />
                  View Analytics
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                  <EyeOffIcon className="size-4" /> Hide
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent className="flex-1 space-y-3 pb-4">
        {/* Status Bubble and Access Level Badge */}
        <div className="flex items-center justify-between">
          <div
            className={`flex items-center gap-2 rounded-full px-3 py-1 text-xs font-medium ${verified === false ? 'border border-border bg-muted text-muted-foreground' : ''}`}
            style={{ backgroundColor: verified === false ? undefined : `${statusColour}1A` }}
          >
            {verified === false ? (
              <>
                <span className="mr-1 inline-block size-2 rounded-full bg-muted-foreground" />
                Pending
              </>
            ) : (
              <>
                <StatusIcon className="size-3.5" style={{ color: statusColour }} />
                {status?.toLowerCase() || 'unknown'}
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Access Level Badge */}
            {getDeviceAccessLevel ? (
              <Badge
                variant={getDeviceAccessLevel === 'write' ? 'default' : 'secondary'}
                className="flex h-5 items-center gap-1 px-2 py-0.5 text-xs"
              >
                {getDeviceAccessLevel === 'write' ? (
                  <UnlockIcon className="size-3" />
                ) : (
                  <LockIcon className="size-3" />
                )}
                {getDeviceAccessLevel === 'write' ? 'Write' : 'Read'}
              </Badge>
            ) : null}

            <span className="hidden text-xs text-muted-foreground xl:inline">
              {verified === false
                ? 'Awaiting review'
                : isOnline
                  ? 'Active now'
                  : (status === 'OFFLINE' || status === 'RETIRED') &&
                    statusMsg && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="relative z-10 cursor-pointer align-middle">
                              <Info className="size-4 text-gray-400" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            side="bottom"
                            className="max-w-xs border-sidebar-border bg-sidebar text-xs text-foreground"
                          >
                            <div className="mt-1">{statusMsg}</div>
                            <div className="mt-1 text-xs text-muted-foreground">
                              Last online 1 hour ago
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
            </span>
          </div>
        </div>

        {/* Details list */}
        <div className="hidden space-y-2 sm:block @[(max-width:420px)]:hidden">
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] uppercase text-muted-foreground">Provider:</p>
            <p className="truncate text-xs text-foreground">
              {typeof providerId === 'string' ? providerId : providerId?.provider || 'N/A'}
            </p>
          </div>
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] uppercase text-muted-foreground">Qubits:</p>
            <p className="text-xs text-foreground">{numberQubits}</p>
          </div>
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] uppercase text-muted-foreground">Jobs:</p>
            <p className="text-xs text-foreground">{pendingJobs}</p>
          </div>
          <div className="hidden justify-between gap-4 xl:flex">
            <p className="text-[0.7rem] uppercase text-muted-foreground">
              Pricing{' '}
              {pricing?.perTask !== undefined && pricing?.perShot !== undefined
                ? '(task/shot)'
                : '(min)'}
              :{' '}
            </p>
            <p className="text-xs text-foreground">
              {pricing?.perMinute !== null && pricing?.perMinute !== undefined
                ? `$${pricing.perMinute}/min`
                : pricing?.perTask !== null &&
                    pricing?.perTask !== undefined &&
                    pricing?.perShot !== null &&
                    pricing?.perShot !== undefined
                  ? `$${pricing.perTask}/$${pricing.perShot}`
                  : 'N/A'}
            </p>
          </div>
        </div>

        {/* Compact details for narrow screens */}
        <div className="hidden sm:hidden @[(max-width:420px)]:block">
          <div className="flex items-center justify-between gap-2 text-[0.7rem] text-muted-foreground">
            <span className="truncate">
              {typeof providerId === 'string' ? providerId : providerId?.provider || 'N/A'}
            </span>
            <span className="whitespace-nowrap">{numberQubits} qubits</span>
            {typeof pendingJobs === 'number' && (
              <span className="whitespace-nowrap">{pendingJobs} jobs</span>
            )}
          </div>
        </div>
      </CardContent>
      {/* Removed footer view button for cleaner compact display */}

      {/* Edit device modal */}
      {showEditModal && (
        <DeviceModal
          open={showEditModal}
          onOpenChange={(open) => {
            setShowEditModal(open);
            if (!open && props.onDeleteRequest) props.onDeleteRequest(); // refresh after close
          }}
          isEdit={true}
          deviceData={props}
          onSuccess={() => {
            setShowEditModal(false);
            if (props.onDeleteRequest) props.onDeleteRequest();
          }}
        />
      )}

      {/* Delete confirmation dialog */}
      {verified === false && (
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {pendingEdits && Object.keys(pendingEdits).length > 0
                  ? 'Delete Pending Device Request & Edits?'
                  : 'Delete Pending Device Request?'}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {pendingEdits && Object.keys(pendingEdits).length > 0
                  ? 'This will remove all pending edits and revert the device to a visible state. This action cannot be undone.'
                  : 'Are you sure you want to delete this pending add device request? This action cannot be undone.'}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteRequest}
                disabled={deleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {deleting
                  ? 'Deleting...'
                  : pendingEdits && Object.keys(pendingEdits).length > 0
                    ? 'Delete Request & Edits'
                    : 'Delete Request'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </Card>
  );
}
