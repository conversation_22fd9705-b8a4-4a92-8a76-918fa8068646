import { Card, CardContent } from '@/components/ui/card';

interface CalibrationMetric {
  name: string;
  median: string;
  min?: string;
  max?: string;
  color: string;
  value: number; // 0-100 percentage for progress bar
}

interface CalibrationMetricsProps {
  metrics: CalibrationMetric[];
}

export function CalibrationMetrics({ metrics }: CalibrationMetricsProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {metrics.map((metric) => (
        <Card key={metric.name} className="border-[#3b3b3b] bg-[#262131]">
          <CardContent className="p-6">
            <h3 className="mb-6 font-medium text-white">{metric.name}</h3>

            <div className="mb-6 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-[#94a3b8]">Median</span>
                <span className="font-medium text-white">{metric.median}</span>
              </div>

              {metric.min && (
                <div className="flex justify-between">
                  <span className="text-sm text-[#94a3b8]">Min</span>
                  <span className="font-medium text-white">{metric.min}</span>
                </div>
              )}

              {metric.max && (
                <div className="flex justify-between">
                  <span className="text-sm text-[#94a3b8]">Max</span>
                  <span className="font-medium text-white">{metric.max}</span>
                </div>
              )}
            </div>

            <div className="h-2 w-full overflow-hidden rounded-full bg-[#1d1825]">
              <div
                className={`h-full rounded-full`}
                style={{
                  width: `${metric.value}%`,
                  backgroundColor: metric.color,
                }}
              />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
