'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MoreHorizontal,
  Edit,
  Users,
  Activity,
  Trash2,
  PlusIcon,
  Eye,
  Building,
  Cpu,
  Clock,
  ExternalLink,
  Key,
  Filter,
  X,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useOrgProvider } from '@/hooks/use-providers';
import { useProviderDevices } from '@/hooks/use-device-management';
import { DeviceModal } from '@/components/devices/device-edit-modal';
import { DeviceOverviewCard } from '@/components/devices/device-overview-card';
import { useOrgDevices } from '@/hooks/use-api';
import { useOrgDeviceAccess, useDeviceAccessRequests } from '@/hooks/use-device-access';

type AccessFilter = 'all' | 'read' | 'write';

export function MyDevicesTab({ onSwitchToAvailable }: { onSwitchToAvailable?: () => void }) {
  const { currentOrgId } = useOrgContext();
  const { data: orgProvider } = useOrgProvider(currentOrgId || '');
  const { data: devices, isLoading, refetch } = useOrgDevices(currentOrgId || '');
  const { data: deviceAccess, isLoading: accessLoading } = useOrgDeviceAccess(currentOrgId || '');
  const { data: requestsData } = useDeviceAccessRequests('', currentOrgId || undefined);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isHoveringAdd, setIsHoveringAdd] = useState(false);
  const [accessFilter, setAccessFilter] = useState<AccessFilter>('all');

  // Log component mount and data state
  useEffect(() => {
    console.log('📱 [MY-DEVICES-TAB] Component mounted', {
      orgId: currentOrgId,
      timestamp: new Date().toISOString(),
    });
  }, [currentOrgId]);

  // Access the device data from the response structure
  const responseData = deviceAccess as any;
  const readDevices = responseData?.json?.read || responseData?.read || [];
  const writeDevices = responseData?.json?.write || responseData?.write || [];
  const customDevices = responseData?.json?.custom || responseData?.custom || [];

  // Memoized device access level mapping for better performance
  const deviceAccessMap = useMemo(() => {
    if (!deviceAccess) return new Map();

    const accessData = deviceAccess as any;
    const { read = [], write = [] } = accessData;
    const map = new Map();

    // Map write access (highest priority)
    write.forEach((device: any) => {
      map.set(device.qrn, 'write');
    });

    // Map read access (if not already mapped as write)
    read.forEach((device: any) => {
      if (!map.has(device.qrn)) {
        map.set(device.qrn, 'read');
      }
    });

    return map;
  }, [deviceAccess]);

  // Get device access level for filtering
  const getDeviceAccessLevel = (qrn: string) => {
    return deviceAccessMap.get(qrn) || null;
  };

  // Filter devices based on access level
  const filteredDevices = useMemo(() => {
    if (!devices) return [];

    return devices.filter((device) => {
      if (accessFilter === 'all') return true;

      const accessLevel = getDeviceAccessLevel(device.qrn);

      if (accessFilter === 'read') {
        // For read filter, only show devices that have read access but NOT write access
        return accessLevel === 'read';
      }

      return accessLevel === accessFilter;
    });
  }, [devices, accessFilter, deviceAccessMap]);

  // Calculate stats
  const stats = useMemo(() => {
    const totalDevices = devices?.length || 0;

    // Count devices that have read access but NOT write access
    const readOnlyCount = readDevices.filter((device: any) => {
      return !writeDevices.some((writeDevice: any) => writeDevice.qrn === device.qrn);
    }).length;

    const writeCount = writeDevices.length;
    const pendingRequests =
      requestsData?.requests?.filter((r: any) => r.status === 'pending').length || 0;

    return {
      totalDevices,
      readCount: readOnlyCount,
      writeCount,
      pendingRequests,
    };
  }, [devices, readDevices, writeDevices, requestsData]);

  if (!orgProvider) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">No provider associated with your organization.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-2xl font-semibold">My Devices</h3>
          <p className="text-muted-foreground">
            Devices you own and manage as {orgProvider.provider}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20 border-blue-200 dark:border-blue-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-blue-700 dark:text-blue-300 flex items-center gap-2 text-sm">
              <Cpu className="size-4" />
              Total Devices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              {stats.totalDevices}
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400">owned devices</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20 border-green-200 dark:border-green-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-green-700 dark:text-green-300 flex items-center gap-2 text-sm">
              <Eye className="size-4" />
              Read Access
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {stats.readCount}
            </div>
            <p className="text-xs text-green-600 dark:text-green-400">devices</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20 border-purple-200 dark:border-purple-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-purple-700 dark:text-purple-300 flex items-center gap-2 text-sm">
              <Edit className="size-4" />
              Write Access
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              {stats.writeCount}
            </div>
            <p className="text-xs text-purple-600 dark:text-purple-400">devices</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20 border-orange-200 dark:border-orange-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-orange-700 dark:text-orange-300 flex items-center gap-2 text-sm">
              <Activity className="size-4" />
              Pending Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
              {stats.pendingRequests}
            </div>
            <p className="text-xs text-orange-600 dark:text-orange-400">requests</p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Filter Controls */}
      <motion.div
        className="flex items-center gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex items-center gap-2">
          <Filter className="size-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filter by access:</span>
        </div>
        <div className="flex gap-2">
          {[
            { key: 'all', label: 'All', count: stats.totalDevices },
            { key: 'read', label: 'Read', count: stats.readCount },
            { key: 'write', label: 'Write', count: stats.writeCount },
          ].map((filter) => (
            <Button
              key={filter.key}
              variant={accessFilter === filter.key ? 'default' : 'outline'}
              size="sm"
              onClick={() => setAccessFilter(filter.key as AccessFilter)}
              className="text-xs"
            >
              {filter.label}
              <Badge variant="secondary" className="ml-2 text-xs">
                {filter.count}
              </Badge>
            </Button>
          ))}
        </div>
      </motion.div>

      {/* Devices Grid */}
      {isLoading ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>
      ) : (
        <AnimatePresence mode="wait">
          <motion.div
            key={accessFilter}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {filteredDevices.map((device, index) => (
              <motion.div
                key={device.qrn}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <DeviceOverviewCard
                  {...device}
                  deviceAccess={deviceAccess}
                  onDeleteRequest={refetch}
                  showDropdownMenu={true}
                />
              </motion.div>
            ))}

            {/* Add Device Button */}
            {orgProvider.status === 'public' && accessFilter === 'all' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: filteredDevices.length * 0.1 }}
                className={`h-full transition-all duration-300 ${
                  isHoveringAdd ? 'scale-95 transition-transform duration-300' : ''
                }`}
              >
                <button
                  type="button"
                  className={`size-full min-h-[210px] rounded-xl border-2 border-dashed border-sidebar-border bg-gradient-to-t from-sidebar/50 to-sidebar/30 flex flex-col items-center justify-center transition-all duration-300 ${
                    isHoveringAdd
                      ? 'border-brand bg-gradient-to-t from-brand/20 to-brand/10 scale-105'
                      : 'hover:border-brand hover:bg-gradient-to-t hover:from-brand/10 hover:to-brand/5'
                  }`}
                  onMouseEnter={() => setIsHoveringAdd(true)}
                  onMouseLeave={() => setIsHoveringAdd(false)}
                  onClick={() => setIsAddModalOpen(true)}
                >
                  <div className="flex flex-col items-center gap-3 text-center group flex-1 w-full justify-center">
                    <div
                      className={`size-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                        isHoveringAdd
                          ? 'bg-brand scale-110'
                          : 'bg-sidebar-border group-hover:bg-brand/80'
                      }`}
                    >
                      <PlusIcon
                        className={`size-8 transition-all duration-300 ${
                          isHoveringAdd
                            ? 'text-brand-foreground rotate-90'
                            : 'text-muted-foreground group-hover:text-brand-foreground'
                        }`}
                      />
                    </div>
                    <div className="space-y-1">
                      <h3
                        className={`font-semibold transition-colors duration-300 ${
                          isHoveringAdd ? 'text-brand' : 'text-foreground'
                        }`}
                      >
                        Request to Add Device
                      </h3>
                      <p className="text-sm text-muted-foreground group-hover:text-muted-foreground/80">
                        Submit a request for a new quantum device
                      </p>
                    </div>
                  </div>
                </button>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>
      )}

      {/* Recent Access Requests */}
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-center justify-between">
          <h4 className="font-semibold flex items-center gap-2">
            <Activity className="size-4" />
            Recent Access Requests
          </h4>
          <Button variant="outline" size="sm" onClick={onSwitchToAvailable}>
            <Key className="size-4 mr-2" />
            Browse Available Devices
          </Button>
        </div>

        {requestsData?.requests && requestsData.requests.length > 0 ? (
          <div className="space-y-3">
            {requestsData.requests.slice(0, 5).map((request, index) => (
              <motion.div
                key={request._id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{request.deviceName}</span>
                          <Badge variant="outline" className="text-xs">
                            {request.requestType}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground flex items-center gap-1">
                          <Clock className="size-3" />
                          Requested {new Date(request.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge
                        variant={
                          request.status === 'approved'
                            ? 'default'
                            : (request.status === 'denied'
                              ? 'destructive'
                              : 'secondary')
                        }
                        className="text-xs"
                      >
                        {request.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        ) : (
          <Card className="border-dashed">
            <CardContent className="text-center py-8">
              <p className="text-sm text-muted-foreground">No recent access requests</p>
            </CardContent>
          </Card>
        )}
      </motion.div>

      {/* Device Add Modal */}
      <DeviceModal
        open={isAddModalOpen}
        onOpenChange={(open) => setIsAddModalOpen(open)}
        isEdit={false}
        onSuccess={() => {
          setIsAddModalOpen(false);
          refetch();
        }}
        addLabel="Request to Add Device"
      />
    </div>
  );
}
