import React, { useState, useRef, useEffect } from 'react';
import { Command, CommandInput, CommandList, CommandItem } from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, X, Upload } from 'lucide-react';
import { RUN_INPUT_TYPE_OPTIONS_BY_PARADIGM } from './constants';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FileUpload } from '@/components/ui/file-upload';

interface MultiSelectProps {
  value: string[];
  onChange: (newValue: string[]) => void;
  error?: boolean;
  errorMessage?: string;
  paradigm?: string;
  onFileUpload?: (file: File) => void;
  uploadedFile?: File | null;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  value,
  onChange,
  error,
  errorMessage,
  paradigm,
  onFileUpload,
  uploadedFile,
}) => {
  const [open, setOpen] = useState(false);
  const [customInputType, setCustomInputType] = useState('');
  const selected = Array.isArray(value) ? value : [];
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get available options based on paradigm
  const availableOptions =
    paradigm && paradigm in RUN_INPUT_TYPE_OPTIONS_BY_PARADIGM
      ? RUN_INPUT_TYPE_OPTIONS_BY_PARADIGM[
          paradigm as keyof typeof RUN_INPUT_TYPE_OPTIONS_BY_PARADIGM
        ]
      : [];

  // Close dropdown when paradigm changes to prevent stale state
  useEffect(() => {
    setOpen(false);
  }, [paradigm]);

  useEffect(() => {
    if (!open) return;
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  const handleCustomInputType = () => {
    if (customInputType.trim()) {
      onChange([...selected, customInputType.trim()]);
      setCustomInputType('');
    }
  };

  const handleOtherSelection = () => {
    // Add "other" to selected items if not already selected
    if (!selected.includes('other')) {
      onChange([...selected, 'other']);
    }
    setOpen(false);
  };

  return (
    <div className="flex flex-col" ref={dropdownRef}>
      <div className="relative">
        <div
          role="button"
          tabIndex={0}
          className={`flex min-h-[40px] w-full items-center justify-between rounded-md border bg-background px-3 py-2 text-sm ${error ? 'border-red-500' : 'border-input'} cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2`}
          onClick={() => setOpen((v) => !v)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              setOpen((v) => !v);
            }
          }}
          aria-haspopup="listbox"
          aria-expanded={open}
        >
          <div className="flex flex-wrap gap-1">
            {selected.length === 0 ? (
              <span className="text-muted-foreground">
                {paradigm ? `Select ${paradigm} input types...` : 'Select input types...'}
              </span>
            ) : (
              selected.map((option) => (
                <Badge
                  key={option}
                  variant="secondary"
                  className="mr-1 flex items-center gap-1 pr-1"
                >
                  {option}
                  <button
                    type="button"
                    aria-label={`Remove ${option}`}
                    className="ml-0.5 rounded hover:bg-muted/50 focus:outline-none"
                    onClick={(e) => {
                      e.stopPropagation();
                      const newSelected = selected.filter((v) => v !== option);
                      onChange(newSelected);

                      // If "other" is removed, clear the custom input type
                      if (option === 'other') {
                        setCustomInputType('');
                      }

                      // If the removed option corresponds to an uploaded file, clear the file
                      if (uploadedFile && uploadedFile.name.split('.')[0] === option && onFileUpload) {
                          onFileUpload(null as any);
                        }
                    }}
                  >
                    <X className="size-3 text-muted-foreground hover:text-destructive" />
                  </button>
                </Badge>
              ))
            )}
          </div>
          <ChevronDown className="ml-2 size-4 opacity-50" />
        </div>

        {open && (
          <div className="absolute z-10 mt-1 w-full rounded-md border border-border bg-popover shadow-lg">
            <Command className="w-full">
              <CommandInput placeholder={`Search ${paradigm || ''} input types...`} />
              <CommandList>
                {availableOptions.map((option) => (
                  <CommandItem
                    key={option}
                    onSelect={() => {
                      let newSelected;
                      newSelected = selected.includes(option) ? selected.filter((v) => v !== option) : [...selected, option];
                      onChange(newSelected);
                      // Don't close dropdown after selection
                    }}
                    className={selected.includes(option) ? 'bg-accent text-accent-foreground' : ''}
                  >
                    <input
                      type="checkbox"
                      checked={selected.includes(option)}
                      readOnly
                      className="mr-2"
                    />
                    {option}
                  </CommandItem>
                ))}

                {/* Custom input type option */}
                <CommandItem
                  onSelect={handleOtherSelection}
                  className={selected.includes('other') ? 'bg-accent text-accent-foreground' : ''}
                >
                  <input
                    type="checkbox"
                    checked={selected.includes('other')}
                    readOnly
                    className="mr-2"
                  />
                  <div className="flex items-center gap-2">
                    <span>other</span>
                    <Upload className="size-3 text-muted-foreground" />
                  </div>
                </CommandItem>
              </CommandList>
            </Command>
          </div>
        )}
      </div>

      {/* File upload section for "other" option */}
      {selected.includes('other') && (
        <div className="mt-2 rounded-md border border-dashed border-muted-foreground/30 bg-muted/20 p-2">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Upload className="size-3 text-muted-foreground" />
              <span className="text-xs font-medium text-muted-foreground">Custom Input Type</span>
            </div>

            <div className="flex gap-2">
              <Input
                placeholder="Enter custom input type name..."
                value={customInputType}
                onChange={(e) => setCustomInputType(e.target.value)}
                className="h-8 flex-1 text-xs"
              />
              <Button
                type="button"
                size="sm"
                onClick={handleCustomInputType}
                disabled={!customInputType.trim()}
                className="h-8 px-3 text-xs"
              >
                Add
              </Button>
            </div>

            <div className="text-xs text-muted-foreground">Or upload a file:</div>

            <FileUpload
              id="input-type-file-upload"
              currentFile={uploadedFile}
              onUpload={(fileName: string) => {
                // fileName is just the file name, we keep the file in state
              }}
              onFileSelect={(file: File) => {
                if (onFileUpload) {
                  onFileUpload(file);
                }
                // Auto-add the file name as a custom input type, but remove "other"
                const fileName = file.name.split('.')[0];
                const newSelected = selected.filter((v) => v !== 'other');
                onChange([...newSelected, fileName]);
                setCustomInputType('');
              }}
              onRemove={() => {
                if (onFileUpload) {
                  onFileUpload(null as any); // Clear the file
                }
              }}
              placeholder="Upload input type file"
              accept=".py,.txt,.json,.js,.yml,.yaml"
              allowedTypes={[
                'text/plain',
                'text/x-python',
                'application/json',
                'text/javascript',
                'application/javascript',
                'text/yaml',
                'application/x-yaml',
                'text/x-yaml',
              ]}
              maxFileSize={5 * 1024 * 1024} // 5MB
              compact={true}
              className="w-full text-xs"
            />

            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={() => {
                // Remove "other" from selected items when canceling
                const newSelected = selected.filter((v) => v !== 'other');
                onChange(newSelected);
                setCustomInputType('');
              }}
              className="h-6 px-2 text-xs"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {error && errorMessage && <div className="mt-1 text-xs text-red-400">{errorMessage}</div>}
    </div>
  );
};

export default MultiSelect;
