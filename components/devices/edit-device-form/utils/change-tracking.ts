import type { DeviceFormType } from '../schema';
import type { ChangedFields } from '../types';
import { isFileObject } from '../types';

function arraysEqual(a: string[] | undefined, b: string[] | undefined): boolean {
  const aNorm = a ?? [];
  const bNorm = b ?? [];
  if (aNorm.length !== bNorm.length) return false;
  for (const [i, element] of aNorm.entries()) {
    if (element !== bNorm[i]) return false;
  }
  return true;
}

function filesEqual(a: File | string | null | undefined, b: File | string | null | undefined) {
  if (isFileObject(a) && isFileObject(b)) {
    return a.name === b.name && a.size === b.size;
  }
  if (isFileObject(a) || isFileObject(b)) return false;
  return (a ?? undefined) === (b ?? undefined);
}

/**
 * Deep compare two DeviceFormType objects and return only changed fields
 */
export function getChangedFields(
  currentValues: DeviceFormType,
  initialValues: DeviceFormType,
): ChangedFields {
  const changes: ChangedFields = {};

  // Primitives and simple strings
  if (currentValues.name !== initialValues.name) changes.name = currentValues.name;
  if (currentValues.type !== initialValues.type) changes.type = currentValues.type;
  if (currentValues.paradigm !== initialValues.paradigm) changes.paradigm = currentValues.paradigm;
  if ((currentValues.modality ?? '') !== (initialValues.modality ?? ''))
    changes.modality = currentValues.modality;
  if ((currentValues.processorType ?? '') !== (initialValues.processorType ?? ''))
    changes.processorType = currentValues.processorType;
  if (currentValues.numberQubits !== initialValues.numberQubits)
    changes.numberQubits = currentValues.numberQubits;
  if (currentValues.providerId !== initialValues.providerId)
    changes.providerId = currentValues.providerId;
  if ((currentValues.status ?? '') !== (initialValues.status ?? ''))
    changes.status = currentValues.status;
  if ((currentValues.statusMsg ?? '') !== (initialValues.statusMsg ?? ''))
    changes.statusMsg = currentValues.statusMsg;
  if ((currentValues.deviceDescription ?? '') !== (initialValues.deviceDescription ?? ''))
    changes.deviceDescription = currentValues.deviceDescription;
  if ((currentValues.about ?? '') !== (initialValues.about ?? ''))
    changes.about = currentValues.about;
  if ((currentValues.notes ?? '') !== (initialValues.notes ?? ''))
    changes.notes = currentValues.notes;
  if ((currentValues.pricingType ?? '') !== (initialValues.pricingType ?? ''))
    changes.pricingType = currentValues.pricingType;

  // Arrays
  if (!arraysEqual(currentValues.runInputTypes, initialValues.runInputTypes)) {
    changes.runInputTypes = currentValues.runInputTypes;
  }
  if (!arraysEqual(currentValues.noiseModels, initialValues.noiseModels)) {
    changes.noiseModels = currentValues.noiseModels ?? [];
  }
  if (!arraysEqual(currentValues.whiteListedDomains, initialValues.whiteListedDomains)) {
    changes.whiteListedDomains = currentValues.whiteListedDomains ?? [];
  }
  if (!arraysEqual(currentValues.blackListedDomains, initialValues.blackListedDomains)) {
    changes.blackListedDomains = currentValues.blackListedDomains ?? [];
  }

  // Files/Images
  if (!filesEqual(currentValues.deviceImage ?? null, initialValues.deviceImage ?? null)) {
    changes.deviceImage = currentValues.deviceImage ?? null;
  }

  // Nested pricing
  const pricingChanges: NonNullable<ChangedFields['pricing']> = {};
  const currP = currentValues.pricing ?? {};
  const initP = initialValues.pricing ?? {};
  if ((currP.perMinute ?? '') !== (initP.perMinute ?? ''))
    pricingChanges.perMinute = currP.perMinute;
  if ((currP.perTask ?? '') !== (initP.perTask ?? '')) pricingChanges.perTask = currP.perTask;
  if ((currP.perShot ?? '') !== (initP.perShot ?? '')) pricingChanges.perShot = currP.perShot;
  if (!filesEqual(currP.pricingFile ?? null, initP.pricingFile ?? null))
    pricingChanges.pricingFile = currP.pricingFile ?? null;

  if (Object.keys(pricingChanges).length > 0) {
    changes.pricing = pricingChanges;
  }

  return changes;
}
