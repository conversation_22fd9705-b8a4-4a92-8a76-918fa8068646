import type { DeviceApiPayload, DeviceFormValues, DeviceCreatePayload } from '../types';

/**
 * Utility to clean and type-correct the device payload for backend
 */
export function cleanDevicePayload(payload: DeviceCreatePayload): DeviceApiPayload {
  // Helper function to convert string or string[] to string[]
  const convertToStringArray = (value: string | string[] | undefined): string[] | undefined => {
    if (!value) return undefined;
    if (Array.isArray(value)) return value;
    if (typeof value === 'string') {
      return value
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean);
    }
    return undefined;
  };

  // Destructure problematic properties to handle them separately
  const {
    isAvailable,
    pricing,
    runInputTypes,
    noiseModels,
    whiteListedDomains,
    blackListedDomains,
    ...payloadWithoutConflicts
  } = payload;

  const cleanedPayload: DeviceApiPayload = {
    ...payloadWithoutConflicts,
    numberQubits: payload.numberQubits ? Number(payload.numberQubits) : undefined,
    deviceDescription: payload.deviceDescription || undefined,
    about: payload.about || undefined,
    // Handle deviceImage - exclude File objects since they can't be serialized
    deviceImage: payload.deviceImage instanceof File ? undefined : payload.deviceImage || undefined,
    runInputTypes: convertToStringArray(runInputTypes),
    noiseModels: convertToStringArray(noiseModels),
    statusMsg: payload.statusMsg || undefined,
    // Handle additional properties that might be present
    vendor: payload.vendor,
    vrn: payload.vrn,
    visibility: payload.visibility,
  };

  // Handle isAvailable separately to avoid type conflicts
  if (isAvailable !== undefined) {
    cleanedPayload.isAvailable = isAvailable === 'true';
  }

  // Only include pricing if it's present in the payload
  if (payload.pricing) {
    cleanedPayload.pricing = {
      perMinute: payload.pricing.perMinute ? Number(payload.pricing.perMinute) : undefined,
      perShot: payload.pricing.perShot ? Number(payload.pricing.perShot) : undefined,
      perTask: payload.pricing.perTask ? Number(payload.pricing.perTask) : undefined,
    };
  }

  // Only include domain arrays if they're present in the payload
  if (payload.whiteListedDomains !== undefined) {
    cleanedPayload.whiteListedDomains = convertToStringArray(payload.whiteListedDomains);
  }
  if (payload.blackListedDomains !== undefined) {
    cleanedPayload.blackListedDomains = convertToStringArray(payload.blackListedDomains);
  }

  return cleanedPayload;
}
