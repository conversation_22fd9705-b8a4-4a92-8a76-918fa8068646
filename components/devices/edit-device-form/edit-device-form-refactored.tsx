'use client';

import React from 'react';
import type { DeviceData } from '@/types/device';
import { useDeviceForm } from './hooks/use-device-form';
import {
  transformFormValuesToStepValues,
  type DeviceStepValues,
  type DeviceFormValues,
} from './types';
import { FormStepper } from './components/form-stepper';
import { FormNavigation } from './components/form-navigation';
import { FormStatus } from './components/form-status';
import BasicInfoStep from './steps/BasicInfoStep';
import TechnicalStep from './steps/TechnicalStep';
import AccessPricingStep from './steps/AccessPricingStep';
import ReviewStep from './steps/ReviewStep';
import { useOrgProvider } from '@/hooks/use-provider-management';
import { useOrgContext } from '@/components/org/org-context-provider';

interface EditDeviceFormProps {
  deviceData?: DeviceData;
  isEdit?: boolean;
  onSuccess?: () => void;
  navigateOnSuccess?: boolean;
}

export function EditDeviceForm({
  deviceData,
  isEdit = false,
  onSuccess,
  navigateOnSuccess = true,
}: EditDeviceFormProps) {
  const { currentOrgId } = useOrgContext();
  const { data: provider, isLoading: providersLoading } = useOrgProvider(currentOrgId || '');
  const providers = provider ? [provider] : []; // Convert to array for backward compatibility

  const {
    // State
    step,
    setStep,
    pendingSubmitted,
    setPendingSubmitted,
    submitError,
    steps,
    hasChanges,
    changedFields,

    // Form
    register,
    handleSubmit,
    errors,
    setValue,
    watch,
    trigger,
    control,

    // Functions
    handleStepChange,
    getStepChanges,
    setFieldValue,
    setSelectFieldValue,
    onSubmit,
    handleFileUpload,

    // Values
    currentValues,
    uploadedFile,
  } = useDeviceForm({ deviceData, isEdit, onSuccess });

  const handleFormSubmit = async () => {
    setPendingSubmitted(true);
    await onSubmit(currentValues as DeviceFormValues);
    setPendingSubmitted(false);
  };

  const renderCurrentStep = () => {
    switch (step) {
      case 0: {
        return (
          <section role="region" aria-labelledby="step-basic-info-title">
            <h2 id="step-basic-info-title" className="sr-only">
              Basic Info
            </h2>
            <BasicInfoStep
              values={transformFormValuesToStepValues(currentValues as DeviceFormValues)}
              errors={errors}
              register={register}
              setFieldValue={setFieldValue}
              setSelectFieldValue={setSelectFieldValue}
              watch={watch}
              providers={providers}
              providersLoading={providersLoading}
            />
          </section>
        );
      }
      case 1: {
        return (
          <section role="region" aria-labelledby="step-technical-title">
            <h2 id="step-technical-title" className="sr-only">
              Technical
            </h2>
            <TechnicalStep
              values={transformFormValuesToStepValues(currentValues as DeviceFormValues)}
              errors={errors}
              register={register}
              setFieldValue={setFieldValue}
              setSelectFieldValue={setSelectFieldValue}
              setValue={setValue}
              trigger={trigger}
              watch={watch}
              handleFileUpload={handleFileUpload}
              uploadedFile={uploadedFile}
            />
          </section>
        );
      }
      case 2: {
        return (
          <section role="region" aria-labelledby="step-access-pricing-title">
            <h2 id="step-access-pricing-title" className="sr-only">
              Access & Pricing
            </h2>
            <AccessPricingStep
              values={transformFormValuesToStepValues(currentValues as DeviceFormValues)}
              errors={errors}
              register={register}
              setFieldValue={setFieldValue}
              setSelectFieldValue={setSelectFieldValue}
              watch={watch}
              control={control}
              isEdit={isEdit}
              vendor={deviceData?.vendor}
            />
          </section>
        );
      }
      case 3: {
        return (
          <section role="region" aria-labelledby="step-review-title">
            <h2 id="step-review-title" className="sr-only">
              Review
            </h2>
            <ReviewStep
              values={transformFormValuesToStepValues(currentValues as DeviceFormValues)}
              onEditStep={setStep}
              changedFields={changedFields}
              isEdit={isEdit}
              vendor={deviceData?.vendor}
              uploadedFile={uploadedFile}
              register={register}
            />
          </section>
        );
      }
      default: {
        return null;
      }
    }
  };

  return (
    <div className="w-full px-2">
      <FormStatus submitError={submitError} isSubmitting={pendingSubmitted} />

      {!submitError && !pendingSubmitted && (
        <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
          {/* Hidden input for qrn to ensure it is always present in form state */}
          <input type="hidden" {...register('qrn')} />

          <FormStepper
            steps={steps}
            currentStep={step}
            onStepChange={handleStepChange}
            getStepChanges={getStepChanges}
          />

          <div className="mb-4">{renderCurrentStep()}</div>

          <FormNavigation
            currentStep={step}
            totalSteps={steps.length}
            onStepChange={handleStepChange}
            onSubmit={handleFormSubmit}
            isSubmitting={pendingSubmitted}
            isEdit={isEdit}
            hasChanges={hasChanges}
          />
        </form>
      )}
    </div>
  );
}
