// Device form constants

export const PROVIDER_OPTIONS = [
  'AWS',
  'IBM',
  'IQM',
  'IonQ',
  'OQC',
  'QuEra',
  '<PERSON><PERSON><PERSON>',
  'NEC',
  'qBraid',
  'Azure',
  'Pasqal',
  'Quantinuum',
  'Equal1',
] as const;

// Paradigm-based run input type options
export const RUN_INPUT_TYPE_OPTIONS_BY_PARADIGM = {
  'gate-based': [
    'braket',
    'cirq',
    'cudaq',
    'ionq',
    'pennylane',
    'pyquil',
    'pytket',
    'qasm2',
    'qasm3',
    'qiskit',
    'autoqasm',
    'pyqpanda3',
    'openqasm3',
    'pyqir',
    'qasm2_kirin',
    'stim',
    'qibo',
  ],
  annealing: ['cpp_pyqubo', 'qubo'],
  analog: ['bloqade', 'braket_ahs', 'pulser'],
} as const;

// All run input type options (for backward compatibility)
export const RUN_INPUT_TYPE_OPTIONS = [
  'braket',
  'cirq',
  'cudaq',
  'ionq',
  'pennylane',
  'pyquil',
  'pytket',
  'qasm2',
  'qasm3',
  'qiskit',
  'autoqasm',
  'pyqpanda3',
  'openqasm3',
  'pyqir',
  'qasm2_kirin',
  'stim',
  'qibo',
  'cpp_pyqubo',
  'qubo',
  'bloqade',
  'braket_ahs',
  'pulser',
  'other',
] as const;

// Paradigm options
export const PARADIGM_OPTIONS = [
  { value: 'gate-based', label: 'Gate-based' },
  { value: 'annealing', label: 'Annealing' },
  { value: 'analog', label: 'Analog' },
] as const;
