import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import type { DeviceFormType } from '../schema';
import { FormField } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Info, Plus, X as XIcon } from 'lucide-react';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';
import { FileUpload } from '@/components/ui/file-upload';
import type { SetFieldValue, SetSelectFieldValue } from '../types';

/**
 * Props for AccessPricingStep
 */
export interface AccessPricingStepProps {
  values: DeviceFormType;
  errors: any;
  register: any;
  setFieldValue: SetFieldValue;
  setSelectFieldValue: SetSelectFieldValue;
  watch: any;
  control: any;
  isEdit?: boolean;
  vendor?: string;
}

/**
 * Combined Access and Pricing Step of the device form
 */
const AccessPricingStep: React.FC<AccessPricingStepProps> = ({
  values,
  errors,
  register,
  control,
  setFieldValue,
  setSelectFieldValue,
  watch,
  isEdit = false,
  vendor,
}) => {
  const [whiteDomainInput, setWhiteDomainInput] = React.useState('');
  const [blackDomainInput, setBlackDomainInput] = React.useState('');

  // Helper to validate domain or URL
  const isValidDomainOrUrl = (value: string): boolean => {
    if (!value) return false;
    const domainRegex =
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    const trimmed = value.trim();
    // Allow simple domains (without protocol) or full URLs
    if (!trimmed.includes('://') && !trimmed.startsWith('http')) {
      // Require at least one dot to ensure it is a proper domain (e.g., example.com)
      if (trimmed.split('.').length < 2) {
        return false;
      }
      return domainRegex.test(trimmed);
    }
    try {
      // new URL will throw if invalid
      new URL(trimmed);
      return true;
    } catch {
      return false;
    }
  };
  const [pricingType, setPricingType] = React.useState(values.pricingType || 'perMinute');

  const whiteListedDomains = Array.isArray(values.whiteListedDomains)
    ? values.whiteListedDomains
    : (values.whiteListedDomains
      ? [values.whiteListedDomains]
      : []);
  const blackListedDomains = Array.isArray(values.blackListedDomains)
    ? values.blackListedDomains
    : (values.blackListedDomains
      ? [values.blackListedDomains]
      : []);

  // Determine if status field should be shown
  // Show status field only when editing AND vendor is qBraid
  const shouldShowStatus = isEdit && vendor === 'qBraid';

  return (
    <div className="flex flex-col gap-6">
      {/* Hidden input for isAvailable to ensure it's always submitted */}
      <input type="hidden" {...register('isAvailable')} value="true" />

      {/* Pricing Section */}
      <div className="flex flex-col gap-4">
        <h3 className="text-lg font-semibold">Pricing</h3>

        {/* Hidden input for pricingType to sync with form state */}
        <input type="hidden" {...register('pricingType')} value={pricingType} />

        {/* Pricing Type */}
        <div className="flex flex-col">
          <Label htmlFor="pricingType" className="mb-2 px-1">
            Pricing Type
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>How pricing is calculated.</TooltipContent>
            </Tooltip>
          </Label>
          <Select
            value={pricingType}
            onValueChange={(v) => {
              setPricingType(v);
              setFieldValue('pricingType', v);

              // Clear pricing values when switching to file upload
              if (v === 'file') {
                setFieldValue('pricing.perMinute', undefined);
                setFieldValue('pricing.perTask', undefined);
                setFieldValue('pricing.perShot', undefined);
              }

              setSelectFieldValue && setSelectFieldValue('pricingType', v);
            }}
          >
            <SelectTrigger className="max-w-full">
              <SelectValue placeholder="Select pricing type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="perMinute">Price per minute</SelectItem>
              <SelectItem value="perTaskPerShot">Price per task/shot</SelectItem>
              <SelectItem value="file">Upload pricing file</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Pricing Fields */}
        {pricingType === 'perMinute' && (
          <div className="flex flex-col">
            <Label htmlFor="pricing.perMinute" className="mb-2 px-1">
              Price per minute ($) <span className="text-red-500">*</span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>Cost per minute of usage.</TooltipContent>
              </Tooltip>
            </Label>
            <Input
              id="pricing.perMinute"
              placeholder="0.00"
              type="number"
              step="0.01"
              min="0"
              {...register('pricing.perMinute', {
                required: pricingType === 'perMinute' ? 'Price per minute is required' : false,
              })}
              className={`max-w-full ${errors.pricing ? 'border-red-500' : ''}`}
            />
            {errors.pricing?.perMinute && (
              <div className="text-xs text-red-400">{errors.pricing?.perMinute.message}</div>
            )}
            {/* Show group error if present */}
            {errors.pricing && typeof errors.pricing.message === 'string' && (
              <div className="mt-2 text-xs text-red-400">{errors.pricing.message}</div>
            )}
          </div>
        )}

        {pricingType === 'perTaskPerShot' && (
          <>
            <div className="flex flex-col">
              <Label htmlFor="pricing.perTask" className="mb-2 px-1">
                Price per task ($) <span className="text-red-500">*</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>Cost per quantum task.</TooltipContent>
                </Tooltip>
              </Label>
              <Input
                id="pricing.perTask"
                placeholder="0.00"
                type="number"
                step="0.01"
                min="0"
                {...register('pricing.perTask', {
                  required: pricingType === 'perTaskPerShot' ? 'Price per task is required' : false,
                })}
                className={`max-w-full ${errors.pricing ? 'border-red-500' : ''}`}
              />
              {errors.pricing?.perTask && (
                <div className="text-xs text-red-400">{errors.pricing?.perTask.message}</div>
              )}
            </div>
            <div className="flex flex-col">
              <Label htmlFor="pricing.perShot" className="mb-2 px-1">
                Price per shot ($) <span className="text-red-500">*</span>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>Cost per shot (circuit execution).</TooltipContent>
                </Tooltip>
              </Label>
              <Input
                id="pricing.perShot"
                placeholder="0.00"
                type="number"
                step="0.01"
                min="0"
                {...register('pricing.perShot', {
                  required: pricingType === 'perTaskPerShot' ? 'Price per shot is required' : false,
                })}
                className={`max-w-full ${errors.pricing ? 'border-red-500' : ''}`}
              />
              {errors.pricing?.perShot && (
                <div className="text-xs text-red-400">{errors.pricing?.perShot.message}</div>
              )}
              {/* Show group error if present */}
              {errors.pricing && typeof errors.pricing.message === 'string' && (
                <div className="mt-2 text-xs text-red-400">{errors.pricing.message}</div>
              )}
            </div>
          </>
        )}

        {/* File Upload Option */}
        {pricingType === 'file' && (
          <div className="flex flex-col">
            <Label htmlFor="pricing.pricingFile" className="mb-2 px-1">
              Pricing File <span className="text-red-500">*</span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  Upload a document or file explaining your pricing schema.
                </TooltipContent>
              </Tooltip>
            </Label>

            <FileUpload
              id="pricing-file-upload"
              currentFile={values.pricing?.pricingFile}
              onUpload={(fileName) => {
                // fileName is just the file name, we keep the file in state
              }}
              onFileSelect={(file) => {
                setFieldValue('pricing.pricingFile', file);
              }}
              onRemove={() => {
                setFieldValue('pricing.pricingFile', null);
              }}
              placeholder="Upload pricing document"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt,.py,.json,.js,.yml,.yaml"
              allowedTypes={[
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/csv',
                'application/json',
                'text/plain',
                'text/x-python',
                'text/javascript',
                'application/javascript',
                'text/yaml',
                'application/x-yaml',
                'text/x-yaml',
              ]}
              maxFileSize={10 * 1024 * 1024} // 10MB for pricing documents
              compact={true}
              className="w-full"
            />

            {errors.pricing?.pricingFile && (
              <div className="mt-1 text-xs text-red-400">{errors.pricing?.pricingFile.message}</div>
            )}
          </div>
        )}
      </div>

      <Separator />

      {/* Domain Access Control */}
      <div className="flex flex-col gap-4">
        <h3 className="text-lg font-semibold">Access Control</h3>

        {/* Device Status - Only show when editing and vendor is qBraid */}
        {shouldShowStatus && (
          <div className="flex flex-col">
            <Label htmlFor="status" className="mb-2 px-1">
              Device Status <span className="text-red-500">*</span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>How device status displays to users.</TooltipContent>
              </Tooltip>
            </Label>
            <Select value={watch('status')} onValueChange={(v) => setSelectFieldValue('status', v)}>
              <SelectTrigger className={`max-w-full ${errors.status ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Select device status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ONLINE">ONLINE</SelectItem>
                <SelectItem value="OFFLINE">OFFLINE</SelectItem>
                <SelectItem value="RETIRED">RETIRED</SelectItem>
              </SelectContent>
            </Select>
            {errors.status && <div className="text-xs text-red-400">{errors.status.message}</div>}
            {(watch('status') === 'OFFLINE' || watch('status') === 'RETIRED') && (
              <>
                <div className="mt-3 flex flex-col">
                  <Label htmlFor="statusMsg" className="mb-2 px-1">
                    Reason for status <span className="text-red-500">*</span>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>Reason for offline/retired status.</TooltipContent>
                    </Tooltip>
                  </Label>
                  <Input
                    id="statusMsg"
                    placeholder="e.g. Offline for calibration, retired due to hardware failure, etc."
                    {...register('statusMsg', { required: 'Offline reason is required' })}
                    className={`max-w-full ${errors.statusMsg ? 'border-red-500' : ''}`}
                  />
                  {errors.statusMsg && (
                    <div className="text-xs text-red-400">{errors.statusMsg.message}</div>
                  )}
                </div>
              </>
            )}
          </div>
        )}
        <div className="flex flex-row gap-4">
          {/* Whitelisted Domains */}
          <div className="flex flex-1 flex-col">
            <Label htmlFor="whiteListedDomains" className="mb-2 px-1">
              Whitelisted Domains
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  Domains with access (one at a time, press + to add).
                </TooltipContent>
              </Tooltip>
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="whiteListedDomains"
                type="url"
                placeholder="https://quantumscam.net"
                value={whiteDomainInput}
                onChange={(e) => setWhiteDomainInput(e.target.value)}
                className="max-w-full"
              />
              <Tooltip
                open={
                  !!whiteDomainInput.trim() && whiteListedDomains.includes(whiteDomainInput.trim())
                }
              >
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    onClick={() => {
                      if (
                        whiteDomainInput.trim() &&
                        isValidDomainOrUrl(whiteDomainInput.trim()) &&
                        !whiteListedDomains.includes(whiteDomainInput.trim())
                      ) {
                        setFieldValue('whiteListedDomains', [
                          ...whiteListedDomains,
                          whiteDomainInput.trim(),
                        ]);
                        setWhiteDomainInput('');
                      }
                    }}
                    className={`rounded-full bg-muted p-1 ${whiteDomainInput.trim() && isValidDomainOrUrl(whiteDomainInput.trim()) && !whiteListedDomains.includes(whiteDomainInput.trim()) ? 'transition-transform duration-200 hover:rotate-90 hover:bg-accent' : 'cursor-not-allowed opacity-50'}`}
                    aria-label="Add whitelisted domain"
                    disabled={
                      !whiteDomainInput.trim() ||
                      !isValidDomainOrUrl(whiteDomainInput.trim()) ||
                      whiteListedDomains.includes(whiteDomainInput.trim())
                    }
                  >
                    <Plus className="size-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent className="text-xs text-red-500">
                  Domain already added
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {whiteListedDomains.map((domain, idx) => (
                <span key={domain + idx} className="group relative">
                  <a
                    href={domain.startsWith('http') ? domain : `https://${domain}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex max-w-[115px] items-center truncate rounded bg-muted px-2 py-1 align-middle text-xs text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                  >
                    <span className="flex-1 truncate">{domain}</span>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        setFieldValue(
                          'whiteListedDomains',
                          whiteListedDomains.filter((d) => d !== domain),
                        );
                      }}
                      className="ml-1 flex size-3 items-center justify-center rounded-full bg-transparent text-xs text-muted-foreground transition-colors hover:text-foreground"
                      tabIndex={-1}
                      aria-label="Remove domain"
                    >
                      <XIcon className="size-3" />
                    </button>
                  </a>
                </span>
              ))}
            </div>
          </div>
          {/* Blacklisted Domains */}
          <div className="flex flex-1 flex-col">
            <Label htmlFor="blackListedDomains" className="mb-2 px-1">
              Blacklisted Domains
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  Domains denied access (one at a time, press + to add).
                </TooltipContent>
              </Tooltip>
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="blackListedDomains"
                type="url"
                placeholder="https://qnon.io"
                value={blackDomainInput}
                onChange={(e) => setBlackDomainInput(e.target.value)}
                className="max-w-full"
              />
              <Tooltip
                open={
                  !!blackDomainInput.trim() && blackListedDomains.includes(blackDomainInput.trim())
                }
              >
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    onClick={() => {
                      if (
                        blackDomainInput.trim() &&
                        isValidDomainOrUrl(blackDomainInput.trim()) &&
                        !blackListedDomains.includes(blackDomainInput.trim())
                      ) {
                        setFieldValue('blackListedDomains', [
                          ...blackListedDomains,
                          blackDomainInput.trim(),
                        ]);
                        setBlackDomainInput('');
                      }
                    }}
                    className={`rounded-full bg-muted p-1 ${blackDomainInput.trim() && isValidDomainOrUrl(blackDomainInput.trim()) && !blackListedDomains.includes(blackDomainInput.trim()) ? 'transition-transform duration-200 hover:rotate-90 hover:bg-accent' : 'cursor-not-allowed opacity-50'}`}
                    aria-label="Add blacklisted domain"
                    disabled={
                      !blackDomainInput.trim() ||
                      !isValidDomainOrUrl(blackDomainInput.trim()) ||
                      blackListedDomains.includes(blackDomainInput.trim())
                    }
                  >
                    <Plus className="size-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Domain already exists</TooltipContent>
              </Tooltip>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {blackListedDomains.map((domain, idx) => (
                <span key={domain + idx} className="group relative">
                  <a
                    href={domain.startsWith('http') ? domain : `https://${domain}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex max-w-[115px] items-center truncate rounded bg-muted px-2 py-1 align-middle text-xs text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                  >
                    <span className="flex-1 truncate">{domain}</span>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        setFieldValue(
                          'blackListedDomains',
                          blackListedDomains.filter((d) => d !== domain),
                        );
                      }}
                      className="ml-1 flex size-3 items-center justify-center rounded-full bg-transparent text-xs text-muted-foreground transition-colors hover:text-foreground"
                      tabIndex={-1}
                      aria-label="Remove domain"
                    >
                      <XIcon className="size-3" />
                    </button>
                  </a>
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessPricingStep;
