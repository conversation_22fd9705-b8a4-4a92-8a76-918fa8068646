'use client';
import React from 'react';
import { getFileIcon, getMimeTypeFromExtension } from '@/lib/files/file-upload-client';
import type { DeviceFormType } from '../schema';
import { Pencil, AlertCircle } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface ReviewStepProps {
  values: DeviceFormType;
  onEditStep: (stepIdx: number) => void;
  changedFields?: Record<string, any> | null;
  isEdit?: boolean;
  vendor?: string;
  uploadedFile?: File | null;
  register: any;
}

const sectionStyle = 'mb-4 p-4 rounded-lg border bg-card text-card-foreground shadow-sm';
const labelStyle = 'block text-xs font-medium text-muted-foreground mb-0.5';
const valueStyle = 'text-sm text-foreground break-words';

const ReviewStep: React.FC<ReviewStepProps> = ({
  values,
  onEditStep,
  changedFields,
  isEdit = false,
  vendor,
  uploadedFile,
  register,
}) => {
  // Determine if status should be shown
  const shouldShowStatus = isEdit && vendor === 'qBraid';

  return (
    <div className="gap-y-4">
      {/* Show changes indicator for edit mode */}
      {isEdit && changedFields && Object.keys(changedFields).length > 0 && (
        <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950/20">
          <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <AlertCircle className="size-4" />
            <span className="text-sm font-medium">Changes detected</span>
          </div>
          <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
            Only modified fields will be submitted: {Object.keys(changedFields).join(', ')}
          </div>
        </div>
      )}
      {/* Basic Info */}
      <div className={sectionStyle}>
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-base font-semibold">Basic Info</h3>
          <button
            type="button"
            onClick={() => onEditStep(0)}
            className="m-0 flex items-center border-none bg-transparent p-0 text-muted-foreground shadow-none hover:text-foreground focus:outline-none"
            aria-label="Edit section"
          >
            <Pencil className="size-4" />
          </button>
        </div>
        <div className="grid grid-cols-1 gap-x-6 gap-y-2 md:grid-cols-2">
          <div>
            <span className={labelStyle}>Name</span>
            <span className={valueStyle}>{values.name}</span>
          </div>
          <div>
            <span className={labelStyle}>Type</span>
            <span className={valueStyle}>{values.type}</span>
          </div>
          <div>
            <span className={labelStyle}>Provider ID</span>
            <span className={valueStyle}>{values.providerId}</span>
          </div>
          {values.deviceDescription && (
            <div className="md:col-span-2">
              <span className={labelStyle}>Description</span>
              <span className={valueStyle}>{values.deviceDescription}</span>
            </div>
          )}
          {values.about && (
            <div className="md:col-span-2">
              <span className={labelStyle}>About URL</span>
              <span className={valueStyle}>{values.about}</span>
            </div>
          )}
          {values.deviceImage && (
            <div className="md:col-span-2">
              <span className={labelStyle}>Device Image</span>
              {(() => {
                let url: string | null = null;
                let displayName = '';
                let icon = '📎';

                if (typeof values.deviceImage === 'string') {
                  url = values.deviceImage;
                  displayName = values.deviceImage.split('/').pop() || values.deviceImage;
                  const ext = displayName.split('.').pop() || '';
                  icon = getFileIcon(getMimeTypeFromExtension(ext));
                } else if (values.deviceImage instanceof File) {
                  url = URL.createObjectURL(values.deviceImage);
                  displayName = values.deviceImage.name;
                  icon = getFileIcon(values.deviceImage.type);
                }

                if (!url) return null;

                // If the file is an image, show a small thumbnail
                const isImage = /\.(png|jpe?g|gif|webp|bmp|svg)$/i.test(displayName);

                return (
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                  >
                    {isImage ? (
                      <img src={url} alt={displayName} className="size-8 rounded object-cover" />
                    ) : (
                      <span className="no-underline">{icon}</span>
                    )}
                    <span className="max-w-[200px] truncate underline">{displayName}</span>
                  </a>
                );
              })()}
            </div>
          )}
        </div>
      </div>
      {/* Technical */}
      <div className={sectionStyle}>
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-base font-semibold">Technical</h3>
          <button
            type="button"
            onClick={() => onEditStep(1)}
            className="m-0 flex items-center border-none bg-transparent p-0 text-muted-foreground shadow-none hover:text-foreground focus:outline-none"
            aria-label="Edit section"
          >
            <Pencil className="size-4" />
          </button>
        </div>
        <div className="grid grid-cols-1 gap-x-6 gap-y-2 md:grid-cols-2">
          <div>
            <span className={labelStyle}>Run Input Types</span>
            <span className={valueStyle}>
              {Array.isArray(values.runInputTypes)
                ? values.runInputTypes.join(', ')
                : values.runInputTypes}
            </span>

            {uploadedFile && (
              <div className="mt-1">
                {(() => {
                  const url = URL.createObjectURL(uploadedFile);
                  const icon = getFileIcon(uploadedFile.type);
                  const displayName = uploadedFile.name;
                  return (
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 rounded py-1 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                      <span className="no-underline">{icon}</span>
                      <span className="max-w-[120px] truncate underline">{displayName}</span>
                    </a>
                  );
                })()}
              </div>
            )}
          </div>
          <div>
            <span className={labelStyle}>Paradigm</span>
            <span className={valueStyle}>{values.paradigm}</span>
          </div>
          {values.type === 'QPU' && (
            <div>
              <span className={labelStyle}>Modality</span>
              <span className={valueStyle}>{values.modality}</span>
            </div>
          )}
          {values.type === 'Simulator' && (
            <div>
              <span className={labelStyle}>Processor Type</span>
              <span className={valueStyle}>{values.processorType}</span>
            </div>
          )}
          <div>
            <span className={labelStyle}>
              {values.type === 'QPU' ? 'Number of Qubits' : 'Max # of Qubits'}
            </span>
            <span className={valueStyle}>{values.numberQubits}</span>
          </div>
          {values.type === 'Simulator' && (
            <div>
              <span className={labelStyle}>Noise Models</span>
              <span className={valueStyle}>
                {Array.isArray(values.noiseModels)
                  ? values.noiseModels.join(', ')
                  : values.noiseModels}
              </span>
            </div>
          )}
        </div>
      </div>
      {/* Access & Pricing */}
      <div className={sectionStyle}>
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-base font-semibold">Access & Pricing</h3>
          <button
            type="button"
            onClick={() => onEditStep(2)}
            className="m-0 flex items-center border-none bg-transparent p-0 text-muted-foreground shadow-none hover:text-foreground focus:outline-none"
            aria-label="Edit section"
          >
            <Pencil className="size-4" />
          </button>
        </div>
        <div className="grid grid-cols-1 gap-x-6 gap-y-2 md:grid-cols-2">
          {/* Left Column */}
          {shouldShowStatus && (
            <div>
              <span className={labelStyle}>Device Status</span>
              <span className={valueStyle}>{values.status}</span>
            </div>
          )}
          <div>
            <span className={labelStyle}>Device Availability</span>
            <span className={valueStyle}>{values.isAvailable || 'true'}</span>
          </div>
          <div>
            <span className={labelStyle}>Pricing Type</span>
            <span className={valueStyle}>{values.pricingType || 'perMinute'}</span>
          </div>
          {values.pricingType === 'perMinute' && values.pricing.perMinute && (
            <div>
              <span className={labelStyle}>Price per Minute</span>
              <span className={valueStyle}>${values.pricing.perMinute}</span>
            </div>
          )}
          {values.pricingType === 'perTaskPerShot' && values.pricing.perTask && (
            <div>
              <span className={labelStyle}>Price per Task</span>
              <span className={valueStyle}>${values.pricing.perTask}</span>
            </div>
          )}

          {/* Right Column */}
          {shouldShowStatus && (values.status === 'OFFLINE' || values.status === 'RETIRED') && (
            <div>
              <span className={labelStyle}>Offline Reason</span>
              <span className={valueStyle}>{values.statusMsg}</span>
            </div>
          )}
          {values.pricingType === 'perTaskPerShot' && values.pricing.perShot && (
            <div>
              <span className={labelStyle}>Price per Shot</span>
              <span className={valueStyle}>${values.pricing.perShot}</span>
            </div>
          )}

          {/* Full width items */}
          {values.pricingType === 'file' && values.pricing.pricingFile && (
            <div className="md:col-span-2">
              <span className={labelStyle}>Pricing File</span>
              {(() => {
                let url: string | null = null;
                let displayName = '';
                let icon = '📎';

                if (typeof values.pricing.pricingFile === 'string') {
                  url = values.pricing.pricingFile;
                  displayName =
                    values.pricing.pricingFile.split('/').pop() || values.pricing.pricingFile;
                  const ext = displayName.split('.').pop() || '';
                  icon = getFileIcon(getMimeTypeFromExtension(ext));
                } else if (values.pricing.pricingFile instanceof File) {
                  url = URL.createObjectURL(values.pricing.pricingFile);
                  displayName = values.pricing.pricingFile.name;
                  icon = getFileIcon(values.pricing.pricingFile.type);
                }

                if (!url) return null;

                return (
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                  >
                    <span className="no-underline">{icon}</span>
                    <span className="max-w-[200px] truncate underline">{displayName}</span>
                  </a>
                );
              })()}
            </div>
          )}

          {/* Domains side by side */}
          <div className="flex flex-row gap-4 md:col-span-2">
            {/* Whitelisted Domains */}
            <div className="flex-1">
              <span className={labelStyle}>Whitelisted Domains</span>
              <div className="mt-1 flex flex-wrap gap-2">
                {(() => {
                  const domains = Array.isArray(values.whiteListedDomains)
                    ? values.whiteListedDomains
                    : (values.whiteListedDomains
                      ? [values.whiteListedDomains]
                      : []);

                  if (domains.length === 0) {
                    return (
                      <span className={`${valueStyle} italic text-muted-foreground`}>None</span>
                    );
                  }

                  return domains.map((domain, idx) => (
                    <a
                      key={domain + idx}
                      href={domain.startsWith('http') ? domain : `https://${domain}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-block max-w-[120px] truncate rounded bg-muted px-2 py-1 align-middle text-xs text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                      {domain}
                    </a>
                  ));
                })()}
              </div>
            </div>
            {/* Blacklisted Domains */}
            <div className="flex-1">
              <span className={labelStyle}>Blacklisted Domains</span>
              <div className="mt-1 flex flex-wrap gap-2">
                {(() => {
                  const domains = Array.isArray(values.blackListedDomains)
                    ? values.blackListedDomains
                    : (values.blackListedDomains
                      ? [values.blackListedDomains]
                      : []);

                  if (domains.length === 0) {
                    return (
                      <span className={`${valueStyle} italic text-muted-foreground`}>None</span>
                    );
                  }

                  return domains.map((domain, idx) => (
                    <a
                      key={domain + idx}
                      href={domain.startsWith('http') ? domain : `https://${domain}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-block max-w-[120px] truncate rounded bg-muted px-2 py-1 align-middle text-xs text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                      {domain}
                    </a>
                  ));
                })()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Notes */}
      <div className={sectionStyle}>
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-base font-semibold">Additional Notes</h3>
        </div>
        <Textarea
          placeholder="Add any additional notes or suggestions..."
          {...register('notes')}
          defaultValue={values.notes}
        />
      </div>
    </div>
  );
};

export default ReviewStep;
