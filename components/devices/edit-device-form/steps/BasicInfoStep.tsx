import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import CustomSelect from '../custom-select';
import { ChevronDown, Info, FileText } from 'lucide-react';
import type { DeviceFormType } from '../schema';
import type { ProviderResponseType } from '@/types/provider';
import Image from 'next/image';
import { ImageUpload } from '@/components/providers/image-upload';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import type { SetFieldValue, SetSelectFieldValue } from '../types';

/**
 * Props for BasicInfoStep
 */
export interface BasicInfoStepProps {
  values: DeviceFormType;
  errors: any;
  register: any;
  setFieldValue: SetFieldValue;
  setSelectFieldValue: SetSelectFieldValue;
  watch: any;
  providers?: ProviderResponseType[];
  providersLoading?: boolean;
}

/**
 * Basic Info Step of the device form
 */
const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  values,
  errors,
  register,
  setFieldValue,
  setSelectFieldValue,
  watch,
  providers = [],
  providersLoading = false,
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // Helper function to get the appropriate logo URL
  const getProviderLogo = (provider: ProviderResponseType) => {
    return provider.logoUrl || provider.logoUrlDark || null;
  };

  // Helper function to get device image value for ImageUpload component
  const getDeviceImageValue = () => {
    const deviceImage = watch('deviceImage');
    // If it's a File object, return undefined (ImageUpload will handle preview)
    // If it's a string URL, return it for display
    return typeof deviceImage === 'string' ? deviceImage : undefined;
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Device Name */}
      <div className="flex flex-col">
        <Label htmlFor="name" className="mb-2 px-1">
          Device Name <span className="text-red-500">*</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>The name of the quantum device.</TooltipContent>
          </Tooltip>
        </Label>
        <Input
          id="name"
          placeholder="e.g. SV1"
          {...register('name', { required: 'Device name is required' })}
          className={`max-w-full ${errors.name ? 'border-red-500' : ''}`}
        />
        {errors.name && <div className="text-xs text-red-400">{errors.name.message}</div>}
      </div>
      {/* Device Type (standard select) */}
      <div className="flex flex-col">
        <Label htmlFor="type" className="mb-2 px-1">
          Device Type <span className="text-red-500">*</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>Physical or simulator device.</TooltipContent>
          </Tooltip>
        </Label>
        <Select
          value={watch('type')}
          onValueChange={(v) => setSelectFieldValue('type', v as 'QPU' | 'Simulator')}
        >
          <SelectTrigger className={`max-w-full ${errors.type ? 'border-red-500' : ''}`}>
            <SelectValue placeholder="Select device type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="QPU">QPU</SelectItem>
            <SelectItem value="Simulator">Simulator</SelectItem>
          </SelectContent>
        </Select>
        {errors.type && <div className="text-xs text-red-400">{errors.type.message}</div>}
      </div>
      {/* Provider Info Display (Read-only) */}
      {providers.length > 0 && providers[0] && (
        <div className="flex flex-col">
          <Label className="mb-2 px-1">
            Provider
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>Your organization's assigned provider.</TooltipContent>
            </Tooltip>
          </Label>
          <span className="mb-1 text-xs text-muted-foreground">
            Your organization&apos;s assigned provider.
          </span>
          <div className="flex items-center gap-2 rounded-md border border-input bg-muted/50 p-2">
            {getProviderLogo(providers[0]) ? (
              <div className="relative size-6 shrink-0">
                <Image
                  src={getProviderLogo(providers[0])!}
                  alt={`${providers[0].provider} logo`}
                  width={24}
                  height={24}
                  className="size-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
            ) : (
              <div className="flex size-6 shrink-0 items-center justify-center rounded bg-muted">
                <span className="text-xs font-medium text-muted-foreground">
                  {providers[0].provider?.charAt(0)?.toUpperCase() || 'P'}
                </span>
              </div>
            )}
            <span className="font-medium">{providers[0].provider || 'Loading...'}</span>
          </div>
          <span className="mt-2 px-1 text-xs text-muted-foreground">
            Provider ID: {providers[0]._id || 'Loading...'}
          </span>
        </div>
      )}
      {/* Show loading state when providers are loading */}
      {providersLoading && (
        <div className="flex flex-col">
          <Label className="mb-2 px-1">
            Provider <span className="text-red-500">*</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>Your organization's assigned provider.</TooltipContent>
            </Tooltip>
          </Label>
          <span className="mb-1 text-xs text-muted-foreground">
            Your organization&apos;s assigned provider.
          </span>
          <div className="flex items-center gap-2 rounded-md border border-input bg-muted/50 p-3">
            <div className="flex size-6 shrink-0 items-center justify-center rounded bg-muted">
              <span className="text-xs font-medium text-muted-foreground">...</span>
            </div>
            <span className="font-medium text-muted-foreground">Loading provider...</span>
          </div>
        </div>
      )}
      {/* Show error state when no provider is assigned */}
      {!providersLoading && providers.length === 0 && (
        <div className="flex flex-col">
          <Label className="mb-2 px-1">
            Provider <span className="text-red-500">*</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>Your organization's assigned provider.</TooltipContent>
            </Tooltip>
          </Label>
          <span className="mb-1 text-xs text-muted-foreground">
            Your organization&apos;s assigned provider.
          </span>
          <div className="flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3">
            <div className="flex size-6 shrink-0 items-center justify-center rounded bg-red-100">
              <span className="text-xs font-medium text-red-600">!</span>
            </div>
            <span className="font-medium text-red-600">No provider assigned to organization</span>
          </div>
          <span className="mt-1 text-xs text-red-500">
            Please contact your administrator to assign a provider to your organization.
          </span>
        </div>
      )}
      {/* Details (optional) dropdown */}
      <div>
        <button
          type="button"
          className="flex items-center gap-2 rounded-md px-1 pb-2 text-sm text-muted-foreground transition-colors hover:bg-muted/10 hover:text-foreground"
          onClick={() => setShowDetails((v) => !v)}
          aria-expanded={showDetails}
        >
          Details (optional)
          <ChevronDown
            className={`size-4 transition-transform ${showDetails ? 'rotate-180' : ''}`}
          />
        </button>
        {showDetails && (
          <div className="space-y-3 rounded-md border border-sidebar-border p-4 animate-in fade-in">
            {/* Device Description */}
            <div className="flex flex-col">
              <Label htmlFor="deviceDescription" className="mb-2 px-1">
                Device Description
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>Short device description.</TooltipContent>
                </Tooltip>
              </Label>
              <Input
                id="deviceDescription"
                placeholder="e.g. 11 qubit quantum processor"
                {...register('deviceDescription')}
                className={`max-w-full ${errors.deviceDescription ? 'border-red-500' : ''}`}
              />
              {errors.deviceDescription && (
                <div className="text-xs text-red-400">{errors.deviceDescription.message}</div>
              )}
            </div>
            {/* Device About URL */}
            <div className="flex flex-col">
              <Label htmlFor="about" className="mb-2 px-1">
                Device About URL
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>URL for more info.</TooltipContent>
                </Tooltip>
              </Label>
              <Input
                id="about"
                type="url"
                placeholder="e.g. https://example.com/quantum-systems/harmony"
                {...register('about')}
                className={`max-w-full ${errors.about ? 'border-red-500' : ''}`}
              />
              {errors.about && <div className="text-xs text-red-400">{errors.about.message}</div>}
            </div>
            {/* Device Image Upload */}
            <div className="flex flex-col">
              <Label htmlFor="deviceImage" className=" px-1">
                Device Image
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>Upload an image of the quantum device.</TooltipContent>
                </Tooltip>
              </Label>

              {/* Current device image link */}
              {getDeviceImageValue() && (
                <div className="my-1 px-1">
                  <a
                    href={getDeviceImageValue()}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    <FileText className="size-3" />
                    Current image
                  </a>
                </div>
              )}

              <ImageUpload
                id="device-image-upload"
                currentUrl={getDeviceImageValue()}
                onUpload={(url) => {
                  // url is the preview URL, we keep the file in form state
                }}
                onFileSelect={(file) => {
                  setFieldValue('deviceImage', file);
                }}
                onRemove={() => {
                  setFieldValue('deviceImage', null);
                }}
                placeholder="Upload device image"
                className="w-full"
              />
              {errors.deviceImage && (
                <div className="text-xs text-red-400">{errors.deviceImage.message}</div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BasicInfoStep;
