import React, { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Command, CommandInput, CommandList, CommandItem } from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, X, Info, File as FileIcon } from 'lucide-react';
import { RUN_INPUT_TYPE_OPTIONS } from '../constants';
import type { DeviceFormType } from '../schema';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import CustomSelect from '../custom-select';
import MultiSelect from '../multi-select';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import type { SetFieldValue, SetSelectFieldValue } from '../types';
import type { UseFormSetValue } from 'react-hook-form';

export interface TechnicalStepProps {
  values: DeviceFormType;
  errors: any;
  register: any;
  setFieldValue: SetFieldValue;
  setSelectFieldValue: SetSelectFieldValue;
  setValue: UseFormSetValue<DeviceFormType>;
  trigger: any;
  watch: any;
  handleFileUpload?: (file: File) => void;
  uploadedFile?: File | null;
}

const TechnicalStep: React.FC<TechnicalStepProps> = ({
  values,
  errors,
  register,
  setFieldValue,
  setSelectFieldValue,
  setValue,
  trigger,
  watch,
  handleFileUpload,
  uploadedFile,
}) => {
  const [open, setOpen] = useState(false);
  const selected = Array.isArray(values.runInputTypes) ? values.runInputTypes : [];
  const currentParadigm = watch('paradigm');

  // Clear run input types when paradigm changes
  const [previousParadigm, setPreviousParadigm] = useState<string | undefined>();

  useEffect(() => {
    // Only clear if the paradigm actually changed
    if (
      previousParadigm &&
      currentParadigm &&
      previousParadigm !== currentParadigm &&
      values.runInputTypes.length > 0
    ) {
      // Keep only the "other" option if it exists, clear the rest
      const otherOptions = values.runInputTypes.filter((option) => option === 'other');
      if (otherOptions.length !== values.runInputTypes.length) {
        setFieldValue('runInputTypes', otherOptions, { shouldValidate: false });
      }
    }

    // Update the previous paradigm
    setPreviousParadigm(currentParadigm);
  }, [currentParadigm, setFieldValue, previousParadigm]);

  return (
    <div className="flex flex-col gap-4">
      {/* Paradigm (standard select) */}
      <div className="flex flex-col">
        <Label htmlFor="paradigm" className="mb-2 px-1">
          Paradigm <span className="text-red-500">*</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>Quantum computing paradigm.</TooltipContent>
          </Tooltip>
        </Label>
        <Select
          value={watch('paradigm')}
          onValueChange={(v: string) => setSelectFieldValue('paradigm', v)}
        >
          <SelectTrigger
            className={`max-w-full ${errors.paradigm ? 'border-red-500' : 'border-input'}`}
          >
            <SelectValue placeholder="Select paradigm" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="gate-based">gate-based</SelectItem>
            <SelectItem value="annealing">annealing</SelectItem>
            <SelectItem value="analog">analog</SelectItem>
          </SelectContent>
        </Select>
        {errors.paradigm && <div className="text-xs text-red-400">{errors.paradigm.message}</div>}
      </div>

      {/* Run Input Types */}
      <div className="flex flex-col">
        <Label htmlFor="runInputTypes" className="mb-2 px-1">
          Run Input Types <span className="text-red-500">*</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              Select one or more supported input types for{' '}
              {currentParadigm || 'the selected paradigm'}.
            </TooltipContent>
          </Tooltip>
        </Label>
        <MultiSelect
          value={Array.isArray(values.runInputTypes) ? values.runInputTypes : []}
          onChange={(newValue) => {
            // Use setValue directly to avoid validation issues
            setValue('runInputTypes', newValue, { shouldValidate: false, shouldDirty: true });
            // Trigger validation after a short delay to clear error state
            setTimeout(() => {
              trigger('runInputTypes');
            }, 100);
          }}
          error={!!errors.runInputTypes}
          errorMessage={errors.runInputTypes?.message}
          paradigm={currentParadigm}
          onFileUpload={handleFileUpload}
          uploadedFile={uploadedFile}
        />
      </div>

      {/* Show uploaded file between Run Input Types and Modality/Processor Type */}
      {uploadedFile && (
        <div className="flex flex-col">
          <div className="rounded-md border border-border/50 bg-muted/20 p-3">
            <div className="flex items-center gap-2">
              <FileIcon className="size-4 text-muted-foreground" />
              <div className="min-w-0 flex-1">
                <p className="truncate text-sm font-medium text-foreground">{uploadedFile.name}</p>
                <p className="text-xs text-muted-foreground">
                  {uploadedFile.size < 1024 * 1024
                    ? `${(uploadedFile.size / 1024).toFixed(1)} KB`
                    : `${(uploadedFile.size / 1024 / 1024).toFixed(2)} MB`}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (handleFileUpload) {
                    handleFileUpload(null as any);
                  }
                  // Also remove the corresponding run input type from the multi-select
                  const fileName = uploadedFile.name.split('.')[0];
                  const currentRunInputTypes = Array.isArray(values.runInputTypes)
                    ? values.runInputTypes
                    : [];
                  const updatedRunInputTypes = currentRunInputTypes.filter(
                    (type) => type !== fileName,
                  );
                  setValue('runInputTypes', updatedRunInputTypes, {
                    shouldValidate: false,
                    shouldDirty: true,
                  });
                }}
                className="size-6 p-0"
              >
                <X className="size-3" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modality or Processor Type (CustomSelect) */}
      <div className="flex flex-col">
        {watch('type') === 'QPU' ? (
          <>
            <Label htmlFor="modality" className="mb-2 px-1">
              Modality <span className="text-red-500">*</span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>Physical modality.</TooltipContent>
              </Tooltip>
            </Label>
            <CustomSelect
              value={watch('modality') || ''}
              onValueChange={(v: string) => setSelectFieldValue('modality', v)}
              placeholder="Select or enter modality"
              options={[
                { value: 'trapped-ion', label: 'trapped ion' },
                { value: 'superconducting', label: 'superconducting' },
                { value: 'photonic', label: 'photonic' },
                { value: 'neutral-atom', label: 'neutral atom' },
                { value: 'silicon', label: 'silicon' },
              ]}
              customPlaceholder="Enter custom modality..."
              className={`max-w-full ${errors.modality ? 'border-red-500' : 'border-input'}`}
            />
            {errors.modality && (
              <div className="text-xs text-red-400">{errors.modality.message}</div>
            )}
          </>
        ) : (
          <>
            <Label htmlFor="processorType" className="mb-2 px-1">
              Processor Type <span className="text-red-500">*</span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>Simulator processor type.</TooltipContent>
              </Tooltip>
            </Label>
            <CustomSelect
              value={watch('processorType') || ''}
              onValueChange={(v: string) => setSelectFieldValue('processorType', v)}
              placeholder="Select or enter processor type"
              options={[
                { value: 'state vector', label: 'state vector' },
                { value: 'tensor network', label: 'tensor network' },
                { value: 'density matrix', label: 'density matrix' },
              ]}
              customPlaceholder="Enter custom processor type..."
              className={`max-w-full ${errors.processorType ? 'border-red-500' : 'border-input'}`}
            />
            {errors.processorType && (
              <div className="text-xs text-red-400">{errors.processorType.message}</div>
            )}
          </>
        )}
      </div>
      {/* Number of Qubits */}
      <div className="flex flex-col">
        <Label htmlFor="numberQubits" className="mb-2 px-1">
          {watch('type') === 'QPU' ? 'Number of Qubits' : 'Max # of Qubits'}{' '}
          <span className="text-red-500">*</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              {watch('type') === 'QPU'
                ? 'Max qubits available.'
                : 'Maximum number of qubits users can request on the simulator.'}
            </TooltipContent>
          </Tooltip>
        </Label>
        <Input
          id="numberQubits"
          placeholder={watch('type') === 'QPU' ? 'e.g. 32' : 'e.g. 32'}
          type="number"
          min="0"
          {...register('numberQubits', { required: 'Number of qubits is required' })}
          className={`${errors.numberQubits ? 'border-red-500' : ''}`}
        />
        {errors.numberQubits && (
          <div className="text-xs text-red-400">{errors.numberQubits.message}</div>
        )}
      </div>

      {watch('type') === 'Simulator' && (
        <div className="flex flex-col">
          <Label htmlFor="noiseModels" className="mb-2 px-1">
            Noise Model(s)
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="ml-1.5 inline size-3.5 cursor-help align-middle text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>Comma separated list of noise models.</TooltipContent>
            </Tooltip>
          </Label>
          <Input
            id="noiseModels"
            placeholder="e.g. depolarizing, amplitude damping"
            {...register('noiseModels')}
            className={`${errors.noiseModels ? 'border-red-500' : ''}`}
          />
          {errors.noiseModels && (
            <div className="text-xs text-red-400">{errors.noiseModels.message}</div>
          )}
        </div>
      )}
    </div>
  );
};

export default TechnicalStep;
