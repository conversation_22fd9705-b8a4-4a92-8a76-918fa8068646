import type { DeviceData } from '@/types/device';
import type {
  Path,
  PathValue,
  UseFormReturn,
  UseFormRegister,
  FieldErrors,
  UseFormSetValue,
  UseFormWatch,
  UseFormTrigger,
  Control,
} from 'react-hook-form';
import type { DeviceFormType } from './schema';

/**
 * Type for form values that can handle both string and array inputs
 * This matches the Zod schema transformation behavior
 */
export interface DeviceFormValues {
  name: string;
  type: 'QPU' | 'Simulator';
  paradigm: string;
  modality?: string;
  processorType?: string;
  numberQubits: string;
  providerId: string; // Changed from provider to providerId
  runInputTypes: string | string[];
  noiseModels?: string | string[];
  status?: string;
  isAvailable?: string;
  statusMsg?: string | null;
  pricing: {
    perMinute?: string;
    perTask?: string;
    perShot?: string;
    pricingFile?: string | File | null;
  };
  pricingType?: string;
  deviceDescription?: string;
  about?: string;
  deviceImage?: string | File | null;
  whiteListedDomains?: string | string[];
  blackListedDomains?: string | string[];
  // Optional notes or suggestions
  notes?: string;
  qrn?: string;
  organization?: string;
}

/**
 * Type for step components that expect processed arrays
 * This is what the step components actually receive after Zod transformation
 */
export interface DeviceStepValues {
  name: string;
  type: 'QPU' | 'Simulator';
  paradigm: string;
  modality?: string;
  processorType?: string;
  numberQubits: string;
  providerId: string; // Changed from provider to providerId
  runInputTypes: string[];
  noiseModels?: string[];
  status?: string;
  isAvailable?: string;
  statusMsg?: string;
  pricing: {
    perMinute?: string;
    perTask?: string;
    perShot?: string;
    pricingFile?: string | File | null;
  };
  pricingType?: string;
  deviceDescription?: string;
  about?: string;
  deviceImage?: string | File | null;
  whiteListedDomains: string[];
  blackListedDomains: string[];
  // Optional notes or suggestions
  notes?: string;
  qrn?: string;
  organization?: string;
}

/**
 * Typed setters shared across steps and hook return
 */
export type SetFieldValue = <K extends Path<DeviceFormType>>(
  field: K,
  value: PathValue<DeviceFormType, K>,
  options?: { shouldValidate?: boolean },
) => void;

export type SetSelectFieldValue = <K extends Path<DeviceFormType>>(
  field: K,
  value: PathValue<DeviceFormType, K>,
) => void;

/**
 * Type for the pricing object in API payloads
 */
export interface DevicePricing {
  perMinute?: number;
  perTask?: number;
  perShot?: number;
  pricingFile?: string;
}

/**
 * Type for the cleaned API payload
 */
export interface DeviceApiPayload {
  name?: string;
  type?: 'QPU' | 'Simulator';
  paradigm?: string;
  modality?: string;
  processorType?: string;
  numberQubits?: number;
  providerId?: string;
  runInputTypes?: string[] | undefined;
  noiseModels?: string[] | undefined;
  status?: string;
  isAvailable?: boolean;
  statusMsg?: string | null;
  pricing?: DevicePricing;
  pricingType?: string;
  deviceDescription?: string;
  about?: string;
  deviceImage?: string;
  whiteListedDomains?: string[] | undefined;
  blackListedDomains?: string[] | undefined;
  notes?: string;
  qrn?: string;
  vendor?: string;
  vrn?: string;
  organization?: string;
  visibility?: string;
}

/**
 * Type for the extended payload that includes additional properties for device creation
 */
export interface DeviceCreatePayload extends Partial<DeviceFormValues> {
  vendor?: string;
  vrn?: string;
  visibility?: string;
}

/**
 * Type for changed fields tracking
 */
export interface ChangedFields {
  name?: string;
  type?: 'QPU' | 'Simulator';
  paradigm?: string;
  modality?: string;
  processorType?: string;
  numberQubits?: string;
  providerId?: string;
  runInputTypes?: string[];
  noiseModels?: string[];
  status?: string;
  isAvailable?: string;
  statusMsg?: string | null;
  pricing?: {
    perMinute?: string;
    perTask?: string;
    perShot?: string;
    pricingFile?: string | File | null;
  };
  pricingType?: string;
  deviceDescription?: string;
  about?: string;
  deviceImage?: string | File | null;
  whiteListedDomains?: string[];
  blackListedDomains?: string[];
  notes?: string;
  qrn?: string;
}

/**
 * Type for the create device payload (new device creation)
 */
export interface CreateDevicePayload {
  name: string;
  type: 'QPU' | 'Simulator';
  paradigm: string;
  modality?: string;
  processorType?: string;
  numberQubits: string;
  deviceId?: string;
  vendor: string;
  providerId: string;
  vrn: string;
  runInputTypes: string[];
  noiseModels: string[];
  status: string;
  visibility: string;
  whiteListedDomains: string[];
  blackListedDomains: string[];
  pricing: DevicePricing;
  deviceDescription: string;
  about: string;
  notes?: string;
  deviceImage?: string;
  isAvailable: string;
  organization: string;
  statusMsg?: string | null;
  qrn: string;
}

/**
 * Type for provider information
 */
export interface ProviderInfo {
  _id: string;
  provider: string;
}

/**
 * Type for the useDeviceForm hook props
 */
export interface UseDeviceFormProps {
  deviceData?: DeviceData;
  isEdit?: boolean;
  onSuccess?: () => void;
}

/**
 * Type for the useDeviceForm hook return value
 */
export interface UseDeviceFormReturn {
  // State management
  step: number;
  setStep: (step: number) => void;
  pendingSubmitted: boolean;
  setPendingSubmitted: (pending: boolean) => void;
  submitError: string | null;
  setSubmitError: (error: string | null) => void;
  steps: string[];
  hasChanges: boolean;
  changedFields: ChangedFields | null;

  // React Hook Form integration
  form: UseFormReturn<DeviceFormType>;
  register: UseFormRegister<DeviceFormType>;
  handleSubmit: UseFormReturn<DeviceFormType>['handleSubmit'];
  errors: FieldErrors<DeviceFormType>;
  setValue: UseFormSetValue<DeviceFormType>;
  watch: UseFormWatch<DeviceFormType>;
  trigger: UseFormTrigger<DeviceFormType>;
  control: Control<DeviceFormType>;

  // Custom handlers
  handleStepChange: (targetStep: number) => Promise<void>;
  getStepChanges: (stepIndex: number) => string[];
  setFieldValue: SetFieldValue;
  setSelectFieldValue: SetSelectFieldValue;
  onSubmit: (values: DeviceFormValues) => Promise<void>;
  handleFileUpload: (file: File | null) => void;

  // Form data
  currentValues: DeviceFormType;
  initialValues: DeviceFormType;
  uploadedFile: File | null;
}

/**
 * Type for file upload handling
 */
export interface FileUploadState {
  deviceImage?: File | null;
  pricingFile?: File | null;
  runInputTypeFile?: File | null;
}

/**
 * Type for form data creation
 */
export interface FormDataConfig {
  values: DeviceFormValues;
  orgId: string;
  providerId: string | ProviderInfo;
  isEdit: boolean;
  uploadedFile?: File | null;
  changedFields?: ChangedFields | null;
}

/**
 * Utility function to transform form values to step values
 * This handles the string/array conversion that Zod would normally do
 */
export function transformFormValuesToStepValues(values: DeviceFormValues): DeviceStepValues {
  const transformArray = (value: string | string[] | undefined): string[] => {
    if (!value) return [];
    if (typeof value === 'string') {
      return value
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean);
    }
    return value;
  };

  return {
    ...values,
    runInputTypes: transformArray(values.runInputTypes),
    noiseModels: values.noiseModels ? transformArray(values.noiseModels) : undefined,
    whiteListedDomains: transformArray(values.whiteListedDomains),
    blackListedDomains: transformArray(values.blackListedDomains),
    statusMsg: values.statusMsg ?? undefined,
  };
}

/**
 * Type guard to check if a value is a File object
 */
export function isFileObject(value: unknown): value is File {
  return value instanceof File;
}

/**
 * Type guard to check if a value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard to check if a value is an array of strings
 */
export function isStringArray(value: unknown): value is string[] {
  return Array.isArray(value) && value.every((item) => typeof item === 'string');
}
