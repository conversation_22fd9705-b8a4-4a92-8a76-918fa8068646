import React from 'react';
import {
  Step<PERSON>,
  <PERSON>per<PERSON><PERSON>,
  <PERSON>per<PERSON><PERSON><PERSON>,
  Stepper<PERSON>ndicator,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StepperSeparator,
} from '@/components/ui/stepper';
import { Cpu, Settings, Lock, DollarSign, CheckCircle2 } from 'lucide-react';

interface FormStepperProps {
  steps: string[];
  currentStep: number;
  onStepChange: (step: number) => void;
  getStepChanges: (stepIndex: number) => string[];
}

export function FormStepper({
  steps,
  currentStep,
  onStepChange,
  getStepChanges,
}: FormStepperProps) {
  const getStepIcon = (idx: number, step: number) => {
    if (idx < step) {
      return <CheckCircle2 size={16} className="text-green-500" />;
    }

    switch (idx) {
      case 0: {
        return <Cpu size={14} />;
      }
      case 1: {
        return <Settings size={14} />;
      }
      case 2: {
        return <Lock size={14} />;
      }
      case 3: {
        return <CheckCircle2 size={14} />;
      }
      default: {
        return idx + 1;
      }
    }
  };

  return (
    <Stepper
      value={currentStep}
      onValueChange={onStepChange}
      orientation="horizontal"
      className="px-1 py-2"
    >
      {steps.map((label, idx) => {
        const stepChanges = getStepChanges(idx);
        const hasStepChanges = stepChanges.length > 0;
        const isStepAvailable = idx <= currentStep + 1; // Only allow current step, previous steps, and next step

        return (
          <React.Fragment key={label}>
            <StepperItem step={idx}>
              <StepperTrigger
                className={`flex flex-col items-center gap-1 ${isStepAvailable ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                disabled={!isStepAvailable}
              >
                <StepperIndicator asChild className="size-8">
                  {getStepIcon(idx, currentStep)}
                </StepperIndicator>
                <StepperTitle className="mt-1 text-xs">
                  {label}
                  {hasStepChanges && (
                    <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">
                      ({stepChanges.length})
                    </span>
                  )}
                </StepperTitle>
              </StepperTrigger>
            </StepperItem>
            {idx < steps.length - 1 && (
              <div className="mx-2 flex flex-1 items-start justify-center pt-3">
                <StepperSeparator className="h-0.5 w-full bg-gray-300 dark:bg-gray-600" />
              </div>
            )}
          </React.Fragment>
        );
      })}
    </Stepper>
  );
}
