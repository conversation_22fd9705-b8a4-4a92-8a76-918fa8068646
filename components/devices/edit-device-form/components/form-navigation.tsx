import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface FormNavigationProps {
  currentStep: number;
  totalSteps: number;
  onStepChange: (step: number) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  isEdit: boolean;
  hasChanges: boolean;
}

export function FormNavigation({
  currentStep,
  totalSteps,
  onStepChange,
  onSubmit,
  isSubmitting,
  isEdit,
  hasChanges,
}: FormNavigationProps) {
  return (
    <div className="mt-6 flex justify-between">
      <Button
        type="button"
        variant="outline"
        className="px-6"
        onClick={() => onStepChange(currentStep - 1)}
        disabled={currentStep === 0}
      >
        Back
      </Button>
      {currentStep < totalSteps - 1 && (
        <Button
          type="button"
          className="bg-brand px-8 text-white"
          onClick={() => onStepChange(currentStep + 1)}
        >
          Continue
        </Button>
      )}
      {currentStep === totalSteps - 1 && (
        <Button
          type="button"
          className="bg-brand px-8 text-white"
          onClick={onSubmit}
          disabled={isSubmitting || (isEdit && !hasChanges)}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 animate-spin" />
              Submitting...
            </>
          ) : (isEdit && !hasChanges ? (
            'No Changes'
          ) : (
            'Submit Request'
          ))}
        </Button>
      )}
    </div>
  );
}
