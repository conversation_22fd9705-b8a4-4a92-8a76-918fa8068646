import { AlertTriangle } from 'lucide-react';

interface FormStatusProps {
  submitError: string | null;
  isSubmitting: boolean;
}

export function FormStatus({ submitError, isSubmitting }: FormStatusProps) {
  if (submitError) {
    return (
      <div className="flex min-h-[300px] flex-col items-center justify-center p-8 text-center">
        <AlertTriangle className="mb-4 size-12 text-red-500" />
        <h2 className="mb-2 text-xl font-semibold text-foreground">Submission Error</h2>
        <p className="mb-4 text-sm text-muted-foreground">{submitError}</p>
        <p className="mb-4 text-sm text-muted-foreground">
          Please close the modal and try again later.
        </p>
      </div>
    );
  }

  if (isSubmitting) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-brand"></div>
          <p className="text-sm text-muted-foreground">Submitting...</p>
        </div>
      </div>
    );
  }

  return null;
}
