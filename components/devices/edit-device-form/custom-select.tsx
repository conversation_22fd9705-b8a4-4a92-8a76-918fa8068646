import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';

/**
 * CustomSelect component for dropdown with custom value option.
 * @param value - The current value
 * @param onValueChange - Callback when value changes
 * @param placeholder - Placeholder text
 * @param options - Array of { value, label }
 * @param customPlaceholder - Placeholder for custom input
 */
export interface CustomSelectOption {
  value: string;
  label: string;
}

export interface CustomSelectProps {
  value: string;
  onValueChange: (v: string) => void;
  placeholder: string;
  options: CustomSelectOption[];
  customPlaceholder?: string;
  className?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  onValueChange,
  placeholder,
  options,
  customPlaceholder = 'Enter custom value...',
  className,
}) => {
  const [isCustom, setIsCustom] = useState(false);
  const [customValue, setCustomValue] = useState('');

  const isValueInOptions = options.some((option) => option.value === value);

  useEffect(() => {
    if (value && !isValueInOptions) {
      setIsCustom(true);
      setCustomValue(value);
    }
  }, [value, isValueInOptions]);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setIsCustom(true);
      setCustomValue('');
      onValueChange('');
    } else {
      setIsCustom(false);
      setCustomValue('');
      onValueChange(selectedValue);
    }
  };

  const handleCustomInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setCustomValue(newValue);
    onValueChange(newValue);
  };

  if (isCustom) {
    return (
      <div className="space-y-2">
        <Input
          value={customValue}
          onChange={handleCustomInputChange}
          placeholder={customPlaceholder}
          className="w-full"
          aria-label={customPlaceholder}
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            setIsCustom(false);
            setCustomValue('');
            onValueChange('');
          }}
          className="text-xs"
        >
          Choose from list instead
        </Button>
      </div>
    );
  }

  return (
    <Select onValueChange={handleSelectChange} value={value}>
      <SelectTrigger
        aria-haspopup="listbox"
        aria-expanded={!!value}
        aria-label={placeholder}
        className={className}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
        <SelectItem value="custom" className="font-medium text-violet-600 dark:text-violet-400">
          + Add custom option
        </SelectItem>
      </SelectContent>
    </Select>
  );
};

export default CustomSelect;
