import { z } from 'zod';

// Base device form Zod schema
const baseDeviceSchema = z
  .object({
    name: z.string().min(1, 'Device name is required'),
    type: z.enum(['QPU', 'Simulator'], { required_error: 'Device type is required' }),
    providerId: z.string().min(1, 'Provider is required'), // Changed from provider to providerId
    paradigm: z.string().min(1, 'Paradigm is required'),
    modality: z.string().optional(),
    processorType: z.string().optional(),
    numberQubits: z
      .string()
      .min(1, 'Number of Qubits is required')
      .refine(
        (val) => {
          const num = Number.parseInt(val, 10);
          return !isNaN(num) && num >= 0;
        },
        {
          message: 'Number of Qubits must be a non-negative integer',
        },
      ),

    // Preprocess string/array inputs
    runInputTypes: z
      .union([z.string(), z.array(z.string())])
      .transform((val) =>
        typeof val === 'string'
          ? val
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : val,
      )
      .pipe(z.array(z.string()).min(1, 'At least one input type is required')),

    noiseModels: z
      .union([z.string(), z.array(z.string())])
      .transform((val) =>
        typeof val === 'string'
          ? val
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : val,
      )
      .pipe(z.array(z.string()))
      .optional(),

    status: z.string().optional(),
    isAvailable: z.string().optional(),
    statusMsg: z.string().optional(),
    pricing: z.object({
      perMinute: z.string().optional(),
      perTask: z.string().optional(),
      perShot: z.string().optional(),
      pricingFile: z.union([z.instanceof(File).nullable(), z.string().optional()]).optional(),
    }),
    pricingType: z.string().optional(),
    deviceDescription: z.string().optional(),
    about: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val || val.trim() === '') return true; // Allow empty values
          try {
            new URL(val);
            return true;
          } catch {
            return false;
          }
        },
        {
          message: 'Please enter a valid URL (e.g., https://example.com)',
        },
      ),
    // Support both File objects (for uploads) and strings (for existing URLs)
    deviceImage: z.union([z.instanceof(File).nullable(), z.string().optional()]).optional(),
    // Optional notes or suggestions added by user during review step
    notes: z.string().optional(),

    whiteListedDomains: z
      .union([z.string(), z.array(z.string())])
      .transform((val) =>
        typeof val === 'string'
          ? val
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : val,
      )
      .pipe(z.array(z.string()))
      .default([]),

    blackListedDomains: z
      .union([z.string(), z.array(z.string())])
      .transform((val) =>
        typeof val === 'string'
          ? val
              .split(',')
              .map((s) => s.trim())
              .filter(Boolean)
          : val,
      )
      .pipe(z.array(z.string()))
      .default([]),
    qrn: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === 'QPU' && (!data.modality || data.modality.trim() === '')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Modality is required',
        path: ['modality'],
      });
    }
    if (data.type === 'Simulator' && (!data.processorType || data.processorType.trim() === '')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Processor type is required',
        path: ['processorType'],
      });
    }
    // Status validation - only when status is provided
    if (
      data.status &&
      (data.status === 'OFFLINE' || data.status === 'RETIRED') &&
      (!data.statusMsg || data.statusMsg.trim() === '')
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Offline reason is required when status is OFFLINE or RETIRED',
        path: ['statusMsg'],
      });
    }
    // Pricing validation: require only the relevant fields for the selected pricingType
    switch (data.pricingType) {
      case 'perMinute': {
        if (!data.pricing.perMinute || data.pricing.perMinute.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Price per minute is required',
            path: ['pricing', 'perMinute'],
          });
        }

        break;
      }
      case 'perTaskPerShot': {
        if (!data.pricing.perTask || data.pricing.perTask.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Price per task is required',
            path: ['pricing', 'perTask'],
          });
        }
        if (!data.pricing.perShot || data.pricing.perShot.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Price per shot is required',
            path: ['pricing', 'perShot'],
          });
        }

        break;
      }
      case 'file': {
        // For file pricing type, only require the pricing file
        if (!data.pricing.pricingFile) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Pricing file is required',
            path: ['pricing', 'pricingFile'],
          });
        }
        // Note: perMinute, perTask, and perShot are intentionally not required and should be undefined

        break;
      }
      default: {
        // If pricingType is not set, require at least one pricing field
        const hasPerMinute = !!data.pricing.perMinute && data.pricing.perMinute.trim() !== '';
        const hasPerTask = !!data.pricing.perTask && data.pricing.perTask.trim() !== '';
        const hasPerShot = !!data.pricing.perShot && data.pricing.perShot.trim() !== '';
        if (!hasPerMinute && !(hasPerTask && hasPerShot)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message:
              'You must provide either a price per minute, or both a price per task and per shot.',
            path: ['pricing'],
          });
        }
      }
    }
  });

// Function to create device schema with conditional status validation
export function createDeviceSchema(shouldRequireStatus: boolean = true) {
  return baseDeviceSchema.superRefine((data, ctx) => {
    // Add status requirement validation when needed
    if (shouldRequireStatus && (!data.status || data.status.trim() === '')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status is required',
        path: ['status'],
      });
    }
  });
}

// Export the base schema as the default
export const deviceSchema = baseDeviceSchema;

export type DeviceFormType = z.infer<typeof baseDeviceSchema>;
