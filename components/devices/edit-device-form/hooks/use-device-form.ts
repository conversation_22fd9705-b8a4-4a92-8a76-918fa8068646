// ============================================================================
// IMPORTS - All external dependencies and internal utilities
// ============================================================================
import { useForm, type UseFormReturn, type Resolver } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useRef, useMemo, useEffect } from 'react';
import { useUpdateDeviceData, apiClient } from '@/hooks/use-api';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useOrgProvider } from '@/hooks/use-provider-management';
import { createDeviceSchema, DeviceFormType } from '../schema';
import { getChangedFields } from '../utils/change-tracking';
import { cleanDevicePayload } from '../utils/payload-cleaner';
import type {
  DeviceFormValues,
  UseDeviceFormProps,
  UseDeviceFormReturn,
  ChangedFields,
  DeviceCreatePayload,
  SetFieldValue,
  SetSelectFieldValue,
} from '../types';
import { isFileObject } from '../types';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

// Common API error shape and type guard
type ApiError = { message: string; status?: number; code?: string; details?: unknown };
function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as { message?: unknown }).message === 'string'
  );
}

// ============================================================================
// HELPER FUNCTIONS - Utility functions used by the main hook
// ============================================================================

/**
 * Helper function to create FormData for device submission with image upload
 * This handles both regular form data and file uploads (device images, pricing files)
 * @param values - Form values from the device form
 * @param orgId - Organization ID for the submission
 * @param providerId - Provider ID for the device
 * @param isEdit - Whether this is an edit operation
 * @param uploadedFile - Optional uploaded file (legacy parameter)
 * @returns FormData object ready for API submission
 */
function createDeviceFormData(
  values: DeviceFormValues,
  orgId: string,
  providerId: string | { _id: string; provider: string },
  isEdit: boolean = false,
  uploadedFile?: File | null,
  changedFields?: ChangedFields | null,
): FormData {
  const deviceImageFile = isFileObject(values.deviceImage) ? values.deviceImage : null;
  const pricingFile = isFileObject(values.pricing.pricingFile) ? values.pricing.pricingFile : null;

  // Creating FormData with configuration details

  const formData = new FormData();

  // Build device data based on mode
  let deviceData: DeviceCreatePayload;

  if (isEdit && changedFields) {
    // Edit mode: only include changed fields
    deviceData = { ...changedFields };

    // Handle nested pricing changes
    if (changedFields.pricing) {
      deviceData.pricing = changedFields.pricing;
    }

    // If device image is uploaded, include it in deviceData for admin visibility
    if (deviceImageFile) {
      deviceData.deviceImage = `File: ${deviceImageFile.name} (${deviceImageFile.size} bytes)`;
      // Device image uploaded - including in deviceData
    }

    // If pricing file is uploaded, include all pricing fields for admin visibility
    if (pricingFile) {
      const currentPricing = values.pricing;
      deviceData.pricing = {
        perMinute: currentPricing.perMinute || '',
        perTask: currentPricing.perTask || '',
        perShot: currentPricing.perShot || '',
        pricingFile: currentPricing.pricingFile || '',
      };
      // Also include pricing type for complete pricing context
      deviceData.pricingType = values.pricingType;
      // Pricing file uploaded - including all pricing fields
    }

    // Edit mode - using changed fields only
  } else {
    // Create mode: include all fields
    deviceData = {
      name: values.name,
      type: values.type,
      paradigm: values.paradigm,
      modality: values.type === 'QPU' ? values.modality : undefined,
      processorType: values.type === 'Simulator' ? values.processorType : undefined,
      numberQubits: values.numberQubits,
      vendor: 'NEEDS ADMIN APPROVAL',
      providerId: typeof providerId === 'string' ? providerId : providerId._id,
      vrn: 'NEEDS ADMIN APPROVAL',
      runInputTypes: values.runInputTypes,
      noiseModels: values.noiseModels || [],
      status: values.status,
      visibility: 'private',
      whiteListedDomains: values.whiteListedDomains || [],
      blackListedDomains: values.blackListedDomains || [],
      notes: values.notes,
      pricing: (() => {
        const blank = { perMinute: '', perTask: '', perShot: '', pricingFile: '' };
        switch (values.pricingType) {
          case 'perMinute':
          default: {
            return { ...blank, perMinute: values.pricing.perMinute };
          }
          case 'perTaskPerShot': {
            return { ...blank, perTask: values.pricing.perTask, perShot: values.pricing.perShot };
          }
          case 'file': {
            return { ...blank, pricingFile: values.pricing.pricingFile };
          }
        }
      })(),
      deviceDescription: values.deviceDescription,
      about: values.about,
      isAvailable: values.isAvailable,
      organization: orgId,
      statusMsg: values.status === 'OFFLINE' || values.status === 'RETIRED' ? values.statusMsg : '',
    };

    // Only include qrn for new devices (not in edit mode)
    const deviceName = values.name.replaceAll(/\s+/g, '_').toLowerCase();
    const providerSlug =
      typeof providerId === 'string' ? '' : (providerId?.provider ?? '').toLowerCase();
    deviceData.qrn = `${providerSlug}_${deviceName}`;

    // Create mode - using all fields
  }

  // Add device data as JSON (excluding File objects)
  formData.append('deviceData', JSON.stringify(deviceData));
  formData.append('orgId', orgId);

  // Add device image if it's a File object
  if (deviceImageFile) {
    formData.append('deviceImage', deviceImageFile);
  }

  // Add pricing file if it's a File object
  if (pricingFile) {
    formData.append('pricingFile', pricingFile);
  }

  // Add uploaded file for "other" input type if it exists
  if (uploadedFile) {
    formData.append('runInputTypeFile', uploadedFile);
  }

  return formData;
}

// ============================================================================
// ============================================================================
// SUBMISSION HELPERS - Extracted for readability
// ============================================================================

/**
 * Build payload for edited device submission, including only changed fields.
 * Nested pricing object is handled automatically.
 */
const buildChangedFieldsPayload = (
  changedFields: ChangedFields,
  currentValues: { pricing?: { perMinute?: string; perTask?: string; perShot?: string } } = {},
): Partial<DeviceFormValues> => {
  const payload: Partial<DeviceFormValues> = {};
  if (!changedFields) return payload;

  const directKeys = [
    'name',
    'type',
    'paradigm',
    'modality',
    'processorType',
    'numberQubits',
    'providerId',
    'runInputTypes',
    'noiseModels',
    'status',
    'statusMsg',
    'deviceDescription',
    'about',
    'deviceImage',
    'notes',
    'whiteListedDomains',
    'blackListedDomains',
    'pricingType',
  ] as const;

  const typedChanged = changedFields as Partial<DeviceFormValues>;

  directKeys.forEach((key) => {
    const value = typedChanged[key as keyof DeviceFormValues];
    if (value !== undefined) {
      (payload as Partial<DeviceFormValues>)[key as keyof DeviceFormValues] = value as never;
    }
  });

  if (changedFields?.pricing) {
    const pricingChanges = changedFields.pricing as Partial<DeviceFormValues['pricing']>;
    const hasPricingChanges = Object.keys(pricingChanges).some(
      (k) =>
        (pricingChanges as Record<string, string | File | null | undefined>)[
          k as keyof typeof pricingChanges
        ] !== undefined &&
        (pricingChanges as Record<string, string | File | null | undefined>)[
          k as keyof typeof pricingChanges
        ] !== '',
    );
    if (hasPricingChanges) {
      payload.pricing = {};

      // If either perTask or perShot changed, include BOTH values to prevent one from being cleared
      const perTaskOrShotChanged =
        pricingChanges.perTask !== undefined || pricingChanges.perShot !== undefined;

      if (perTaskOrShotChanged) {
        payload.pricing.perTask = currentValues.pricing?.perTask;
        payload.pricing.perShot = currentValues.pricing?.perShot;
      }

      // Handle perMinute independently
      if (pricingChanges.perMinute !== undefined) {
        payload.pricing.perMinute = pricingChanges.perMinute;
      }
    }
  }

  return payload;
};

// ============================================================================
// MAIN HOOK - useDeviceForm
// ============================================================================

/**
 * Custom hook for managing device form state and logic
 * Handles both new device creation and device editing with the following features:
 * - Multi-step form navigation with validation
 * - Change tracking for edit mode (only submit modified fields)
 * - Dynamic schema validation based on device vendor and edit mode
 * - Image and file upload support
 * - Status field visibility based on vendor (only qBraid can edit status)
 *
 * @param props - Configuration object with device data, edit mode, and success callback
 * @returns Object containing form state, handlers, and validation functions
 */
export function useDeviceForm({
  deviceData,
  isEdit = false,
  onSuccess,
}: UseDeviceFormProps): UseDeviceFormReturn {
  // ============================================================================
  // STATE MANAGEMENT - Local component state for form behavior
  // ============================================================================
  const [step, setStep] = useState(0); // Current step in multi-step form (0-4)
  const [pendingSubmitted, setPendingSubmitted] = useState(false); // Loading state during submission
  const [submitError, setSubmitError] = useState<string | null>(null); // Error messages for display
  const [uploadedFile, setUploadedFile] = useState<File | null>(null); // File upload state

  // ============================================================================
  // EXTERNAL DATA & CONTEXT - API calls and context providers
  // ============================================================================
  const updateDeviceMutation = useUpdateDeviceData(); // Mutation hook for device updates
  const { currentOrgId } = useOrgContext(); // Current organization context
  const { data: provider, isLoading: providersLoading } = useOrgProvider(currentOrgId || ''); // Organization's provider info

  // Form step configuration - defines the order and names of form steps
  const steps = ['Basic Info', 'Technical', 'Access & Pricing', 'Review'];

  // ============================================================================
  // DATA TRANSFORMATION - Convert device data to form-compatible format
  // ============================================================================
  // These sections normalize array/string fields from device data into consistent arrays
  // for use in the form. This handles cases where API might return strings or arrays.
  let runInputTypesArray: string[] = [];
  if (Array.isArray(deviceData?.runInputTypes)) {
    runInputTypesArray = deviceData.runInputTypes;
  } else if (typeof deviceData?.runInputTypes === 'string') {
    runInputTypesArray = deviceData.runInputTypes
      .split(',')
      .map((s: string) => s.trim())
      .filter(Boolean);
  }
  let noiseModelsArray: string[] = [];
  if (Array.isArray(deviceData?.noiseModels)) {
    noiseModelsArray = deviceData.noiseModels;
  } else if (typeof deviceData?.noiseModels === 'string') {
    noiseModelsArray = deviceData.noiseModels
      .split(',')
      .map((s: string) => s.trim())
      .filter(Boolean);
  }
  let whiteListedDomainsArray: string[] = [];
  if (Array.isArray(deviceData?.whiteListedDomains)) {
    whiteListedDomainsArray = deviceData.whiteListedDomains;
  } else if (typeof deviceData?.whiteListedDomains === 'string') {
    whiteListedDomainsArray = deviceData.whiteListedDomains
      .split(',')
      .map((s: string) => s.trim())
      .filter(Boolean);
  }
  let blackListedDomainsArray: string[] = [];
  if (Array.isArray(deviceData?.blackListedDomains)) {
    blackListedDomainsArray = deviceData.blackListedDomains;
  } else if (typeof deviceData?.blackListedDomains === 'string') {
    blackListedDomainsArray = deviceData.blackListedDomains
      .split(',')
      .map((s: string) => s.trim())
      .filter(Boolean);
  }

  const initialValues: DeviceFormType = {
    name: deviceData?.name || '',
    type:
      deviceData?.type === 'QPU' || deviceData?.type === 'Simulator'
        ? (deviceData.type as 'QPU' | 'Simulator')
        : 'QPU',
    paradigm: deviceData?.paradigm || 'gate-based',
    modality: deviceData?.modality || 'trapped-ion',
    processorType: deviceData?.processorType || '',
    numberQubits: deviceData?.numberQubits.toString() || '',
    providerId:
      typeof deviceData?.providerId === 'string'
        ? deviceData.providerId
        : deviceData?.providerId?._id || '', // Handle new providerId object structure
    runInputTypes: runInputTypesArray,
    noiseModels: noiseModelsArray.length > 0 ? noiseModelsArray : undefined,
    status: deviceData?.status || (isEdit ? '' : 'ONLINE'), // Default to ONLINE for new devices
    isAvailable: deviceData?.isAvailable?.toString() || 'true', // Use device data if available, otherwise default to true
    statusMsg: deviceData?.statusMsg || '',
    pricing: {
      perMinute:
        deviceData?.pricing?.perMinute == undefined ? '' : deviceData.pricing.perMinute.toString(),
      perTask:
        deviceData?.pricing?.perTask == undefined ? '' : deviceData.pricing.perTask.toString(),
      perShot:
        deviceData?.pricing?.perShot == undefined ? '' : deviceData.pricing.perShot.toString(),
    },
    pricingType:
      deviceData?.pricing?.perMinute && deviceData.pricing.perMinute !== ''
        ? 'perMinute'
        : deviceData?.pricing?.perTask &&
            deviceData?.pricing?.perShot &&
            deviceData.pricing.perTask !== '' &&
            deviceData.pricing.perShot !== ''
          ? 'perTaskPerShot'
          : 'perMinute',
    deviceDescription: deviceData?.deviceDescription || '',
    about: deviceData?.about || '',
    deviceImage: deviceData?.deviceImage || '',
    whiteListedDomains: whiteListedDomainsArray,
    blackListedDomains: blackListedDomainsArray,
    qrn: deviceData?.qrn || '',
  };

  // Store initial values for change tracking in edit mode
  const initialValuesRef = useRef<DeviceFormType>(initialValues);

  // ============================================================================
  // REACT HOOK FORM CONFIGURATION - Dynamic form setup with conditional validation
  // ============================================================================

  // Determine if status should be shown and required (only qBraid vendors can edit device status)
  const shouldShowStatus = isEdit && deviceData?.vendor === 'qBraid';

  // Dynamic step field names based on status visibility - controls which fields are validated per step
  const stepFieldNames = [
    ['name', 'type', 'deviceDescription', 'about', 'deviceImage'], // Step 0: Basic Info
    ['paradigm', 'modality', 'processorType', 'numberQubits', 'runInputTypes', 'noiseModels'], // Step 1: Technical
    [
      // Step 2: Access - conditionally includes status fields
      ...(shouldShowStatus ? ['status'] : []),
      'visibility',
      'isAvailable',
      ...(shouldShowStatus ? ['statusMsg'] : []),
      'whiteListedDomains',
      'blackListedDomains',
      // Pricing fields belong to this combined step
      'pricing.perMinute',
      'pricing.perTask',
      'pricing.perShot',
    ],
    [], // Step 3 (Review) does not track fields
  ];

  // Use dynamic schema based on whether status is required - enables conditional validation
  const dynamicSchema = createDeviceSchema(shouldShowStatus);

  // Initialize React Hook Form with dynamic schema and initial values
  const form: UseFormReturn<DeviceFormType> = useForm<DeviceFormType>({
    defaultValues: initialValues,
    resolver: zodResolver(dynamicSchema) as Resolver<DeviceFormType>,
    mode: 'onChange', // Validate on every change for immediate feedback
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    trigger,
    control,
  } = form;

  // ============================================================================
  // FORM STATE MANAGEMENT - Watch values and track changes
  // ============================================================================

  // Watch all form values for real-time change tracking and validation
  const currentValues = watch();

  // Auto-populate providerId when provider data is available (new devices only)
  useEffect(() => {
    if (provider && !isEdit) {
      setValue('providerId', provider._id);
    }
  }, [provider, setValue, isEdit]);

  // Calculate which fields have changed from initial values (edit mode only)
  const changedFields = useMemo(() => {
    if (!isEdit) return null;
    const changes = getChangedFields(currentValues, initialValuesRef.current);
    return changes;
  }, [currentValues, isEdit]);

  // Determine if form has any changes (always true for new devices, conditional for edits)
  const hasChanges = useMemo(() => {
    if (!isEdit) return true; // Always allow submission for new devices
    return Object.keys(changedFields || {}).length > 0;
  }, [changedFields, isEdit]);

  // ============================================================================
  // STEP NAVIGATION & VALIDATION - Multi-step form navigation logic
  // ============================================================================

  // Helper function to check if a specific step has changes (for edit mode UI indicators)
  const getStepChanges = (stepIndex: number): string[] => {
    if (!isEdit || !changedFields) return [];

    const stepFields = stepFieldNames[stepIndex] || [];
    const changes = stepFields.filter((field) => {
      // Handle nested fields like pricing.perMinute
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (parent === 'pricing') {
          const p = changedFields.pricing as Partial<DeviceFormValues['pricing']> | undefined;
          return p ? p[child as keyof DeviceFormValues['pricing']] !== undefined : false;
        }
        return false;
      }
      return (
        (changedFields as Partial<DeviceFormValues>)[field as keyof DeviceFormValues] !== undefined
      );
    });

    return changes;
  };

  // Centralized step navigation with validation - ensures users can't skip steps without completing them
  const handleStepChange = async (targetStep: number) => {
    if (targetStep === step) return;
    // Only allow moving to the next step or previous steps, not skipping ahead
    if (targetStep > step + 1) return;
    if (targetStep < step) {
      setStep(targetStep);
      return;
    }
    // Validate current step before moving forward
    let valid = false;
    switch (step) {
      case 0: {
        valid = await trigger(['name', 'type', 'deviceDescription', 'about']); // Removed provider from validation
        break;
      }
      case 1: {
        valid = await trigger([
          'paradigm',
          'modality',
          'processorType',
          'numberQubits',
          'runInputTypes',
          'noiseModels',
        ]);
        break;
      }
      case 2: {
        valid = await trigger([
          'status',
          'isAvailable',
          'statusMsg',
          'whiteListedDomains',
          'blackListedDomains',
          'pricing.perMinute',
          'pricing.perTask',
          'pricing.perShot',
          'pricing.pricingFile',
        ]);
        break;
      }
      case 3: {
        valid = true; // Review step (no fields to validate)
        break;
      }
      default: {
        valid = true;
      }
    }
    if (valid) {
      setStep(targetStep);
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS - Helper functions for form field management
  // ============================================================================

  // Custom setFieldValue wrapper that triggers validation on change
  const setFieldValue: SetFieldValue = (field, value, options) => {
    setValue(field, value, { shouldValidate: true, ...options });
  };

  // Custom setSelectFieldValue wrapper for select components with validation
  const setSelectFieldValue: SetSelectFieldValue = (field, value) => {
    setValue(field, value, { shouldValidate: true });
  };

  // File upload handler for device images and pricing files
  const handleFileUpload = (file: File | null) => {
    setUploadedFile(file);
  };

  // ============================================================================
  // FORM SUBMISSION - Main submission logic for create/edit operations

  // ==========================================================================
  // CLEAN & MODULAR FORM SUBMISSION HANDLER
  // ==========================================================================
  const onSubmit = async (values: DeviceFormValues) => {
    // ---------- COMMON PREPARATION ----------
    const providerId = provider?._id;
    if (!providerId) {
      setSubmitError('No provider assigned to organization');
      return;
    }

    // Arrays that the API expects to always be arrays
    const runInputTypesArray = values.runInputTypes;
    const noiseModelsArray = values.noiseModels || [];
    const whiteListedDomainsArray = values.whiteListedDomains || [];
    const blackListedDomainsArray = values.blackListedDomains || [];

    // ---------- EDIT MODE ----------
    if (isEdit) {
      if (!hasChanges) {
        onSuccess?.();
        return;
      }

      // Check for file uploads in edit mode
      const hasImageUpload = isFileObject(values.deviceImage);
      const hasPricingFileUpload = isFileObject(values.pricing.pricingFile);
      const hasRunInputTypeFileUpload = isFileObject(uploadedFile);

      try {
        if (hasImageUpload || hasPricingFileUpload || hasRunInputTypeFileUpload) {
          // Handle file uploads in edit mode
          const formData = createDeviceFormData(
            values,
            currentOrgId || '',
            provider,
            true, // isEdit = true
            uploadedFile,
            changedFields,
          );

          // Add edit-specific flags to FormData
          if (hasImageUpload) {
            formData.append('deviceImageFileUpdate', 'true');
          }
          if (hasPricingFileUpload) {
            formData.append('priceFileUpdate', 'true');
          }
          if (hasRunInputTypeFileUpload) {
            formData.append('runInputTypeFileUpdate', 'true');
          }

          await updateDeviceMutation.mutateAsync({
            deviceId: deviceData?.qrn || '',
            postBody: formData,
            isFormData: true,
          });
        } else {
          // Regular JSON payload for non-file changes
          const changedPayload = buildChangedFieldsPayload(changedFields!, currentValues);
          const cleanedPayload = cleanDevicePayload(changedPayload);
          await updateDeviceMutation.mutateAsync({
            deviceId: deviceData?.qrn || '',
            postBody: cleanedPayload,
            isFormData: false,
          });
        }
        onSuccess?.();
      } catch (error: unknown) {
        const message = isApiError(error)
          ? error.message
          : error instanceof Error
            ? error.message
            : 'An unexpected error occurred.';
        setSubmitError(message);
      }

      return;
    }

    // ---------- CREATE MODE ----------
    const hasImageUpload = isFileObject(values.deviceImage);
    const hasPricingFileUpload = isFileObject(values.pricing.pricingFile);

    try {
      if (hasImageUpload || hasPricingFileUpload || uploadedFile) {
        const formData = createDeviceFormData(
          values,
          currentOrgId || '',
          provider,
          false,
          uploadedFile,
        );
        await apiClient('/api/quantum-devices', {
          method: 'POST',
          body: formData,
        });
      } else {
        const blankPricing = { perMinute: '', perTask: '', perShot: '', pricingFile: '' };
        const pricing = (() => {
          switch (values.pricingType) {
            case 'perTaskPerShot': {
              return {
                ...blankPricing,
                perTask: values.pricing.perTask,
                perShot: values.pricing.perShot,
              };
            }
            case 'file': {
              return { ...blankPricing, pricingFile: values.pricing.pricingFile };
            }
            case 'perMinute':
            default: {
              return { ...blankPricing, perMinute: values.pricing.perMinute };
            }
          }
        })();

        const deviceNameSlug = values.name.replaceAll(/\s+/g, '_').toLowerCase();

        const payload = cleanDevicePayload({
          name: values.name,
          type: values.type,
          paradigm: values.paradigm,
          modality: values.type === 'QPU' ? values.modality : undefined,
          processorType: values.type === 'Simulator' ? values.processorType : undefined,
          numberQubits: values.numberQubits,
          vendor: 'NEEDS ADMIN APPROVAL',
          providerId,
          vrn: 'NEEDS ADMIN APPROVAL',
          runInputTypes: runInputTypesArray,
          noiseModels: noiseModelsArray,
          status: values.status,
          visibility: 'private',
          whiteListedDomains: whiteListedDomainsArray,
          blackListedDomains: blackListedDomainsArray,
          pricing,
          deviceDescription: values.deviceDescription,
          about: values.about,
          notes: values.notes,
          ...(typeof values.deviceImage === 'string' && { deviceImage: values.deviceImage }),
          isAvailable: values.isAvailable,
          organization: currentOrgId || '',
          statusMsg:
            values.status === 'OFFLINE' || values.status === 'RETIRED'
              ? (values.statusMsg ?? undefined)
              : undefined,
          qrn: `${(provider?.provider ?? '').toLowerCase()}_${deviceNameSlug}`,
        });

        await apiClient('/api/quantum-devices', {
          method: 'POST',
          body: JSON.stringify({ ...payload, orgId: currentOrgId }),
        });
      }

      setPendingSubmitted(true);
      onSuccess?.();
    } catch (error: unknown) {
      const message = isApiError(error)
        ? error.message
        : error instanceof Error
          ? error.message
          : 'An unexpected error occurred.';
      setSubmitError(message);
    }
  };

  // ============================================================================
  // HOOK RETURN - All state, functions, and handlers exposed to components
  // ============================================================================

  return {
    // ===== STATE MANAGEMENT =====
    step, // Current form step (0-4)
    setStep, // Direct step setter (use handleStepChange instead for validation)
    pendingSubmitted, // Loading state during form submission
    setPendingSubmitted, // Setter for loading state
    submitError, // Error message to display to user
    setSubmitError, // Setter for error messages
    steps, // Array of step names for stepper component
    hasChanges, // Boolean indicating if form has unsaved changes (edit mode)
    changedFields, // Object containing specific fields that have changed (edit mode)

    // ===== REACT HOOK FORM INTEGRATION =====
    form, // Complete react-hook-form form object
    register, // Field registration function for form inputs
    handleSubmit, // Form submission handler wrapper
    errors, // Validation error object
    setValue, // Function to programmatically set field values
    watch, // Function to watch field values for changes
    trigger, // Function to manually trigger field validation
    control, // Form control object for complex components

    // ===== CUSTOM HANDLERS =====
    handleStepChange, // Step navigation with validation
    getStepChanges, // Get changed fields for a specific step (edit mode)
    setFieldValue, // Custom field setter with validation
    setSelectFieldValue, // Custom select field setter with validation
    onSubmit, // Main form submission logic
    handleFileUpload, // File upload handler for images/documents

    // ===== FORM DATA =====
    currentValues, // Current form values (live)
    initialValues, // Initial form values (for change tracking)
    uploadedFile, // Currently uploaded file state
  };
}
