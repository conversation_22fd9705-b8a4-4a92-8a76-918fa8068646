# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

#package-lock.json
package-lock.json

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# vitest
/coverage

# cypress
cypress.json
cypress/videos
cypress/screenshots

/package-lock.json