import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from './auth-middleware';

// Activity Log Action Types (matching backend)
export enum ActivityLogAction {
  // User Actions
  USER_INVITED = 'USER_INVITED',
  USER_REMOVED = 'USER_REMOVED',
  USER_ROLE_CHANGED = 'USER_ROLE_CHANGED',
  USER_ACCEPTED_INVITE = 'USER_ACCEPTED_INVITE',

  // Device Actions
  DEVICE_CREATED = 'DEVICE_CREATED',
  DEVICE_UPDATED = 'DEVICE_UPDATED',
  DEVICE_DELETED = 'DEVICE_DELETED',

  DEVICE_ACCESS_REQUESTED = 'DEVICE_ACCESS_REQUESTED',
  DEVICE_ACCESS_APPROVED = 'DEVICE_ACCESS_APPROVED',
  DEVICE_ACCESS_DENIED = 'DEVICE_ACCESS_DENIED',

  // Organization Actions
  ORG_CREATED = 'ORG_CREATED',
  ORG_UPDATED = 'ORG_UPDATED',

  // Job Actions
  JOB_SUBMITTED = 'JOB_SUBMITTED',
  JOB_CANCELLED = 'JOB_CANCELLED',

  // Permission Actions
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED',

  // System Actions
  SYSTEM_LOGIN = 'SYSTEM_LOGIN',
  SYSTEM_LOGOUT = 'SYSTEM_LOGOUT',

  // Provider Actions
  PROVIDER_CREATED = 'PROVIDER_CREATED',
  PROVIDER_UPDATED = 'PROVIDER_UPDATED',
  PROVIDER_REMOVED = 'PROVIDER_REMOVED',
  PROVIDER_ADDED = 'PROVIDER_ADDED',
}

// Resource Types
export enum ResourceType {
  USER = 'USER',
  DEVICE = 'DEVICE',
  ORGANIZATION = 'ORGANIZATION',
  JOB = 'JOB',
  PERMISSION = 'PERMISSION',
  SYSTEM = 'SYSTEM',
  PROVIDER = 'PROVIDER',
}

interface ActivityLogEntry {
  organizationId: string;
  action: ActivityLogAction;
  resourceType: ResourceType;
  resourceId?: string;
  resourceName?: string;
  description: string;
  userRole: string;
  metadata?: Record<string, any>;
  userAgent?: string;
  ipAddress?: string;
}

interface ActivityLogResponse {
  success: boolean;
  message?: string;
  error?: string;
  activityLog?: any;
}

/**
 * Core function to log activity after successful API operations
 */
async function logActivity(entry: ActivityLogEntry): Promise<ActivityLogResponse> {
  try {
    const { session, error } = await requireAuth();
    if (error) {
      console.warn('⚠️ [ACTIVITY-LOG] No session found, skipping audit log');
      return { success: false, error: 'No authenticated user' };
    }
    if (!session?.email) {
      console.warn('⚠️ [ACTIVITY-LOG] No session found, skipping audit log');
      return { success: false, error: 'No authenticated user' };
    }

    const activityEntry = {
      organizationId: entry.organizationId,
      action: entry.action,
      resourceType: entry.resourceType,
      resourceId: entry.resourceId,
      resourceName: entry.resourceName,
      description: entry.description,
      userRole: entry.userRole,
      metadata: entry.metadata,
      userAgent: entry.userAgent,
      ipAddress: entry.ipAddress,
    };

    console.log(`📝 [ACTIVITY-LOG] Logging: ${entry.action} for ${entry.resourceType}`);
    console.log(JSON.stringify(activityEntry, null, 2));

    const response = await externalClient.post('/activity-logs', activityEntry);

    console.log(`✅ [ACTIVITY-LOG] Successfully logged: ${entry.action}`);
    return {
      success: true,
      message: 'Activity logged successfully',
      activityLog: response.data,
    };
  } catch (error) {
    console.error('❌ [ACTIVITY-LOG] Failed to log activity:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// ============================================================================
// USER ACTIVITY LOGGING FUNCTIONS
// ============================================================================

export async function logUserInvited(
  invitedEmail: string,
  role: string,
  userRole: string,
  organizationId: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.USER_INVITED,
    resourceType: ResourceType.USER,
    resourceId: invitedEmail,
    resourceName: invitedEmail,
    description: `User ${invitedEmail} invited with role ${role}`,
    userRole,
    metadata: { ...metadata },
  });
}

export async function logUserRemoved(
  removedEmail: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.USER_REMOVED,
    resourceType: ResourceType.USER,
    resourceId: removedEmail,
    resourceName: removedEmail,
    description: `User ${removedEmail} removed from organization`,
    userRole,
    metadata,
  });
}

export async function logUserRoleChanged(
  targetEmail: string,
  oldRole: string,
  newRole: string,
  userRole: string,
  organizationId: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.USER_ROLE_CHANGED,
    resourceType: ResourceType.USER,
    resourceId: targetEmail,
    resourceName: targetEmail,
    description: `User ${targetEmail} role changed from ${oldRole} to ${newRole}`,
    userRole,
    metadata: { oldRole, newRole, ...metadata },
  });
}

export async function logUserAcceptedInvite(
  userEmail: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.USER_ACCEPTED_INVITE,
    resourceType: ResourceType.USER,
    resourceId: userEmail,
    resourceName: userEmail,
    description: `User ${userEmail} accepted organization invite`,
    userRole,
    metadata,
  });
}

// ============================================================================
// DEVICE ACTIVITY LOGGING FUNCTIONS
// ============================================================================

export async function logDeviceCreated(
  deviceId: string,
  deviceName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_CREATED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `Device ${deviceName} created`,
    userRole,
    metadata,
  });
}

export async function logDeviceUpdated(
  deviceId: string,
  deviceName: string,
  organizationId: string,
  changes: Record<string, any>,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_UPDATED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `Device ${deviceName} updated`,
    userRole,
    metadata: { ...metadata },
  });
}

export async function logDeviceDeleted(
  deviceId: string,
  deviceName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_DELETED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `Device ${deviceName} deleted`,
    userRole,
    metadata,
  });
}

export async function logDeviceAccessRequested(
  deviceId: string,
  deviceName: string,
  requestType: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_ACCESS_REQUESTED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `${requestType} access on ${deviceName} requested by ${metadata?.email}`,
    userRole,
    metadata,
  });
}

export async function logDeviceAccessApproved(
  deviceId: string,
  deviceName: string,
  requestType: string,
  organizationId: string,
  approverEmail: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_ACCESS_APPROVED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `${requestType} access on ${deviceName} approved by ${approverEmail}`,
    userRole: 'qbraid_admin',
    metadata,
  });
}

export async function logDeviceAccessDenied(
  deviceId: string,
  deviceName: string,
  requestType: string,
  organizationId: string,
  approverEmail: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.DEVICE_ACCESS_DENIED,
    resourceType: ResourceType.DEVICE,
    resourceId: deviceId,
    resourceName: deviceName,
    description: `${requestType} access on ${deviceName} denied by ${approverEmail}`,
    userRole: 'qbraid_admin',
    metadata,
  });
}

// Enhanced logging functions for new invite actions
export async function logUserReinvited(params: {
  organizationName: string;
  invitedUserEmail: string;
  invitedUserRole: string;
  userRole: string;
  invitedByEmail: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId: params.organizationName, // Use org name as identifier
    action: ActivityLogAction.USER_INVITED, // Reuse existing action
    resourceType: ResourceType.USER,
    resourceId: params.invitedUserEmail,
    resourceName: params.invitedUserEmail,
    description: `User ${params.invitedUserEmail} re-invited with role ${params.invitedUserRole} by ${params.invitedByEmail}`,
    userRole: params.userRole,
    metadata: {
      invitedBy: params.invitedByEmail,
      isReinvite: true,
      timestamp: params.timestamp.toISOString(),
      ...params.metadata,
    },
  });
}

export async function logUserInviteCancelled(params: {
  organizationId: string;
  cancelledUserEmail: string;
  cancelledByEmail: string;
  timestamp: Date;
  userRole: string;
  metadata?: Record<string, any>;
}): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId: params.organizationId,
    action: ActivityLogAction.USER_REMOVED, // Use remove action for cancelled invites
    resourceType: ResourceType.USER,
    resourceId: params.cancelledUserEmail,
    resourceName: params.cancelledUserEmail,
    description: `Invitation for ${params.cancelledUserEmail} cancelled by ${params.cancelledByEmail}`,
    userRole: params.userRole,
    metadata: {
      cancelledBy: params.cancelledByEmail,
      isCancelInvite: true,
      timestamp: params.timestamp.toISOString(),
      ...params.metadata,
    },
  });
}

// ============================================================================
// PROVIDER ACTIVITY LOGGING FUNCTIONS
// ============================================================================

export async function logProviderCreated(
  providerId: string,
  providerName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.PROVIDER_CREATED,
    resourceType: ResourceType.PROVIDER,
    resourceId: providerId,
    resourceName: providerName,
    description: `Provider ${providerName} created by ${metadata?.email}`,
    userRole,
    metadata,
  });
}

export async function logProviderAdded(
  providerId: string,
  providerName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.PROVIDER_ADDED,
    resourceType: ResourceType.PROVIDER,
    resourceId: providerId,
    resourceName: providerName,
    description: `Provider ${providerName} added to organization by ${metadata?.email}`,
    userRole,
    metadata,
  });
}

export async function logProviderRemoved(
  providerId: string,
  providerName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.PROVIDER_REMOVED,
    resourceType: ResourceType.PROVIDER,
    resourceId: providerId,
    resourceName: providerName,
    description: `Provider ${providerName} removed from organization`,
    userRole,
    metadata,
  });
}

export async function logProviderUpdated(
  providerId: string,
  providerName: string,
  organizationId: string,
  userRole: string,
  metadata?: Record<string, any>,
): Promise<ActivityLogResponse> {
  return logActivity({
    organizationId,
    action: ActivityLogAction.PROVIDER_UPDATED,
    resourceType: ResourceType.PROVIDER,
    resourceId: providerId,
    resourceName: providerName,
    description: `Provider ${providerName} updated`,
    userRole,
    metadata,
  });
}
