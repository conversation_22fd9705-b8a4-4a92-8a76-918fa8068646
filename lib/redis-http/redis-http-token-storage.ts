import { TokenStorage } from '@/types/auth';
import { getHttpRedisClient, isHttpRedisHealthy } from './redis-http';

/**
 * HTTP Redis-based token storage implementation with in-memory fallback
 * Uses Upstash Redis REST API for serverless-compatible token storage
 */
export class HttpRedisTokenStorage implements TokenStorage {
  private fallbackStorage: Map<string, string> = new Map();
  private readonly keyPrefix = 'auth:tokens:http:';
  private readonly defaultTtl = 3600; // 1 hour TTL for auth tokens

  /**
   * Check if HTTP Redis is available, fallback to in-memory storage if not
   */
  private async useHttpRedis(): Promise<boolean> {
    try {
      return await isHttpRedisHealthy();
    } catch {
      return false;
    }
  }

  /**
   * Get the full Redis key with prefix
   */
  private getRedisKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * Set a token in storage with optional TTL
   */
  async setItem(key: string, value: string, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTtl;

    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();
        const redisKey = this.getRedisKey(key);

        await redis.setex(redisKey, ttl, value);
        console.log(`🌐 [HTTP-REDIS-STORAGE] Token stored in HTTP Redis: ${key} (TTL: ${ttl}s)`);
      } else {
        // Fallback to in-memory storage
        this.fallbackStorage.set(key, value);
        console.log(`🔐 [MEMORY-STORAGE] Token stored in memory: ${key} (fallback)`);

        // Set TTL for in-memory storage using setTimeout
        if (ttl > 0) {
          setTimeout(() => {
            this.fallbackStorage.delete(key);
            console.log(`🔐 [MEMORY-STORAGE] Token expired: ${key}`);
          }, ttl * 1000);
        }
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to store token ${key}:`, error);

      // Emergency fallback to in-memory
      this.fallbackStorage.set(key, value);
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback for token: ${key}`);
    }
  }

  /**
   * Get a token from storage
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();
        const redisKey = this.getRedisKey(key);

        const value = await redis.get(redisKey);
        console.log(
          `🌐 [HTTP-REDIS-STORAGE] Token retrieved: ${key} - ${value ? 'found' : 'not found'}`,
        );
        return value;
      } else {
        // Fallback to in-memory storage
        const value = this.fallbackStorage.get(key) || null;
        console.log(
          `🔐 [MEMORY-STORAGE] Token retrieved: ${key} - ${value ? 'found' : 'not found'} (fallback)`,
        );
        return value;
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to retrieve token ${key}:`, error);

      // Emergency fallback to in-memory
      const value = this.fallbackStorage.get(key) || null;
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback retrieval for token: ${key}`);
      return value;
    }
  }

  /**
   * Remove a token from storage
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();
        const redisKey = this.getRedisKey(key);

        await redis.del(redisKey);
        console.log(`🌐 [HTTP-REDIS-STORAGE] Token removed: ${key}`);
      } else {
        // Fallback to in-memory storage
        this.fallbackStorage.delete(key);
        console.log(`🔐 [MEMORY-STORAGE] Token removed: ${key} (fallback)`);
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to remove token ${key}:`, error);

      // Emergency fallback to in-memory
      this.fallbackStorage.delete(key);
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback removal for token: ${key}`);
    }
  }

  /**
   * Clear all tokens from storage
   */
  async clear(): Promise<void> {
    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();

        // Get all keys with our prefix
        const keys = await redis.keys(`${this.keyPrefix}*`);

        if (keys.length > 0) {
          await redis.del(...keys);
          console.log(`🌐 [HTTP-REDIS-STORAGE] Cleared ${keys.length} tokens`);
        } else {
          console.log(`🌐 [HTTP-REDIS-STORAGE] No tokens to clear`);
        }
      } else {
        // Fallback to in-memory storage
        const count = this.fallbackStorage.size;
        this.fallbackStorage.clear();
        console.log(`🔐 [MEMORY-STORAGE] Cleared ${count} tokens (fallback)`);
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to clear tokens:`, error);

      // Emergency fallback to in-memory
      const count = this.fallbackStorage.size;
      this.fallbackStorage.clear();
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback clear: ${count} tokens`);
    }
  }

  /**
   * Get token with automatic TTL refresh
   * Useful for long-running operations
   */
  async getItemWithRefresh(key: string, refreshTtl?: number): Promise<string | null> {
    const value = await this.getItem(key);

    if (value && refreshTtl) {
      // Refresh TTL if token exists
      await this.setItem(key, value, refreshTtl);
      console.log(`🌐 [HTTP-TOKEN-STORAGE] Token TTL refreshed: ${key} (${refreshTtl}s)`);
    }

    return value;
  }

  /**
   * Get storage statistics
   * Useful for monitoring and debugging
   */
  async getStats(): Promise<{
    httpRedisAvailable: boolean;
    memoryTokens: number;
    httpRedisTokens?: number;
  }> {
    const httpRedisAvailable = await this.useHttpRedis();
    const memoryTokens = this.fallbackStorage.size;

    let httpRedisTokens: number | undefined;

    if (httpRedisAvailable) {
      try {
        const redis = getHttpRedisClient();
        const keys = await redis.keys(`${this.keyPrefix}*`);
        httpRedisTokens = keys.length;
      } catch (error) {
        console.warn('⚠️ [HTTP-TOKEN-STORAGE] Failed to get HTTP Redis stats:', error);
      }
    }

    return {
      httpRedisAvailable,
      memoryTokens,
      httpRedisTokens,
    };
  }

  /**
   * Batch set multiple tokens
   * More efficient for setting multiple tokens at once
   */
  async setItems(items: Array<{ key: string; value: string; ttl?: number }>): Promise<void> {
    if (await this.useHttpRedis()) {
      try {
        const redis = getHttpRedisClient();
        const commands = items.map((item) => ({
          command: 'SETEX' as const,
          args: [this.getRedisKey(item.key), item.ttl || this.defaultTtl, item.value],
        }));

        await redis.pipeline(commands);
        console.log(`🌐 [HTTP-REDIS-STORAGE] Batch stored ${items.length} tokens`);
        return;
      } catch (error) {
        console.warn(
          '⚠️ [HTTP-TOKEN-STORAGE] Batch set failed, falling back to individual sets:',
          error,
        );
      }
    }

    // Fallback to individual sets
    for (const item of items) {
      await this.setItem(item.key, item.value, item.ttl);
    }
  }

  /**
   * Batch get multiple tokens
   */
  async getItems(keys: string[]): Promise<Array<{ key: string; value: string | null }>> {
    if (await this.useHttpRedis()) {
      try {
        const redis = getHttpRedisClient();
        const commands = keys.map((key) => ({
          command: 'GET' as const,
          args: [this.getRedisKey(key)],
        }));

        const results = await redis.pipeline(commands);
        return keys.map((key, index) => ({
          key,
          value: results[index],
        }));
      } catch (error) {
        console.warn(
          '⚠️ [HTTP-TOKEN-STORAGE] Batch get failed, falling back to individual gets:',
          error,
        );
      }
    }

    // Fallback to individual gets
    const results: Array<{ key: string; value: string | null }> = [];
    for (const key of keys) {
      const value = await this.getItem(key);
      results.push({ key, value });
    }
    return results;
  }

  /**
   * Check if a key exists
   */
  async hasItem(key: string): Promise<boolean> {
    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();
        const exists = await redis.exists(this.getRedisKey(key));
        return exists > 0;
      } else {
        return this.fallbackStorage.has(key);
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to check key ${key}:`, error);
      return this.fallbackStorage.has(key);
    }
  }

  /**
   * Get TTL for a key
   */
  async getItemTtl(key: string): Promise<number> {
    try {
      if (await this.useHttpRedis()) {
        const redis = getHttpRedisClient();
        return await redis.ttl(this.getRedisKey(key));
      } else {
        // For in-memory storage, we can't track TTL accurately
        return -1;
      }
    } catch (error) {
      console.error(`❌ [HTTP-TOKEN-STORAGE] Failed to get TTL for key ${key}:`, error);
      return -1;
    }
  }
}
