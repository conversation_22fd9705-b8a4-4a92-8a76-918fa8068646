interface HttpRedisCommand {
  command: string;
  args: (string | number)[];
}

interface HttpRedisResponse {
  result: any;
  error?: string;
}

interface UpstashRedisConfig {
  url: string;
  token: string;
  timeout?: number;
  retries?: number;
}

/**
 * HTTP Redis client for Upstash Redis REST API
 * Provides Redis-like interface using HTTP requests instead of direct TCP connections
 */
export class HttpRedisClient {
  private config: UpstashRedisConfig;
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(config: UpstashRedisConfig) {
    this.config = {
      timeout: 5000,
      retries: 3,
      ...config,
    };

    // Parse Redis URL to extract host
    const url = new URL(this.config.url);
    this.baseUrl = `https://${url.host}`;

    this.headers = {
      Authorization: `Bearer ${this.config.token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Execute a single Redis command via HTTP
   */
  async exec(command: string, ...args: (string | number)[]): Promise<any> {
    const body: HttpRedisCommand = { command, args };

    for (let attempt = 0; attempt < this.config.retries!; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/pipeline`, {
          method: 'POST',
          headers: this.headers,
          body: JSON.stringify([body]),
          signal: AbortSignal.timeout(this.config.timeout!),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data: HttpRedisResponse[] = await response.json();

        if (data[0]?.error) {
          throw new Error(data[0].error);
        }

        return data[0]?.result;
      } catch (error) {
        if (attempt === this.config.retries! - 1) {
          throw error;
        }

        // Exponential backoff
        const delay = Math.pow(2, attempt) * 100;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Execute multiple commands in a pipeline
   */
  async pipeline(commands: HttpRedisCommand[]): Promise<any[]> {
    const response = await fetch(`${this.baseUrl}/pipeline`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(commands),
      signal: AbortSignal.timeout(this.config.timeout!),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: HttpRedisResponse[] = await response.json();

    // Check for errors in any command
    const errors = data.filter((item) => item.error);
    if (errors.length > 0) {
      throw new Error(`Pipeline errors: ${errors.map((e) => e.error).join(', ')}`);
    }

    return data.map((item) => item.result);
  }

  // Redis command implementations
  async ping(): Promise<string> {
    return await this.exec('PING');
  }

  async get(key: string): Promise<string | null> {
    return await this.exec('GET', key);
  }

  async set(key: string, value: string, ...options: string[]): Promise<'OK'> {
    return await this.exec('SET', key, value, ...options);
  }

  async setex(key: string, seconds: number, value: string): Promise<'OK'> {
    return await this.exec('SETEX', key, seconds, value);
  }

  async del(...keys: string[]): Promise<number> {
    return await this.exec('DEL', ...keys);
  }

  async exists(key: string): Promise<number> {
    return await this.exec('EXISTS', key);
  }

  async expire(key: string, seconds: number): Promise<number> {
    return await this.exec('EXPIRE', key, seconds);
  }

  async ttl(key: string): Promise<number> {
    return await this.exec('TTL', key);
  }

  async keys(pattern: string): Promise<string[]> {
    return await this.exec('KEYS', pattern);
  }

  async info(section?: string): Promise<string> {
    return await this.exec('INFO', section || 'default');
  }

  async flushdb(): Promise<'OK'> {
    return await this.exec('FLUSHDB');
  }

  async hget(key: string, field: string): Promise<string | null> {
    return await this.exec('HGET', key, field);
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    return await this.exec('HSET', key, field, value);
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    return await this.exec('HGETALL', key);
  }

  async hdel(key: string, ...fields: string[]): Promise<number> {
    return await this.exec('HDEL', key, ...fields);
  }

  async sadd(key: string, ...members: string[]): Promise<number> {
    return await this.exec('SADD', key, ...members);
  }

  async srem(key: string, ...members: string[]): Promise<number> {
    return await this.exec('SREM', key, ...members);
  }

  async smembers(key: string): Promise<string[]> {
    return await this.exec('SMEMBERS', key);
  }

  async scard(key: string): Promise<number> {
    return await this.exec('SCARD', key);
  }
}

let httpRedisClient: HttpRedisClient | null = null;

/**
 * Get HTTP Redis configuration for Upstash
 */
function getHttpRedisConfig(): UpstashRedisConfig {
  const redisUrl = process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL;
  const redisToken = process.env.UPSTASH_REDIS_REST_TOKEN || process.env.REDIS_PASSWORD;

  if (!redisUrl) {
    throw new Error('REDIS_URL environment variable is required');
  }

  if (!redisToken) {
    throw new Error('UPSTASH_REDIS_REST_TOKEN or REDIS_PASSWORD environment variable is required');
  }

  return {
    url: redisUrl,
    token: redisToken,
    timeout: Number.parseInt(process.env.REDIS_TIMEOUT || '5_000'),
    retries: Number.parseInt(process.env.REDIS_RETRIES || '3'),
  };
}

/**
 * Get or create HTTP Redis client instance
 */
export function getHttpRedisClient(): HttpRedisClient {
  if (!httpRedisClient) {
    const config = getHttpRedisConfig();
    console.log('🌐 [HTTP-REDIS] Creating new HTTP Redis client');
    httpRedisClient = new HttpRedisClient(config);
  }
  return httpRedisClient;
}

/**
 * Health check for HTTP Redis connection
 */
export async function isHttpRedisHealthy(): Promise<boolean> {
  try {
    const client = getHttpRedisClient();
    const pingResult = await client.ping();
    return pingResult === 'PONG';
  } catch (error) {
    console.warn('⚠️ [HTTP-REDIS] Health check failed:', error);
    return false;
  }
}

/**
 * Test HTTP Redis connection with performance metrics
 */
export async function testHttpRedisConnection(): Promise<{
  connected: boolean;
  error?: string;
  latency?: number;
  operations?: {
    ping: number;
    set: number;
    get: number;
    del: number;
  };
}> {
  const startTime = Date.now();
  const operations = { ping: 0, set: 0, get: 0, del: 0 };

  try {
    const client = getHttpRedisClient();

    // Test ping
    const pingStart = Date.now();
    const pingResult = await client.ping();
    operations.ping = Date.now() - pingStart;

    if (pingResult !== 'PONG') {
      return {
        connected: false,
        error: `Ping returned unexpected result: ${pingResult}`,
        latency: Date.now() - startTime,
        operations,
      };
    }

    // Test basic operations
    const testKey = `test:http:${Date.now()}:${Math.random().toString(36).slice(2, 11)}`;
    const testValue = `test-value-${Date.now()}`;

    // SET operation
    const setStart = Date.now();
    await client.set(testKey, testValue, 'EX', '10');
    operations.set = Date.now() - setStart;

    // GET operation
    const getStart = Date.now();
    const value = await client.get(testKey);
    operations.get = Date.now() - getStart;

    // DEL operation
    const delStart = Date.now();
    await client.del(testKey);
    operations.del = Date.now() - delStart;

    const totalLatency = Date.now() - startTime;

    if (value !== testValue) {
      return {
        connected: false,
        error: 'Set/get operation failed - value mismatch',
        latency: totalLatency,
        operations,
      };
    }

    return {
      connected: true,
      latency: totalLatency,
      operations,
    };
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: Date.now() - startTime,
      operations,
    };
  }
}

/**
 * Get HTTP Redis status
 */
export function getHttpRedisStatus(): {
  connected: boolean;
  url: string;
} {
  return {
    connected: httpRedisClient !== null,
    url: process.env.REDIS_URL || 'not configured',
  };
}
