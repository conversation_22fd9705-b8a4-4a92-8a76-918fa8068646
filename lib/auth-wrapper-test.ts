import { AuthWrapper } from '@/lib/auth-wrapper';

/**
 * Test utility to verify the UserAlreadyAuthenticatedException workaround
 * This can be used to test the authentication wrapper in isolation
 */
export const AuthWrapperTest = {
  /**
   * Tests the signInWithRetry function with a simulated conflict scenario
   */
  async testSignInRetry() {
    console.log('🧪 [TEST] Starting AuthWrapper sign-in retry test...');
    
    try {
      // This would normally trigger UserAlreadyAuthenticatedException
      // if there's an existing session
      const result = await AuthWrapper.signInWithRetry(
        '<EMAIL>',
        'testPassword',
        2 // max retries
      );
      
      console.log('✅ [TEST] Sign-in test completed successfully');
      return result;
    } catch (error) {
      console.error('❌ [TEST] Sign-in test failed:', error);
      throw error;
    }
  },

  /**
   * Tests multiple rapid authentication attempts to ensure
   * UserAlreadyAuthenticatedException is handled properly
   */
  async testRapidAuthAttempts() {
    console.log('🧪 [TEST] Testing rapid authentication attempts...');
    
    const attempts = [];
    const numAttempts = 3;
    
    for (let i = 0; i < numAttempts; i++) {
      console.log(`🔄 [TEST] Attempt ${i + 1} of ${numAttempts}`);
      
      try {
        // Configure wrapper (this will clear any existing state)
        await AuthWrapper.configure();
        
        // Attempt sign in
        const result = await AuthWrapper.signInWithRetry(
          `test${i}@example.com`,
          'testPassword',
          2
        );
        
        attempts.push({ attempt: i + 1, success: true, result });
        console.log(`✅ [TEST] Attempt ${i + 1} successful`);
      } catch (error) {
        attempts.push({ attempt: i + 1, success: false, error });
        console.error(`❌ [TEST] Attempt ${i + 1} failed:`, error);
      }
      
      // Small delay between attempts
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('📊 [TEST] Rapid auth attempts summary:', attempts);
    return attempts;
  },

  /**
   * Tests configuration clearing to ensure no state conflicts
   */
  async testConfigurationClearing() {
    console.log('🧪 [TEST] Testing configuration clearing...');
    
    try {
      // Initial configuration
      await AuthWrapper.configure();
      console.log('✅ [TEST] Initial configuration successful');
      
      // Force clear state
      await (AuthWrapper as any).forceClearAuthState();
      console.log('✅ [TEST] State clearing successful');
      
      // Reconfigure
      (AuthWrapper as any).isConfigured = false;
      await AuthWrapper.configure();
      console.log('✅ [TEST] Reconfiguration successful');
      
      return true;
    } catch (error) {
      console.error('❌ [TEST] Configuration clearing test failed:', error);
      throw error;
    }
  },
};

/**
 * Helper function to simulate UserAlreadyAuthenticatedException
 * This can be used for testing the error handling
 */
export function simulateUserAlreadyAuthenticatedException() {
  const error = new Error('There is already a signed in user.');
  error.name = 'UserAlreadyAuthenticatedException';
  return error;
}