import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { QuantumJob, JobStatus, TimeStamps } from '@/types/quantum-job';

// Define filter options
export interface FilterOptions {
  status: string[];
  vendor: string[];
  device: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  qubitsRange: {
    min: number | null;
    max: number | null;
  };
  costRange: {
    min: number | null;
    max: number | null;
  };
  timeRange: '7d' | '30d' | '60d' | '90d' | 'all';
}

// Define chart data types
export interface ChartDataPoint {
  date: string;
  jobs: number;
  completed: number;
  failed: number;
  avgExecutionTime: number;
  totalCost: number;
}

export interface DevicePerformanceData {
  device: string;
  jobs: number;
  avgExecutionTime: number;
  successRate: number;
  totalCost: number;
}

export interface QubitUsageData {
  qubits: number;
  jobs: number;
  avgExecutionTime: number;
}

interface DashboardStore {
  // Data
  quantumJobs: QuantumJob[];
  filteredJobs: QuantumJob[];
  chartData: ChartDataPoint[];
  devicePerformanceData: DevicePerformanceData[];
  qubitUsageData: QubitUsageData[];

  // Filter state
  filters: FilterOptions;
  isLoading: boolean;
  hasJobs: boolean;
  hasFilteredJobs: boolean;

  // Actions
  setQuantumJobs: (jobs: QuantumJob[]) => void;
  setFilters: (filters: Partial<FilterOptions>) => void;
  resetFilters: () => void;
  resetStore: () => void;
  applyFilters: () => void;
  setLoading: (loading: boolean) => void;
  generateChartData: () => void;
  generateDevicePerformanceData: () => void;
  generateQubitUsageData: () => void;

  // Computed data
  getJobStats: () => {
    total: number;
    completed: number;
    failed: number;
    running: number;
    pending: number;
    cancelled: number;
    successRate: number;
    avgExecutionTime: number;
    totalCost: number;
    hasJobs: boolean;
  };
}

const initialFilters: FilterOptions = {
  status: [],
  vendor: [],
  device: [],
  dateRange: {
    start: null,
    end: null,
  },
  qubitsRange: {
    min: null,
    max: null,
  },
  costRange: {
    min: null,
    max: null,
  },
  timeRange: '90d',
};

export const useDashboardStore = create<DashboardStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        quantumJobs: [],
        filteredJobs: [],
        chartData: [],
        devicePerformanceData: [],
        qubitUsageData: [],
        filters: initialFilters,
        isLoading: false,
        hasJobs: false,
        hasFilteredJobs: false,

        // Actions
        setQuantumJobs: (jobs) => {
          const hasJobs = jobs.length > 0;
          set({
            quantumJobs: jobs,
            hasJobs,
          });
          get().applyFilters();
        },

        setFilters: (newFilters) => {
          set((state) => ({
            filters: { ...state.filters, ...newFilters },
          }));
          get().applyFilters();
        },

        resetFilters: () => {
          set({ filters: initialFilters });
          get().applyFilters();
        },

        resetStore: () => {
          set({
            quantumJobs: [],
            filteredJobs: [],
            chartData: [],
            devicePerformanceData: [],
            qubitUsageData: [],
            filters: initialFilters,
            isLoading: false,
            hasJobs: false,
            hasFilteredJobs: false,
          });
        },

        applyFilters: () => {
          const { quantumJobs, filters } = get();

          let filtered = [...quantumJobs];

          // Status filter
          if (filters.status.length > 0) {
            filtered = filtered.filter((job) => filters.status.includes(job.status));
          }

          // Vendor filter
          if (filters.vendor.length > 0) {
            filtered = filtered.filter((job) => filters.vendor.includes(job.vendor));
          }

          // Device filter (qbraidDeviceId)
          if (filters.device.length > 0) {
            filtered = filtered.filter((job) => filters.device.includes(job.qbraidDeviceId));
          }

          // Date range filter
          if (filters.dateRange.start) {
            filtered = filtered.filter(
              (job) => new Date(job.createdAt) >= filters.dateRange.start!,
            );
          }
          if (filters.dateRange.end) {
            filtered = filtered.filter((job) => new Date(job.createdAt) <= filters.dateRange.end!);
          }

          // Time range filter
          if (filters.timeRange !== 'all') {
            const referenceDate = new Date();
            let daysToSubtract = 90;
            if (filters.timeRange === '30d') {
              daysToSubtract = 30;
            } else if (filters.timeRange === '7d') {
              daysToSubtract = 7;
            }
            const startDate = new Date(referenceDate);
            startDate.setDate(startDate.getDate() - daysToSubtract);

            filtered = filtered.filter((job) => new Date(job.createdAt) >= startDate);
          }

          // Qubits range filter
          if (filters.qubitsRange.min !== null) {
            filtered = filtered.filter((job) => job.circuitNumQubits >= filters.qubitsRange.min!);
          }
          if (filters.qubitsRange.max !== null) {
            filtered = filtered.filter((job) => job.circuitNumQubits <= filters.qubitsRange.max!);
          }

          // Cost range filter
          if (filters.costRange.min !== null) {
            filtered = filtered.filter(
              (job) => job.cost !== undefined && job.cost >= filters.costRange.min!,
            );
          }
          if (filters.costRange.max !== null) {
            filtered = filtered.filter(
              (job) => job.cost !== undefined && job.cost <= filters.costRange.max!,
            );
          }

          const hasFilteredJobs = filtered.length > 0;
          set({
            filteredJobs: filtered,
            hasFilteredJobs,
          });
          // Generate chart data after state update
          setTimeout(() => {
            get().generateChartData();
            get().generateDevicePerformanceData();
            get().generateQubitUsageData();
          }, 0);
        },

        setLoading: (loading) => set({ isLoading: loading }),

        // Generate chart data
        generateChartData: () => {
          const { filteredJobs } = get();

          // Group jobs by date
          const groupedByDate: Record<string, ChartDataPoint> = {};

          for (const job of filteredJobs) {
            const date = new Date(job.createdAt).toISOString().split('T')[0];

            if (!groupedByDate[date]) {
              groupedByDate[date] = {
                date,
                jobs: 0,
                completed: 0,
                failed: 0,
                avgExecutionTime: 0,
                totalCost: 0,
              };
            }

            groupedByDate[date].jobs += 1;
            groupedByDate[date].totalCost += (job.cost || 0) / 100;

            if (job.status === 'COMPLETED') {
              groupedByDate[date].completed += 1;
            } else if (job.status === 'FAILED') {
              groupedByDate[date].failed += 1;
            }
          }

          // Calculate average execution time for each date
          for (const date of Object.keys(groupedByDate)) {
            const dateJobs = filteredJobs.filter(
              (job) => new Date(job.createdAt).toISOString().split('T')[0] === date,
            );

            const totalExecutionTime = dateJobs.reduce(
              (sum, job) => sum + (job.timeStamps?.executionDuration || 0),
              0,
            );

            groupedByDate[date].avgExecutionTime =
              dateJobs.length > 0 ? totalExecutionTime / dateJobs.length : 0;
          }

          // Convert to array and sort by date
          const chartData = Object.values(groupedByDate).sort(
            (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
          );

          set({ chartData });
        },

        // Generate device performance data
        generateDevicePerformanceData: () => {
          const { filteredJobs } = get();

          // Group jobs by device
          const groupedByDevice: Record<string, DevicePerformanceData> = {};

          for (const job of filteredJobs) {
            if (!groupedByDevice[job.qbraidDeviceId]) {
              groupedByDevice[job.qbraidDeviceId] = {
                device: job.qbraidDeviceId,
                jobs: 0,
                avgExecutionTime: 0,
                successRate: 0,
                totalCost: 0,
              };
            }

            groupedByDevice[job.qbraidDeviceId].jobs += 1;
            groupedByDevice[job.qbraidDeviceId].totalCost += (job.cost || 0) / 100;
          }

          // Calculate average execution time and success rate for each device
          for (const device of Object.keys(groupedByDevice)) {
            const deviceJobs = filteredJobs.filter((job) => job.qbraidDeviceId === device);

            const totalExecutionTime = deviceJobs.reduce(
              (sum, job) => sum + (job.timeStamps?.executionDuration || 0),
              0,
            );

            const completedJobs = deviceJobs.filter((job) => job.status === 'COMPLETED').length;

            groupedByDevice[device].avgExecutionTime =
              deviceJobs.length > 0 ? totalExecutionTime / deviceJobs.length : 0;

            groupedByDevice[device].successRate =
              deviceJobs.length > 0 ? (completedJobs / deviceJobs.length) * 100 : 0;
          }

          // Convert to array and sort by number of jobs
          const devicePerformanceData = Object.values(groupedByDevice).sort(
            (a, b) => b.jobs - a.jobs,
          );

          set({ devicePerformanceData });
        },

        // Generate qubit usage data
        generateQubitUsageData: () => {
          const { filteredJobs } = get();

          // Group jobs by number of qubits
          const groupedByQubits: Record<number, QubitUsageData> = {};

          for (const job of filteredJobs) {
            const qubits = job.circuitNumQubits || 0;

            if (!groupedByQubits[qubits]) {
              groupedByQubits[qubits] = {
                qubits,
                jobs: 0,
                avgExecutionTime: 0,
              };
            }

            groupedByQubits[qubits].jobs += 1;
          }

          // Calculate average execution time for each qubit count
          for (const qubits of Object.keys(groupedByQubits)) {
            const qubitJobs = filteredJobs.filter(
              (job) => (job.circuitNumQubits || 0) === Number.parseInt(qubits),
            );

            const totalExecutionTime = qubitJobs.reduce(
              (sum, job) => sum + (job.timeStamps?.executionDuration || 0),
              0,
            );

            groupedByQubits[Number.parseInt(qubits)].avgExecutionTime =
              qubitJobs.length > 0 ? totalExecutionTime / qubitJobs.length : 0;
          }

          // Convert to array and sort by number of qubits
          const qubitUsageData = Object.values(groupedByQubits).sort((a, b) => a.qubits - b.qubits);

          set({ qubitUsageData });
        },

        // Computed data
        getJobStats: () => {
          const { filteredJobs, hasFilteredJobs } = get();

          const total = filteredJobs.length;
          const completed = filteredJobs.filter((job) => job.status === 'COMPLETED').length;
          const failed = filteredJobs.filter((job) => job.status === 'FAILED').length;
          const running = filteredJobs.filter((job) => job.status === 'RUNNING').length;
          const pending = filteredJobs.filter((job) => job.status === 'INITIALIZED').length;
          const cancelled = filteredJobs.filter((job) => job.status === 'CANCELLED').length;

          const successRate = total > 0 ? (completed / total) * 100 : 0;

          const totalExecutionTime = filteredJobs.reduce(
            (sum, job) => sum + (job.timeStamps?.executionDuration || 0),
            0,
          );
          const avgExecutionTime = total > 0 ? totalExecutionTime / total : 0;

          const totalCost = filteredJobs.reduce((sum, job) => sum + (job.cost || 0) / 100, 0);

          return {
            total,
            completed,
            failed,
            running,
            pending,
            cancelled,
            successRate,
            avgExecutionTime,
            totalCost,
            hasJobs: hasFilteredJobs,
          };
        },
      }),
      {
        name: 'dashboard-store',
        partialize: (state) => ({
          filters: state.filters,
        }),
      },
    ),
    {
      name: 'dashboard-store',
    },
  ),
);
