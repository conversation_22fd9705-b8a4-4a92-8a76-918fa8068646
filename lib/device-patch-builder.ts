/**
 * Device Patch Builder
 *
 * This module handles the transformation of field values into the correct format
 * for sending to the backend API. It ensures that the data structure matches
 * what the backend expects, including special handling for pricing and array fields.
 *
 * Key Responsibilities:
 * - Convert field values to proper backend format
 * - Handle pricing fields (create pricing object)
 * - Handle array fields (convert comma-separated to arrays)
 * - Ensure type safety and data integrity
 */

import { getFieldValidation, isPricingField, isArrayField } from './device-field-validation';

/**
 * Device patch interface
 *
 * Represents the structure of a patch object that will be sent to the backend.
 * The backend expects specific formats for different field types.
 */
export interface DevicePatch {
  [key: string]: any;
}

/**
 * Builds a patch object for device updates based on field type and value
 *
 * This function transforms user input into the correct format for the backend API.
 * It handles special cases like pricing fields (which need a nested object)
 * and array fields (which need to be converted from comma-separated strings).
 *
 * @param field - The field name being updated (e.g., 'paradigm', 'pricing.perMinute')
 * @param value - The new value for the field
 * @returns A properly formatted patch object for the backend
 *
 * Examples:
 * - buildPatchObject('paradigm', 'superconducting')
 *   → { paradigm: 'superconducting' }
 *
 * - buildPatchObject('pricing.perMinute', 0.50)
 *   → { pricing: { perMinute: 0.50 } }
 *
 * - buildPatchObject('noiseModels', 'aquila, ionq')
 *   → { noiseModels: ['aquila', 'ionq'] }
 */
export function buildPatchObject(field: string, value: any): DevicePatch {
  // Handle pricing fields - backend expects a pricing object
  if (isPricingField(field)) {
    const pricingKey = field.replace('pricing.', '');
    return { pricing: { [pricingKey]: value } };
  }

  // Handle array fields - convert comma-separated string back to array
  if (isArrayField(field) && typeof value === 'string') {
    // Convert comma-separated string to array, trim whitespace, filter empty values
    const arrayValue = value
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
    return { [field]: arrayValue };
  }

  // Handle other fields normally
  return { [field]: value };
}

/**
 * Converts array values to comma-separated strings for input fields
 *
 * This function is used when displaying array values in text inputs.
 * Arrays are stored as arrays in the backend but displayed as comma-separated
 * strings in the UI for better user experience.
 *
 * @param value - The array value to convert
 * @returns A comma-separated string representation of the array
 *
 * Examples:
 * - formatArrayForInput(['aquila', 'ionq']) → 'aquila, ionq'
 * - formatArrayForInput(null) → ''
 * - formatArrayForInput([]) → ''
 */
export function formatArrayForInput(value: any): string {
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  return value ?? '';
}

/**
 * Converts input value to appropriate type based on field configuration
 *
 * This function ensures that the initial value for an input field is
 * properly formatted based on the field's validation rules.
 *
 * @param field - The field name to get configuration for
 * @param value - The current value to format
 * @returns The properly formatted value for the input field
 *
 * Examples:
 * - formatValueForInput('noiseModels', ['aquila', 'ionq']) → 'aquila, ionq'
 * - formatValueForInput('numberQubits', 100) → 100
 * - formatValueForInput('isAvailable', true) → true
 */
export function formatValueForInput(field: string, value: any): any {
  const fieldConfig = getFieldValidation(field);

  if (fieldConfig?.type === 'array' && Array.isArray(value)) {
    return formatArrayForInput(value);
  }

  return value ?? '';
}
