import React from 'react';
import { useIsQbraidAdmin } from '@/hooks/use-qbraid-admin-role';
import { apiClient } from '@/hooks/use-api';

/**
 * Admin check utility functions for centralized auth management
 * These functions provide consistent admin status checking across the application
 */

/**
 * Hook that provides comprehensive admin checking with enhanced error handling
 * This is the recommended way to check admin status in components
 */
export const useQbraidAdminAuth = () => {
  const { isQbraidAdmin, isLoading, isError } = useIsQbraidAdmin();

  return {
    isQbraidAdmin,
    isLoading,
    isError,

    // Helper methods for common use cases
    canAccessAdminPanel: isQbraidAdmin && !isLoading && !isError,
    getAdminStatusMessage: () => {
      if (isLoading) return 'Checking admin permissions...';
      if (isError) return 'Error verifying admin status';
      if (!isQbraidAdmin) return 'Access denied: Admin privileges required';
      return 'Admin access granted';
    },
  };
};

/**
 * Server-side admin check utility for API routes and middleware
 * This can be used in server components and API routes
 */
export async function verifyQbraidAdminStatus(request: Request): Promise<{
  isQbraidAdmin: boolean;
  error?: string;
  userRole?: string;
}> {
  try {
    // This would typically call the external API directly
    // For now, we'll return a placeholder implementation
    // In a real implementation, this would make a direct call to the external API

    const data = await apiClient('/api/user/role', {
      headers: {
        // Forward the authorization header from the original request
        Authorization: request.headers.get('authorization') || '',
        Cookie: request.headers.get('cookie') || '',
      },
    });

    return {
      isQbraidAdmin: data.role === 'admin',
      userRole: data.role,
    };
  } catch (error) {
    return {
      isQbraidAdmin: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Higher-order component for protecting admin-only routes
 * This can be used to wrap components that require admin access
 */
export function withQbraidAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType | React.ReactElement,
) {
  return function AdminProtectedComponent(props: P) {
    const { isQbraidAdmin, isLoading, isError } = useIsQbraidAdmin();

    if (isLoading) {
      return (
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="space-y-4 text-center">
            <div className="mx-auto size-8 animate-spin rounded-full border-b-2 border-brand"></div>
            <p className="text-muted-foreground">Verifying admin access...</p>
          </div>
        </div>
      );
    }

    if (isError || !isQbraidAdmin) {
      if (fallback) {
        if (React.isValidElement(fallback)) {
          return fallback;
        }
        const FallbackComponent = fallback as React.ComponentType;
        return <FallbackComponent />;
      }

      return (
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="max-w-md space-y-4 text-center">
            <div className="text-red-500">
              <svg
                className="mx-auto size-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold">Access Denied</h3>
            <p className="text-muted-foreground">
              This area requires administrator privileges. Please contact your system administrator
              if you believe this is an error.
            </p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

/**
 * Type guard for admin status
 * Useful for type narrowing in TypeScript
 */
export function isQbraidAdminUser(user: { role?: string }): user is { role: 'admin' } {
  return user.role === 'admin';
}

/**
 * Admin permission levels for granular access control
 */
export enum QbraidAdminPermissionLevel {
  NONE = 'none',
  READ_ONLY = 'read_only',
  FULL = 'full',
}

/**
 * Check admin permission level based on role and additional context
 */
export function getQbraidAdminPermissionLevel(
  adminRole: string | undefined,
  additionalContext?: {
    organizationId?: string;
    specificPermissions?: string[];
  },
): QbraidAdminPermissionLevel {
  if (!adminRole || adminRole !== 'admin') {
    return QbraidAdminPermissionLevel.NONE;
  }

  // If additional context is provided, check for specific permissions
  if (additionalContext?.specificPermissions) {
    const hasFullAccess = additionalContext.specificPermissions.includes('admin:full');
    const hasReadOnly = additionalContext.specificPermissions.includes('admin:read');

    if (hasFullAccess) {
      return QbraidAdminPermissionLevel.FULL;
    }
    if (hasReadOnly) {
      return QbraidAdminPermissionLevel.READ_ONLY;
    }
  }

  // Default to full access for admin role
  return QbraidAdminPermissionLevel.FULL;
}
