/**
 * Device Field Validation System
 *
 * This module defines the validation rules and types for device fields.
 * It provides a centralized configuration that matches the backend validation
 * and ensures type safety throughout the application.
 *
 * The validation system supports:
 * - Type validation (string, number, boolean, array)
 * - Range validation (min values for numbers)
 * - Enum validation (predefined options for strings)
 * - Length validation (minLength for strings)
 * - Step validation (increment values for numbers)
 */

// Field type definitions
export type FieldType = 'string' | 'number' | 'boolean' | 'array';

/**
 * Field validation configuration interface
 *
 * Defines the validation rules for a single device field.
 * Each field can have different validation requirements based on its type.
 */
export interface FieldValidation {
  type: FieldType;
  min?: number; // Minimum value for numbers
  step?: number; // Step increment for number inputs
  minLength?: number; // Minimum length for strings
  enum?: readonly string[]; // Allowed values for enum fields
}

/**
 * Field validation configuration mapping
 *
 * Maps field names to their validation rules.
 * This configuration must match the backend validation rules exactly.
 */
export type FieldValidationConfig = Record<string, FieldValidation>;

/**
 * Backend validation rules mapping
 *
 * This configuration defines all the fields that can be edited and their
 * validation requirements. It must stay in sync with the backend validation.
 *
 * Field Categories:
 * - Basic fields: name, paradigm, type, status, etc.
 * - Numeric fields: numberQubits, pendingJobs, pricing fields
 * - Boolean fields: isAvailable
 * - Array fields: noiseModels, blackListedDomains, etc.
 * - Pricing fields: Special handling for nested pricing object
 */
export const FIELD_VALIDATION: FieldValidationConfig = {
  // Basic device properties
  numberQubits: { type: 'number', min: 0 },
  statusRefresh: { type: 'number' },
  visibility: { type: 'string', enum: ['public', 'private'] },
  name: { type: 'string', minLength: 1 },
  paradigm: { type: 'string', enum: ['AHS', 'annealing', 'gate-based'] },
  type: { type: 'string', enum: ['QPU', 'Simulator'] },
  status: { type: 'string', enum: ['ONLINE', 'OFFLINE', 'RETIRED'] },
  architecture: {
    type: 'string',
    enum: ['trapped ion', 'superconducting', 'photonic', 'neutral atom'],
  },
  pendingJobs: { type: 'number', min: 0 },
  availabilityCD: { type: 'string' },
  isAvailable: { type: 'boolean' },

  // Array fields (comma-separated in UI, arrays in backend)
  noiseModels: { type: 'array' },
  blackListedDomains: { type: 'array' },
  whiteListedDomains: { type: 'array' },
  spec: { type: 'array' },
  runInputTypes: { type: 'array' },

  // Description fields
  about: { type: 'string' },
  deviceAbout: { type: 'string' },
  deviceDescription: { type: 'string' },
  deviceImage: { type: 'string' },
  processorType: { type: 'string' },

  // Pricing fields (special handling - backend expects pricing object)
  'pricing.perMinute': { type: 'number', min: 0, step: 0.01 },
  'pricing.perTask': { type: 'number', min: 0, step: 0.01 },
  'pricing.perShot': { type: 'number', min: 0, step: 0.01 },
} as const;

/**
 * Utility Functions
 *
 * Helper functions to work with field validation configuration.
 */

/**
 * Get validation configuration for a specific field
 *
 * @param field - The field name to get validation for
 * @returns FieldValidation configuration or undefined if not found
 */
export function getFieldValidation(field: string): FieldValidation | undefined {
  return FIELD_VALIDATION[field as keyof typeof FIELD_VALIDATION];
}

/**
 * Check if a field is an array type
 *
 * @param field - The field name to check
 * @returns true if the field is configured as an array type
 */
export function isArrayField(field: string): boolean {
  const config = getFieldValidation(field);
  return config?.type === 'array';
}

/**
 * Check if a field is a pricing field (starts with 'pricing.')
 *
 * @param field - The field name to check
 * @returns true if the field is a pricing field
 */
export function isPricingField(field: string): boolean {
  return field.startsWith('pricing.');
}

/**
 * Check if a field is an enum field (string with predefined options)
 *
 * @param field - The field name to check
 * @returns true if the field is configured as an enum string
 */
export function isEnumField(field: string): boolean {
  const config = getFieldValidation(field);
  return config?.type === 'string' && 'enum' in config && !!config.enum;
}
