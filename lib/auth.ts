import { redirect } from 'next/navigation';
import { requireAuth as requireAuthMiddleware } from '@/lib/auth-middleware';
import { AuthSession } from 'aws-amplify/auth';

export async function requireAuth() {
  const { session } = await requireAuthMiddleware();
  if (!session) {
    redirect('/signin');
  }
  return session as AuthSession;
}

export async function requireNoAuth() {
  const { session } = await requireAuthMiddleware();
  if (session) {
    redirect('/');
  }
}
