// Session configuration
export const SESSION_SECRET = new TextEncoder().encode(
  process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
);
// Fixed session cookie name
export const SESSION_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
export const CSRF_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Host-csrf-token' : 'csrf-token';
export const ACCESS_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-access-token' : 'access-token';
export const ID_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-id-token' : 'id-token';
// Session configuration with environment variable support
export const SESSION_DURATION_SECONDS =
  Number.parseInt(process.env.SESSION_DURATION_HOURS || '24') * 60 * 60; // Default 24 hours
export const SESSION_DURATION_MS = SESSION_DURATION_SECONDS * 1000; // Convert to milliseconds
export const CSRF_TOKEN_LENGTH = 32;

// Redis key prefixes for serverless session storage
export const REDIS_SESSION_PREFIX = 'session:';
export const REDIS_COGNITO_PREFIX = 'cognito:';

// Session storage mode: 'redis' for serverless, 'cookies' for traditional
export const STORAGE_MODE = process.env.SESSION_STORAGE_MODE || 'redis'; // Default to Redis for serverless

// Constants
export const REDIS_HEALTH_CACHE_TTL = 30_000; // 30 seconds
export const SESSION_ID_PATTERN = /^[a-f0-9]{16}$/i; // Changed from 32 to 16 to match generateSecureToken(16)
export const JWT_PATTERN = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/;
export const CSRF_TOKEN_EXPIRY_MS = 3_600_000; // 1 hour
export const TOKEN_COOKIE_MAX_AGE = 3600; // 1 hour
export const SESSION_REFRESH_THRESHOLD = 0.25; // Refresh when 25% time remaining
export const CLEANUP_BATCH_SIZE = 10;

// image
export const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

// ============================================================================
// ROLE AND PERMISSION CONSTANTS
// ============================================================================

/**
 * Role hierarchy levels - Higher number = Higher authority
 *
 * This defines the power structure of roles in the organization.
 * Used to prevent users from promoting others above their own level.
 */
export const ROLE_HIERARCHY: Record<string, number> = {
  viewer: 1, // Basic user with limited write access
  manager: 2, // Team management capabilities
  admin: 3, // Full administrative access
  owner: 4, // Organization owner - highest authority
};

/**
 * Get all available role names for UI dropdowns, sorted by hierarchy
 */
export const ALL_ROLES = Object.keys(ROLE_HIERARCHY).sort(
  (a, b) => ROLE_HIERARCHY[a] - ROLE_HIERARCHY[b],
);

/**
 * Defines which roles each role can manage (assign/remove)
 *
 * Rules:
 * - Users can only manage roles below their hierarchy level
 * - Owners cannot be managed by anyone
 * - Members and viewers cannot manage any roles
 */
export const ROLE_MANAGEMENT_PERMISSIONS: Record<string, string[]> = {
  owner: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [],
};

/**
 * Maps application routes to required permissions
 *
 * When adding new routes:
 * 1. Add the route path as key
 * 2. Specify the minimum permission required
 * 3. More specific routes should come before general ones
 */
export const ROUTE_PERMISSIONS: Record<string, string> = {
  // Device management
  '/devices': 'view:devices',
  '/edit-device': 'manage:devices',
  '/add-device': 'manage:devices',

  // User profile
  '/profile': 'view:profile',
  '/profile/edit': 'edit:profile',

  // Team management
  '/team': 'view:team',
  '/team/invite': 'manage:team',
  '/team/members': 'view:team',

  // Financial
  '/earnings': 'view:earnings',
  '/earnings/manage': 'manage:earnings',

  // Jobs
  '/jobs': 'view:jobs',
  '/jobs/manage': 'manage:jobs',

  // Providers
  '/providers': 'view:providers',
  // '/providers/add': 'manage:providers',
};

// ============================================================================
// UI ROLE CONSTANTS
// ============================================================================

/**
 * Role icons for UI display
 */
export const ROLE_ICONS: Record<string, string> = {
  owner: '👑',
  admin: '🛡️',
  manager: '👤',
  member: '🙋',
  viewer: '👁️',
};

/**
 * Role gradient styles for UI components
 */
export const ROLE_GRADIENTS: Record<string, string> = {
  owner: 'from-yellow-500/20 to-orange-500/20',
  admin: 'from-blue-500/20 to-purple-500/20',
  manager: 'from-green-500/20 to-emerald-500/20',
  member: 'from-pink-500/20 to-rose-500/20',
  viewer: 'from-gray-500/20 to-slate-500/20',
};

/**
 * Role border styles for UI components
 */
export const ROLE_BORDERS: Record<string, string> = {
  owner: 'border-yellow-500/30',
  admin: 'border-blue-500/30',
  manager: 'border-green-500/30',
  member: 'border-pink-500/30',
  viewer: 'border-gray-500/30',
};

/**
 * Role selected background styles for UI components
 */
export const ROLE_SELECTED_BG: Record<string, string> = {
  owner: 'bg-yellow-500/10',
  admin: 'bg-blue-500/10',
  manager: 'bg-green-500/10',
  member: 'bg-pink-500/10',
  viewer: 'bg-gray-500/10',
};

/**
 * Human-readable role display names
 */
export const ROLE_DISPLAY_NAMES: Record<string, string> = {
  viewer: 'Viewer',
  manager: 'Manager',
  admin: 'Admin',
  owner: 'Owner',
};

/**
 * Role descriptions for UI tooltips
 */
export const ROLE_DESCRIPTIONS: Record<string, string> = {
  viewer: 'Basic user with ability to use devices and edit own profile',
  manager: 'Can manage team members with lower roles',
  admin: 'Full administrative access except ownership transfers',
  owner: 'Complete control over the organization',
};
