import { S3Client, HeadBucketCommand } from '@aws-sdk/client-s3';
import { BUCKET_NAME, MAX_FILE_SIZE, ALLOWED_TYPES } from '@/lib/constant';

// S3 configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Types
export interface S3UploadConfig {
  bucketName: string;
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  maxFileSize: number;
  allowedTypes: string[];
}

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  key?: string;
}

export interface FileMetadata {
  originalName: string;
  size: number;
  type: string;
  uploadType: string;
  userId: string;
  orgId?: string;
}

// Validate file for server-side upload (Node.js File from FormData)
export function validateServerFile(
  file: File,
  maxSizeBytes: number = MAX_FILE_SIZE,
): { valid: boolean; error?: string } {
  console.log('🔍 [VALIDATE-SERVER-FILE] Validating file', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    maxSizeBytes,
    allowedTypes: ALLOWED_TYPES,
  });

  if (!ALLOWED_TYPES.includes(file.type)) {
    console.warn('❌ [VALIDATE-SERVER-FILE] Invalid file type', {
      fileName: file.name,
      fileType: file.type,
      allowedTypes: ALLOWED_TYPES,
    });
    return {
      valid: false,
      error: `Invalid file type. Allowed types: ${ALLOWED_TYPES.join(', ')}`,
    };
  }

  if (file.size > maxSizeBytes) {
    console.warn('❌ [VALIDATE-SERVER-FILE] File too large', {
      fileName: file.name,
      fileSize: file.size,
      maxSizeBytes,
      maxSizeMB: Math.round(maxSizeBytes / 1024 / 1024),
    });
    return {
      valid: false,
      error: `File too large. Maximum size: ${Math.round(maxSizeBytes / 1024 / 1024)}MB`,
    };
  }

  console.log('✅ [VALIDATE-SERVER-FILE] File validation passed', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
  });

  return { valid: true };
}

// Generate secure, organized file key with proper folder structure
export function generateSecureFileKey(
  folderName: string,
  orgId: string,
  providerName: string,
  uploadType: string,
  themeExtension: string = '',
): string {
  const sanitizedProviderName = providerName.replaceAll(/[^a-zA-Z0-9.-]/g, ''); // Remove special chars
  const timestamp = Date.now();
  const random = Math.random().toString(36).slice(2, 8);

  const fileKey = `partner-dashboard/${orgId}/${folderName}/${sanitizedProviderName}${themeExtension ? `-${themeExtension}` : ''}-${uploadType}-${timestamp}-${random}`;

  return fileKey;
}

// Validate S3 configuration
export function validateS3Config(): { valid: boolean; error?: string } {
  console.log('🔧 [VALIDATE-S3-CONFIG] Validating S3 configuration');

  const requiredEnvVars = [
    'AWS_S3_BUCKET_NAME',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.error('❌ [VALIDATE-S3-CONFIG] Missing environment variable', {
        missingVar: envVar,
      });
      return {
        valid: false,
        error: `Missing required environment variable: ${envVar}`,
      };
    }
  }

  // Validate bucket name format
  const bucketName = process.env.AWS_S3_BUCKET_NAME!;
  console.log('📦 [VALIDATE-S3-CONFIG] Validating bucket name', {
    bucketName,
    bucketLength: bucketName.length,
  });

  if (bucketName.includes('/') || bucketName.includes('\\')) {
    console.error('❌ [VALIDATE-S3-CONFIG] Invalid bucket name - contains path separators', {
      bucketName,
    });
    return {
      valid: false,
      error: `Invalid bucket name: '${bucketName}'. S3 bucket names cannot contain path separators (/ or \\)`,
    };
  }

  // Validate bucket name length and format
  if (bucketName.length < 3 || bucketName.length > 63) {
    console.error('❌ [VALIDATE-S3-CONFIG] Invalid bucket name length', {
      bucketName,
      length: bucketName.length,
      minLength: 3,
      maxLength: 63,
    });
    return {
      valid: false,
      error: `Invalid bucket name length: '${bucketName}'. Bucket names must be 3-63 characters long`,
    };
  }

  // Validate bucket name characters
  if (!/^[a-z0-9][a-z0-9.-]*[a-z0-9]$/.test(bucketName)) {
    console.error('❌ [VALIDATE-S3-CONFIG] Invalid bucket name format', {
      bucketName,
      pattern: /^[a-z0-9][a-z0-9.-]*[a-z0-9]$/,
    });
    return {
      valid: false,
      error: `Invalid bucket name format: '${bucketName}'. Bucket names must contain only lowercase letters, numbers, dots, and hyphens, and cannot start or end with a dot or hyphen`,
    };
  }

  console.log('✅ [VALIDATE-S3-CONFIG] S3 configuration is valid', {
    bucketName,
    region: process.env.AWS_REGION,
    hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
    hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
  });

  return { valid: true };
}

// Test S3 connectivity
export async function testS3Connection(): Promise<{ success: boolean; error?: string }> {
  console.log('🔌 [TEST-S3-CONNECTION] Testing S3 connectivity', {
    bucketName: BUCKET_NAME,
    region: process.env.AWS_REGION,
  });

  try {
    const command = new HeadBucketCommand({ Bucket: BUCKET_NAME });
    await s3Client.send(command);

    console.log('✅ [TEST-S3-CONNECTION] S3 connection successful', {
      bucketName: BUCKET_NAME,
    });

    return { success: true };
  } catch (error: any) {
    console.error('❌ [TEST-S3-CONNECTION] S3 connection test failed:', {
      bucketName: BUCKET_NAME,
      error: error.message,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode,
    });
    return {
      success: false,
      error: error.message || 'Failed to connect to S3',
    };
  }
}

// Get S3 client for use in other modules
export function getS3Client(): S3Client {
  console.log('🔧 [GET-S3-CLIENT] Returning S3 client instance', {
    region: process.env.AWS_REGION,
    hasCredentials: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
  });
  return s3Client;
}

// Get bucket name
export function getBucketName(): string {
  console.log('📦 [GET-BUCKET-NAME] Returning bucket name', {
    bucketName: BUCKET_NAME,
  });
  return BUCKET_NAME;
}

// Get allowed file types
export function getAllowedTypes(): string[] {
  console.log('📋 [GET-ALLOWED-TYPES] Returning allowed file types', {
    allowedTypes: ALLOWED_TYPES,
    count: ALLOWED_TYPES.length,
  });
  return [...ALLOWED_TYPES];
}

// Get max file size
export function getMaxFileSize(): number {
  console.log('📏 [GET-MAX-FILE-SIZE] Returning max file size', {
    maxFileSize: MAX_FILE_SIZE,
    maxFileSizeMB: Math.round(MAX_FILE_SIZE / 1024 / 1024),
  });
  return MAX_FILE_SIZE;
}
