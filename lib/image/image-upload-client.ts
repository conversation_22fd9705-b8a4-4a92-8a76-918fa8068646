import { MAX_FILE_SIZE, ALLOWED_TYPES } from '@/lib/constant';

// Client-side utilities for browser environment only

// Validate file for client-side (browser File object)
export function validateClientFile(
  file: File,
  maxSizeBytes: number = MAX_FILE_SIZE,
): { valid: boolean; error?: string } {

  if (!ALLOWED_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: `Invalid file type. Allowed types: ${ALLOWED_TYPES.join(', ')}`,
    };
  }

  if (file.size > maxSizeBytes) {
    console.warn('❌ [VALIDATE-CLIENT-FILE] File too large', {
      fileName: file.name,
      fileSize: file.size,
      maxSizeBytes,
    });
    return {
      valid: false,
      error: `File too large. Maximum size: ${Math.round(maxSizeBytes / 1024 / 1024)}MB`,
    };
  }


  return { valid: true };
}

// Helper to get file preview URL
export function getFilePreviewUrl(file: File): string {
  const url = URL.createObjectURL(file);
  return url;
}

// Helper to cleanup preview URL
export function cleanupPreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

// Image optimization function
export async function resizeImage(
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8,
): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.addEventListener('load', () => {
      // Calculate new dimensions
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const optimizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(optimizedFile);
          } else {
            resolve(file); // Fallback to original if compression fails
          }
        },
        file.type,
        quality,
      );
    });

    img.addEventListener('error', () => {
      resolve(file); // Fallback to original if image load fails
    });

    img.src = getFilePreviewUrl(file);
  });
}

// Get allowed file types for client validation
export function getAllowedTypesClient(): string[] {
  return [...ALLOWED_TYPES];
}

// Get max file size for client validation
export function getMaxFileSizeClient(): number {
  return MAX_FILE_SIZE;
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check if file is an image
export function isImageFile(file: File): boolean {
  return ALLOWED_TYPES.includes(file.type);
}

// Generate a preview thumbnail
export async function generateThumbnail(file: File, size: number = 150): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.addEventListener('load', () => {
      // Set canvas size
      canvas.width = size;
      canvas.height = size;

      // Calculate crop dimensions to maintain aspect ratio
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const scale = size / minDimension;

      const scaledWidth = width * scale;
      const scaledHeight = height * scale;

      // Center the image
      const x = (size - scaledWidth) / 2;
      const y = (size - scaledHeight) / 2;

      // Draw image
      ctx?.drawImage(img, x, y, scaledWidth, scaledHeight);

      // Convert to data URL
      resolve(canvas.toDataURL(file.type, 0.8));
    });

    img.addEventListener('error', () => {
      reject(new Error('Failed to load image for thumbnail generation'));
    });

    img.src = getFilePreviewUrl(file);
  });
}
