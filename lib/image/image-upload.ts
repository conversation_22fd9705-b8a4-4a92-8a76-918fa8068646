import { PutObjectCommand } from '@aws-sdk/client-s3';
import {
  generateSecureFileKey,
  validateServerFile,
  validateS3Config,
  getS3Client,
  getBucketName,
  type FileMetadata,
} from '@/lib/image/image-upload-server';

// ===================================================================
// CONSTANTS AND INTERFACES
// ===================================================================

// Upload configuration interface
export interface ImageUploadConfig {
  uploadType: string; // e.g., 'provider-logo', 'user-avatar', 'product-image', 'provider-dark-logo', 'provider-light-logo'
  orgId: string; // Organization/tenant ID
  userId: string; // User performing the upload
  providerName: string; // Provider name alias as device name or any other name related to location it is being used
  requestId?: string; // Optional request tracking ID
  maxFileSize?: number; // Override default max file size
  allowedTypes?: string[]; // Override default allowed types
  folderName: string; // e.g., 'providers', 'users', 'products'
}

// Upload result interface
export interface ImageUploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    type: string;
    uploadedAt: string;
  };
}

// Batch upload result interface
export interface BatchUploadResult {
  success: boolean;
  results: Array<{
    fieldName: string;
    result: ImageUploadResult;
  }>;
  errors?: string[];
}

// ===================================================================
// UTILITY FUNCTIONS
// ===================================================================

// Sanitize filename for S3 metadata (AWS has strict character restrictions)
function sanitizeFilenameForMetadata(filename: string): string {
  const sanitized = filename
    .replaceAll(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscores
    .replaceAll(/_+/g, '_') // Replace multiple underscores with single
    .replaceAll(/^_|_$/g, '') // Remove leading/trailing underscores
    .slice(0, 100); // Limit length for metadata

  if (sanitized !== filename) {
    // Filename sanitized
  }

  return sanitized;
}

// ===================================================================
// CORE UPLOAD FUNCTIONS
// ===================================================================

/**
 * Upload a single image to S3
 *
 * @param file - The file to upload (from FormData)
 * @param config - Upload configuration
 * @returns Promise<ImageUploadResult>
 *
 * @example
 * ```typescript
 * const result = await uploadImageToS3(logoFile, {
 *   uploadType: 'provider-logo',
 *   orgId: 'org-123',
 *   userId: 'user-456',
 *   requestId: 'req-789'
 * });
 *
 * if (result.success) {
 *   // Image uploaded successfully
 * } else {
 *   // Upload failed
 * }
 * ```
 */
export async function uploadImageToS3(
  file: File,

  config: ImageUploadConfig,
): Promise<ImageUploadResult> {
  try {
    // 1. Validate S3 configuration
    const s3ConfigCheck = validateS3Config();
    if (!s3ConfigCheck.valid) {
      return {
        success: false,
        error: `S3 configuration error: ${s3ConfigCheck.error}`,
      };
    }

    // 2. Validate file
    const validation = validateServerFile(file, config.maxFileSize);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    const toSend = {
      folderName: config.folderName,
      orgId: config.orgId,
      providerName: config.providerName,
      themeExtension: config.uploadType,
      uploadType: file.name.split('.').pop() || '',
    };
    // 3. Generate secure file key
    const fileKey = generateSecureFileKey(
      toSend.folderName,
      toSend.orgId,
      toSend.providerName,
      toSend.themeExtension,
      toSend.uploadType,
    );

    // 4. Prepare metadata
    const metadata: FileMetadata = {
      originalName: file.name,
      size: file.size,
      type: file.type,
      uploadType: config.uploadType,
      userId: config.userId,
      orgId: config.orgId,
    };

    // Preparing metadata

    // 5. Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // 6. Prepare S3 upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
      Body: fileBuffer,
      ContentType: file.type,
      CacheControl: 'max-age=31536000', // 1 year cache
      Metadata: {
        'original-name': sanitizeFilenameForMetadata(metadata.originalName),
        'upload-type': metadata.uploadType,
        'user-id': metadata.userId,
        'org-id': metadata.orgId || '',
        'uploaded-at': new Date().toISOString(),
        ...(config.requestId && { 'request-id': config.requestId }),
      },
    });

    // 7. Upload to S3
    // Starting S3 upload

    const s3Client = getS3Client();
    await s3Client.send(uploadCommand);

    // S3 upload completed successfully

    // 8. Generate file URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const bucketName = getBucketName();

    const fileUrl = `https://${bucketName}.s3.${region}.amazonaws.com/${fileKey}?t=${Date.now()}`;

    return {
      success: true,
      url: fileUrl,
      key: fileKey,
      metadata: {
        originalName: file.name,
        size: file.size,
        type: file.type,
        uploadedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    // Image upload failed
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

/**
 * Upload multiple images to S3 in batch
 *
 * @param files - Array of files with field names
 * @param config - Upload configuration (shared across all files)
 * @returns Promise<BatchUploadResult>
 *
 * @example
 * ```typescript
 * const files = [
 *   { fieldName: 'logo', file: logoFile },
 *   { fieldName: 'logoDark', file: logoDarkFile },
 * ];
 *
 * const result = await uploadMultipleImagesToS3(files, {
 *   uploadType: 'provider-assets',
 *   orgId: 'org-123',
 *   userId: 'user-456'
 * });
 *
 * result.results.forEach(({ fieldName, result }) => {
 *   if (result.success) {
 *     // Field uploaded successfully
 *   }
 * });
 * ```
 */
export async function uploadMultipleImagesToS3(
  files: Array<{ fieldName: string; file: File }>,
  folderName: string,
  config: ImageUploadConfig,
): Promise<BatchUploadResult> {
  const results: Array<{
    fieldName: string;
    result: ImageUploadResult;
  }> = [];

  const errors: string[] = [];

  // Upload each file
  for (const { fieldName, file } of files) {
    try {
      const result = await uploadImageToS3(file, {
        ...config,
        uploadType: `${config.uploadType}-${fieldName}`, // e.g., 'provider-logo', 'provider-logoDark'
      });

      results.push({ fieldName, result });

      if (!result.success && result.error) {
        errors.push(`${fieldName}: ${result.error}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      results.push({
        fieldName,
        result: { success: false, error: errorMessage },
      });
      errors.push(`${fieldName}: ${errorMessage}`);
    }
  }

  const success = results.every(({ result }) => result.success);

  return {
    success,
    results,
    ...(errors.length > 0 && { errors }),
  };
}

// ===================================================================
// HELPER FUNCTIONS
// ===================================================================

/**
 * Helper to extract files from FormData
 *
 * @param formData - FormData object
 * @param fileFields - Array of field names to extract
 * @returns Array of files with field names
 *
 * @example
 * ```typescript
 * const files = extractFilesFromFormData(formData, ['logo', 'logoDark']);
 * // Returns: [{ fieldName: 'logo', file: File }, { fieldName: 'logoDark', file: File }]
 * ```
 */
export function extractFilesFromFormData(
  formData: FormData,
  fileFields: string[],
): Array<{ fieldName: string; file: File }> {
  const files: Array<{ fieldName: string; file: File }> = [];

  for (const fieldName of fileFields) {
    const file = formData.get(fieldName) as File | null;
    if (file && file.size > 0) {
      files.push({ fieldName, file });
    }
  }

  return files;
}

/**
 * Helper to create upload configuration
 *
 * @param baseConfig - Base configuration
 * @param overrides - Configuration overrides
 * @returns Complete upload configuration
 */
export function createUploadConfig(
  baseConfig: Omit<ImageUploadConfig, 'uploadType'>,
  uploadType: string,
  overrides?: Partial<ImageUploadConfig>,
): ImageUploadConfig {
  return {
    ...baseConfig,
    uploadType,
    ...overrides,
  };
}

/**
 * Helper to log upload operations
 */
export function logUploadOperation(
  operation: 'start' | 'success' | 'error',
  config: ImageUploadConfig,
  details?: {
    fileName?: string;
    fileSize?: number;
    duration?: number;
    error?: string;
    url?: string;
  },
): void {
  const logData = {
    operation,
    uploadType: config.uploadType,
    orgId: config.orgId,
    userId: config.userId,
    requestId: config.requestId,
    ...details,
  };

  switch (operation) {
    case 'start': {
      // Starting upload
      break;
    }
    case 'success': {
      // Upload successful
      break;
    }
    case 'error': {
      // Upload failed
      break;
    }
  }
}
