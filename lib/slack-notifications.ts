// No longer needs Next imports as they weren't used

interface SlackMessage {
  text?: string;
  attachments?: Array<{
    color?: string;
    title?: string;
    title_link?: string;
    text?: string;
    fields?: Array<{
      title: string;
      value: string;
      short?: boolean;
    }>;
    footer?: string;
    ts?: number;
    author_name?: string;
    author_link?: string;
    author_icon?: string;
  }>;
}

interface DeviceAccessRequestData {
  userName: string;
  userEmail: string;
  deviceName: string;
  deviceId: string;
  organization: string;
  justification: string;
  requestType: 'access' | 'add' | 'edit';
  status?: 'pending' | 'approved' | 'denied';
  adminNotes?: string;
}

interface DeviceAddEditRequestData {
  deviceName: string;
  deviceType: string;
  provider: string;
  organization: string;
  requestType: 'add' | 'edit';
  status?: 'pending' | 'approved' | 'denied';
  adminNotes?: string;
  changes?: string;
}

class SlackNotificationService {
  private webhookUrl: string;

  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL || '';

    if (!this.webhookUrl) {
      console.warn('SLACK_WEBHOOK_URL environment variable is not set');
    }
  }

  async sendNotification(message: SlackMessage): Promise<boolean> {
    if (!this.webhookUrl) {
      console.log('Slack webhook not configured - skipping notification');
      return false;
    }

    try {
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        throw new Error(`Slack API error: ${response.status} ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Failed to send Slack notification:', error);
      return false;
    }
  }

  async sendDeviceAccessRequest(data: DeviceAccessRequestData): Promise<boolean> {
    const color = this.c[data.status as keyof typeof this.c] || 'warning';

    const statusEmoji = this.d[data.status as keyof typeof this.d] || '⏳';

    const message: SlackMessage = {
      text: `${statusEmoji} :key: Device Access ${data.status ? data.status.charAt(0).toUpperCase() + data.status.slice(1) : 'Request'}: ${data.deviceName}`,
      attachments: [
        {
          color,
          title: `:key: Device Access ${data.status ? data.status.charAt(0).toUpperCase() + data.status.slice(1) : 'Request'}`,
          title_link: `${process.env.NEXT_PUBLIC_APP_URL}/admin?tab=device-access-requests`,
          author_name: 'qbraid Partner Dashboard',
          author_link: process.env.NEXT_PUBLIC_APP_URL,
          author_icon: `${process.env.NEXT_PUBLIC_APP_URL}/favicon.ico`,
          fields: [
            {
              title: ':bust_in_silhouette: User',
              value: `*${data.userName}*\n<mailto:${data.userEmail}|${data.userEmail}>`,
              short: true,
            },
            {
              title: ':computer: Device',
              value: `*${data.deviceName}*`,
              short: true,
            },
            {
              title: ':fingerprint: Device ID',
              value: `\`${data.deviceId}\``,
              short: true,
            },
            {
              title: ':building_construction: Organization',
              value: `\`${data.organization}\``,
              short: true,
            },
            {
              title: ':clipboard: Justification',
              value: `_${data.justification}_`,
              short: false,
            },
          ],
          footer: 'qBraidPartner Dashboard',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    if (data.adminNotes) {
      message.attachments![0].fields!.push({
        title: ':speech_balloon: Admin Notes',
        value: `_${data.adminNotes}_`,
        short: false,
      });
    }

    return this.sendNotification(message);
  }
  d = {
    denied: '❌',
    approved: '✅',
    pending: '⏳',
  };
  c = {
    good: '✅',
    danger: '❌',
    warning: '⏳',
  };
  i = {
    access: ':key:',
    add: '➕',
    edit: '✏️',
  };
  async sendDeviceAddEditRequest(data: DeviceAddEditRequestData): Promise<boolean> {
    const color = this.c[data.status as keyof typeof this.c] || 'warning';

    const statusEmoji = this.d[data.status as keyof typeof this.d] || '⏳';

    const actionText = data.requestType === 'add' ? 'Addition' : 'Edit';
    const actionEmoji = data.requestType === 'add' ? '➕' : '✏️';

    const message: SlackMessage = {
      text: `${statusEmoji} ${actionEmoji} Device ${actionText} ${data.status ? data.status.charAt(0).toUpperCase() + data.status.slice(1) : 'Request'}: ${data.deviceName}`,
      attachments: [
        {
          color,
          title: `${actionEmoji} Device ${actionText} ${data.status ? data.status.charAt(0).toUpperCase() + data.status.slice(1) : 'Request'}`,
          title_link: `${process.env.NEXT_PUBLIC_APP_URL}/device-management`,
          author_name: 'qbraid Partner Dashboard',
          author_link: process.env.NEXT_PUBLIC_APP_URL,
          author_icon: `${process.env.NEXT_PUBLIC_APP_URL}/favicon.ico`,
          fields: [
            {
              title: ':computer: Device Name',
              value: `*${data.deviceName}*`,
              short: true,
            },
            {
              title: ':microchip: Device Type',
              value: `\`${data.deviceType}\``,
              short: true,
            },
            {
              title: ':cloud: Provider',
              value: data.provider || '*Not specified*',
              short: true,
            },
            {
              title: ':building_construction: Organization',
              value: `\`${data.organization}\``,
              short: true,
            },
          ],
          footer: 'qbraid Partner Dashboard',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    if (data.changes) {
      message.attachments![0].fields!.push({
        title: ':memo: Changes',
        value: `\`\`\`${data.changes}\`\`\``,
        short: false,
      });
    }

    if (data.adminNotes) {
      message.attachments![0].fields!.push({
        title: ':speech_balloon: Admin Notes',
        value: `_${data.adminNotes}_`,
        short: false,
      });
    }

    return this.sendNotification(message);
  }

  async sendDeviceApprovalNotification(
    requestType: 'access' | 'add' | 'edit',
    deviceName: string,
    userName: string,
    status: 'approved' | 'denied',
    adminName: string,
    adminNotes?: string,
  ): Promise<boolean> {
    const color = this.c[status as keyof typeof this.c] || 'warning';
    const statusEmoji = this.d[status as keyof typeof this.d] || '⏳';

    const requestIcon = this.i[requestType as keyof typeof this.i] || '⏳';

    const message: SlackMessage = {
      text: `${statusEmoji} ${requestIcon} Device ${requestType.charAt(0).toUpperCase() + requestType.slice(1)} ${status}: ${deviceName}`,
      attachments: [
        {
          color,
          title: `${requestIcon} Device ${requestType.charAt(0).toUpperCase() + requestType.slice(1)} ${status}`,
          author_name: 'qbraid Partner Dashboard',
          author_link: process.env.NEXT_PUBLIC_APP_URL,
          author_icon: `${process.env.NEXT_PUBLIC_APP_URL}/favicon.ico`,
          fields: [
            {
              title: ':computer: Device',
              value: `*${deviceName}*`,
              short: true,
            },
            {
              title: ':bust_in_silhouette: User',
              value: `*${userName}*`,
              short: true,
            },
            {
              title: ':shield: Reviewed by',
              value: `*${adminName}*`,
              short: true,
            },
            {
              title: ':white_check_mark: Status',
              value:
                status === 'approved' ? ':large_green_circle: Approved' : ':red_circle: Denied',
              short: true,
            },
          ],
          footer: 'qbraid Partner Dashboard',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    if (adminNotes) {
      message.attachments![0].fields!.push({
        title: ':speech_balloon: Admin Notes',
        value: `_${adminNotes}_`,
        short: false,
      });
    }

    return this.sendNotification(message);
  }
}

export const slackNotificationService = new SlackNotificationService();
export { SlackNotificationService };
