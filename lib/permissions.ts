import { Permission, externalRoleToPermissions } from '@/types/auth';
import {
  ROLE_HIERARCHY,
  ALL_ROLES,
  ROLE_MANAGEMENT_PERMISSIONS,
  ROUTE_PERMISSIONS,
  ROLE_DISPLAY_NAMES,
  ROLE_DESCRIPTIONS,
} from '@/lib/constant';

// Re-export constants for backward compatibility


// ============================================================================
// ROUTE PROTECTION
// ============================================================================


/**
 * Maps application routes to required permissions (converted from string constants to Permission enum)
 */
export const routePermissions: Record<string, Permission> = Object.fromEntries(
  Object.entries(ROUTE_PERMISSIONS).map(([route, permission]) => [route, permission as Permission]),
);

// ============================================================================
// PERMISSION CHECK FUNCTIONS
// ============================================================================

/**
 * Check if a user can change another user's role
 *
 * @param currentUserRole - The role of the user attempting the change
 * @param targetUserRole - The current role of the user being changed
 * @param newRole - The role to assign to the target user
 * @returns Object with canChange boolean and optional reason for denial
 */
export function canChangeUserRole(
  currentUserRole: string,
  targetUserRole: string,
  newRole: string,
): { canChange: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();
  const normalizedNewRole = newRole.toLowerCase();

  // Get hierarchy levels
  const currentLevel = ROLE_HIERARCHY[normalizedCurrentRole] || 0;
  const targetLevel = ROLE_HIERARCHY[normalizedTargetRole] || 0;
  const newLevel = ROLE_HIERARCHY[normalizedNewRole] || 0;

  // Check management permissions
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];

  // Validation checks
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  if (!managableRoles.includes(normalizedNewRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  if (newLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You cannot promote someone to a role equal or higher than your own`,
    };
  }

  return { canChange: true };
}

/**
 * Check if a user can remove another user from the organization
 *
 * @param currentUserRole - The role of the user attempting removal
 * @param targetUserRole - The role of the user to be removed
 * @returns Object with canRemove boolean and optional reason for denial
 */
export function canRemoveUser(
  currentUserRole: string,
  targetUserRole: string,
): { canRemove: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();

  // Special rule: Owners cannot be removed
  if (normalizedTargetRole === 'owner') {
    return {
      canRemove: false,
      reason: 'Organization owners cannot be removed',
    };
  }

  // Check management permissions
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canRemove: false,
      reason: `You don't have permission to remove users with ${targetUserRole} role`,
    };
  }

  return { canRemove: true };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get list of roles that a user can assign to others
 *
 * @param currentUserRole - The role of the user
 * @returns Array of role names that can be assigned
 */
export function getAssignableRoles(currentUserRole: string): string[] {
  const normalizedRole = currentUserRole.toLowerCase();

  // Owners can assign any role
  if (normalizedRole === 'owner') {
    return ALL_ROLES;
  }

  return ROLE_MANAGEMENT_PERMISSIONS[normalizedRole] || [];
}

/**
 * Convert external role names to internal permission sets
 *
 * @param externalRoles - Single role or array of role names from external API
 * @returns Array of unique permissions
 */
export function mapRolesToPermissions(externalRoles: string | string[]): Permission[] {
  if (typeof externalRoles === 'string') {
    return externalRoleToPermissions[externalRoles?.toLowerCase()] || [];
  }

  // Handle array of roles - flatten and deduplicate permissions
  const allPermissions = externalRoles.flatMap(
    (role) => externalRoleToPermissions[role?.toLowerCase()] || [],
  );

  // Remove duplicates manually for better compatibility
  const uniquePermissions: Permission[] = [];
  for (const permission of allPermissions) {
    if (!uniquePermissions.includes(permission)) {
      uniquePermissions.push(permission);
    }
  }
  return uniquePermissions;
}

/**
 * Check if user has a specific permission
 *
 * @param userPermissions - User's permission array
 * @param requiredPermission - Permission to check for
 * @returns true if user has permission or admin access
 */
export function hasPermission(
  userPermissions: Permission[],
  requiredPermission: Permission,
): boolean {
  return userPermissions.includes(requiredPermission);
}

/**
 * Check if user has any of the required permissions
 *
 * @param userPermissions - User's permission array
 * @param requiredPermissions - Array of permissions (user needs at least one)
 * @returns true if user has any of the required permissions
 */
export function hasAnyPermission(
  userPermissions: Permission[],
  requiredPermissions: Permission[],
): boolean {
  return requiredPermissions.some((permission) => hasPermission(userPermissions, permission));
}

/**
 * Get required permission for a specific route
 *
 * @param pathname - The route path to check
 * @returns Required permission or null if route is unprotected
 */
export function getRoutePermission(pathname: string): Permission | null {
  // Exact match
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Prefix match (for nested routes)
  for (const [route, permission] of Object.entries(routePermissions)) {
    if (pathname.startsWith(route)) {
      return permission;
    }
  }

  return null;
}

// ============================================================================
// HELPER FUNCTIONS FOR UI
// ============================================================================

/**
 * Get human-readable role display name
 */
export function getRoleDisplayName(role: string): string {
  return ROLE_DISPLAY_NAMES[role.toLowerCase()] || role;
}

/**
 * Get role description for UI tooltips
 */
export function getRoleDescription(role: string): string {
  return ROLE_DESCRIPTIONS[role.toLowerCase()] || 'Custom role';
}

export {ROLE_HIERARCHY, ALL_ROLES, ROLE_MANAGEMENT_PERMISSIONS} from '@/lib/constant';
export {externalRoleToPermissions} from '@/types/auth';