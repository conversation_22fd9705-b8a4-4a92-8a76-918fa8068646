/**
 * ===================================================================
 * AUTH MIDDLEWARE - Server-side API Route Authentication Helpers
 * ===================================================================
 *
 * This file provides auth middleware functions for API routes.
 *
 * 📝 Architecture Overview:
 *
 * 1. `middleware.ts` (Next.js Edge Middleware)
 *    - Runs on EVERY request at the EDGE (before pages/API routes)
 *    - Purpose: URL redirects, basic auth checks, security headers
 *    - Limitations: No full Node.js APIs, no Redis/DB access
 *
 * 2. `lib/auth-middleware.ts` (This file - API Route Helpers)
 *    - Runs INSIDE API routes on the SERVER
 *    - Purpose: Fine-grained permissions, database queries, complex auth
 *    - Full Access: Redis, external APIs, all Node.js features
 *
 * ===================================================================
 */

import { NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { SessionPayload } from '@/types/auth';
import { Permission } from '@/types/auth';
import { canChangeUserRole, canRemoveUser } from '@/lib/permissions';
import { getCachedOrgRoles, UserOrgRoles } from '@/lib/external-roles';

// ===================================================================
// TYPES & INTERFACES
// ===================================================================

interface AuthResult<T = {}> {
  session: SessionPayload | null;
  error?: NextResponse;
  data?: T;
}

interface TeamAuthData {
  currentUserRole: string;
}

type TeamAction = 'invite' | 'remove' | 'update_role';
type DeviceAction = 'view' | 'manage';
type ProviderAction = 'view' | 'manage';

// ===================================================================
// CORE AUTH FUNCTIONS
// ===================================================================

/**
 * Basic session authentication check
 * ✅ RECOMMENDED: Use this for basic authentication without permission checks
 */
export async function requireAuth(): Promise<AuthResult> {
  const session = await getSession();

  if (!session?.email) {
    return {
      session: null,
      error: createAuthError('auth_required', {
        hasSession: !!session,
        sessionKeys: session ? Object.keys(session) : [],
      }),
    };
  }

  return { session: session as SessionPayload };
}

/**
 * Get user's cached organization roles with error handling
 */
async function getUserOrgRoles(userEmail: string): Promise<UserOrgRoles | null> {
  try {
    return await getCachedOrgRoles(userEmail);
  } catch (error) {
    console.error('❌ [AUTH-MIDDLEWARE] Failed to get cached org roles:', error);
    return null;
  }
}

/**
 * Validate user's membership and permissions in an organization
 */
async function validateOrgMembership(
  userEmail: string,
  orgId: string,
  requiredPermissions: Permission[] = [],
): Promise<{
  isValid: boolean;
  userRole?: string;
  permissions?: Permission[];
  error?: NextResponse;
}> {
  const cachedOrgRoles = await getUserOrgRoles(userEmail);

  if (!cachedOrgRoles?.[orgId]) {
    return {
      isValid: false,
      error: createAuthError('org_not_member', {
        orgId,
        availableOrgs: cachedOrgRoles ? Object.keys(cachedOrgRoles) : [],
      }),
    };
  }

  const { role: userRole, permissions } = cachedOrgRoles[orgId];

  // Check required permissions
  const missingPermissions = requiredPermissions.filter((perm) => !permissions?.includes(perm));

  if (missingPermissions.length > 0) {
    return {
      isValid: false,
      userRole,
      permissions,
      error: createAuthError('insufficient_permissions', {
        orgId,
        requiredPermissions,
        missingPermissions,
        userPermissions: permissions,
      }),
    };
  }

  return {
    isValid: true,
    userRole,
    permissions,
  };
}

// ===================================================================
// SPECIALIZED AUTH FUNCTIONS
// ===================================================================

/**
 * Validate team management permissions using cached org roles
 * ✅ RECOMMENDED: Use this for all team operations (invite/remove/role change)
 */
export async function requireTeamManagementPermissions(
  orgId: string,
  action: TeamAction,
  targetUserRole?: string,
  newRole?: string,
): Promise<AuthResult<TeamAuthData>> {
  // Basic auth check
  const { session, error: authError } = await requireAuth();
  if (authError) return { session, error: authError };

  // Validate org membership and ManageTeam permission
  const {
    isValid,
    userRole,
    error: orgError,
  } = await validateOrgMembership(session!.email, orgId, [Permission.ManageTeam]);

  if (!isValid) {
    return { session, error: orgError };
  }

  // Additional validation for specific actions
  if (action === 'update_role' && targetUserRole && newRole) {
    const roleCheck = canChangeUserRole(userRole!, targetUserRole, newRole);
    if (!roleCheck.canChange) {
      return {
        session,
        data: { currentUserRole: userRole! },
        error: createAuthError('role_forbidden', {
          action: 'role_change',
          reason: roleCheck.reason,
          currentUserRole: userRole,
          targetUserRole,
          requestedRole: newRole,
        }),
      };
    }
  }

  if (action === 'remove' && targetUserRole) {
    const removeCheck = canRemoveUser(userRole!, targetUserRole);
    if (!removeCheck.canRemove) {
      return {
        session,
        data: { currentUserRole: userRole! },
        error: createAuthError('role_forbidden', {
          action: 'user_removal',
          reason: removeCheck.reason,
          currentUserRole: userRole,
          targetUserRole,
        }),
      };
    }
  }

  return {
    session,
    data: { currentUserRole: userRole! },
  };
}

/**
 * Validate device management permissions
 * ✅ RECOMMENDED: Use this for all device operations (view/manage)
 */
export async function requireDevicePermission(
  action: DeviceAction,
  orgId: string,
): Promise<AuthResult<{ role: string }>> {
  const requiredPermission = action === 'view' ? Permission.ViewDevices : Permission.ManageDevices;

  // Basic auth check
  const { session, error: authError } = await requireAuth();
  if (authError) return { session, error: authError };

  // Organization-specific device permissions
  const {
    isValid,
    error: orgError,
    userRole,
  } = await validateOrgMembership(session!.email, orgId, [requiredPermission]);

  if (!isValid) {
    return { session, error: orgError };
  }

  return { session, data: { role: userRole! } };
}

/**
 * Validate provider management permissions
 * ✅ RECOMMENDED: Use this for all provider operations (view/manage)
 */
export async function requireProviderPermissions(
  action: ProviderAction,
  orgId: string,
): Promise<AuthResult<{ role: string }>> {
  const requiredPermission =
    action === 'view' ? Permission.ViewProviders : Permission.ManageProviders;

  // Basic auth check
  const { session, error: authError } = await requireAuth();
  if (authError) return { session, error: authError };

  // Organization-specific provider permissions
  const {
    isValid,
    error: orgError,
    userRole,
  } = await validateOrgMembership(session!.email, orgId, [requiredPermission]);

  if (!isValid) {
    return { session, error: orgError };
  }

  return { session, data: { role: userRole! } };
}

// ===================================================================
// UTILITY FUNCTIONS
// ===================================================================

/**
 * Self-service check - user can only modify their own data
 */
export function isSelfService(sessionEmail: string, targetEmail: string): boolean {
  return sessionEmail.toLowerCase() === targetEmail.toLowerCase();
}

/**
 * Get all permissions across all organizations for a user
 * Useful for global permission checks or admin features
 */
export async function getAllUserPermissions(
  userEmail: string,
  orgId: string,
): Promise<Permission[]> {
  const cachedOrgRoles = await getUserOrgRoles(userEmail);
  if (!cachedOrgRoles) return [];

  const allPermissions = cachedOrgRoles[orgId]?.permissions;

  // Remove duplicates
  return [...new Set(allPermissions)];
}

/**
 * Check if user has admin access in any organization
 */
export async function hasGlobalAdminAccess(userEmail: string): Promise<boolean> {
  //todo: check if user is qbraid admin  using /user/role endpoint
  return false;
}

/**
 * Enhanced error responses with detailed info
 */
export function createAuthError(
  type: 'auth_required' | 'insufficient_permissions' | 'org_not_member' | 'role_forbidden',
  details: Record<string, any> = {},
): NextResponse {
  const errorMap = {
    auth_required: {
      status: 401,
      error: 'Authentication required',
      message: 'Valid session required to access this resource',
      code: 'AUTH_REQUIRED',
    },
    insufficient_permissions: {
      status: 403,
      error: 'Insufficient permissions',
      message: 'You do not have permission to perform this action',
      code: 'INSUFFICIENT_PERMISSIONS',
    },
    org_not_member: {
      status: 403,
      error: 'Not organization member',
      message: 'You are not a member of this organization',
      code: 'NOT_ORG_MEMBER',
    },
    role_forbidden: {
      status: 403,
      error: 'Role action forbidden',
      message: 'Your role does not allow this action',
      code: 'ROLE_FORBIDDEN',
    },
  };

  const errorInfo = errorMap[type];

  return NextResponse.json(
    {
      ...errorInfo,
      timestamp: new Date().toISOString(),
      ...details,
    },
    { status: errorInfo.status },
  );
}

// ===================================================================
// DEPRECATED FUNCTIONS (For backward compatibility)
// ===================================================================

/**
 * @deprecated Use requireTeamManagementPermissions, requireDevicePermission,
 * or requireProviderPermissions instead
 */
export async function requirePermissions(): Promise<AuthResult> {
  console.warn(
    '⚠️ [AUTH-MIDDLEWARE] requirePermissions() is deprecated. Use specific permission functions instead.',
  );
  return requireAuth();
}

/**
 * @deprecated Use validateOrgMembership or specific permission functions instead
 */
export async function requireOrgPermissions(): Promise<AuthResult> {
  console.warn(
    '⚠️ [AUTH-MIDDLEWARE] requireOrgPermissions() is deprecated. Use specific permission functions instead.',
  );
  return requireAuth();
}
