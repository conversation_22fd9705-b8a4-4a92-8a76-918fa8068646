import { createHash } from 'node:crypto';

// Security interfaces
export interface SecurityScanResult {
  safe: boolean;
  issues: SecurityIssue[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityIssue {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation?: string;
}

export interface FileSecurityConfig {
  maxFileSize: number;
  allowedTypes: string[];
  allowedExtensions: string[];
  scanForMalware: boolean;
  validateFileSignature: boolean;
  checkForExecutableContent: boolean;
  maxFileNameLength: number;
  sanitizeFileName: boolean;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: FileSecurityConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    'application/json',
    'text/plain',
    'text/x-python',
    'text/javascript',
    'application/javascript',
  ],
  allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'json', 'txt', 'py', 'js'],
  scanForMalware: true,
  validateFileSignature: true,
  checkForExecutableContent: true,
  maxFileNameLength: 255,
  sanitizeFileName: true,
};

// File signature (magic number) validation
const FILE_SIGNATURES: Record<string, string[]> = {
  'application/pdf': ['25 50 44 46'], // %PDF
  'application/msword': ['D0 CF 11 E0 A1 B1 1A E1'], // DOC header
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['50 4B 03 04'], // ZIP (DOCX)
  'application/vnd.ms-excel': ['D0 CF 11 E0 A1 B1 1A E1'], // XLS header
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['50 4B 03 04'], // ZIP (XLSX)
  'text/plain': [], // Text files have no signature
  'text/csv': [], // CSV files have no signature
  'application/json': [], // JSON files have no signature
  'text/x-python': [], // Python files have no signature
  'text/javascript': [], // JavaScript files have no signature
  'application/javascript': [], // JavaScript files have no signature
};

// Dangerous patterns to check for in text-based files
const DANGEROUS_PATTERNS = [
  // Command injection patterns
  /exec\s*\(/i,
  /eval\s*\(/i,
  /system\s*\(/i,
  /shell_exec\s*\(/i,
  /passthru\s*\(/i,
  /proc_open\s*\(/i,
  /popen\s*\(/i,
  
  // File system access patterns
  /fopen\s*\(/i,
  /file_get_contents\s*\(/i,
  /file_put_contents\s*\(/i,
  /unlink\s*\(/i,
  /rmdir\s*\(/i,
  /mkdir\s*\(/i,
  
  // Network access patterns
  /curl_init\s*\(/i,
  /file_get_contents\s*\(/i,
  /fsockopen\s*\(/i,
  /stream_socket_client\s*\(/i,
  
  // Dangerous PHP tags (if somehow uploaded)
  /<\?php/i,
  /<\?=/i,
  /<\s*script/i,
  /<\s*iframe/i,
  
  // SQL injection patterns
  /SELECT\s+\*/i,
  /INSERT\s+INTO/i,
  /UPDATE\s+SET/i,
  /DELETE\s+FROM/i,
  /DROP\s+TABLE/i,
  
  // Shell command patterns
  /\|\s*sh/i,
  /\|\s*bash/i,
  /\|\s*zsh/i,
  /\|\s*cmd/i,
  /;\s*rm\s+-/i,
  /;\s*del/i,
];

/**
 * Comprehensive file security scan
 */
export async function scanFileSecurity(
  file: File,
  config: FileSecurityConfig = DEFAULT_SECURITY_CONFIG,
): Promise<SecurityScanResult> {
  const issues: SecurityIssue[] = [];
  
  try {
    // 1. Basic file validation
    const basicValidation = validateBasicFileProperties(file, config);
    issues.push(...basicValidation.issues);
    
    // 2. File signature validation
    if (config.validateFileSignature) {
      const signatureValidation = await validateFileSignature(file);
      issues.push(...signatureValidation.issues);
    }
    
    // 3. Content analysis for text-based files
    if (config.checkForExecutableContent && isTextFile(file.type)) {
      const contentAnalysis = await analyzeFileContent(file);
      issues.push(...contentAnalysis.issues);
    }
    
    // 4. File name sanitization
    if (config.sanitizeFileName) {
      const nameValidation = validateFileName(file.name, config);
      issues.push(...nameValidation.issues);
    }
    
    // 5. Calculate overall risk level
    const riskLevel = calculateRiskLevel(issues);
    
    return {
      safe: issues.filter(issue => issue.severity === 'critical' || issue.severity === 'high').length === 0,
      issues,
      riskLevel,
    };
    
  } catch (error) {
    return {
      safe: false,
      issues: [{
        type: 'scan_error',
        severity: 'critical',
        description: `Security scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Reject file upload due to scan failure'
      }],
      riskLevel: 'critical'
    };
  }
}

/**
 * Validate basic file properties
 */
function validateBasicFileProperties(file: File, config: FileSecurityConfig): SecurityScanResult {
  const issues: SecurityIssue[] = [];
  
  // Check file size
  if (file.size > config.maxFileSize) {
    issues.push({
      type: 'file_size_exceeded',
      severity: 'medium',
      description: `File size (${file.size} bytes) exceeds maximum allowed size (${config.maxFileSize} bytes)`,
      recommendation: 'Compress file or choose a smaller file'
    });
  }
  
  // Check file type
  if (!config.allowedTypes.includes(file.type)) {
    issues.push({
      type: 'invalid_file_type',
      severity: 'high',
      description: `File type '${file.type}' is not allowed`,
      recommendation: `Only allowed types: ${config.allowedTypes.join(', ')}`
    });
  }
  
  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !config.allowedExtensions.includes(extension)) {
    issues.push({
      type: 'invalid_file_extension',
      severity: 'high',
      description: `File extension '.${extension || 'none'}' is not allowed`,
      recommendation: `Only allowed extensions: ${config.allowedExtensions.join(', ')}`
    });
  }
  
  // Check for empty files
  if (file.size === 0) {
    issues.push({
      type: 'empty_file',
      severity: 'medium',
      description: 'File is empty',
      recommendation: 'Upload a valid file with content'
    });
  }
  
  return {
    safe: issues.length === 0,
    issues,
    riskLevel: calculateRiskLevel(issues)
  };
}

/**
 * Validate file signature (magic number)
 */
async function validateFileSignature(file: File): Promise<SecurityScanResult> {
  const issues: SecurityIssue[] = [];
  
  try {
    // Read first few bytes to check file signature
    const buffer = Buffer.from(await file.slice(0, 32).arrayBuffer());
    const signature = [...buffer]
      .map(b => b.toString(16).padStart(2, '0').toUpperCase())
      .join(' ');
    
    const expectedSignatures = FILE_SIGNATURES[file.type] || [];
    
    if (expectedSignatures.length > 0) {
      const signatureMatch = expectedSignatures.some(expected => 
        signature.startsWith(expected.replaceAll(/\s+/g, ' '))
      );
      
      if (!signatureMatch) {
        issues.push({
          type: 'signature_mismatch',
          severity: 'high',
          description: `File signature '${signature}' does not match expected signature for type '${file.type}'`,
          recommendation: 'File may be corrupted or have incorrect extension'
        });
      }
    }
    
  } catch (error) {
    issues.push({
      type: 'signature_check_failed',
      severity: 'medium',
      description: `Failed to check file signature: ${error instanceof Error ? error.message : 'Unknown error'}`,
      recommendation: 'Proceed with caution'
    });
  }
  
  return {
    safe: issues.length === 0,
    issues,
    riskLevel: calculateRiskLevel(issues)
  };
}

/**
 * Analyze file content for dangerous patterns
 */
async function analyzeFileContent(file: File): Promise<SecurityScanResult> {
  const issues: SecurityIssue[] = [];
  
  try {
    const content = await file.text();
    
    // Check for dangerous patterns
    for (const pattern of DANGEROUS_PATTERNS) {
      const matches = content.match(pattern);
      if (matches) {
        issues.push({
          type: 'dangerous_pattern_detected',
          severity: 'high',
          description: `Potentially dangerous pattern detected: ${pattern.source}`,
          recommendation: 'Remove or sanitize the dangerous code pattern'
        });
      }
    }
    
    // Check for suspiciously large lines (potential obfuscation)
    const lines = content.split('\n');
    const maxLineLength = Math.max(...lines.map((line: string) => line.length));
    if (maxLineLength > 1000) {
      issues.push({
        type: 'suspicious_line_length',
        severity: 'medium',
        description: `File contains very long lines (max: ${maxLineLength} characters)`,
        recommendation: 'Check for obfuscated or minified code'
      });
    }
    
    // Check for high entropy (potential encryption/obfuscation)
    const entropy = calculateEntropy(content);
    if (entropy > 7) {
      issues.push({
        type: 'high_entropy_content',
        severity: 'medium',
        description: `File has high entropy (${entropy.toFixed(2)}), may contain encrypted or obfuscated content`,
        recommendation: 'Review file content for potential security risks'
      });
    }
    
  } catch (error) {
    // If we can't read as text, that's okay for binary files
    if (!isTextFile(file.type)) {
      return { safe: true, issues: [], riskLevel: 'low' };
    }
    
    issues.push({
      type: 'content_analysis_failed',
      severity: 'medium',
      description: `Failed to analyze file content: ${error instanceof Error ? error.message : 'Unknown error'}`,
      recommendation: 'Proceed with caution'
    });
  }
  
  return {
    safe: issues.length === 0,
    issues,
    riskLevel: calculateRiskLevel(issues)
  };
}

/**
 * Validate file name
 */
function validateFileName(fileName: string, config: FileSecurityConfig): SecurityScanResult {
  const issues: SecurityIssue[] = [];
  
  // Check file name length
  if (fileName.length > config.maxFileNameLength) {
    issues.push({
      type: 'filename_too_long',
      severity: 'low',
      description: `File name is too long (${fileName.length} characters, max: ${config.maxFileNameLength})`,
      recommendation: 'Shorten the file name'
    });
  }
  
  // Check for dangerous characters
  const dangerousChars = /[<>:"/\\|?*]/;
  if (dangerousChars.test(fileName)) {
    issues.push({
      type: 'dangerous_filename_chars',
      severity: 'medium',
      description: 'File name contains potentially dangerous characters',
      recommendation: 'Remove special characters from file name'
    });
  }
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\.\./, // Path traversal
    /^con$/i, // Reserved Windows names
    /^prn$/i,
    /^aux$/i,
    /^nul$/i,
    /^com[1-9]$/i,
    /^lpt[1-9]$/i,
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(fileName)) {
      issues.push({
        type: 'suspicious_filename',
        severity: 'high',
        description: `File name matches suspicious pattern: ${pattern.source}`,
        recommendation: 'Rename the file'
      });
    }
  }
  
  return {
    safe: issues.length === 0,
    issues,
    riskLevel: calculateRiskLevel(issues)
  };
}

/**
 * Sanitize file name
 */
export function sanitizeFileName(fileName: string): string {
  // Remove dangerous characters
  let sanitized = fileName.replaceAll(/[<>:"/\\|?*]/g, '_');
  
  // Remove leading/trailing dots and spaces
  sanitized = sanitized.replaceAll(/^[.]+|[.]+$/g, '').replaceAll(/^\s+|\s+$/g, '');
  
  // Replace multiple consecutive underscores with single underscore
  sanitized = sanitized.replaceAll(/_+/g, '_');
  
  // Limit length
  sanitized = sanitized.slice(0, 255);
  
  // Ensure the filename is not empty
  if (sanitized.length === 0) {
    sanitized = 'unnamed_file';
  }
  
  return sanitized;
}

/**
 * Calculate file entropy for detecting encrypted/obfuscated content
 */
function calculateEntropy(content: string): number {
  const frequencies: Record<string, number> = {};
  const length = content.length;
  
  // Count character frequencies
  for (let i = 0; i < length; i++) {
    const char = content[i];
    frequencies[char] = (frequencies[char] || 0) + 1;
  }
  
  // Calculate entropy
  let entropy = 0;
  for (const char in frequencies) {
    const frequency = frequencies[char] / length;
    entropy -= frequency * Math.log2(frequency);
  }
  
  return entropy;
}

/**
 * Check if file is a text file
 */
function isTextFile(mimeType: string): boolean {
  return mimeType.startsWith('text/') || 
         mimeType === 'application/json' || 
         mimeType === 'application/javascript' ||
         mimeType === 'text/x-python';
}

/**
 * Calculate overall risk level from issues
 */
function calculateRiskLevel(issues: SecurityIssue[]): 'low' | 'medium' | 'high' | 'critical' {
  if (issues.length === 0) return 'low';
  
  const hasCritical = issues.some(issue => issue.severity === 'critical');
  const hasHigh = issues.some(issue => issue.severity === 'high');
  const hasMedium = issues.some(issue => issue.severity === 'medium');
  
  if (hasCritical) return 'critical';
  if (hasHigh) return 'high';
  if (hasMedium) return 'medium';
  return 'low';
}

/**
 * Generate file hash for integrity checking
 */
export async function generateFileHash(file: File, algorithm: 'sha256' | 'sha512' = 'sha256'): Promise<string> {
  const hash = createHash(algorithm);
  const buffer = Buffer.from(await file.arrayBuffer());
  hash.update(buffer);
  return hash.digest('hex');
}

/**
 * Security scan result formatter
 */
export function formatSecurityResult(result: SecurityScanResult): string {
  const issueSummary = result.issues.map(issue => 
    `[${issue.severity.toUpperCase()}] ${issue.type}: ${issue.description}`
  ).join('\n');
  
  return `Security Scan Result:
Risk Level: ${result.riskLevel.toUpperCase()}
Safe: ${result.safe}
Issues Found: ${result.issues.length}

${issueSummary || 'No security issues detected.'}`;
}
