import { PutObjectCommand } from '@aws-sdk/client-s3';
import {
  generateSecureFileKey,
  validateServerFile,
  validateS3Config,
  getS3Client,
  getBucketName,
  sanitizeFilenameForMetadata,
  type FileMetadata,
} from '@/lib/files/file-upload-server';

// ===================================================================
// CONSTANTS AND INTERFACES
// ===================================================================

// Upload configuration interface
export interface FileUploadConfig {
  uploadType: string; // e.g., 'provider-logo', 'user-avatar', 'document', 'video', 'audio'
  orgId: string; // Organization/tenant ID
  userId: string; // User performing the upload
  providerName?: string; // Provider name alias (optional)
  requestId?: string; // Optional request tracking ID
  maxFileSize?: number; // Override default max file size
  allowedTypes?: string[]; // Override default allowed types
  folderName: string; // e.g., 'providers', 'users', 'documents', 'media'
  isPublic?: boolean; // Whether the file should be publicly accessible
  cacheControl?: string; // Cache control header
  contentDisposition?: string; // Content disposition header
}

// Upload result interface
export interface FileUploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    type: string;
    uploadedAt: string;
    category: string;
  };
}

// Batch upload result interface
export interface BatchUploadResult {
  success: boolean;
  results: Array<{
    fieldName: string;
    result: FileUploadResult;
  }>;
  errors?: string[];
}

// ===================================================================
// CORE UPLOAD FUNCTIONS
// ===================================================================

/**
 * Upload a single file to S3
 *
 * @param file - The file to upload (from FormData)
 * @param config - Upload configuration
 * @returns Promise<FileUploadResult>
 *
 * @example
 * ```typescript
 * const result = await uploadFileToS3(documentFile, {
 *   uploadType: 'document',
 *   orgId: 'org-123',
 *   userId: 'user-456',
 *   folderName: 'documents',
 *   allowedTypes: ['application/pdf', 'application/msword']
 * });
 *
 * if (result.success) {
 *   // File uploaded successfully
 * } else {
 *   // Upload failed
 * }
 * ```
 */
export async function uploadFileToS3(
  file: File,
  config: FileUploadConfig,
): Promise<FileUploadResult> {
  try {
    // 1. Validate S3 configuration
    const s3ConfigCheck = validateS3Config();
    if (!s3ConfigCheck.valid) {
      return {
        success: false,
        error: `S3 configuration error: ${s3ConfigCheck.error}`,
      };
    }

    // 2. Validate file
    const validation = validateServerFile(file, config.maxFileSize, config.allowedTypes);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // 3. Generate secure file key
    const fileKey = generateSecureFileKey(
      config.folderName,
      config.orgId,
      config.providerName || 'unknown',
      config.uploadType,
      '',
      file.name,
    );

    // 4. Prepare metadata
    const metadata: FileMetadata = {
      originalName: file.name,
      size: file.size,
      type: file.type,
      uploadType: config.uploadType,
      userId: config.userId,
      orgId: config.orgId,
    };

    // Preparing metadata

    // 5. Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // 6. Prepare S3 upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
      Body: fileBuffer,
      ContentType: file.type,
      CacheControl: config.cacheControl || (config.isPublic ? 'max-age=31536000' : 'no-cache'),
      ContentDisposition: config.contentDisposition || 'inline',
      Metadata: {
        'original-name': sanitizeFilenameForMetadata(metadata.originalName),
        'upload-type': metadata.uploadType,
        'user-id': metadata.userId,
        'org-id': metadata.orgId || '',
        'uploaded-at': new Date().toISOString(),
        'file-category': getFileCategory(file.type),
        ...(config.requestId && { 'request-id': config.requestId }),
        ...(config.isPublic !== undefined && { 'is-public': config.isPublic.toString() }),
      },
      ...(config.isPublic && {
        ACL: 'public-read',
      }),
    });

    // 7. Upload to S3
    // Starting S3 upload

    const s3Client = getS3Client();
    await s3Client.send(uploadCommand);

    // S3 upload completed successfully

    // 8. Generate file URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const bucketName = getBucketName();

    const fileUrl = `https://${bucketName}.s3.${region}.amazonaws.com/${fileKey}?t=${Date.now()}`;

    return {
      success: true,
      url: fileUrl,
      key: fileKey,
      metadata: {
        originalName: file.name,
        size: file.size,
        type: file.type,
        uploadedAt: new Date().toISOString(),
        category: getFileCategory(file.type),
      },
    };
  } catch (error) {
    // File upload failed
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

/**
 * Upload multiple files to S3 in batch
 *
 * @param files - Array of files with field names
 * @param config - Upload configuration (shared across all files)
 * @returns Promise<BatchUploadResult>
 *
 * @example
 * ```typescript
 * const files = [
 *   { fieldName: 'document', file: docFile },
 *   { fieldName: 'image', file: imageFile },
 * ];
 *
 * const result = await uploadMultipleFilesToS3(files, {
 *   uploadType: 'provider-assets',
 *   orgId: 'org-123',
 *   userId: 'user-456',
 *   folderName: 'providers'
 * });
 *
 * result.results.forEach(({ fieldName, result }) => {
 *   if (result.success) {
 *     // Field uploaded successfully
 *   }
 * });
 * ```
 */
export async function uploadMultipleFilesToS3(
  files: Array<{ fieldName: string; file: File }>,
  config: FileUploadConfig,
): Promise<BatchUploadResult> {
  const results: Array<{
    fieldName: string;
    result: FileUploadResult;
  }> = [];

  const errors: string[] = [];

  // Upload each file
  for (const { fieldName, file } of files) {
    try {
      const result = await uploadFileToS3(file, {
        ...config,
        uploadType: `${config.uploadType}-${fieldName}`, // e.g., 'provider-document', 'provider-image'
      });

      results.push({ fieldName, result });

      if (!result.success && result.error) {
        errors.push(`${fieldName}: ${result.error}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      results.push({
        fieldName,
        result: { success: false, error: errorMessage },
      });
      errors.push(`${fieldName}: ${errorMessage}`);
    }
  }

  const success = results.every(({ result }) => result.success);

  return {
    success,
    results,
    ...(errors.length > 0 && { errors }),
  };
}

// ===================================================================
// HELPER FUNCTIONS
// ===================================================================

/**
 * Helper to extract files from FormData
 *
 * @param formData - FormData object
 * @param fileFields - Array of field names to extract
 * @returns Array of files with field names
 *
 * @example
 * ```typescript
 * const files = extractFilesFromFormData(formData, ['document', 'image']);
 * // Returns: [{ fieldName: 'document', file: File }, { fieldName: 'image', file: File }]
 * ```
 */
export function extractFilesFromFormData(
  formData: FormData,
  fileFields: string[],
): Array<{ fieldName: string; file: File }> {
  const files: Array<{ fieldName: string; file: File }> = [];

  for (const fieldName of fileFields) {
    const file = formData.get(fieldName) as File | null;
    if (file && file.size > 0) {
      files.push({ fieldName, file });
    }
  }

  return files;
}

/**
 * Helper to create upload configuration
 *
 * @param baseConfig - Base configuration
 * @param uploadType - Upload type
 * @param overrides - Configuration overrides
 * @returns Complete upload configuration
 */
export function createUploadConfig(
  baseConfig: Omit<FileUploadConfig, 'uploadType'>,
  uploadType: string,
  overrides?: Partial<FileUploadConfig>,
): FileUploadConfig {
  return {
    ...baseConfig,
    uploadType,
    ...overrides,
  };
}

/**
 * Helper to log upload operations
 */
export function logUploadOperation(
  operation: 'start' | 'success' | 'error',
  config: FileUploadConfig,
  details?: {
    fileName?: string;
    fileSize?: number;
    duration?: number;
    error?: string;
    url?: string;
  },
): void {
  const logData = {
    operation,
    uploadType: config.uploadType,
    orgId: config.orgId,
    userId: config.userId,
    requestId: config.requestId,
    ...details,
  };

  switch (operation) {
    case 'start': {
      // Starting upload
      break;
    }
    case 'success': {
      // Upload successful
      break;
    }
    case 'error': {
      // Upload failed
      break;
    }
  }
}

/**
 * Get file category based on MIME type
 */
export function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.includes('pdf')) return 'document';
  if (mimeType.includes('word')) return 'document';
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'spreadsheet';
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'presentation';
  if (mimeType.includes('text')) return 'text';
  if (mimeType.includes('zip') || mimeType.includes('rar')) return 'archive';
  if (mimeType.includes('json') || mimeType.includes('xml')) return 'data';
  return 'other';
}

/**
 * Generate a signed URL for private file access
 */
export async function generateSignedUrl(
  fileKey: string,
  expiresIn: number = 3600, // 1 hour default
): Promise<string> {
  try {
    const { GetObjectCommand } = await import('@aws-sdk/client-s3');
    const { getSignedUrl } = await import('@aws-sdk/s3-request-presigner');

    const command = new GetObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
    });

    const signedUrl = await getSignedUrl(getS3Client(), command, { expiresIn });

    // Generated signed URL

    return signedUrl;
  } catch {
    // Failed to generate signed URL
    throw new Error('Failed to generate signed URL');
  }
}

/**
 * Delete a file from S3
 */
export async function deleteFileFromS3(
  fileKey: string,
): Promise<{ success: boolean; error?: string }> {
  try {
    const { DeleteObjectCommand } = await import('@aws-sdk/client-s3');

    const command = new DeleteObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
    });

    await getS3Client().send(command);

    // File deleted successfully

    return { success: true };
  } catch (error) {
    // Failed to delete file
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete file',
    };
  }
}

/**
 * Check if a file exists in S3
 */
export async function fileExistsInS3(fileKey: string): Promise<boolean> {
  try {
    const { HeadObjectCommand } = await import('@aws-sdk/client-s3');

    const command = new HeadObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
    });

    await getS3Client().send(command);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get file metadata from S3
 */
export async function getFileMetadata(fileKey: string): Promise<{
  size: number;
  type: string;
  lastModified: Date;
} | null> {
  try {
    const { HeadObjectCommand } = await import('@aws-sdk/client-s3');

    const command = new HeadObjectCommand({
      Bucket: getBucketName(),
      Key: fileKey,
    });

    const response = await getS3Client().send(command);

    return {
      size: response.ContentLength || 0,
      type: response.ContentType || 'application/octet-stream',
      lastModified: response.LastModified || new Date(),
    };
  } catch {
    // Failed to get file metadata
    return null;
  }
}
