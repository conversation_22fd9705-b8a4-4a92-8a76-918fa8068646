import { MAX_FILE_SIZE, ALLOWED_TYPES } from '@/lib/constant';

// Client-side utilities for browser environment only

// Validate file for client-side (browser File object)
export function validateClientFile(
  file: File,
  maxSizeBytes: number = MAX_FILE_SIZE,
  allowedTypes: string[] = ALLOWED_TYPES,
): { valid: boolean; error?: string } {
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`,
    };
  }

  if (file.size > maxSizeBytes) {
    console.warn('❌ [VALIDATE-CLIENT-FILE] File too large', {
      fileName: file.name,
      fileSize: file.size,
      maxSizeBytes,
    });
    return {
      valid: false,
      error: `File too large. Maximum size: ${Math.round(maxSizeBytes / 1024 / 1024)}MB`,
    };
  }

  return { valid: true };
}

// Helper to get file preview URL (for images and other previewable files)
export function getFilePreviewUrl(file: File): string {
  const url = URL.createObjectURL(file);
  return url;
}

// Helper to cleanup preview URL
export function cleanupPreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

// Get allowed file types for client validation
export function getAllowedTypesClient(allowedTypes: string[] = ALLOWED_TYPES): string[] {
  return [...allowedTypes];
}

// Get max file size for client validation
export function getMaxFileSizeClient(maxSizeBytes: number = MAX_FILE_SIZE): number {
  return maxSizeBytes;
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check if file is of a specific type
export function isFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

// Get file extension from file name
export function getFileExtension(fileName: string): string {
  return fileName.split('.').pop()?.toLowerCase() || '';
}

// Check if file has allowed extension
export function hasAllowedExtension(fileName: string, allowedExtensions: string[]): boolean {
  const extension = getFileExtension(fileName);
  return allowedExtensions.includes(extension);
}

// Generate a safe file name for upload
export function generateSafeFileName(fileName: string): string {
  const name = fileName.split('.')[0];
  const extension = fileName.split('.').pop() || '';
  const sanitizedName = name
    .replaceAll(/[^a-zA-Z0-9.-]/g, '_')
    .replaceAll(/_+/g, '_')
    .replaceAll(/^_|_$/g, '');

  const timestamp = Date.now();
  const random = Math.random().toString(36).slice(2, 8);

  return `${sanitizedName}_${timestamp}_${random}.${extension}`;
}

// Get file icon based on file type
export function getFileIcon(fileType: string): string {
  if (fileType.startsWith('image/')) return '🖼️';
  if (fileType.startsWith('video/')) return '🎥';
  if (fileType.startsWith('audio/')) return '🎵';
  if (fileType.includes('pdf')) return '📄';
  if (fileType.includes('word')) return '📝';
  if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
  if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📽️';
  if (fileType.includes('zip') || fileType.includes('rar')) return '📦';
  if (fileType.includes('text')) return '📃';
  return '📎';
}

// Read file as data URL (for preview)
export function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => resolve(reader.result as string));
    reader.addEventListener('error', () => reject(new Error('Failed to read file')));
    reader.readAsDataURL(file);
  });
}

// Read file as text
export function readFileAsText(file: File): Promise<string> {
  return file.text();
}

// Get file MIME type from extension
export function getMimeTypeFromExtension(extension: string): string {
  const mimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    txt: 'text/plain',
    csv: 'text/csv',
    json: 'application/json',
    xml: 'application/xml',
    zip: 'application/zip',
    rar: 'application/x-rar-compressed',
    mp4: 'video/mp4',
    avi: 'video/x-msvideo',
    mov: 'video/quicktime',
    wmv: 'video/x-ms-wmv',
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    flac: 'audio/flac',
  };

  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}
