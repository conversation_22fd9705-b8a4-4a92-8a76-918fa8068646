// Provides QueryClient instances for use with @tanstack/react-query throughout the app.
import { QueryClient, QueryClientConfig } from '@tanstack/react-query';

// Default options for all queries
const defaultOptions = {
  queries: {
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    // Don't retry failed queries by default
    retry: 1,
    // Clear cache on error to prevent stale data
    onError: () => {
      // This will be called when a query fails
    },
  },
  mutations: {
    // Don't retry failed mutations
    retry: 0,
  },
};

// Function to create a new QueryClient instance
function makeQueryClient(options?: QueryClientConfig) {
  return new QueryClient({
    defaultOptions,
    ...options,
  });
}

let browserQueryClient: QueryClient | undefined;

// Get QueryClient instance
export function getQueryClient(options?: QueryClientConfig) {
  if (globalThis.window === undefined) {
    // Server: always make a new query client
    return makeQueryClient(options);
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

// Function to clear the browser query client (useful for logout)
export function clearQueryClient() {
  if (browserQueryClient) {
    browserQueryClient.clear();
    browserQueryClient.cancelQueries();
    // Reset the client to force new instance on next use
    browserQueryClient = undefined;
  }
}

// Legacy export for backward compatibility
export const queryClient = getQueryClient();
