import { getRedisClient } from '../redis/redis';
import type { JobsRowProps } from '@/types/jobs';

const CACHE_PREFIX = 'quantum-jobs:';
const CACHE_TTL = 3600; // 1 hour in seconds

export interface QuantumJobsCacheData {
  jobsArray: JobsRowProps[];
  total: number;
  uniqueUsers: number;
  qbraidDeviceId: string;
  deviceName: string;
  deviceProvider: string;
  hasJobs: boolean;
  cachedAt: string;
}

/**
 * Generate cache key for quantum jobs based on device and filters
 */
export function getQuantumJobsCacheKey(
  qbraidDeviceId: string,
  filters?: {
    provider?: string;
    page?: number;
    resultsPerPage?: number | 'all';
  }
): string {
  const filterString = filters ? `:${JSON.stringify(filters)}` : '';
  return `${CACHE_PREFIX}${qbraidDeviceId}${filterString}`;
}

/**
 * Get cached quantum jobs data for a device
 */
export async function getCachedQuantumJobs(
  qbraidDeviceId: string,
  filters?: {
    provider?: string;
    page?: number;
    resultsPerPage?: number | 'all';
  }
): Promise<QuantumJobsCacheData | null> {
  try {
    const redis = getRedisClient();
    const cacheKey = getQuantumJobsCacheKey(qbraidDeviceId, filters);
    
    const cachedData = await redis.get(cacheKey);
    
    if (!cachedData) {
      return null;
    }
    
    const parsedData: QuantumJobsCacheData = JSON.parse(cachedData);
    
    // Check if cache is stale (though Redis TTL should handle this)
    const cachedTime = new Date(parsedData.cachedAt).getTime();
    const now = Date.now();
    const age = (now - cachedTime) / 1000; // age in seconds
    
    if (age > CACHE_TTL) {
      console.log(`🔄 [CACHE] Quantum jobs cache stale for device ${qbraidDeviceId}, age: ${age}s`);
      return null;
    }
    
    console.log(`✅ [CACHE] Quantum jobs cache hit for device ${qbraidDeviceId}`);
    return parsedData;
  } catch (error) {
    console.warn('⚠️ [CACHE] Failed to get cached quantum jobs:', error);
    return null;
  }
}

/**
 * Cache quantum jobs data for a device
 */
export async function cacheQuantumJobs(
  qbraidDeviceId: string,
  data: QuantumJobsCacheData,
  filters?: {
    provider?: string;
    page?: number;
    resultsPerPage?: number | 'all';
  }
): Promise<void> {
  try {
    const redis = getRedisClient();
    const cacheKey = getQuantumJobsCacheKey(qbraidDeviceId, filters);
    
    // Add timestamp to track when data was cached
    const dataToCache = {
      ...data,
      cachedAt: new Date().toISOString(),
    };
    
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(dataToCache));
    console.log(`💾 [CACHE] Cached quantum jobs for device ${qbraidDeviceId}, TTL: ${CACHE_TTL}s`);
  } catch (error) {
    console.warn('⚠️ [CACHE] Failed to cache quantum jobs:', error);
    // Don't throw error - caching is best effort
  }
}

/**
 * Invalidate quantum jobs cache for a specific device
 */
export async function invalidateQuantumJobsCache(
  qbraidDeviceId: string,
  filters?: {
    provider?: string;
    page?: number;
    resultsPerPage?: number | 'all';
  }
): Promise<void> {
  try {
    const redis = getRedisClient();
    const cacheKey = getQuantumJobsCacheKey(qbraidDeviceId, filters);
    
    await redis.del(cacheKey);
    console.log(`🗑️ [CACHE] Invalidated quantum jobs cache for device ${qbraidDeviceId}`);
  } catch (error) {
    console.warn('⚠️ [CACHE] Failed to invalidate quantum jobs cache:', error);
  }
}

/**
 * Clear all quantum jobs cache (useful for development or when all data needs refresh)
 */
export async function clearAllQuantumJobsCache(): Promise<number> {
  try {
    const redis = getRedisClient();
    const keys = await redis.keys(`${CACHE_PREFIX}*`);
    
    if (keys.length === 0) {
      return 0;
    }
    
    const deleted = await redis.del(...keys);
    console.log(`🧹 [CACHE] Cleared ${deleted} quantum jobs cache entries`);
    return deleted;
  } catch (error) {
    console.error('❌ [CACHE] Failed to clear quantum jobs cache:', error);
    throw error;
  }
}