import { invalidateQuantumJobsCache } from './quantum-jobs-cache';

/**
 * Invalidate quantum jobs cache for a device when new jobs are submitted
 * This should be called after a successful job submission
 */
export async function invalidateJobsCacheOnNewJob(
  qbraidDeviceId: string,
  provider?: string
): Promise<void> {
  try {
    // Invalidate cache for all pages and results per page combinations
    // This ensures the cache is properly invalidated when new jobs are added
    await invalidateQuantumJobsCache(qbraidDeviceId, {
      provider,
    });
    
    console.log(`🔄 [CACHE] Invalidated quantum jobs cache for device ${qbraidDeviceId} due to new job submission`);
  } catch (error) {
    console.warn('⚠️ [CACHE] Failed to invalidate cache on new job submission:', error);
    // Don't throw error - this is a non-critical operation
  }
}

/**
 * Invalidate cache for multiple devices (useful for bulk operations)
 */
export async function invalidateMultipleDevicesCache(
  deviceIds: string[],
  provider?: string
): Promise<void> {
  try {
    await Promise.all(
      deviceIds.map(deviceId => 
        invalidateQuantumJobsCache(deviceId, { provider })
      )
    );
    
    console.log(`🔄 [CACHE] Invalidated quantum jobs cache for ${deviceIds.length} devices`);
  } catch (error) {
    console.warn('⚠️ [CACHE] Failed to invalidate cache for multiple devices:', error);
  }
}