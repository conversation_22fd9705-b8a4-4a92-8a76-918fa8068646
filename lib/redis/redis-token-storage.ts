import { TokenStorage } from '@/types/auth';

import { isRedisHealthy, getRedisClient } from '@/lib/redis/redis';

/**
 * Redis-based token storage implementation with in-memory fallback
 * Provides scalable token storage for serverless environments
 */
export class RedisTokenStorage implements TokenStorage {
  private fallbackStorage: Map<string, string> = new Map();
  private readonly keyPrefix = 'auth:tokens:';
  private readonly defaultTtl = 3600; // 1 hour TTL for auth tokens

  /**
   * Check if Redis is available, fallback to in-memory storage if not
   */
  private async useRedis(): Promise<boolean> {
    try {
      return await isRedisHealthy();
    } catch {
      return false;
    }
  }

  /**
   * Get the full Redis key with prefix
   */
  private getRedisKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * Set a token in storage with optional TTL
   */
  async setItem(key: string, value: string, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTtl;

    try {
      if (await this.useRedis()) {
        const redis = getRedisClient();
        const redisKey = this.getRedisKey(key);

        await redis.setex(redisKey, ttl, value);
        console.log(`🔐 [REDIS-STORAGE] Token stored in Redis: ${key} (TTL: ${ttl}s)`);
      } else {
        // Fallback to in-memory storage
        this.fallbackStorage.set(key, value);
        console.log(`🔐 [MEMORY-STORAGE] Token stored in memory: ${key} (fallback)`);

        // Set TTL for in-memory storage using setTimeout
        if (ttl > 0) {
          setTimeout(() => {
            this.fallbackStorage.delete(key);
            console.log(`🔐 [MEMORY-STORAGE] Token expired: ${key}`);
          }, ttl * 1000);
        }
      }
    } catch (error) {
      console.error(`❌ [TOKEN-STORAGE] Failed to store token ${key}:`, error);

      // Emergency fallback to in-memory
      this.fallbackStorage.set(key, value);
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback for token: ${key}`);
    }
  }

  /**
   * Get a token from storage
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (await this.useRedis()) {
        const redis = getRedisClient();
        const redisKey = this.getRedisKey(key);

        const value = await redis.get(redisKey);
        console.log(
          `🔐 [REDIS-STORAGE] Token retrieved: ${key} - ${value ? 'found' : 'not found'}`,
        );
        return value;
      } else {
        // Fallback to in-memory storage
        const value = this.fallbackStorage.get(key) || null;
        console.log(
          `🔐 [MEMORY-STORAGE] Token retrieved: ${key} - ${value ? 'found' : 'not found'} (fallback)`,
        );
        return value;
      }
    } catch (error) {
      console.error(`❌ [TOKEN-STORAGE] Failed to retrieve token ${key}:`, error);

      // Emergency fallback to in-memory
      const value = this.fallbackStorage.get(key) || null;
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback retrieval for token: ${key}`);
      return value;
    }
  }

  /**
   * Remove a token from storage
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (await this.useRedis()) {
        const redis = getRedisClient();
        const redisKey = this.getRedisKey(key);

        await redis.del(redisKey);
        console.log(`🔐 [REDIS-STORAGE] Token removed: ${key}`);
      } else {
        // Fallback to in-memory storage
        this.fallbackStorage.delete(key);
        console.log(`🔐 [MEMORY-STORAGE] Token removed: ${key} (fallback)`);
      }
    } catch (error) {
      console.error(`❌ [TOKEN-STORAGE] Failed to remove token ${key}:`, error);

      // Emergency fallback to in-memory
      this.fallbackStorage.delete(key);
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback removal for token: ${key}`);
    }
  }

  /**
   * Clear all tokens from storage
   */
  async clear(): Promise<void> {
    try {
      if (await this.useRedis()) {
        const redis = getRedisClient();

        // Get all keys with our prefix
        const keys = await redis.keys(`${this.keyPrefix}*`);

        if (keys.length > 0) {
          await redis.del(...keys);
          console.log(`🔐 [REDIS-STORAGE] Cleared ${keys.length} tokens`);
        } else {
          console.log(`🔐 [REDIS-STORAGE] No tokens to clear`);
        }
      } else {
        // Fallback to in-memory storage
        const count = this.fallbackStorage.size;
        this.fallbackStorage.clear();
        console.log(`🔐 [MEMORY-STORAGE] Cleared ${count} tokens (fallback)`);
      }
    } catch (error) {
      console.error(`❌ [TOKEN-STORAGE] Failed to clear tokens:`, error);

      // Emergency fallback to in-memory
      const count = this.fallbackStorage.size;
      this.fallbackStorage.clear();
      console.log(`🔐 [MEMORY-STORAGE] Emergency fallback clear: ${count} tokens`);
    }
  }

  /**
   * Get token with automatic TTL refresh
   * Useful for long-running operations
   */
  async getItemWithRefresh(key: string, refreshTtl?: number): Promise<string | null> {
    const value = await this.getItem(key);

    if (value && refreshTtl) {
      // Refresh TTL if token exists
      await this.setItem(key, value, refreshTtl);
      console.log(`🔐 [TOKEN-STORAGE] Token TTL refreshed: ${key} (${refreshTtl}s)`);
    }

    return value;
  }

  /**
   * Get storage statistics
   * Useful for monitoring and debugging
   */
  async getStats(): Promise<{
    redisAvailable: boolean;
    memoryTokens: number;
    redisTokens?: number;
  }> {
    const redisAvailable = await this.useRedis();
    const memoryTokens = this.fallbackStorage.size;

    let redisTokens: number | undefined;

    if (redisAvailable) {
      try {
        const redis = getRedisClient();
        const keys = await redis.keys(`${this.keyPrefix}*`);
        redisTokens = keys.length;
      } catch (error) {
        console.warn('⚠️ [TOKEN-STORAGE] Failed to get Redis stats:', error);
      }
    }

    return {
      redisAvailable,
      memoryTokens,
      redisTokens,
    };
  }
}
