import { z } from 'zod';

/**
 * Device Field Validation with Zod
 *
 * Replaces device-field-validation.ts and device-patch-builder.ts
 * with a single, type-safe Zod schema that handles both validation
 * and transformation for API calls.
 */

// Enum definitions
const ParadigmEnum = z.enum(['AHS', 'annealing', 'gate-based']);
const TypeEnum = z.enum(['QPU', 'Simulator']);
const StatusEnum = z.enum(['ONLINE', 'OFFLINE', 'RETIRED']);
const VisibilityEnum = z.enum(['public', 'private']);
const ArchitectureEnum = z.enum(['trapped ion', 'superconducting', 'photonic', 'neutral atom']);

// Transform comma-separated string to array
const arrayFieldTransform = z
  .string()
  .transform((val) =>
    val
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item.length > 0),
  )
  .or(z.array(z.string()));

// Device field schemas
export const deviceFieldSchemas = {
  // Basic device properties
  numberQubits: z.coerce.number().min(0),
  statusRefresh: z.coerce.number(),
  visibility: VisibilityEnum,
  name: z.string().min(1),
  paradigm: ParadigmEnum,
  type: TypeEnum,
  status: StatusEnum,
  architecture: ArchitectureEnum,
  pendingJobs: z.coerce.number().min(0),
  availabilityCD: z.string(),
  isAvailable: z.boolean(),

  // Array fields (comma-separated in UI, arrays in backend)
  noiseModels: arrayFieldTransform,
  blackListedDomains: arrayFieldTransform,
  whiteListedDomains: arrayFieldTransform,
  spec: arrayFieldTransform,
  runInputTypes: arrayFieldTransform,

  // Description fields
  about: z.string(),
  deviceAbout: z.string(),
  deviceDescription: z.string(),
  deviceImage: z.string(),
  processorType: z.string(),

  // Pricing fields (transformed to nested object)
  'pricing.perMinute': z.coerce.number().min(0),
  'pricing.perTask': z.coerce.number().min(0),
  'pricing.perShot': z.coerce.number().min(0),
} as const;

export type DeviceFieldName = keyof typeof deviceFieldSchemas;

/**
 * Build patch object for device updates
 * Handles pricing fields and validates input
 */
export function buildDevicePatch(field: DeviceFieldName, value: any) {
  // Validate the field value first
  const schema = deviceFieldSchemas[field];
  const validatedValue = schema.parse(value);

  // Handle pricing fields - backend expects pricing object
  if (field.startsWith('pricing.')) {
    const pricingKey = field.replace('pricing.', '');
    return { pricing: { [pricingKey]: validatedValue } };
  }

  // Return normal field update
  return { [field]: validatedValue };
}

/**
 * Format value for input display
 */
export function formatForInput(field: DeviceFieldName, value: any): string | number | boolean {
  if (isArrayField(field) && Array.isArray(value)) {
    return value.join(', ');
  }
  return value ?? '';
}

/**
 * Get field validation schema
 */
export function getFieldSchema(field: DeviceFieldName) {
  return deviceFieldSchemas[field];
}

/**
 * Check if field is array type
 */
export function isArrayField(field: DeviceFieldName): boolean {
  const arrayFields: DeviceFieldName[] = [
    'noiseModels',
    'blackListedDomains',
    'whiteListedDomains',
    'spec',
    'runInputTypes',
  ];
  return arrayFields.includes(field);
}

/**
 * Check if field is pricing field
 */
export function isPricingField(field: string): boolean {
  return field.startsWith('pricing.');
}

/**
 * Get field type for UI components
 */
export function getFieldType(
  field: DeviceFieldName,
): 'string' | 'number' | 'boolean' | 'array' | 'enum' {
  const schema = deviceFieldSchemas[field];

  if (isArrayField(field)) return 'array';
  if (schema instanceof z.ZodEnum) return 'enum';
  if (schema instanceof z.ZodBoolean) return 'boolean';

  // Check for number fields (including coerced numbers)
  const numberFields: DeviceFieldName[] = [
    'numberQubits',
    'statusRefresh',
    'pendingJobs',
    'pricing.perMinute',
    'pricing.perTask',
    'pricing.perShot',
  ];
  if (numberFields.includes(field)) return 'number';

  return 'string';
}

/**
 * Get enum options for enum fields
 */
export function getEnumOptions(field: DeviceFieldName): string[] | undefined {
  const schema = deviceFieldSchemas[field];
  if (schema instanceof z.ZodEnum) {
    return schema.options;
  }
  return undefined;
}

// Device validation schemas and utilities
export const DeviceFieldSchema = z.object({
  name: z.string().min(1, 'Device name is required'),
  description: z.string().optional(),
  status: z.enum(['ONLINE', 'OFFLINE', 'RETIRED']).default('ONLINE'),
  type: z.enum(['Simulator', 'QPU']).default('Simulator'),
  provider: z.string().min(1, 'Provider is required'),
  // Add other device fields as needed
});

export type DeviceField = z.infer<typeof DeviceFieldSchema>;

export function validateDeviceField(field: unknown): { valid: boolean; error?: string } {
  try {
    DeviceFieldSchema.parse(field);
    return { valid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { valid: false, error: error.errors[0]?.message };
    }
    return { valid: false, error: 'Validation failed' };
  }
}
