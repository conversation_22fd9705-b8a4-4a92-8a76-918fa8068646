import {
  signIn,
  signUp,
  confirmSignUp,
  resetPassword,
  confirmResetPassword,
  signOut as amplifySignOut,
  fetchAuthSession,
  getCurrentUser,
  AuthTokens,
} from '@aws-amplify/auth';
import { Amplify } from 'aws-amplify';
import { RedisTokenStorage } from '@/lib/redis/redis-token-storage';
import { AuthConfig } from '@/types/auth';

/**
 * Enhanced authentication wrapper that handles UserAlreadyAuthenticatedException
 * and other common authentication errors gracefully
 */
export class AuthWrapper {
  private static serverTokenStorage = new RedisTokenStorage();
  private static isConfigured = false;

  /**
   * Configures Amplify with fresh state to prevent authentication conflicts
   */
  static async configure(): Promise<void> {
    if (this.isConfigured) {
      return;
    }

    const config: AuthConfig = {
      Auth: {
        Cognito: {
          userPoolId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID!,
          userPoolClientId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID!,
          identityPoolId: process.env.NEXT_PUBLIC_QBRAID_COGNITO_IDENTITY_POOL_ID!,
          signUpVerificationMethod: 'link' as const,
          loginWith: {
            email: true,
            username: false,
            phone: false,
          },
        },
      },
      Storage: this.serverTokenStorage,
    };

    try {
      console.log('🔧 [AUTH_WRAPPER] Configuring Amplify with fresh state...');

      // Force clear any existing authentication state
      await this.forceClearAuthState();

      // Configure Amplify
      Amplify.configure(config);
      this.isConfigured = true;

      console.log('✅ [AUTH_WRAPPER] Amplify configured successfully');
    } catch (error) {
      console.error('❌ [AUTH_WRAPPER] Failed to configure Amplify:', error);
      throw error;
    }
  }

  /**
   * Force clear all authentication state to prevent conflicts
   */
  private static async forceClearAuthState(): Promise<void> {
    const clearOperations = [
      // 1. Try to sign out globally
      (async () => {
        try {
          await amplifySignOut({ global: true });
          console.log('🔧 [AUTH_WRAPPER] Cleared global auth state');
        } catch {
          // Ignore errors - user might not be signed in
          console.log('🔧 [AUTH_WRAPPER] No global auth state to clear');
        }
      })(),

      // 2. Clear token storage
      (async () => {
        try {
          await this.serverTokenStorage.clear();
          console.log('🔧 [AUTH_WRAPPER] Cleared token storage');
        } catch (error) {
          console.warn('⚠️ [AUTH_WRAPPER] Failed to clear token storage:', error);
        }
      })(),
    ];

    // Execute all clear operations in parallel
    await Promise.allSettled(clearOperations);
  }

  /**
   * Wrapper for signIn with automatic retry on UserAlreadyAuthenticatedException
   */
  static async signInWithRetry(username: string, password: string, maxRetries = 2): Promise<any> {
    await this.configure();

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔐 [AUTH_WRAPPER] Sign in attempt ${attempt} for ${username}`);

        const result = await signIn({ username, password });
        console.log('✅ [AUTH_WRAPPER] Sign in successful');
        return result;
      } catch (error: any) {
        console.error(
          `❌ [AUTH_WRAPPER] Sign in attempt ${attempt} failed:`,
          error.name,
          error.message,
        );

        if (error.name === 'UserAlreadyAuthenticatedException') {
          console.log(
            '🔄 [AUTH_WRAPPER] User already authenticated, clearing state and retrying...',
          );

          // Force clear authentication state
          await this.forceClearAuthState();

          // Reconfigure Amplify
          this.isConfigured = false;
          await this.configure();

          // If this is the last attempt, throw the error
          if (attempt === maxRetries) {
            console.error(
              '❌ [AUTH_WRAPPER] Max retries reached for UserAlreadyAuthenticatedException',
            );
            throw new Error('Authentication failed due to session conflict. Please try again.');
          }

          // Wait a bit before retrying
          await new Promise((resolve) => setTimeout(resolve, 100 * attempt));
          continue;
        }

        // For other errors, throw immediately
        throw error;
      }
    }

    throw new Error('Sign in failed after maximum retries');
  }

  /**
   * Wrapper for signUp with automatic state clearing
   */
  static async signUpWithRetry(username: string, password: string, options?: any): Promise<any> {
    await this.configure();

    try {
      console.log(`📝 [AUTH_WRAPPER] Sign up attempt for ${username}`);

      const result = await signUp({
        username,
        password,
        options,
      });

      console.log('✅ [AUTH_WRAPPER] Sign up successful');
      return result;
    } catch (error: any) {
      console.error('❌ [AUTH_WRAPPER] Sign up failed:', error.name, error.message);

      if (error.name === 'UserAlreadyAuthenticatedException') {
        console.log('🔄 [AUTH_WRAPPER] Clearing auth state for sign up...');
        await this.forceClearAuthState();
        this.isConfigured = false;
        await this.configure();
      }

      throw error;
    }
  }

  /**
   * Wrapper for other auth operations
   */
  static async confirmSignUp(username: string, confirmationCode: string): Promise<any> {
    await this.configure();
    return confirmSignUp({ username, confirmationCode });
  }

  static async resetPassword(username: string): Promise<any> {
    await this.configure();
    return resetPassword({ username });
  }

  static async confirmResetPassword(
    username: string,
    confirmationCode: string,
    newPassword: string,
  ): Promise<any> {
    await this.configure();
    return confirmResetPassword({ username, confirmationCode, newPassword });
  }

  static async fetchAuthSession(): Promise<AuthTokens | undefined> {
    if (!this.isConfigured) {
      await this.configure();
    }
    const session = await fetchAuthSession();
    return session.tokens as AuthTokens;
  }

  static async getCurrentUser(): Promise<any> {
    if (!this.isConfigured) {
      await this.configure();
    }
    return getCurrentUser();
  }

  static async signOut(): Promise<void> {
    try {
      await amplifySignOut({ global: true });
      console.log('✅ [AUTH_WRAPPER] Sign out successful');
    } catch (error) {
      console.warn('⚠️ [AUTH_WRAPPER] Sign out error:', error);
    } finally {
      await this.forceClearAuthState();
      this.isConfigured = false;
    }
  }
}
