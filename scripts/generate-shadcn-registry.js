#!/usr/bin/env node
/*
  Generates a local shadcn-style registry at public/r/index.json
  by embedding the contents of files under components/ui and
  including lib/utils.ts for components that import it.
*/

const fs = require('fs');
const path = require('path');

const projectRoot = process.cwd();
const uiDir = path.join(projectRoot, 'components', 'ui');
const utilsPath = path.join(projectRoot, 'lib', 'utils.ts');
const outDir = path.join(projectRoot, 'public', 'r');
const outIndex = path.join(outDir, 'index.json');
const importPathRegex = /from\s+['"](@\/components\/ui\/([^'"\n]+))['"]/g;

function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function readFileSafe(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch {
    return null;
  }
}

function fileNameToComponentName(fileName) {
  const base = fileName.replace(/\.(tsx?|jsx?)$/, '');
  return base;
}

function buildRegistryItemForFile(fileName) {
  const absPath = path.join(uiDir, fileName);
  const content = readFileSafe(absPath);
  if (content == null) return null;

  const files = [
    {
      path: `components/ui/${fileName}`,
      content,
      type: fileName.endsWith('.tsx') ? 'tsx' : 'ts',
    },
  ];

  if (content.includes("@/lib/utils")) {
    const utilsContent = readFileSafe(utilsPath);
    if (utilsContent) {
      files.push({ path: 'lib/utils.ts', content: utilsContent, type: 'ts' });
    }
  }

  const name = fileNameToComponentName(fileName);

  // derive dependencies on other ui components by import path
  const dependencies = new Set();
  let match;
  while ((match = importPathRegex.exec(content)) !== null) {
    const importedPath = match[2]; // e.g. 'button' or 'button.tsx'
    // normalize to component name (strip extension and directory prefix if any)
    const depName = importedPath.replace(/^.*\//, '').replace(/\.(tsx?|jsx?)$/, '');
    if (depName && depName !== name) {
      dependencies.add(depName);
    }
  }

  return {
    name,
    type: 'component',
    files,
    dependencies: Array.from(dependencies).sort(),
  };
}

function main() {
  if (!fs.existsSync(uiDir)) {
    console.error(`UI directory not found: ${uiDir}`);
    process.exit(1);
  }

  ensureDir(outDir);

  const entries = fs
    .readdirSync(uiDir)
    .filter((f) => /\.(tsx?|jsx?)$/.test(f))
    .sort();

  const items = [];
  for (const file of entries) {
    const item = buildRegistryItemForFile(file);
    if (item) items.push(item);
  }

  const indexPayload = {
    $schema: 'https://ui.shadcn.com/registry.schema.json',
    name: 'qbraid-partner-dashboard-registry',
    version: 1,
    items,
  };

  fs.writeFileSync(outIndex, JSON.stringify(indexPayload, null, 2) + '\n', 'utf8');
  console.log(`Wrote registry with ${items.length} items to ${path.relative(projectRoot, outIndex)}`);
}

main();
