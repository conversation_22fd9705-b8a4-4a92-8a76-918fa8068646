#!/usr/bin/env node --input-type=module

/**
 * Remove Unused Dependencies Script
 *
 * This script removes dependencies that are no longer used in the codebase
 * after the cleanup of demo files and redundant components.
 */

import { execSync } from 'node:child_process';
import readline from 'node:readline';

// Dependencies that are likely unused after cleanup
const potentiallyUnusedDeps = [
  // Three.js related (only used in one UI component that might be removed)
  '@react-three/fiber',
  'three',
  '@types/three',

  // DnD Kit (only used in data-table which might be simplified)
  '@dnd-kit/core',
  '@dnd-kit/modifiers',
  '@dnd-kit/sortable',
  '@dnd-kit/utilities',

  // Formik/Yup (only used in edit-device-form)
  'formik',
  'yup',

  // PapaParse (only used in invite-member-button)
  'papaparse',
  '@types/papaparse',

  // Motion (might be replaced with simpler animations)
  'motion',

  // UUID (can be replaced with crypto.randomUUID)
  'uuid',

  // Vaul (drawer component that might not be used)
  'vaul',
];

console.log('🔍 Checking for unused dependencies...');

// Function to check if a dependency is actually used
function isDependencyUsed(dep) {
  try {
    // Check for imports in TypeScript/JavaScript files
    const result = execSync(
      `grep -r "from '${dep}'" --include="*.tsx" --include="*.ts" . || true`,
      {
        encoding: 'utf8',
        stdio: 'pipe',
      },
    );

    // Also check for require statements
    const requireResult = execSync(
      `grep -r "require('${dep}')" --include="*.tsx" --include="*.ts" --include="*.js" . || true`,
      {
        encoding: 'utf8',
        stdio: 'pipe',
      },
    );

    // Filter out node_modules results
    const filteredResult = result
      .split('\n')
      .filter((line) => line.trim() && !line.includes('node_modules'))
      .join('\n');

    const filteredRequireResult = requireResult
      .split('\n')
      .filter((line) => line.trim() && !line.includes('node_modules'))
      .join('\n');

    return filteredResult.trim().length > 0 || filteredRequireResult.trim().length > 0;
  } catch (error) {
    console.warn(`⚠️  Error checking dependency ${dep}:`, error.message);
    return true; // Assume it's used if we can't check
  }
}

const unusedDeps = [];
const usedDeps = [];

for (const dep of potentiallyUnusedDeps) {
  console.log(`🔍 Checking ${dep}...`);

  if (isDependencyUsed(dep)) {
    console.log(`✅ ${dep} is being used`);
    usedDeps.push(dep);
  } else {
    console.log(`❌ ${dep} appears to be unused`);
    unusedDeps.push(dep);
  }
}

console.log('\n📊 Analysis Results:');
console.log(`✅ Used dependencies: ${usedDeps.length}`);
console.log(`❌ Unused dependencies: ${unusedDeps.length}`);

if (unusedDeps.length > 0) {
  console.log('\n🗑️  Unused dependencies found:');
  for (const dep of unusedDeps) console.log(`   - ${dep}`);

  console.log('\n💡 To remove these dependencies, run:');
  console.log(`npm uninstall ${unusedDeps.join(' ')}`);

  // Ask for confirmation before removing
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question('\n❓ Do you want to remove these unused dependencies? (y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      console.log('\n🗑️  Removing unused dependencies...');

      try {
        execSync(`npm uninstall ${unusedDeps.join(' ')}`, { stdio: 'inherit' });
        console.log('\n✅ Successfully removed unused dependencies!');
      } catch (error) {
        console.error('\n❌ Error removing dependencies:', error.message);
      }
    } else {
      console.log('\n⏭️  Skipping dependency removal.');
    }

    rl.close();
  });
} else {
  console.log('\n✨ No unused dependencies found!');
}
