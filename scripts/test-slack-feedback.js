#!/usr/bin/env node

// Test script for all feedback types to Slack
import fetch from 'node-fetch';

const feedbackTypes = [
  {
    type: 'bug',
    issue: 'Save button not working on device management page',
    details:
      'When I click the save button on the device management page, nothing happens. The button appears to be clickable but no action occurs.',
    steps:
      '1. Navigate to device management\n2. Edit a device\n3. Click save button\n4. No response',
    priority: 'high',
    userEmail: '<EMAIL>',
    page: '/device-management',
    browser: 'Chrome 120.0.0',
    timestamp: new Date().toISOString(),
    orgName: 'Test Organization',
    orgId: 'test-org-123',
    userRole: 'admin',
  },
  {
    type: 'feature',
    issue: 'Add dark mode to the dashboard',
    details:
      'It would be great to have a dark mode option for the dashboard to reduce eye strain during long work sessions.',
    steps: 'N/A',
    priority: 'medium',
    userEmail: '<EMAIL>',
    page: '/dashboard',
    browser: 'Firefox 121.0.0',
    timestamp: new Date().toISOString(),
    orgName: 'Innovate Corp',
    orgId: 'innovate-corp-456',
    userRole: 'user',
  },
  {
    type: 'help',
    issue: 'How to export quantum job results?',
    details:
      'I need to export my quantum job results to a CSV file but I cannot find the export option. Can someone guide me through this process?',
    steps:
      '1. Completed quantum job\n2. Viewed results\n3. Looked for export button\n4. Could not find it',
    priority: 'medium',
    userEmail: '<EMAIL>',
    page: '/devices/quantum-jobs',
    browser: 'Safari 17.2.1',
    timestamp: new Date().toISOString(),
    orgName: 'Quantum Labs',
    orgId: 'quantum-labs-789',
    userRole: 'scientist',
  },
  {
    type: 'other',
    issue: 'Overall great experience with the platform',
    details:
      'I just wanted to say that the qBraid platform has been fantastic for our research. The interface is intuitive and the quantum job scheduling is reliable.',
    steps: 'N/A',
    priority: 'low',
    userEmail: '<EMAIL>',
    page: '/dashboard',
    browser: 'Edge 120.0.0',
    timestamp: new Date().toISOString(),
    orgName: 'Research University',
    orgId: 'research-univ-012',
    userRole: 'professor',
  },
];

async function testFeedbackType(feedbackData, index) {
  console.log(
    `\n🧪 Testing ${feedbackData.type.toUpperCase()} feedback (${index + 1}/${feedbackTypes.length})`,
  );
  console.log('📊 Test data:', JSON.stringify(feedbackData, null, 2));

  try {
    const response = await fetch('http://localhost:3000/api/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(feedbackData),
    });

    const result = await response.json();

    console.log('\n📥 Response status:', response.status);
    console.log('📥 Response body:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log('\n✅ SUCCESS:', feedbackData.type.toUpperCase(), 'feedback sent to Slack!');
    } else {
      console.log('\n❌ FAILED:', feedbackData.type.toUpperCase(), 'feedback failed');
    }
  } catch (error) {
    console.error('\n❌ ERROR:', feedbackData.type.toUpperCase(), 'feedback error:', error.message);
  }

  // Add delay between requests to avoid rate limiting
  console.log('⏳ Waiting 2 seconds before next test...\n');
  await new Promise((resolve) => setTimeout(resolve, 2000));
}

async function runAllTests() {
  console.log('🚀 Starting Slack feedback message tests for all types');
  console.log('========================================================');

  console.log('\n📋 This script will test:');
  console.log('   1. 🐛 Bug Report (High Priority)');
  console.log('   2. 💡 Feature Request');
  console.log('   3. ❓ Help Request');
  console.log('   4. 💬 General Feedback');
  console.log('\n⚠️  Make sure your development server is running on localhost:3000');
  console.log('⚠️  Make sure SLACK_WEBHOOK_URL environment variable is set\n');

  // Test each feedback type
  for (const [index, feedbackData] of feedbackTypes.entries()) {
    await testFeedbackType(feedbackData, index);
  }

  console.log('========================================================');
  console.log('🎉 All tests completed!');
  console.log('📝 Check your Slack channel for messages from each feedback type');
  console.log('🔍 Verify that:');
  console.log('   • Messages have different colors (red, yellow, blue, gray)');
  console.log('   • Organization and role information is included');
  console.log('   • Bug reports show priority levels');
  console.log('   • All required fields are present');
}

// Run the tests
runAllTests().catch(console.error);
