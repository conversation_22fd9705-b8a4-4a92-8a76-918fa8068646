#!/usr/bin/env node

/**
 * Test script for Slack notifications
 * Run this script to test if your Slack webhook configuration is working
 * 
 * Usage: 
 * 1. Set your SLACK_WEBHOOK_URL in .env.local
 * 2. Run: node scripts/test-slack-notifications.js
 */

require('dotenv').config({ path: '.env.local' });

// Simple Slack notification tester
class SlackTester {
  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL;
  }

  async sendTestMessage() {
    if (!this.webhookUrl) {
      console.error('❌ SLACK_WEBHOOK_URL is not configured');
      return false;
    }

    const message = {
      text: '🧪 Test Notification from qbraid Partner Dashboard',
      attachments: [{
        color: 'good',
        title: 'Test Notification',
        fields: [
          { title: 'Status', value: '✅ Working', short: true },
          { title: 'Time', value: new Date().toISOString(), short: true },
          { title: 'Message', value: 'If you see this, your Slack webhook is properly configured!', short: false },
        ],
        footer: 'qbraid Partner Dashboard',
        ts: Math.floor(Date.now() / 1000),
      }],
    };

    try {
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message),
      });

      if (response.ok) {
        console.log('✅ Test notification sent successfully!');
        return true;
      } else {
        console.error('❌ Failed to send notification:', response.status, response.statusText);
        return false;
      }
    } catch (error) {
      console.error('❌ Error sending notification:', error.message);
      return false;
    }
  }
}

async function test() {
  console.log('🧪 Testing Slack webhook configuration...\n');
  
  const tester = new SlackTester();
  const success = await tester.sendTestMessage();
  
  if (success) {
    console.log('\n🎉 Success! Your Slack webhook is working.');
    console.log('Check your Slack channel for the test message.');
  } else {
    console.log('\n❌ Failed to send test notification.');
    console.log('Please check your webhook URL and try again.');
  }
}

test();