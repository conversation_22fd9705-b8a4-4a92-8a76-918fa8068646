#!/usr/bin/env node

/**
 * Simple Redis connection test script
 * Run with: node scripts/test-redis.js
 */

import Redis from 'ioredis';

async function testRedisConnection() {
  console.log('🧪 Testing Redis connection...');

  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
  console.log('📍 Redis URL:', redisUrl.replace(/\/\/.*@/, '//***:***@'));

  // Test with minimal configuration first
  const redis = new Redis(redisUrl, {
    connectTimeout: 5000,
    commandTimeout: 3000,
    lazyConnect: false,
    maxRetriesPerRequest: 1,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    enableAutoPipelining: false,
  });

  // Set up event listeners
  redis.on('connect', () => {
    console.log('✅ Connected to Redis');
  });

  redis.on('ready', () => {
    console.log('🚀 Redis client ready');
  });

  redis.on('error', (error) => {
    console.error('❌ Redis error:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
    });
  });

  redis.on('close', () => {
    console.log('🔌 Redis connection closed');
  });

  redis.on('reconnecting', (delay) => {
    console.log(`🔄 Redis reconnecting in ${delay}ms...`);
  });

  redis.on('end', () => {
    console.log('🔚 Redis connection ended');
  });

  try {
    // Test basic operations
    console.log('\n🧪 Testing basic operations...');

    // Ping test
    console.log('📡 Testing ping...');
    const pingResult = await redis.ping();
    console.log('✅ Ping result:', pingResult);

    // Set/Get test
    console.log('💾 Testing set/get...');
    const testKey = `test:${Date.now()}`;
    await redis.set(testKey, 'hello world', 'EX', 10);
    const value = await redis.get(testKey);
    console.log('✅ Set/Get result:', value);

    // Delete test
    console.log('🗑️ Testing delete...');
    const deleted = await redis.del(testKey);
    console.log('✅ Delete result:', deleted);

    // Info test
    console.log('📊 Testing info...');
    const info = await redis.info('server');
    const version = info.match(/redis_version:([^\r\n]+)/)?.[1];
    console.log('✅ Redis version:', version);

    console.log('\n🎉 All tests passed! Redis is working correctly.');
  } catch (error) {
    console.error('\n❌ Test failed:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
    });

    // Additional diagnostics
    console.log('\n🔍 Connection diagnostics:');
    console.log('- Redis status:', redis.status);
    console.log('- Connection options:', {
      host: redis.options.host,
      port: redis.options.port,
      connectTimeout: redis.options.connectTimeout,
      commandTimeout: redis.options.commandTimeout,
    });
  } finally {
    console.log('\n🧹 Cleaning up...');
    try {
      await redis.quit();
      console.log('✅ Redis connection closed gracefully');
    } catch (quitError) {
      console.warn('⚠️ Error during quit:', quitError.message);
      redis.disconnect();
      console.log('🔌 Redis connection force closed');
    }
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, exiting...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, exiting...');
  process.exit(0);
});

// Run the test
try {
  await testRedisConnection();
} catch (error) {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
}
