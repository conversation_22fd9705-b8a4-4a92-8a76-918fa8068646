#!/usr/bin/env node

// Test script for bug report API endpoint
import fetch from 'node-fetch';

async function testBugReport() {
  const testData = {
    title: 'Test Bug Report',
    description: 'This is a test bug report submitted via API',
    steps: '1. Open the app\n2. Click the bug button\n3. Fill the form\n4. Submit',
    expected: 'The bug report should be sent to Slack',
    actual: 'The bug report was sent to Slack successfully',
    userEmail: '<EMAIL>',
    route: '/dashboard',
    userAgent: 'Node.js Test Script',
    timestamp: new Date().toISOString(),
  };

  try {
    console.log('🐛 Testing bug report API...');
    console.log('📊 Test data:', JSON.stringify(testData, null, 2));

    const response = await fetch('http://localhost:3000/api/bug-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();
    
    console.log('\n📥 Response status:', response.status);
    console.log('📥 Response body:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log('\n✅ Bug report test successful!');
    } else {
      console.log('\n❌ Bug report test failed!');
    }
  } catch (error) {
    console.error('\n❌ Error testing bug report:', error.message);
  }
}

testBugReport();