#!/usr/bin/env node

/**
 * Redis Session Cleanup Utility
 *
 * This script helps clean up orphaned and expired sessions from Redis.
 * Run this script to manually clean up accumulated session data.
 *
 * Usage:
 *   node scripts/cleanup-redis-sessions.js
 *
 * Options:
 *   --dry-run    Show what would be cleaned up without actually deleting
 *   --all        Clean up all expired sessions (not just orphaned ones)
 */

import Redis from 'ioredis';

// Redis configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_SESSION_PREFIX = 'session:';
const REDIS_COGNITO_PREFIX = 'cognito:';

// Command line arguments
const args = new Set(process.argv.slice(2));
const isDryRun = args.has('--dry-run');
const cleanAll = args.has('--all');

async function cleanupSessions() {
  const redis = new Redis(REDIS_URL);

  try {
    console.log('✅ Connected to Redis');

    // Get all session keys
    const sessionKeys = await redis.keys(`${REDIS_SESSION_PREFIX}*`);
    console.log(`🔍 Found ${sessionKeys.length} session keys in Redis`);

    if (sessionKeys.length === 0) {
      console.log('✨ No sessions found in Redis');
      return;
    }

    let expiredCount = 0;
    let totalSize = 0;

    for (const key of sessionKeys) {
      try {
        const data = await redis.get(key);
        if (!data) continue;

        totalSize += data.length;
        const sessionData = JSON.parse(data);
        const sessionId = key.replace(REDIS_SESSION_PREFIX, '');

        // Check if session is expired
        const isExpired = sessionData.exp && Date.now() / 1000 > sessionData.exp;

        if (isExpired || cleanAll) {
          expiredCount++;

          if (isDryRun) {
            console.log(
              `🗑️  [DRY RUN] Would delete expired session: ${sessionId.slice(0, 10)}... (user: ${sessionData.email || 'unknown'})`,
            );
          } else {
            // Delete session and associated cognito data
            const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
            await Promise.all([redis.del(key), redis.del(cognitoKey)]);

            console.log(
              `🗑️  Deleted expired session: ${sessionId.slice(0, 10)}... (user: ${sessionData.email || 'unknown'})`,
            );
          }
        } else {
          const timeLeft = Math.round((sessionData.exp * 1000 - Date.now()) / (1000 * 60 * 60));
          console.log(
            `✅ Active session: ${sessionId.slice(0, 10)}... (user: ${sessionData.email || 'unknown'}, expires in ${timeLeft}h)`,
          );
        }
      } catch (error) {
        console.warn(`⚠️  Error processing session key ${key}:`, error.message);
      }
    }

    // Summary
    console.log('\n📊 Cleanup Summary:');
    console.log(`   Total sessions found: ${sessionKeys.length}`);
    console.log(`   Expired/cleaned sessions: ${expiredCount}`);
    console.log(`   Active sessions remaining: ${sessionKeys.length - expiredCount}`);
    console.log(`   Total data size: ${(totalSize / 1024).toFixed(2)} KB`);

    if (isDryRun) {
      console.log(
        '\n💡 This was a dry run. Use without --dry-run to actually delete expired sessions.',
      );
    } else if (expiredCount > 0) {
      console.log(`\n✅ Successfully cleaned up ${expiredCount} expired sessions`);
    } else {
      console.log('\n✨ No expired sessions found to clean up');
    }
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  } finally {
    await redis.disconnect();
    console.log('👋 Disconnected from Redis');
  }
}

// Show usage if help requested
if (args.has('--help') || args.has('-h')) {
  console.log(`
Redis Session Cleanup Utility

Usage:
  node scripts/cleanup-redis-sessions.js [options]

Options:
  --dry-run    Show what would be cleaned up without actually deleting
  --all        Clean up all sessions (not just expired ones)
  --help, -h   Show this help message

Examples:
  node scripts/cleanup-redis-sessions.js --dry-run
  node scripts/cleanup-redis-sessions.js
  node scripts/cleanup-redis-sessions.js --all --dry-run
`);
  return;
}

// Run the cleanup
console.log('🧹 Starting Redis session cleanup...');
if (isDryRun) {
  console.log('🔍 DRY RUN MODE - No data will be deleted');
}
if (cleanAll) {
  console.log('⚠️  ALL MODE - Will clean up all sessions (not just expired)');
}

await cleanupSessions();
