#!/bin/bash

# Simple test script for Slack feedback messages
# Usage: ./test-slack-simple.sh [type]
# Types: bug, feature, help, other

echo "🚀 Testing Slack feedback messages..."

# Check if server is running
if ! curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "❌ Error: Development server not running on localhost:3000"
    echo "Please start your development server first:"
    echo "  npm run dev"
    exit 1
fi

FEEDBACK_TYPE=${1:-bug}

case $FEEDBACK_TYPE in
    bug)
        DATA='{
            "type": "bug",
            "issue": "Test Bug: Login button not working",
            "details": "The login button on the homepage is not responding to clicks.",
            "steps": "1. Navigate to homepage\\n2. Click login button\\n3. No response",
            "priority": "high",
            "userEmail": "<EMAIL>",
            "page": "/",
            "browser": "Chrome 120.0.0",
            "timestamp": "'$(date -Iseconds)'",
            "orgName": "Test Org",
            "orgId": "test-org-123",
            "userRole": "admin"
        }'
        ;;
    feature)
        DATA='{
            "type": "feature",
            "issue": "Test Feature: Add dark mode",
            "details": "Users have requested a dark mode option for better visibility in low light conditions.",
            "steps": "N/A",
            "priority": "medium",
            "userEmail": "<EMAIL>",
            "page": "/dashboard",
            "browser": "Firefox 121.0.0",
            "timestamp": "'$(date -Iseconds)'",
            "orgName": "Feature Requesters Inc",
            "orgId": "feature-inc-456",
            "userRole": "user"
        }'
        ;;
    help)
        DATA='{
            "type": "help",
            "issue": "Test Help: How to export data?",
            "details": "User cannot find the export button for their quantum job results.",
            "steps": "1. Complete quantum job\\n2. View results\\n3. Look for export option",
            "priority": "medium",
            "userEmail": "<EMAIL>",
            "page": "/devices/quantum-jobs",
            "browser": "Safari 17.2.1",
            "timestamp": "'$(date -Iseconds)'",
            "orgName": "Help Needed Ltd",
            "orgId": "help-ltd-789",
            "userRole": "scientist"
        }'
        ;;
    other)
        DATA='{
            "type": "other",
            "issue": "Test Feedback: Great platform!",
            "details": "User wants to express satisfaction with the platform performance.",
            "steps": "N/A",
            "priority": "low",
            "userEmail": "<EMAIL>",
            "page": "/dashboard",
            "browser": "Edge 120.0.0",
            "timestamp": "'$(date -Iseconds)'",
            "orgName": "Happy Users Co",
            "orgId": "happy-co-012",
            "userRole": "professor"
        }'
        ;;
    *)
        echo "❌ Invalid feedback type: $FEEDBACK_TYPE"
        echo "Usage: $0 [bug|feature|help|other]"
        exit 1
        ;;
esac

echo "📋 Testing $FEEDBACK_TYPE feedback..."
echo "📊 Data: $DATA"

echo "📤 Sending to Slack..."
RESPONSE=$(curl -s -X POST http://localhost:3000/api/feedback \
    -H "Content-Type: application/json" \
    -d "$DATA")

echo "📥 Response: $RESPONSE"

# Check if response contains success
if echo "$RESPONSE" | grep -q "success.*true"; then
    echo "✅ SUCCESS: $FEEDBACK_TYPE feedback sent to Slack!"
else
    echo "❌ FAILED: $FEEDBACK_TYPE feedback failed"
    echo "🔍 Check server logs for more details"
fi

echo ""
echo "📝 Check your Slack channel for the message"
echo "🔍 Verify it has the correct color, emoji, and organization info"