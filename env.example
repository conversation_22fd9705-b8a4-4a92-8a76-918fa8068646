# ====================================
# Partner Dashboard Environment Variables
# ====================================
# Copy this file to .env.local and update with your values

# ====================================
# AWS Cognito Configuration
# ====================================
# Required: Your AWS Cognito User Pool ID
NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX

# Required: Your AWS Cognito App Client ID
NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID=xxxxxxxxxxxxxxxxxxxxxxxxxx

# Optional: AWS Cognito Identity Pool ID (for federated identities)
NEXT_PUBLIC_QBRAID_COGNITO_IDENTITY_POOL_ID=us-east-1:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

# Required: AWS Cognito Domain (without https://)
NEXT_PUBLIC_QBRAID_COGNITO_DOMAIN=your-cognito-domain.auth.us-east-1.amazoncognito.com

# ====================================
# Redis Configuration
# ====================================
# Required: Redis connection URL
# Format: redis://[username:password@]host[:port][/database]
# Examples:
#   Local: redis://localhost:6379
#   Cloud: redis://default:<EMAIL>:6379
REDIS_URL=redis://localhost:6379

# ====================================
# Session Configuration
# ====================================
# Optional: Session duration in hours (default: 24)
SESSION_DURATION_HOURS=24

# ====================================
# QBraid API Configuration
# ====================================
# Optional: QBraid API token for server-side requests
# Used when no user session is available
QBRAID_API_TOKEN=your-api-token-here

# Optional: QBraid API email for authentication
# Used as fallback when no token is available
QBRAID_API_EMAIL=<EMAIL>

# Optional: Referrer header for API requests
NEXT_PUBLIC_REFERRER=partner-dashboard.qbraid.com

# Required: Application URL for OAuth callbacks
NEXT_PUBLIC_APP_URL=http://localhost:3000

# ====================================
# Environment Configuration
# ====================================
# Required: Node environment (development, production, test)
NODE_ENV=development

# Optional: Custom environment flag for debugging
NEXT_PUBLIC_NODE_ENV=development

# ====================================
# OAuth Configuration (Google Sign-In)
# ====================================
# The following are automatically configured through AWS Cognito
# No additional OAuth environment variables needed

# ====================================
# AWS S3 Configuration (Required for File Uploads)
# ====================================
# Required: S3 bucket name for file uploads
AWS_S3_BUCKET_NAME=qbraid-static

# Required: AWS access credentials for S3 uploads
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key

# Required: AWS region where your S3 bucket is located
AWS_REGION=us-east-1

# Optional: Public S3 bucket name for frontend URL generation
NEXT_PUBLIC_S3_BUCKET_NAME=qbraid-static

# Optional: Public AWS region for frontend URL generation
NEXT_PUBLIC_AWS_REGION=us-east-1

# ====================================
# Optional: Development/Debug Settings
# ====================================
# Enable debug logging (remove in production)
# DEBUG=true

# ====================================
# Notes
# ====================================
# 1. All NEXT_PUBLIC_* variables are exposed to the browser
# 2. Server-only variables (like REDIS_URL) should not have NEXT_PUBLIC_ prefix
# 3. In production, ensure all sensitive values are properly secured
# 4. Redis is required for session storage; the app will fail without it
# 5. AWS Cognito configuration must match your User Pool settings
# 6. The QBraid API uses https://api.qbraid.com/api as the base URL
# 7. S3 configuration is required for file uploads to work 