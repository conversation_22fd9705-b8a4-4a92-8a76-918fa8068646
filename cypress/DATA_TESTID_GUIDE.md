# Data-testid Attributes Guide for Team and Provider E2E Tests

## Team Management Components

### 1. Team Page (`app/(dashboard)/team/page.tsx`) ✅
- Added: `team-stats-container` - Main stats container
- Added: `total-members-stat` - Total members card
- Added: `active-users-stat` - Active users card  
- Added: `pending-invitations-stat` - Pending invites card
- Added: `active-roles-stat` - Active roles card
- Added: `invite-member-btn` - Invite member button
- Added: `team-members-table` - Team members table container
- Added: `roles-permissions-container` - Roles tab container
- Added: `activity-log-container` - Activity log container

### 2. Invite Modal (`components/team/invite-modal.tsx`)
- To Add: `invite-member-modal` - Main modal container
- To Add: `invite-email-input` - Email input field
- To Add: `invite-role-select` - Role select dropdown
- To Add: `send-invite-btn` - Send invitation button
- To Add: `cancel-invite-btn` - Cancel button
- To Add: `multiple-invites-container` - Multiple invites tab
- To Add: `bulk-emails-textarea` - Bulk invites textarea
- To Add: `bulk-role-select` - Bulk role select

### 3. Team Member Row (`components/team/team-member-row.tsx`)
- To Add: `member-row-{userId}` - Individual member row
- To Add: `member-email-{userId}` - Member email display
- To Add: `member-role-{userId}` - Member role display
- To Add: `change-role-btn-{userId}` - Change role button
- To Add: `remove-user-btn-{userId}` - Remove user button
- To Add: `reinvite-btn-{userId}` - Reinvite button
- To Add: `cancel-invite-btn-{userId}` - Cancel invite button

### 4. Change Role Modal
- To Add: `change-role-modal` - Role change modal
- To Add: `current-role-display` - Current role display
- To Add: `new-role-select` - New role selection
- To Add: `confirm-role-change-btn` - Confirm change button

### 5. Remove User Modal
- To Add: `remove-user-modal` - Remove user confirmation
- To Add: `remove-confirmation-text` - Confirmation message
- To Add: `remove-warning-text` - Warning message
- To Add: `confirm-remove-btn` - Confirm removal button

### 6. Roles Tab (`components/team/roles-tab.tsx`)
- To Add: `role-hierarchy-table` - Role hierarchy table
- To Add: `role-{roleName}` - Individual role rows
- To Add: `permissions-{roleName}` - Role permissions display

### 7. Activity Log Tab (`components/team/activity-log-tab.tsx`)
- To Add: `activity-entries` - Activity entries container
- To Add: `activity-entry-{id}` - Individual activity entries
- To Add: `activity-filter-select` - Activity filter dropdown
- To Add: `activity-search-input` - Activity search input
- To Add: `activity-entry-role-change-{id}` - Role change entries

### 8. Search and Pagination
- To Add: `team-search-input` - Search input field
- To Add: `search-btn` - Search button
- To Add: `pagination-controls` - Pagination container
- To Add: `page-info` - Page information display
- To Add: `next-page-btn` - Next page button
- To Add: `prev-page-btn` - Previous page button
- To Add: `no-results-message` - No results message

### 9. Bulk Actions
- To Add: `bulk-actions-menu` - Bulk actions menu
- To Add: `select-user-{userId}` - User checkbox
- To Add: `bulk-change-role-btn` - Bulk role change button
- To Add: `bulk-role-modal` - Bulk role modal
- To Add: `selected-users-count` - Selected count display
- To Add: `confirm-bulk-role-btn` - Confirm bulk role change

### 10. User Limits
- To Add: `user-limit-warning` - User limit warning
- To Add: `remaining-slots-info` - Remaining slots info

## Provider Management Components

### 1. Provider Page (`app/(dashboard)/providers/page.tsx`)
- To Add: `provider-onboarding` - Onboarding flow container
- To Add: `create-provider-btn` - Create provider button
- To Add: `provider-management` - Management dashboard
- To Add: `provider-overview-card` - Provider overview card
- To Add: `provider-name` - Provider name display
- To Add: `provider-description` - Provider description
- To Add: `provider-status` - Provider status badge
- To Add: `provider-website` - Provider website link
- To Add: `edit-provider-btn` - Edit provider button

### 2. Provider Form Modal
- To Add: `provider-form-modal` - Provider form modal
- To Add: `provider-name-input` - Provider name input
- To Add: `provider-description-input` - Description input
- To Add: `provider-status-select` - Status select
- To Add: `provider-website-input` - Website input
- To Add: `logo-upload-input` - Logo upload input
- To Add: `submit-provider-btn` - Submit button

### 3. Provider Settings
- To Add: `provider-settings` - Settings panel
- To Add: `self-service-toggle` - Self-service toggle
- To Add: `approval-toggle` - Approval toggle
- To Add: `max-devices-input` - Max devices input
- To Add: `job-queue-select` - Job queue select
- To Add: `notifications-toggle` - Notifications toggle
- To Add: `rate-limit-input` - Rate limit input
- To Add: `save-settings-btn` - Save settings button

### 4. Provider Devices
- To Add: `provider-devices-table` - Devices table
- To Add: `device-row-{deviceId}` - Device rows
- To Add: `device-name-{deviceId}` - Device name
- To Add: `device-type-{deviceId}` - Device type
- To Add: `device-status-{deviceId}` - Device status
- To Add: `device-qubits-{deviceId}` - Qubit count
- To Add: `add-device-btn` - Add device button
- To Add: `device-form-modal` - Device form modal
- To Add: `device-name-input` - Device name input
- To Add: `device-type-select` - Device type select
- To Add: `device-qubits-input` - Qubits input
- To Add: `submit-device-btn` - Submit device button

### 5. Access Control
- To Add: `access-control-panel` - Access control panel
- To Add: `access-rule-{ruleId}` - Access rule rows
- To Add: `org-name-{ruleId}` - Organization name
- To Add: `access-level-{ruleId}` - Access level
- To Add: `add-access-rule-btn` - Add rule button

### 6. Billing Settings
- To Add: `billing-settings` - Billing panel
- To Add: `billing-plan` - Current plan
- To Add: `rate-per-hour` - Rate display
- To Add: `currency` - Currency display
- To Add: `billing-cycle` - Billing cycle
- To Add: `auto-billing-toggle` - Auto-billing toggle
- To Add: `invoice-email` - Invoice email
- To Add: `edit-billing-btn` - Edit billing button

### 7. Analytics
- To Add: `provider-analytics` - Analytics dashboard
- To Add: `total-jobs-metric` - Total jobs
- To Add: `success-rate-metric` - Success rate
- To Add: `avg-duration-metric` - Average duration
- To Add: `total-usage-metric` - Total usage
- To Add: `usage-chart` - Usage chart
- To Add: `monthly-trend-chart` - Monthly trend
- To Add: `popular-devices-chart` - Popular devices

### 8. Export Functionality
- To Add: `export-data-btn` - Export button
- To Add: `export-modal` - Export modal
- To Add: `export-devices-checkbox` - Export devices option
- To Add: `export-jobs-checkbox` - Export jobs option
- To Add: `export-format-select` - Format select
- To Add: `date-range-select` - Date range select
- To Add: `confirm-export-btn` - Confirm export button

### 9. Loading and Error States
- To Add: `provider-loading` - Loading state
- To Add: `provider-error` - Error state
- To Add: `refresh-page-btn` - Refresh button
- To Add: `access-denied` - Access denied message
- To Add: `return-dashboard-btn` - Return to dashboard button

### 10. Validation Errors
- To Add: `name-error` - Name validation error
- To Add: `description-error` - Description validation error
- To Add: `website-error` - Website validation error
- To Add: `email-error` - Email validation error

## Implementation Priority

1. **High Priority** - Core functionality tests:
   - Team member invitation and management
   - Role changes and removal
   - Provider creation and editing

2. **Medium Priority** - Additional features:
   - Bulk operations
   - Search and filtering
   - Settings and configuration

3. **Low Priority** - Nice to have:
   - Analytics and charts
   - Export functionality
   - Advanced settings

## Notes

- Use dynamic test IDs for list items (e.g., `member-row-{userId}`)
- Ensure test IDs are unique within their scope
- Add test IDs to interactive elements only (buttons, inputs, selects)
- Consider accessibility when adding test IDs
- Test both success and error scenarios