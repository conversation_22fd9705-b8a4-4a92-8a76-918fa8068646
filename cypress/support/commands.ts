/// <reference types="cypress" />

// User role types
export type UserRole = 'owner' | 'admin' | 'manager' | 'viewer';

// User credentials for each role (will be loaded from fixture)
const USER_CREDENTIALS = {
  owner: {
    email: Cypress.env('OWNER_EMAIL') || '<EMAIL>',
    password: Cypress.env('OWNER_PASSWORD') || '<EMAIL>',
  },
  admin: {
    email: Cypress.env('ADMIN_EMAIL') || '<EMAIL>',
    password: Cypress.env('ADMIN_PASSWORD') || '<EMAIL>',
  },
  manager: {
    email: Cypress.env('MANAGER_EMAIL') || '<EMAIL>',
    password: Cypress.env('MANAGER_PASSWORD') || '<EMAIL>',
  },
  viewer: {
    email: Cypress.env('VIEWER_EMAIL') || '<EMAIL>',
    password: Cypress.env('VIEWER_PASSWORD') || '<EMAIL>',
  },
} as const;

// Custom login command
Cypress.Commands.add('login', (role: UserRole) => {
  const credentials = USER_CREDENTIALS[role];

  cy.session([role, credentials.email], () => {
    cy.visit('/signin');
    cy.get('[data-testid="email-input"]').type(credentials.email);
    cy.get('[data-testid="password-input"]').type(credentials.password);
    cy.get('[data-testid="signin-submit-button"]').click();
    cy.url().should('eq', 'http://localhost:3000/');
  });
});

// Custom logout command
Cypress.Commands.add('logout', () => {
  // Visit dashboard to access logout
  cy.visit('/');
  // /html/body/div[2]/div/div[5]
  // find class="lucide lucide-log-out" and click it
  // Look for user menu or logout button

  cy.get('sidebar').within(() => {
    //its not a button, its a div
    cy.get('div[data-sidebar="menu-button"]').click();
    cy.get('div[data-sidebar="logout-button"]').click();
  });
  cy.url().should('include', '/signin');
});

// Clear authentication state completely
Cypress.Commands.add('clearAuthState', () => {
  // Clear all cookies with various domain patterns
  cy.clearCookies({ domain: 'localhost' });
  cy.clearCookies({ domain: '.localhost' });
  cy.clearCookies();

  // Clear localStorage
  cy.clearLocalStorage();

  // Clear sessionStorage using window command
  cy.window().then((win) => {
    win.sessionStorage.clear();
  });

  // Visit signin page to ensure clean state
  cy.visit('/signin');

  // Wait for any redirects to complete
  cy.url().should('include', '/signin');
});

// Command to check if element is visible based on role
Cypress.Commands.add(
  'shouldSeeElement',
  { prevSubject: 'element' },
  (subject, role: UserRole, shouldSee = true) => {
    const visibility = shouldSee ? 'be.visible' : 'not.exist';
    cy.wrap(subject).should(visibility);
  },
);

// Command to check device access
Cypress.Commands.add('checkDeviceAccess', (deviceName: string, hasAccess: boolean) => {
  cy.visit('/devices');
  if (hasAccess) {
    cy.contains(deviceName).should('be.visible');
  } else {
    cy.contains(deviceName).should('not.exist');
  }
});

// Command to check navigation menu items
Cypress.Commands.add('checkNavigationAccess', (role: UserRole) => {
  const navigationPermissions = {
    owner: ['dashboard', 'devices', 'team', 'profile', 'admin'],
    admin: ['dashboard', 'devices', 'team', 'profile'],
    manager: ['dashboard', 'devices', 'team', 'profile'],
    viewer: ['dashboard', 'devices', 'profile'],
  } as const;

  const allowedItems = navigationPermissions[role];

  cy.get('[data-testid="sidebar"]').within(() => {
    allowedItems.forEach((item) => {
      cy.contains(new RegExp(item, 'i')).should('be.visible');
    });
  });
});

// Signup command
Cypress.Commands.add('signup', (userData: { name: string; email: string; password: string }) => {
  cy.visit('/signup');

  // Fill out signup form
  cy.get('[data-testid="name-input"]').type(userData.name);
  cy.get('[data-testid="email-input"]').type(userData.email);
  cy.get('[data-testid="password-input"]').type(userData.password);
  cy.get('[data-testid="confirm-password-input"]').type(userData.password);

  // Submit form
  cy.get('[data-testid="signup-submit-button"]').click();

  // Should redirect to verify page
  cy.url().should('include', '/verify');
});

// Signin command with credentials
Cypress.Commands.add('signin', (email: string, password: string) => {
  cy.visit('/signin');

  // Wait for CSRF token to be available
  cy.get('input[name="csrfToken"]', { timeout: 10_000 }).should('exist');

  // Fill out signin form
  cy.get('[data-testid="email-input"]').type(email);
  cy.get('[data-testid="password-input"]').type(password);

  // Intercept the form submission
  cy.intercept('POST', '/signin').as('signinRequest');

  // Submit form
  cy.get('[data-testid="signin-submit-button"]').click();

  // Wait for request to complete
  cy.wait('@signinRequest', { timeout: 10_000 });

  // Should redirect to root (dashboard)
  cy.url().should('eq', 'http://localhost:3000/');
});

// Check form validation error
Cypress.Commands.add('shouldHaveValidationError', (field: string, message: string) => {
  cy.get(`input[name="${field}"]`).parents('form').contains(message).should('be.visible');
});

// Check alert message
Cypress.Commands.add('shouldSeeAlert', (message: string, type: 'error' | 'success' = 'error') => {
  const alertClass = type === 'error' ? 'bg-red-50' : 'bg-green-50';
  cy.get(`[class*="${alertClass}"]`).contains(message).should('be.visible');
});

// Initiate password reset
Cypress.Commands.add('initiatePasswordReset', (email: string) => {
  cy.visit('/forgot-password');
  cy.get('input[name="email"]').type(email);
  cy.get('button[type="submit"]').click();
  cy.shouldSeeAlert('Password reset email sent', 'success');
});

// Complete password reset
Cypress.Commands.add(
  'completePasswordReset',
  (email: string, code: string, newPassword: string) => {
    cy.visit('/reset-password');
    cy.get('input[name="email"]').type(email);
    cy.get('input[name="code"]').type(code);
    cy.get('input[name="password"]').type(newPassword);
    cy.get('input[name="confirmPassword"]').type(newPassword);
    cy.get('button[type="submit"]').click();
    cy.url().should('include', '/signin');
  },
);

// Check password requirements
Cypress.Commands.add('checkPasswordRequirements', () => {
  cy.visit('/signup');
  const passwordInput = cy.get('input[name="password"]');

  // Test each requirement
  const requirements = [
    { password: 'short', message: 'at least 8 characters' },
    { password: 'longbutnonumber', message: 'at least one number' },
    { password: 'longwithnumber1', message: 'at least one special character' },
  ];

  requirements.forEach(({ password, message }) => {
    passwordInput.clear().type(password);
    cy.contains(message).should('be.visible');
  });

  // Test valid password
  passwordInput.clear().type('ValidPassword123!');
  cy.contains('at least 8 characters').should('not.exist');
});

// Login with credentials from fixture
Cypress.Commands.add('loginWithFixture', (role: UserRole) => {
  cy.fixture('test-users').then((testData) => {
    const user = testData.users[role];
    cy.session([role, user.email], () => {
      cy.visit('/signin');
      cy.get('input[name="email"]').type(user.email);
      cy.get('input[name="password"]').type(user.password);
      cy.get('button[type="submit"]').click();
      cy.url().should('eq', 'http://localhost:3000/');
    });
  });
});

// Get user credentials from fixture
Cypress.Commands.add('getUserCredentials', (role: UserRole) => {
  return cy.fixture('test-users').then((testData) => {
    return testData.users[role];
  });
});

// Dev cookie name is 'session' (prod is '__Secure-session').
// This helper is useful if SESSION_STORAGE_MODE=cookies or Redis is not healthy locally.
Cypress.Commands.add('loginBySessionCookie', (sessionValue: string = 'aaaaaaaaaaaaaaaa') => {
  cy.setCookie('session', sessionValue, { path: '/' });
});

// Creates a real session via dev-only API and sets cookie, compatible with Redis mode.
Cypress.Commands.add(
  'loginByApi',
  (user: { email?: string; userId?: string; username?: string } = {}) => {
    cy.request('POST', '/api/debug/test-login', user as any)
      .its('status')
      .should('eq', 200);
  },
);

// Logs in through the UI to exercise the full auth flow (happy path)
Cypress.Commands.add('loginViaUI', (email: string, password: string) => {
  // Ensure CSRF token is loaded before submitting
  cy.intercept('GET', '/api/csrf-token').as('csrf');
  cy.visit('/signin');
  cy.wait('@csrf');
  cy.location('pathname').should('eq', '/signin');
  // Fill inputs by name to avoid type toggles changing selectors
  cy.get('input[name="email"]')
    .should('be.visible')
    .and('not.be.disabled')
    .clear()
    .type(email, { delay: 0 });
  cy.get('input[name="password"]')
    .should('be.visible')
    .and('not.be.disabled')
    .clear()
    .type(password, { delay: 0 });
  // Wait for submit to be enabled (csrf loaded and ready)
  cy.get('button[type="submit"]').should('not.be.disabled');
  // Prefer Enter key to avoid any React dev guard against programmatic submission
  cy.get('input[name="password"]').type('{enter}');
  // Wait for redirect off /signin then ensure readiness
  cy.location('pathname', { timeout: 15_000 }).should('not.eq', '/signin');
  // Ensure app session and tokens are ready for subsequent API routes
  cy.ensureAppSessionReady();
});

// Waits until server-side session and tokens are usable by API routes
Cypress.Commands.add('ensureAppSessionReady', () => {
  // Soft probe session endpoint; do not hard-fail if still initializing
  cy.request({ url: '/api/auth/session', failOnStatusCode: false }).then((res) => {
    Cypress.log({ name: 'auth/session', message: `status ${res.status}` });
  });

  // Probe tokens; do not hard-fail to avoid flakiness
  cy.request({ url: '/api/debug/tokens', failOnStatusCode: false }).then((res) => {
    const hasTokens = !!(res.body?.tokens?.hasIdToken || res.body?.tokens?.hasAccessToken);
    if (!hasTokens) {
      cy.wait(750);
      cy.request({ url: '/api/debug/tokens', failOnStatusCode: false }).then((r2) => {
        Cypress.log({
          name: 'debug/tokens',
          message: `idToken=${!!r2.body?.tokens?.hasIdToken} accessToken=${!!r2.body?.tokens?.hasAccessToken}`,
        });
      });
    }
  });
});

// Sets localStorage.currentOrgId to a valid org from server permissions
Cypress.Commands.add('ensureOrgContext', () => {
  cy.request({ url: '/api/auth/permissions', failOnStatusCode: false }).then((res) => {
    const orgRoles = res.body?.orgRoles || {};
    const orgIds = Object.keys(orgRoles);
    if (orgIds.length > 0) {
      const firstOrg = orgIds[0];
      cy.window().then((win) => {
        win.localStorage.setItem('currentOrgId', firstOrg);
      });
    }
  });
});

Cypress.Commands.add('findByTestId', (testdata: string) => {
  cy.get(`[data-testid="${testdata}"]`);
});

// ----- Role stubs -----
// Admin role stub for external role check route
Cypress.Commands.add('stubAdminRole', () => {
  cy.intercept('GET', '/api/user/role', { statusCode: 200, body: 'admin' }).as('getUserRole');
  cy.intercept('GET', '/api/user/get-role', { statusCode: 200, body: 'admin' }).as(
    'getUserRoleAlias',
  );
});

// Non-admin role stub for external role check route
Cypress.Commands.add('stubUserRole', (role: string = 'member') => {
  cy.intercept('GET', '/api/user/role', { statusCode: 200, body: role }).as('getUserRole');
  cy.intercept('GET', '/api/user/get-role', { statusCode: 200, body: role }).as('getUserRoleAlias');
});

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      login(role: UserRole): Chainable<void>;
      logout(): Chainable<void>;
      clearAuthState(): Chainable<void>;
      shouldSeeElement(role: UserRole, shouldSee?: boolean): Chainable<JQuery<HTMLElement>>;
      checkDeviceAccess(deviceName: string, hasAccess: boolean): Chainable<void>;
      checkNavigationAccess(role: UserRole): Chainable<void>;
      signup(userData: { name: string; email: string; password: string }): Chainable<void>;
      signin(email: string, password: string): Chainable<void>;
      shouldHaveValidationError(field: string, message: string): Chainable<void>;
      shouldSeeAlert(message: string, type?: 'error' | 'success'): Chainable<void>;
      initiatePasswordReset(email: string): Chainable<void>;
      completePasswordReset(email: string, code: string, newPassword: string): Chainable<void>;
      checkPasswordRequirements(): Chainable<void>;
      loginWithFixture(role: UserRole): Chainable<void>;
      getUserCredentials(role: UserRole): Chainable<any>;
      loginBySessionCookie(sessionValue?: string): Chainable;
      loginByApi(user?: { email?: string; userId?: string; username?: string }): Chainable;
      loginViaUI(email: string, password: string): Chainable;
      ensureAppSessionReady(): Chainable;
      ensureOrgContext(): Chainable;
      findByTestId(testdata: string): Chainable<JQuery<HTMLElement>>;
      /**
       * Stubs GET /api/user/role (and alias route as getUserRole) with an admin role.
       * It also covers /api/user/get-role for safety.
       */
      stubAdminRole(): Chainable<void>;
      stubUserRole(role?: string): Chainable<void>;
    }
  }
}
