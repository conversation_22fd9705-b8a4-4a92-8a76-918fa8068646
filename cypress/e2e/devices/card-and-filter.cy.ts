/// <reference types="cypress" />

// Covers selecting device cards and deep-linking via id query param
describe('Devices page - card selection and deep link', () => {
  const orgId = '686e957b8573f25916e806a7';

  beforeEach(() => {
    cy.loginByApi({});
    // Avoid 401s from external role check
    cy.stubAdminRole();
    cy.intercept('GET', '/api/auth/permissions*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          roles: ['admin'],
          permissions: ['view:devices'],
          orgRoles: {
            [orgId]: { orgId, orgName: 'Test Org', role: 'admin', permissions: ['view:devices'] },
          },
          orgContext: {
            currentOrg: { orgId, orgName: 'Test Org', role: 'admin' },
          },
        },
      });
    }).as('getPermissions');
    cy.intercept('GET', '/api/user/organizations/0/50', {
      statusCode: 200,
      body: {
        success: true,
        organizations: [
          { orgId, orgName: 'Test Org', role: 'admin', accepted: true, invited: false },
        ],
        pagination: { currentPage: 0, limit: 50, totalPages: 1, totalOrganizations: 1 },
      },
    }).as('getUserOrgs');

    cy.intercept('GET', '/api/user/profile', {
      statusCode: 200,
      body: { email: '<EMAIL>' },
    }).as('getProfile');
  });

  it('navigates to device by query id and highlights the correct card', () => {
    const devices = [
      {
        qrn: 'qrn_1',
        name: 'Alpha',
        type: 'QPU',
        paradigm: 'gate_model',
        deviceDescription: 'desc',
        numberQubits: '16',
        status: 'ONLINE',
        providerId: 'aws',
        modality: 'superconducting',
        processorType: 'rigetti',
        pricing: { perMinute: '1', perTask: '0', perShot: '0.01' },
        vendor: 'aws',
        pendingJobs: 0,
        runInputTypes: ['qasm'],
        noiseModels: [],
        verified: true,
      },
      {
        qrn: 'qrn_2',
        name: 'Beta',
        type: 'QPU',
        paradigm: 'gate_model',
        deviceDescription: 'desc',
        numberQubits: '32',
        status: 'ONLINE',
        providerId: 'aws',
        modality: 'superconducting',
        processorType: 'rigetti',
        pricing: { perMinute: '1', perTask: '0', perShot: '0.01' },
        vendor: 'aws',
        pendingJobs: 0,
        runInputTypes: ['qasm'],
        noiseModels: [],
        verified: true,
      },
    ];

    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: devices,
    }).as('getOrgDevices');

    cy.visit(`/devices?id=${devices[1].qrn}`, {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    cy.wait('@getOrgDevices');

    // Ensure the target card exists; page already loaded with id so no need to click
    cy.get(`[data-testid="device-card-${devices[1].qrn}"]`).should('exist');

    // Wait for tabs to render (by test id or label) and switch tabs
    cy.get('[data-testid="devices-page"]')
      .should('be.visible')
      .within(() => {
        cy.contains('Device Details').should('be.visible');
      });
    cy.get('[data-testid="device-tabs"]').should('exist');
    cy.get('[data-testid="tab-jobs"],button:contains("Jobs Overview")').first().click();
    cy.get('[data-testid="tab-content-jobs"]').should('be.visible');
  });
});
