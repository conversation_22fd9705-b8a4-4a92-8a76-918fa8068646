/// <reference types="cypress" />

describe('Devices page - empty state', () => {
  const orgId = '686e957b8573f25916e806a7';

  beforeEach(() => {
    // Prefer fast login shortcut in CI/local dev
    cy.loginByApi({});
    cy.intercept('GET', '/api/auth/permissions*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          roles: ['admin'],
          permissions: ['view:devices'],
          orgRoles: {
            [orgId]: { orgId, orgName: 'Test Org', role: 'admin', permissions: ['view:devices'] },
          },
          orgContext: {
            currentOrg: { orgId, orgName: 'Test Org', role: 'admin' },
          },
        },
      });
    }).as('getPermissions');

    // Ensure OrgCheck sees at least one accepted organization
    cy.intercept('GET', '/api/user/organizations/0/50', {
      statusCode: 200,
      body: {
        success: true,
        organizations: [
          { orgId, orgName: 'Test Org', role: 'admin', accepted: true, invited: false },
        ],
        pagination: { currentPage: 0, limit: 50, totalPages: 1, totalOrganizations: 1 },
      },
    }).as('getUserOrgs');

    // Avoid profile 500s in logs
    cy.intercept('GET', '/api/user/profile', {
      statusCode: 200,
      body: { email: '<EMAIL>' },
    }).as('getProfile');
  });

  it('shows empty state when no org devices are returned', () => {
    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: [],
    }).as('getOrgDevices');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    // No explicit wait on device fetch; rely on UI state instead

    cy.contains('No devices found').should('be.visible');
  });
});
