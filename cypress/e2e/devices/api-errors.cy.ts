/// <reference types="cypress" />

describe('Devices page - API error handling', () => {
  const orgId = '686e957b8573f25916e806a7';

  beforeEach(() => {
    cy.loginByApi({});
    cy.stubAdminRole();
    cy.intercept('GET', '/api/auth/permissions*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          roles: ['admin'],
          permissions: ['view:devices'],
          orgRoles: {
            [orgId]: { orgId, orgName: 'Test Org', role: 'admin', permissions: ['view:devices'] },
          },
          orgContext: {
            currentOrg: { orgId, orgName: 'Test Org', role: 'admin' },
          },
        },
      });
    }).as('getPermissions');
    cy.intercept('GET', '/api/user/organizations/0/50', {
      statusCode: 200,
      body: {
        success: true,
        organizations: [
          { orgId, orgName: 'Test Org', role: 'admin', accepted: true, invited: false },
        ],
        pagination: { currentPage: 0, limit: 50, totalPages: 1, totalOrganizations: 1 },
      },
    }).as('getUserOrgs');

    cy.intercept('GET', '/api/user/profile', {
      statusCode: 200,
      body: { email: '<EMAIL>' },
    }).as('getProfile');
  });

  it('handles 500 error from devices endpoint gracefully', () => {
    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 500,
      body: { error: 'Failed to fetch organization quantum devices', data: [] },
    }).as('getOrgDevices');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    // With a 500 the page renders the empty devices state
    cy.contains('No devices found').should('be.visible');
  });
});
