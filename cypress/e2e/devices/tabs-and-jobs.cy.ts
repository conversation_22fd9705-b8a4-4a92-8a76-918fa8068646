/// <reference types="cypress" />

describe('Devices page - tabs and jobs interactions (stubbed jobs)', () => {
  const orgId = '686e957b8573f25916e806a7';

  beforeEach(() => {
    cy.loginByApi({});
    cy.stubAdminRole();
    cy.intercept('GET', '/api/auth/permissions*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          roles: ['admin'],
          permissions: ['view:devices'],
          orgRoles: {
            [orgId]: { orgId, orgName: 'Test Org', role: 'admin', permissions: ['view:devices'] },
          },
          orgContext: {
            currentOrg: { orgId, orgName: 'Test Org', role: 'admin' },
          },
        },
      });
    }).as('getPermissions');
    cy.intercept('GET', '/api/user/organizations/0/50', {
      statusCode: 200,
      body: {
        success: true,
        organizations: [
          { orgId, orgName: 'Test Org', role: 'admin', accepted: true, invited: false },
        ],
        pagination: { currentPage: 0, limit: 50, totalPages: 1, totalOrganizations: 1 },
      },
    }).as('getUserOrgs');

    cy.intercept('GET', '/api/user/profile', {
      statusCode: 200,
      body: { email: '<EMAIL>' },
    }).as('getProfile');
  });

  it('shows jobs data table with sortable columns when jobs are present', () => {
    // Stub devices to have a predictable device id
    const device = {
      qrn: 'test_device_qrn',
      name: 'Test QPU',
      type: 'QPU',
      paradigm: 'gate_model',
      deviceDescription: 'desc',
      numberQubits: '32',
      status: 'ONLINE',
      providerId: 'aws',
      modality: 'superconducting',
      processorType: 'rigetti',
      pricing: { perMinute: '1', perTask: '0', perShot: '0.01' },
      vendor: 'aws',
      pendingJobs: 0,
      runInputTypes: ['qasm'],
      noiseModels: [],
      verified: true,
    };

    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: [device],
    }).as('getOrgDevices');

    // Stub jobs for that device
    cy.intercept('GET', `/api/quantum-jobs/${device.qrn}*`, {
      statusCode: 200,
      body: {
        jobsArray: [
          {
            id: 1,
            qbraidStatus: 'COMPLETED',
            status: 'COMPLETED',
            statusText: '',
            queuePosition: 1,
            shots: 100,
            cost: 0.25,
            timeStamps: {
              createdAt: new Date().toISOString(),
              endedAt: new Date().toISOString(),
              executionDuration: 10,
            },
            experimentType: 'gate_model',
          },
          {
            id: 2,
            qbraidStatus: 'FAILED',
            status: 'FAILED',
            statusText: 'Bad input',
            queuePosition: 2,
            shots: 50,
            cost: 0.1,
            timeStamps: {
              createdAt: new Date().toISOString(),
              endedAt: new Date().toISOString(),
            },
            experimentType: 'gate_model',
          },
        ],
        total: 2,
        uniqueUsers: 1,
        qbraidDeviceId: device.qrn,
        deviceName: device.name,
        deviceProvider: device.vendor,
        hasJobs: true,
      },
    }).as('getJobs');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    cy.wait('@getOrgDevices');

    // Select the card for stability
    cy.get(`[data-testid="device-card-${device.qrn}"]`).click();

    // Switch to Jobs tab
    cy.get('[data-testid="tab-jobs"]').click();
    cy.get('[data-testid="tab-content-jobs"]').should('be.visible');
    cy.get('[data-testid="jobs-table"]').should('be.visible');

    // Sort by Queue Position (if table header button exists)
    cy.contains('button', 'Queue Position').click();
    cy.contains('button', 'Queue Position').click();

    // Export PDF action wired
    cy.contains('button', 'Export PDF').click({ force: true });
  });

  it('shows empty jobs message when no jobs are present', () => {
    const device = {
      qrn: 'test_device_empty_jobs',
      name: 'Empty Jobs QPU',
      type: 'QPU',
      paradigm: 'gate_model',
      deviceDescription: 'desc',
      numberQubits: '8',
      status: 'ONLINE',
      providerId: 'aws',
      modality: 'superconducting',
      processorType: 'rigetti',
      pricing: { perMinute: '1', perTask: '0', perShot: '0.01' },
      vendor: 'aws',
      pendingJobs: 0,
      runInputTypes: ['qasm'],
      noiseModels: [],
      verified: true,
    };

    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: [device],
    }).as('getOrgDevices');

    cy.intercept('GET', `/api/quantum-jobs/${device.qrn}*`, {
      statusCode: 200,
      body: {
        jobsArray: [],
        total: 0,
        uniqueUsers: 0,
        qbraidDeviceId: device.qrn,
        deviceName: device.name,
        deviceProvider: device.vendor,
        hasJobs: false,
      },
    }).as('getJobs');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    cy.wait('@getOrgDevices');
    cy.get(`[data-testid="device-card-${device.qrn}"]`).click();
    cy.get('[data-testid="tab-jobs"]').click();

    cy.get('[data-testid="jobs-table"]').within(() => {
      cy.contains('No results').should('exist');
    });
  });
});
