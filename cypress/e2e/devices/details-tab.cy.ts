/// <reference types="cypress" />

describe('Devices page - details tab content (stubbed device)', () => {
  const orgId = '686e957b8573f25916e806a7';

  beforeEach(() => {
    cy.loginByApi({});
    cy.stubAdminRole();
    cy.intercept('GET', '/api/auth/permissions*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          roles: ['admin'],
          permissions: ['view:devices'],
          orgRoles: {
            [orgId]: { orgId, orgName: 'Test Org', role: 'admin', permissions: ['view:devices'] },
          },
          orgContext: {
            currentOrg: { orgId, orgName: 'Test Org', role: 'admin' },
          },
        },
      });
    }).as('getPermissions');
    cy.intercept('GET', '/api/user/organizations/0/50', {
      statusCode: 200,
      body: {
        success: true,
        organizations: [
          { orgId, orgName: 'Test Org', role: 'admin', accepted: true, invited: false },
        ],
        pagination: { currentPage: 0, limit: 50, totalPages: 1, totalOrganizations: 1 },
      },
    }).as('getUserOrgs');

    cy.intercept('GET', '/api/user/profile', {
      statusCode: 200,
      body: { email: '<EMAIL>' },
    }).as('getProfile');
  });

  it('renders and validates QPU device details (per-minute pricing, available/public)', () => {
    const device = {
      qrn: 'qpu_qrn',
      name: 'QPU Device',
      type: 'QPU',
      paradigm: 'gate_model',
      deviceDescription: 'desc',
      numberQubits: '32',
      status: 'ONLINE',
      providerId: 'aws',
      modality: 'superconducting',
      processorType: 'rigetti',
      pricing: { perMinute: '1.50' },
      vendor: 'aws',
      pendingJobs: 3,
      runInputTypes: ['qasm', 'openqasm3'],
      noiseModels: ['depolarizing'],
      isAvailable: true,
      visibility: 'public',
      whiteListedDomains: 'example.com,example.org',
      blackListedDomains: '',
      verified: true,
    };

    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: [device],
    }).as('getOrgDevices');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    cy.wait('@getOrgDevices');

    cy.get(`[data-testid="device-card-${device.qrn}"]`).click();
    cy.get('[data-testid="tab-details"]').click();
    cy.get('[data-testid="tab-content-details"]').should('be.visible');

    // Field assertions
    cy.get('[data-testid="detail-id"]').should('contain', device.qrn);
    cy.get('[data-testid="detail-paradigm"]').should('contain', device.paradigm);
    cy.get('[data-testid="detail-modalityOrProcessor-label"]').should('contain', 'Modality:');
    cy.get('[data-testid="detail-modalityOrProcessor-value"]').should('contain', device.modality);
    cy.get('[data-testid="detail-qubits"]').should('contain', device.numberQubits);
    cy.get('[data-testid="detail-run-input-types"]').should('contain', 'qasm');
    cy.get('[data-testid="detail-noise-models"]').should('contain', 'depolarizing');
    cy.get('[data-testid="detail-provider"]').should('contain', 'aws');
    cy.get('[data-testid="detail-vendor"]').should('contain', 'aws');
    cy.get('[data-testid="detail-cost-per-minute"]').should('contain', '$1.50');
    cy.get('[data-testid="detail-pending-jobs"]').should('contain', '3');
    cy.get('[data-testid="detail-queue-status"]').should('exist');
    cy.get('[data-testid="detail-availability"]').should('have.attr', 'data-available', 'true');
    cy.get('[data-testid="detail-visibility"]').should('have.attr', 'data-visibility', 'public');
    // Assert whitelist using serialized data attribute for stability (tooltip is flaky in headless)
    cy.get('[data-testid="detail-whitelist"] [data-testid="detail-domains"]')
      .invoke('attr', 'data-domains-full')
      .then((attr) => {
        const full = (attr || '').split(',');
        expect(full).to.include('example.com');
        expect(full).to.include('example.org');
      });
    cy.get('[data-testid="detail-blacklist"]').should('contain', 'None');

    // Topology tab disabled
    cy.get('[data-testid="tab-topology"]').should('have.attr', 'disabled');
  });

  it('renders and validates Simulator device details (per-task/shot pricing, unavailable/private)', () => {
    const device = {
      qrn: 'sim_qrn',
      name: 'Sim Device',
      type: 'Simulator',
      paradigm: 'gate_model',
      deviceDescription: 'desc',
      numberQubits: '8',
      status: 'OFFLINE',
      providerId: { _id: 'p1', provider: 'ibm' },
      modality: '',
      processorType: 'qir',
      pricing: { perTask: '0.20', perShot: '0.001' },
      vendor: 'ibm',
      pendingJobs: 0,
      runInputTypes: 'qasm, qir',
      noiseModels: [],
      isAvailable: false,
      visibility: 'private',
      whiteListedDomains: '',
      blackListedDomains: 'blocked.com',
      verified: true,
    };

    cy.intercept('GET', `/api/orgs/${orgId}/quantum-devices*`, {
      statusCode: 200,
      body: [device],
    }).as('getOrgDevices');

    cy.visit('/devices', {
      onBeforeLoad: (win) => {
        win.localStorage.setItem('currentOrgId', orgId);
      },
    });
    cy.wait('@getPermissions');
    cy.wait('@getOrgDevices');

    cy.get(`[data-testid="device-card-${device.qrn}"]`).click();
    cy.get('[data-testid="tab-details"]').click();
    cy.get('[data-testid="tab-content-details"]').should('be.visible');

    // Field assertions for simulator
    cy.get('[data-testid="detail-modalityOrProcessor-label"]').should('contain', 'Processor Type:');
    cy.get('[data-testid="detail-modalityOrProcessor-value"]').should('contain', 'qir');
    cy.get('[data-testid="detail-provider"]').should('contain', 'ibm');
    cy.get('[data-testid="detail-vendor"]').should('contain', 'ibm');
    cy.get('[data-testid="detail-cost-per-task"]').should('contain', '$0.20');
    cy.get('[data-testid="detail-cost-per-shot"]').should('contain', '$0.001');
    cy.get('[data-testid="detail-availability"]').should('have.attr', 'data-available', 'false');
    cy.get('[data-testid="detail-visibility"]').should('have.attr', 'data-visibility', 'private');
    cy.get('[data-testid="detail-whitelist"]').should('contain', 'None');
    // Assert blacklist via serialized data attribute as well
    cy.get('[data-testid="detail-blacklist"] [data-testid="detail-domains"]')
      .invoke('attr', 'data-domains-full')
      .then((attr) => {
        const full = (attr || '').split(',');
        expect(full).to.include('blocked.com');
      });
  });
});
