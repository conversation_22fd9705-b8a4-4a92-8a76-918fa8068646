// Provider Management E2E Tests - Navigation
// Tests focus on basic page access and navigation

import { PROVIDER_MANAGEMENT_ASSUMPTIONS, setupProviderManagementTest } from './setup';

describe('Provider Management - Navigation', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-navigation'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Set up default intercepts to prevent infinite loading
    cy.intercept('GET', '**/api/orgs/*/provider*', { statusCode: 200, body: null }).as(
      'defaultOrgProvider',
    );
    cy.intercept('GET', '**/api/providers*', { statusCode: 200, body: [] }).as('defaultProviders');

    // Log test assumptions
    cy.log(
      `Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
    cy.log(
      `Required permissions: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.permissions.join(', ')}`,
    );
  });

  it('should navigate to provider management page', () => {
    setupProviderManagementTest();
    cy.url().should('include', '/providers');
  });

  it('should show onboarding flow when no provider exists', () => {
    // Intercept the specific API endpoint that returns provider data
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: null, // API returns null when no provider is assigned
    }).as('getOrgProvider');

    // Also intercept any potential providers list endpoint
    cy.intercept('GET', '**/api/orgs/*/providers', {
      statusCode: 200,
      body: [], // Empty array when no providers
    }).as('getOrgProviders');

    setupProviderManagementTest();

    // Wait for the API call to complete
    cy.wait('@getOrgProvider');

    // Verify onboarding elements appear
    cy.contains('Setup Your Provider', { timeout: 10_000 }).should('be.visible');
    cy.contains('Create Provider').should('be.visible');
  });

  it('should show provider dashboard when provider exists', () => {
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: {
        id: 'provider-1',
        provider: 'Test Quantum Provider',
        providerDescription: 'A leading quantum computing provider',
        status: 'public',
        about: 'https://quantum-provider.com',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    }).as('getOrgProvider');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Verify provider dashboard elements
    cy.contains('Test Quantum Provider').should('be.visible');
    cy.contains('Edit Details').should('be.visible');
  });

  it('should handle access denied for unauthorized users', () => {
    // Stub permissions without provider management access
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {},
      },
    });

    cy.visit('/providers');

    // Verify access denied message
    cy.contains('Access Denied').should('be.visible');
    cy.contains('Return to Dashboard').should('be.visible');
  });
});
