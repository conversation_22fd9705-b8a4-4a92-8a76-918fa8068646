/**
 * Provider Management Test Setup
 *
 * Basic Assumptions:
 * - User is logged in with Owner role and ManageProviders permission
 * - Organization may or may not have a provider assigned
 * - User can create, edit, and manage providers
 */

export const PROVIDER_MANAGEMENT_ASSUMPTIONS = {
  currentUser: {
    role: 'Owner',
    permissions: ['ManageProviders', 'ManageDevices'],
  },
  requiredFields: ['name', 'description', 'website'],
  optionalFields: ['supportEmail', 'documentation', 'sdkLink'],
};

/**
 * Simple setup for provider management tests
 */
export function setupProviderManagementTest() {
  cy.visit('/providers');
  cy.contains('Provider Management').should('be.visible');
  cy.log('Provider management test setup complete');
}

/**
 * Helper to check current provider status
 */
export function checkProviderStatus() {
  return cy.get('body').then(($body) => {
    const hasProvider = $body.find('[data-testid*="provider"], .provider-card').length > 0;
    const hasOnboarding =
      $body.text().includes('Setup Your Provider') || $body.text().includes('Create Provider');

    if (hasProvider) {
      cy.log('✓ Organization has a provider assigned');
      return cy.wrap('has_provider');
    } else if (hasOnboarding) {
      cy.log('✓ Organization needs provider setup');
      return cy.wrap('needs_provider');
    } else {
      cy.log('? Provider status unclear');
      return cy.wrap('unknown');
    }
  });
}

/**
 * Helper to open provider form modal
 */
export function openProviderForm() {
  // Look for create/edit provider buttons with various selectors
  cy.get('body').then(($body) => {
    const createButtons = $body.find(
      '[data-testid*="create"], button:contains("Create Provider"), button:contains("Add Provider")',
    );
    const editButtons = $body.find(
      '[data-testid*="edit"], button:contains("Edit"), button:contains("Edit Details")',
    );

    if (createButtons.length > 0) {
      cy.get(
        '[data-testid*="create"], button:contains("Create Provider"), button:contains("Add Provider")',
      )
        .first()
        .click();
    } else if (editButtons.length > 0) {
      cy.get('[data-testid*="edit"], button:contains("Edit"), button:contains("Edit Details")')
        .first()
        .click();
    } else {
      cy.log('No provider form buttons found');
    }
  });

  // Wait for modal to open
  cy.get('[data-testid*="modal"], [role="dialog"], .modal').should('be.visible');
}

/**
 * Helper to fill provider form with test data
 */
export function fillProviderForm(data: {
  name?: string;
  description?: string;
  website?: string;
  supportEmail?: string;
  documentation?: string;
  sdkLink?: string;
}) {
  const testData = {
    name: data.name || `Test Provider ${Date.now()}`,
    description: data.description || 'A test quantum computing provider for automated testing',
    website: data.website || 'https://example.com',
    supportEmail: data.supportEmail || '<EMAIL>',
    documentation: data.documentation || 'https://docs.example.com',
    sdkLink: data.sdkLink || 'https://github.com/example/sdk',
  };

  // Fill required fields
  cy.get('input[name="name"], input[placeholder*="name"], input[placeholder*="Provider"]')
    .clear()
    .type(testData.name);
  cy.get(
    'textarea[name="description"], textarea[placeholder*="description"], textarea[placeholder*="Describe"]',
  )
    .clear()
    .type(testData.description);
  cy.get('input[name="website"], input[type="url"], input[placeholder*="https://"]')
    .first()
    .clear()
    .type(testData.website);

  // Fill optional fields if they exist and are visible
  cy.get('body').then(($body) => {
    if ($body.find('input[name="supportEmail"], input[type="email"]').length > 0) {
      cy.get('input[name="supportEmail"], input[type="email"]').clear().type(testData.supportEmail);
    }

    if ($body.find('input[name="documentation"]').length > 0) {
      cy.get('input[name="documentation"]').clear().type(testData.documentation);
    }

    if ($body.find('input[name="sdkLink"]').length > 0) {
      cy.get('input[name="sdkLink"]').clear().type(testData.sdkLink);
    }
  });

  return testData;
}

/**
 * Helper to check if user has specific provider permissions
 */
export function checkProviderPermissions(permission: string) {
  const permissionChecks: Record<string, () => Cypress.Chainable<any>> = {
    create_providers: () =>
      cy.get('[data-testid*="create"], button:contains("Create Provider")').should('exist'),
    edit_providers: () => cy.get('[data-testid*="edit"], button:contains("Edit")').should('exist'),
    manage_providers: () => cy.contains('Provider Management').should('exist'),
    view_providers: () => cy.url().should('include', '/providers'),
  };

  const check = permissionChecks[permission];
  if (check) {
    return check();
  } else {
    cy.log(`Unknown permission check: ${permission}`);
    return cy.wrap(null);
  }
}
