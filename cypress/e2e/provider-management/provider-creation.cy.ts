// Provider Management E2E Tests - Provider Creation
// Tests focus on provider creation functionality

import { PROVIDER_MANAGEMENT_ASSUMPTIONS, setupProviderManagementTest, openProviderForm, fillProviderForm } from './setup';

describe('Provider Management - Provider Creation', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-creation'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Set up no provider state for creation tests
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 404,
      body: { success: false, error: 'No provider found' },
    }).as('getOrgProvider');

    // Log test assumptions
    cy.log(`Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
  });

  it('should open create provider modal', () => {
    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Click create button
    cy.contains('Create Provider').click();

    // Verify modal elements
    cy.contains('Create Provider').should('be.visible');
    cy.get('input[placeholder*="Provider name"]').should('be.visible');
    cy.get('textarea[placeholder*="description"]').should('be.visible');
    cy.get('input[placeholder*="website"]').should('be.visible');
  });

  it('should create new provider successfully', () => {
    // Set up API intercepts
    cy.intercept('POST', '/api/providers', {
      statusCode: 201,
      body: {
        success: true,
        data: {
          id: 'new-provider',
          provider: 'New Quantum Provider',
          providerDescription: 'Innovative quantum solutions',
          status: 'private',
          about: 'https://new-quantum.com',
          createdAt: new Date().toISOString(),
        },
        metadata: { requestId: 'test-123', processingTime: '100ms' },
      },
    }).as('createProvider');

    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 200,
      body: {
        id: 'new-provider',
        provider: 'New Quantum Provider',
        providerDescription: 'Innovative quantum solutions',
        status: 'private',
        about: 'https://new-quantum.com',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    }).as('getOrgProviderAfterCreate');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Open create modal
    openProviderForm();

    // Fill form with valid data
    fillProviderForm({
      name: 'New Quantum Provider',
      description: 'Innovative quantum solutions',
      website: 'https://new-quantum.com',
    });

    // Submit form
    cy.contains('Create Provider').click();

    // Verify API call
    cy.wait('@createProvider').then((interception) => {
      expect(interception.request.method).to.equal('POST');
    });

    // Verify success message
    cy.contains('Provider created successfully').should('be.visible');

    // Verify redirect to dashboard
    cy.wait('@getOrgProviderAfterCreate');
    cy.contains('New Quantum Provider').should('be.visible');
  });

  it('should validate required fields on creation', () => {
    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    openProviderForm();

    // Try to submit without filling required fields
    cy.contains('Create Provider').click();

    // Verify validation errors
    cy.contains('Provider name is required').should('be.visible');
    cy.contains('Description is required').should('be.visible');
    cy.contains('Website is required').should('be.visible');

    // Fill only name field
    cy.get('input[placeholder*="Provider name"]').type('Test Provider');

    // Try to submit again
    cy.contains('Create Provider').click();

    // Verify remaining validation errors
    cy.contains('Description is required').should('be.visible');
    cy.contains('Website is required').should('be.visible');
    cy.contains('Provider name is required').should('not.exist');
  });

  it('should validate URL format on creation', () => {
    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    openProviderForm();

    // Fill all fields except invalid URL
    cy.get('input[placeholder*="Provider name"]').type('Test Provider');
    cy.get('textarea[placeholder*="description"]').type('Test description');
    cy.get('input[placeholder*="website"]').type('invalid-url');

    // Try to submit
    cy.contains('Create Provider').click();

    // Verify URL validation error
    cy.contains('Please enter a valid website URL').should('be.visible');

    // Fix URL
    cy.get('input[placeholder*="website"]').clear().type('https://test-provider.com');

    // Try to submit again
    cy.contains('Create Provider').click();

    // Verify validation error is cleared
    cy.contains('Please enter a valid website URL').should('not.exist');
  });

  it('should handle API errors during creation', () => {
    cy.intercept('POST', '/api/providers', {
      statusCode: 500,
      body: {
        success: false,
        error: 'Failed to create provider',
        message: 'Internal server error',
      },
    }).as('createProviderError');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    openProviderForm();

    // Fill form with valid data
    fillProviderForm({
      name: 'Error Provider',
      description: 'This should fail',
      website: 'https://error-provider.com',
    });

    // Try to submit
    cy.contains('Create Provider').click();

    // Verify error handling
    cy.wait('@createProviderError');
    cy.contains('Failed to create provider').should('be.visible');
  });

  it('should cancel provider creation without saving', () => {
    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    openProviderForm();

    // Fill some fields
    cy.get('input[placeholder*="Provider name"]').type('Cancelled Provider');
    cy.get('textarea[placeholder*="description"]').type('This will not be saved');

    // Cancel creation
    cy.contains('Cancel').click();

    // Verify modal is closed
    cy.contains('Create Provider').should('not.exist');

    // Verify onboarding is still visible
    cy.contains('Setup Your Provider').should('be.visible');
  });
});