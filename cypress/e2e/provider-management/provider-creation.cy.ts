// Provider Management E2E Tests - Provider Creation
// Tests focus on basic provider creation functionality

import { PROVIDER_MANAGEMENT_ASSUMPTIONS } from './setup';

describe('Provider Management - Provider Creation', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-creation'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Set up no provider state for creation tests
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: null, // No provider found
    }).as('getOrgProvider');

    // Default providers intercept
    cy.intercept('GET', '**/api/providers', { statusCode: 200, body: [] }).as('getProviders');

    cy.log(
      `Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
  });

  it('should open create provider modal', () => {
    cy.visit('/providers');
    cy.wait('@getOrgProvider');

    // Verify onboarding is shown
    cy.contains('Setup Your Provider').should('be.visible');
    cy.contains('Create Provider').should('be.visible');

    // Click create button
    cy.contains('Create Provider').click();

    // Verify modal opens
    cy.get('[role="dialog"]', { timeout: 10_000 }).should('be.visible');
    cy.get('input[name="name"], input[name="provider"]').should('be.visible');
    cy.get('textarea[name="description"], textarea[name="providerDescription"]').should(
      'be.visible',
    );
    cy.get('input[name="website"], input[name="about"]').should('be.visible');
  });

  it('should create new provider successfully', () => {
    // Set up API intercepts
    cy.intercept('POST', '**/api/providers', {
      statusCode: 201,
      body: {
        success: true,
        data: {
          id: 'new-provider',
          provider: 'New Quantum Provider',
          providerDescription: 'Innovative quantum solutions',
          status: 'private',
          about: 'https://new-quantum.com',
          createdAt: new Date().toISOString(),
        },
      },
    }).as('createProvider');

    cy.visit('/providers');
    cy.wait('@getOrgProvider');

    // Open create modal
    cy.contains('Create Provider').click();
    cy.get('[role="dialog"]').should('be.visible');

    // Fill form with valid data
    cy.get('input[name="name"], input[name="provider"]').first().type('New Quantum Provider');
    cy.get('textarea[name="description"], textarea[name="providerDescription"]')
      .first()
      .type('Innovative quantum solutions');
    cy.get('input[name="website"], input[name="about"]').first().type('https://new-quantum.com');

    // Submit form
    cy.get('[role="dialog"]').contains('button', 'Create').click();

    // Verify API call was made
    cy.wait('@createProvider');

    // Just verify the form submission worked (success message may not exist)
    cy.get('[role="dialog"]').should('not.exist');
  });

  it('should show form validation', () => {
    cy.visit('/providers');
    cy.wait('@getOrgProvider');

    // Open create modal
    cy.contains('Create Provider').click();
    cy.get('[role="dialog"]').should('be.visible');

    // Try to submit empty form
    cy.get('[role="dialog"]').contains('button', 'Create').click();

    // Form should still be open (validation prevents submission)
    cy.get('[role="dialog"]').should('be.visible');
  });

  it('should handle form cancellation', () => {
    cy.visit('/providers');
    cy.wait('@getOrgProvider');

    // Open create modal
    cy.contains('Create Provider').click();
    cy.get('[role="dialog"]').should('be.visible');

    // Fill some data
    cy.get('input[name="name"], input[name="provider"]').first().type('Test Provider');

    // Cancel the form
    cy.get('[role="dialog"]').contains('button', 'Cancel').click();

    // Modal should be closed
    cy.get('[role="dialog"]').should('not.exist');

    // Should return to onboarding
    cy.contains('Setup Your Provider').should('be.visible');
  });
});
