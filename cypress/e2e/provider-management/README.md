# Provider Management Tests

This directory contains end-to-end tests for provider management functionality in the partner dashboard.

## Test Assumptions

### User Role & Permissions

- **Test User**: `<EMAIL>` (or `TEST_USER_EMAIL` env var)
- **Required Role**: Owner
- **Required Permissions**:
  - `ManageProviders` - Can create and manage providers
  - `ManageDevices` - Can manage provider devices

### Provider System Structure

- Organizations can have **one provider assigned**
- Provider creation supports both **custom providers** and **existing provider selection**
- Provider editing is available for **owned providers**

### Expected UI Elements

- Provider Management page at `/providers`
- Create/Edit provider buttons with permission guards
- Provider form modal with tabs (Create Custom vs Select Existing)

## Test Files

### `setup.ts`

Contains shared setup functions and assumptions:

- `PROVIDER_MANAGEMENT_ASSUMPTIONS` - Configuration object
- `setupProviderManagementTest()` - Common setup for provider management tests
- Helper functions for form interaction and status checking

### `auth-and-navigation.cy.ts`

Main test file covering:

- Page access and navigation
- Provider creation flow
- Provider management flow
- Error handling and edge cases

## Dynamic Test Approach

These tests are designed to be **dynamic** and **adaptive**:

✅ **Dynamic Approach**:

- Works with organizations that have or don't have providers
- Adapts to different provider statuses and permissions
- Uses flexible selectors for UI elements
- Handles both onboarding and management scenarios
- Tests real API interactions without mocks

❌ **Avoided Hardcoded Approach**:

- No specific provider IDs or names in test logic
- No mocked API responses with fixed data
- No assumptions about specific provider configurations

## Provider States Handled

### 1. **No Provider (Onboarding)**

- Shows "Setup Your Provider" interface
- Tests provider creation flow
- Tests existing provider selection

### 2. **Has Provider (Management)**

- Shows provider information and status
- Tests provider editing functionality
- Tests provider management features

### 3. **Permission Variations**

- Handles users with different permission levels
- Shows appropriate UI elements based on permissions
- Gracefully handles permission restrictions

## Running the Tests

```bash
# Run all provider management tests
npx cypress run --spec "cypress/e2e/provider-management/**/*.cy.ts"

# Run specific test file
npx cypress run --spec "cypress/e2e/provider-management/provider-creation.cy.ts"

# Run with custom test user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=yourpass npx cypress run
```

## Prerequisites Validation

Before each test suite runs, the setup validates:

1. User has provider management permissions (can access `/providers`)
2. Page loads without permission errors
3. Required UI elements are present and accessible
4. User can see management controls appropriate to their permissions

## Test Data Management

### Provider Creation

- Uses dynamic names with timestamps to avoid conflicts
- Tests with realistic provider information
- Validates required vs optional fields

### Provider Editing

- Works with existing provider data
- Tests incremental updates
- Validates form pre-population

### Image Uploads

- Tests file input components (without actual uploads)
- Validates upload UI elements
- Checks for light/dark theme logo support

## Troubleshooting

### "Access Denied" errors

- Verify the test user has Owner role
- Check that user has `ManageProviders` permission
- Ensure organization membership is active

### "No provider management controls visible"

- Check user permissions in organization settings
- Verify UI elements use expected selectors
- Check for permission guard components

### "Provider form not opening"

- Ensure provider management page loads correctly
- Check for JavaScript errors in browser console
- Verify modal/dialog components are functioning

### "Form submission failures"

- Check network connectivity for API calls
- Verify form validation is working
- Check browser console for API errors

## API Endpoints Tested

- `GET /api/providers` - Fetch available providers
- `POST /api/providers` - Create new provider
- `PATCH /api/providers/:id` - Update existing provider
- `GET /api/orgs/:orgId/provider` - Get organization's provider
- `PUT /api/orgs/:orgId/provider` - Assign provider to organization

## File Upload Testing

The tests validate file upload UI components but do not perform actual file uploads to avoid:

- S3 storage costs during testing
- Test data pollution
- Complex file cleanup requirements

Instead, tests verify:

- File input elements exist
- Upload UI components render correctly
- Form handles file selection appropriately
