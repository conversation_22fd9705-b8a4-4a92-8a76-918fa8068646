// Provider Management E2E Tests - Error Handling
// Tests focus on key error scenarios

import { PROVIDER_MANAGEMENT_ASSUMPTIONS, setupProviderManagementTest } from './setup';

describe('Provider Management - Error Handling', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-error-handling'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    cy.log(
      `Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
  });

  it('should handle loading and API errors', () => {
    // Test loading state
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: { id: 'test-provider', provider: 'Test Provider' },
      delay: 1000,
    }).as('getProviderSlow');

    setupProviderManagementTest();
    cy.contains('Loading').should('be.visible');
    cy.wait('@getProviderSlow');
    cy.contains('Test Provider').should('be.visible');

    // Test server error
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 500,
      body: { error: 'Internal server error' },
    }).as('getProviderError');

    cy.reload();
    cy.wait('@getProviderError');
    cy.contains('error', { matchCase: false }).should('be.visible');
  });

  it('should handle 404 and show onboarding', () => {
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: null, // No provider found
    }).as('getProviderNotFound');

    setupProviderManagementTest();
    cy.wait('@getProviderNotFound');

    cy.contains('Setup Your Provider').should('be.visible');
    cy.contains('Create Provider').should('be.visible');
  });

  it('should handle form submission errors', () => {
    cy.intercept('GET', '**/api/orgs/*/provider', { statusCode: 200, body: null }).as(
      'getOrgProvider',
    );
    cy.intercept('POST', '**/api/providers', {
      statusCode: 400,
      body: { error: 'Provider name already exists' },
    }).as('createProviderError');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    cy.contains('Create Provider').click();
    cy.get('input[placeholder*="name"]').type('Existing Provider');
    cy.get('textarea[placeholder*="description"]').type('Test description');
    cy.get('input[placeholder*="website"]').type('https://example.com');
    cy.contains('button', 'Create').click();

    cy.wait('@createProviderError');
    cy.contains('already exists').should('be.visible');
  });

  it('should handle permission and auth errors', () => {
    // Test permission denied
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 403,
      body: { error: 'Access denied' },
    }).as('getProviderForbidden');

    setupProviderManagementTest();
    cy.wait('@getProviderForbidden');
    cy.contains('Access Denied').should('be.visible');

    // Test auth error
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 401,
      body: { error: 'Authentication required' },
    }).as('getProviderAuthError');

    cy.reload();
    cy.wait('@getProviderAuthError');
    cy.contains('Authentication required').should('be.visible');
  });
});
