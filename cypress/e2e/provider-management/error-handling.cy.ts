// Provider Management E2E Tests - Error Handling
// Tests focus on error scenarios and edge cases

import { PROVIDER_MANAGEMENT_ASSUMPTIONS, setupProviderManagementTest } from './setup';

describe('Provider Management - Error Handling', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-error-handling'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Log test assumptions
    cy.log(`Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
  });

  it('should handle loading states', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 200,
      body: { id: 'test-provider', provider: 'Test Provider' },
      delay: 2000, // Simulate slow response
    }).as('getProviderSlow');

    setupProviderManagementTest();

    // Verify loading state
    cy.contains('Loading providers...').should('be.visible');

    // Wait for response
    cy.wait('@getProviderSlow');

    // Verify provider data loads
    cy.contains('Test Provider').should('be.visible');
    cy.contains('Loading providers...').should('not.exist');
  });

  it('should handle API errors gracefully', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 500,
      body: { success: false, error: 'Internal server error' },
    }).as('getProviderError');

    setupProviderManagementTest();
    cy.wait('@getProviderError');

    // Verify error state
    cy.contains('Provider Error').should('be.visible');
    cy.contains('Unable to load provider information').should('be.visible');
    cy.contains('Refresh Page').should('be.visible');

    // Test refresh functionality
    cy.contains('Refresh Page').click();
    cy.wait(1000);
    cy.contains('Provider Error').should('be.visible'); // Should still show error after refresh
  });

  it('should handle network timeouts', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 408,
      body: { success: false, error: 'Request timeout' },
    }).as('getProviderTimeout');

    setupProviderManagementTest();
    cy.wait('@getProviderTimeout');

    // Verify timeout error handling
    cy.contains('Request timeout').should('be.visible');
    cy.contains('Please try again').should('be.visible');
  });

  it('should handle 404 errors gracefully', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 404,
      body: { success: false, error: 'No provider found' },
    }).as('getProviderNotFound');

    setupProviderManagementTest();
    cy.wait('@getProviderNotFound');

    // Should show onboarding flow instead of error
    cy.contains('Setup Your Provider').should('be.visible');
    cy.contains('Create Provider').should('be.visible');
    cy.contains('Provider Error').should('not.exist');
  });

  it('should prevent infinite API calls', () => {
    let apiCallCount = 0;

    cy.intercept('GET', '/api/orgs/*/provider', (req) => {
      apiCallCount++;
      req.reply({
        statusCode: 404,
        body: { success: false, error: 'No provider found' },
      });
    }).as('getOrgProviderCounted');

    setupProviderManagementTest();

    // Wait for any potential retries
    cy.wait(3000);

    // Verify reasonable number of API calls
    expect(apiCallCount).to.be.lte(3);

    // Verify page is still functional
    cy.contains('Setup Your Provider').should('be.visible');
  });

  it('should handle form submission errors', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 404,
      body: { success: false, error: 'No provider found' },
    }).as('getOrgProvider');

    cy.intercept('POST', '/api/providers', {
      statusCode: 400,
      body: {
        success: false,
        error: 'Invalid request data',
        message: 'Provider name already exists',
      },
    }).as('createProviderError');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Open create modal
    cy.contains('Create Provider').click();

    // Fill form with duplicate name
    cy.get('input[placeholder*="Provider name"]').type('Existing Provider');
    cy.get('textarea[placeholder*="description"]').type('Test description');
    cy.get('input[placeholder*="website"]').type('https://example.com');

    // Try to submit
    cy.contains('Create Provider').click();

    // Verify error handling
    cy.wait('@createProviderError');
    cy.contains('Provider name already exists').should('be.visible');
  });

  it('should handle malformed API responses', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 200,
      body: 'invalid json response', // Malformed response
    }).as('getProviderMalformed');

    setupProviderManagementTest();
    cy.wait('@getProviderMalformed');

    // Should handle malformed response gracefully
    cy.contains('Provider Error').should('be.visible');
    cy.contains('Invalid response format').should('be.visible');
  });

  it('should handle permission denied errors', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 403,
      body: {
        success: false,
        error: 'Access denied',
        message: 'You do not have permission to view provider information',
      },
    }).as('getProviderForbidden');

    setupProviderManagementTest();
    cy.wait('@getProviderForbidden');

    // Should show access denied message
    cy.contains('Access Denied').should('be.visible');
    cy.contains('provider management permissions').should('be.visible');
  });

  it('should handle rate limiting', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 429,
      body: {
        success: false,
        error: 'Too many requests',
        message: 'Please wait before trying again',
      },
    }).as('getProviderRateLimited');

    setupProviderManagementTest();
    cy.wait('@getProviderRateLimited');

    // Should show rate limiting message
    cy.contains('Too many requests').should('be.visible');
    cy.contains('Please wait before trying again').should('be.visible');
  });

  it('should handle maintenance mode', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 503,
      body: {
        success: false,
        error: 'Service unavailable',
        message: 'Provider management is currently under maintenance',
      },
    }).as('getProviderMaintenance');

    setupProviderManagementTest();
    cy.wait('@getProviderMaintenance');

    // Should show maintenance message
    cy.contains('Service unavailable').should('be.visible');
    cy.contains('currently under maintenance').should('be.visible');
  });

  it('should handle authentication errors', () => {
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 401,
      body: {
        success: false,
        error: 'Authentication required',
        message: 'Please log in to continue',
      },
    }).as('getProviderAuthError');

    setupProviderManagementTest();
    cy.wait('@getProviderAuthError');

    // Should redirect to login or show auth error
    cy.contains('Authentication required').should('be.visible');
    cy.contains('Please log in').should('be.visible');
  });
});