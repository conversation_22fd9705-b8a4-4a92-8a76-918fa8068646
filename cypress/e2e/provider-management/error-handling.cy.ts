// Provider Management E2E Tests - Error Handling
// Tests focus on key error scenarios

import { PROVIDER_MANAGEMENT_ASSUMPTIONS } from './setup';

describe('Provider Management - Error Handling', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-error-handling'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    cy.log(
      `Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
  });

  it('should handle loading and API errors', () => {
    // Test loading state
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: { id: 'test-provider', provider: 'Test Provider' },
      delay: 1000,
    }).as('getProviderSlow');

    cy.visit('/providers');
    cy.wait('@getProviderSlow');
    cy.contains('Test Provider').should('be.visible');

    // Test server error
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 500,
      body: { error: 'Internal server error' },
    }).as('getProviderError');

    cy.reload();
    cy.wait('@getProviderError');
    cy.contains('error', { matchCase: false }).should('be.visible');
  });

  it('should handle 404 and show onboarding', () => {
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 200,
      body: null, // No provider found
    }).as('getProviderNotFound');

    cy.visit('/providers');
    cy.wait('@getProviderNotFound');

    cy.contains('Setup Your Provider').should('be.visible');
    cy.contains('Create Provider').should('be.visible');
  });

  it('should handle form submission errors', () => {
    cy.intercept('GET', '**/api/orgs/*/provider', { statusCode: 200, body: null }).as(
      'getOrgProvider',
    );
    cy.intercept('POST', '**/api/providers', {
      statusCode: 400,
      body: { error: 'Provider name already exists' },
    }).as('createProviderError');

    cy.visit('/providers');
    cy.wait('@getOrgProvider');

    cy.contains('Create Provider').click();

    // Wait for modal to open and use more flexible selectors
    cy.get('[role="dialog"]').should('be.visible');
    cy.get('input[name="name"], input[name="provider"]').first().type('Existing Provider');
    cy.get('textarea[name="description"], textarea[name="providerDescription"]')
      .first()
      .type('Test description');
    cy.get('input[name="website"], input[name="about"]').first().type('https://example.com');
    cy.get('[role="dialog"]').contains('button', 'Create').click();

    cy.wait('@createProviderError');
    cy.contains('already exists').should('be.visible');
  });

  it('should handle permission and auth errors', () => {
    // Test permission denied - prevent infinite retries
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 403,
      body: { error: 'Access denied' },
    }).as('getProviderForbidden');

    // Also intercept providers list to prevent additional calls
    cy.intercept('GET', '**/api/providers', {
      statusCode: 403,
      body: { error: 'Access denied' },
    }).as('getProvidersForbidden');

    cy.visit('/providers');
    cy.wait('@getProviderForbidden');
    cy.contains('Access Denied', { timeout: 10_000 }).should('be.visible');

    // Test auth error
    cy.intercept('GET', '**/api/orgs/*/provider', {
      statusCode: 401,
      body: { error: 'Authentication required' },
    }).as('getProviderAuthError');

    cy.reload();
    cy.wait('@getProviderAuthError');
    cy.contains('Authentication required', { timeout: 10_000 }).should('be.visible');
  });
});
