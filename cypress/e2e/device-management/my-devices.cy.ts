// Happy path E2E for My Devices tab focusing on real endpoints

describe('Device Management - My Devices tab (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';

  beforeEach(() => {
    const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
  });

  it('shows My Devices & Access and basic stats or empty states', () => {
    cy.visit('/device-management');

    // Ensure tab is present and selected by default
    cy.contains('My Devices & Access').should('be.visible');

    // Basic content loads (stats cards or skeletons then cards)
    // We do not stub; let it hit real API, but be resilient to empty data
    cy.contains('Total Devices').should('exist');

    // Optional: if devices are present, the grid renders; if not, it should not fail
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="device-overview-card"]').length) {
        cy.get('[data-testid="device-overview-card"]').should('exist');
      }
    });
  });

  it('shows a helpful message when no provider is associated with the organization', () => {
    // Force org provider route to return null to simulate no provider assigned
    cy.intercept('GET', /\/api\/orgs\/.+\/provider/, {
      statusCode: 200,
      body: null,
    }).as('orgProviderNull');

    cy.visit('/device-management');

    cy.contains('My Devices & Access').should('be.visible');

    cy.get('body').then(($body) => {
      const text = $body.text();
      cy.contains('No provider associated with your organization.').should('be.visible');
      // No device cards should be rendered in this state
      cy.get('[data-testid="device-overview-card"]').should('not.exist');
    });
  });
});
