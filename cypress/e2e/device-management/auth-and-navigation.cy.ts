// Happy path E2E for Device Management using real endpoints where possible

describe('Device Management - Auth and Navigation (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';

  beforeEach(() => {
    // Perform a real UI login to obtain valid Cognito tokens
    const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
  });

  it('navigates to Device Management and shows tabs', () => {
    // Minimal stubs for auth-dependent endpoints to avoid 401 during page bootstrap
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices', 'manage:devices'],
        roles: ['admin'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'test-org': {
            orgId: 'test-org',
            orgName: 'Test Org',
            role: 'admin',
            updated: new Date().toISOString(),
            permissions: ['view:devices', 'manage:devices'],
          },
        },
      },
    }).as('perm');
    cy.intercept('GET', /\/api\/user\/organizations\/\d+\/\d+/, {
      statusCode: 200,
      body: { success: true, organizations: [], pagination: { currentPage: 0, limit: 50 } },
    }).as('orgs');

    cy.visit('/device-management');

    // Header
    cy.contains('Device Management', { timeout: 10_000 }).should('be.visible');
    cy.contains('Manage quantum device access and submissions').should('be.visible');

    // Tabs
    cy.contains('My Devices & Access').should('be.visible');
    cy.contains('Available Devices').should('be.visible');
  });
});
