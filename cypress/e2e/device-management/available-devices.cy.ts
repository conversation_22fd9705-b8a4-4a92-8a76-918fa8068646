// Happy path E2E for Available Devices tab using real endpoints

describe('Device Management - Available Devices tab (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';

  beforeEach(() => {
    const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
  });

  it('switches to Available Devices tab and loads content', () => {
    cy.visit('/device-management');
    cy.contains('Available Devices').click();

    // Verify the tab switch worked by checking for expected UI from the tab component
    // We avoid stubs; allow hitting real endpoints
    cy.contains(/Available Devices/i).should('exist');
  });
});
