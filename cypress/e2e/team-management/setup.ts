/**
 * Team Management Test Setup
 *
 * Basic Assumptions:
 * - User is logged in with Owner role in their organization
 * - Organization has multiple team members with various roles
 * - User has full permissions to manage team members (add, remove, change roles)
 * - Organization structure includes roles: Owner, Admin, Manager, Viewer, Member
 */

export const TEAM_MANAGEMENT_ASSUMPTIONS = {
  // Current user assumptions
  currentUser: {
    role: 'Owner',
    permissions: [
      'manage_team',
      'change_roles',
      'remove_members',
      'invite_members',
      'view_activity_log',
    ],
  },

  // Organization structure assumptions
  organization: {
    hasMultipleMembers: true,
    availableRoles: ['Owner', 'Admin', 'Manager', 'Viewer', 'Member'],
    minimumMemberCount: 2, // At least owner + 1 other member
  },

  // Expected UI elements
  expectedElements: {
    teamMembersTable: true,
    roleChangeButtons: true,
    removeButtons: true,
    inviteButton: true,
    activityLog: true,
  },
};

/**
 * Validates that the basic assumptions are met before running team management tests
 */
export function validateTeamManagementPrerequisites() {
  cy.log('Validating team management prerequisites...');

  // Check user has owner permissions
  cy.visit('/team');
  cy.contains('Team Members').should('be.visible');

  // Verify user can see management controls (indicates owner/admin role)
  cy.get('body').then(($body) => {
    const hasManagementControls =
      $body.find('[data-testid*="invite"], button:contains("Invite"), [data-testid*="add"]')
        .length > 0 ||
      $body.find('[data-testid*="change-role"], [data-testid*="remove"]').length > 0;

    if (hasManagementControls) {
      cy.log('✓ User has team management permissions');
    } else {
      cy.log('WARNING: User may not have sufficient permissions for team management');
    }
  });

  // Verify there are team members to work with
  cy.get('body').then(($body) => {
    const memberRows = $body.find('[data-testid*="member"], tr').length;
    if (memberRows < 2) {
      cy.log('WARNING: Organization may not have enough members for comprehensive testing');
    } else {
      cy.log(`✓ Found ${memberRows} team members`);
    }
  });
}

/**
 * Common setup for team management tests
 */
export function setupTeamManagementTest() {
  // Ensure we're on the team page
  cy.visit('/team');

  // Wait for page to load
  cy.contains('Team Members').should('be.visible');

  // Validate prerequisites
  validateTeamManagementPrerequisites();

  cy.log('Team management test setup complete');
}

/**
 * Helper to find the first available team member (not the current user)
 */
export function getFirstAvailableTeamMember() {
  return cy.get('body').then(($body) => {
    // Look for team member rows, excluding the current user if possible
    const memberElements = $body
      .find('[data-testid*="member"], tr')
      .not(':contains("You"), :contains("Owner")');

    return memberElements.length > 0
      ? cy.wrap(memberElements.first())
      : cy.get('[data-testid*="member"], tr').first();
  });
}

/**
 * Helper to check if user has specific permissions
 */
export function checkUserPermissions(permission: string) {
  const permissionChecks: Record<string, () => Cypress.Chainable<any>> = {
    change_roles: () => cy.get('[data-testid*="change-role"]').should('exist'),
    remove_members: () =>
      cy.get('[data-testid*="remove"], [data-testid*="delete"]').should('exist'),
    invite_members: () =>
      cy.get('[data-testid*="invite"], button:contains("Invite")').should('exist'),
    view_activity: () => cy.contains('Activity Log').should('exist'),
  };

  const check = permissionChecks[permission];
  if (check) {
    return check();
  } else {
    cy.log(`Unknown permission check: ${permission}`);
    return cy.wrap(null);
  }
}
