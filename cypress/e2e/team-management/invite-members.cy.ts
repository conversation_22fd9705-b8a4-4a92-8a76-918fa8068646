// Happy path E2E for Team Member Invitations using real endpoints

import { TEAM_MANAGEMENT_ASSUMPTIONS, checkUserPermissions } from './setup';

const toInviteEmailRandom = () => `newmembe${Math.random().toString(36).slice(2, 7)}@example.com`;

/**
 * Team Member Invitations Tests
 *
 * Prerequisites: User has Owner role with invite_members permission
 */
describe('Team Management - Member Invitations (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Log assumptions for this test
    cy.log(`Test user: ${email} (expected role: ${TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
    cy.log('Required permission: invite_members');
  });

  it('successfully invites a multiple member', () => {
    // Visit team page and validate permissions
    cy.visit('/team');

    // Check that user has invite permissions
    checkUserPermissions('invite_members');

    // Generate random emails for testing
    const inviteEmail = toInviteEmailRandom();
    const inviteEmail2 = toInviteEmailRandom();
    cy.log(`Inviting: ${inviteEmail} and ${inviteEmail2}`);

    // Open invite modal
    cy.findByTestId('invite-member-btn').click();

    // Fill single invite form twice (one for each row)
    cy.findByTestId('invite-email-input').type(inviteEmail);
    cy.findByTestId('invite-role-select-trigger').click();
    cy.findByTestId('invite-role-select-item').contains('Manager').click();
    cy.findByTestId('add-invite-row-btn').should('be.visible');
    // where field is empty
    cy.findByTestId('add-invite-row-btn').click();
    cy.findByTestId('invite-email-input').eq(1).type(inviteEmail2);
    cy.findByTestId('invite-role-select-trigger').eq(1).click();
    cy.findByTestId('invite-role-select-item').contains('Admin').click();
    // Send invitation
    cy.findByTestId('send-invite-btn').click();

    // Verify API call
    cy.wait(1000);

    // Modal should show completed invitations
    cy.get('[data-testid="invite-member-modal"]')
      .should('be.visible')
      .find('[data-testid="recent-completed-invitations"]')
      .should('exist')
      .within(() => {
        cy.contains(inviteEmail).should('be.visible');
      });
    cy.findByTestId('close-invite-modal-btn').click();
  });

  it('should fail when inviting the same user twice', () => {
    cy.visit('/team');
    const inviteEmail = toInviteEmailRandom();
    // Open invite modal and send first invitation
    cy.findByTestId('invite-member-btn').click();
    cy.findByTestId('invite-email-input').type(inviteEmail);
    cy.findByTestId('invite-role-select-trigger').click();
    cy.findByTestId('invite-role-select-item').contains('Manager').click();
    cy.findByTestId('send-invite-btn').click();

    // Wait for first invitation to complete
    cy.wait(1000);

    // Verify first invitation was successful
    cy.get('[data-testid="invite-member-modal"]')
      .find('[data-testid="recent-completed-invitations"]')
      .should('exist')
      .within(() => {
        cy.contains(inviteEmail).should('be.visible');
      });

    // Try to invite the same user again

    cy.get('[data-testid="invite-email-input"]').eq(0).type(inviteEmail);
    cy.findByTestId('invite-role-select-trigger').eq(0).click();
    cy.findByTestId('invite-role-select-item').contains('Manager').click();
    cy.findByTestId('send-invite-btn').click();

    // Should show error for duplicate invitation
    cy.get('[data-testid="recent-completed-invitations"]').within(() => {
      cy.contains(inviteEmail)
        .parents('.flex')
        .contains(/error|failed|already/i)
        .should('be.visible');
    });
  });

  it('handles bulk invitations with email validation', () => {
    cy.visit('/team');
    cy.findByTestId('invite-member-btn').click();

    // Switch to bulk invite tab
    cy.contains('Bulk').should('be.visible').click();

    // Wait for tab to switch
    cy.wait(500);

    // upload file csv with email,role format
    cy.get('[data-testid="bulk-invite-file-input"]').selectFile(
      'cypress/fixtures/test-bulk-invite.csv',
      {
        force: true,
      },
    );

    // Wait for file to be processed
    cy.wait(1000);

    // Verify file was uploaded and invites are ready
    cy.contains(/2 invite.* ready to send/).should('be.visible');

    // Find and click the send button in the bulk tab
    cy.get('[data-testid="invite-member-modal"]')
      .find('button[type="submit"]')
      .contains(/Send 2 Invitation/)
      .scrollIntoView()
      .should('be.visible')
      .click();

    // Wait for invitations to process
    cy.wait(2000);

    // Should show success with some entries
    cy.get('[data-testid="invite-member-modal"]')
      .find('[data-testid="recent-completed-invitations"]')
      .should('exist')
      .within(() => {
        // Should have some successful invitations
        cy.contains(/<EMAIL>|<EMAIL>/).should('be.visible');
      });
  });
});
