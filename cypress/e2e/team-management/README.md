# Team Management Tests

This directory contains end-to-end tests for team management functionality in the partner dashboard.

## Test Assumptions

### User Role & Permissions
- **Test User**: `<EMAIL>` (or `TEST_USER_EMAIL` env var)
- **Required Role**: Owner
- **Required Permissions**:
  - `manage_team` - Can manage team members
  - `change_roles` - Can change member roles
  - `remove_members` - Can remove team members
  - `invite_members` - Can invite new members
  - `view_activity_log` - Can view team activity

### Organization Structure
- Organization has **multiple team members** (minimum 2: owner + 1 other member)
- Available roles: `Owner`, `Admin`, `Manager`, `Viewer`, `Member`
- Team members have various roles for comprehensive testing

### Expected UI Elements
- Team Members table/list
- Role change buttons/dropdowns
- Remove member buttons
- Invite member button
- Activity log section

## Test Files

### `setup.ts`
Contains shared setup functions and assumptions validation:
- `TEAM_MANAGEMENT_ASSUMPTIONS` - Configuration object with all assumptions
- `validateTeamManagementPrerequisites()` - Validates assumptions before tests
- `setupTeamManagementTest()` - Common setup for team management tests
- `getFirstAvailableTeamMember()` - Helper to find testable team members
- `checkUserPermissions()` - Helper to verify user permissions

### `role-management.cy.ts`
Tests for role management functionality:
- Filter team members by role
- Change user roles dynamically
- Remove team members with confirmation
- View role change history

## Dynamic Test Approach

These tests are designed to be **dynamic** rather than hardcoded:

✅ **Dynamic Approach**:
- Works with any user data in the system
- Detects current roles and selects different ones
- Finds available UI elements using multiple selectors
- Adapts to different organization structures

❌ **Avoided Hardcoded Approach**:
- No specific user IDs or emails in test logic
- No mocked API responses with fixed data
- No assumptions about specific role combinations

## Running the Tests

```bash
# Run all team management tests
npx cypress run --spec "cypress/e2e/team-management/**/*.cy.ts"

# Run specific test file
npx cypress run --spec "cypress/e2e/team-management/role-management.cy.ts"

# Run with custom test user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=yourpass npx cypress run
```

## Prerequisites Validation

Before each test suite runs, the setup validates:
1. User has team management permissions (can see management controls)
2. Organization has sufficient team members for testing
3. Required UI elements are present and accessible

If validation fails, tests will log warnings but continue to run, allowing for graceful degradation.

## Troubleshooting

### "No team members found"
- Ensure the test user's organization has at least one other member
- Check that the user has Owner or Admin role

### "No management controls visible"
- Verify the test user has Owner role
- Check organization permissions settings

### "Role change buttons not found"
- Ensure team members have changeable roles (not all Owners)
- Verify UI elements use expected data-testid attributes

### "Remove buttons not found"
- Check that user has permission to remove members
- Ensure there are removable members (not just the current user)
