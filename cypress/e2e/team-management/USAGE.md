# Quick Usage Guide - Team Management Tests

## 🚀 Running Tests

```bash
# Run all team management tests
npx cypress run --spec "cypress/e2e/team-management/**/*.cy.ts"

# Run specific test
npx cypress run --spec "cypress/e2e/team-management/role-management.cy.ts"

# Run with different user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=pass123 npx cypress run

# Open Cypress UI for debugging
npx cypress open
```

## 📝 Creating New Tests

### 1. Import the setup functions:
```typescript
import { 
  setupTeamManagementTest,           // Complete setup + navigation
  TEAM_MANAGEMENT_ASSUMPTIONS,       // Configuration object
  validateTeamManagementPrerequisites, // Validate before tests
  getFirstAvailableTeamMember,       // Find testable members
  checkUserPermissions               // Check specific permissions
} from './setup';
```

### 2. Standard test structure:
```typescript
describe('Your Test Suite', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Standard auth
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
    
    // Log assumptions
    cy.log(`User: ${email} (role: ${TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
  });

  it('your test', () => {
    // Option 1: Full setup (navigates to /team + validates)
    setupTeamManagementTest();
    
    // Option 2: Manual navigation + validation
    cy.visit('/team');
    validateTeamManagementPrerequisites();
    
    // Your test logic here...
  });
});
```

## 🔧 Helper Functions

### `checkUserPermissions(permission)`
```typescript
// Check if user can invite members
checkUserPermissions('invite_members');

// Check if user can change roles  
checkUserPermissions('change_roles');

// Check if user can remove members
checkUserPermissions('remove_members');
```

### `getFirstAvailableTeamMember()`
```typescript
// Get first team member (excluding current user)
getFirstAvailableTeamMember().then(($member) => {
  cy.wrap($member).click(); // Click on the member
});
```

### `TEAM_MANAGEMENT_ASSUMPTIONS`
```typescript
// Access configuration
const userRole = TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.role; // "Owner"
const permissions = TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.permissions;
const availableRoles = TEAM_MANAGEMENT_ASSUMPTIONS.organization.availableRoles;
```

## 🎯 Common Patterns

### Pattern 1: Test with any team member
```typescript
it('works with any member', () => {
  cy.visit('/team');
  
  getFirstAvailableTeamMember().then(($member) => {
    // Extract email from member row
    const memberText = $member.text();
    const emailMatch = memberText.match(/([^\s]+@[^\s]+)/);
    const email = emailMatch ? emailMatch[1] : '';
    
    // Use this member in your test
    cy.log(`Testing with member: ${email}`);
  });
});
```

### Pattern 2: Dynamic role selection
```typescript
it('changes roles dynamically', () => {
  cy.visit('/team');
  
  // Get available roles from config
  const roles = TEAM_MANAGEMENT_ASSUMPTIONS.organization.availableRoles;
  
  // Find current role and select different one
  cy.get('[data-testid*="role"]').first().then(($roleElement) => {
    const currentRole = $roleElement.text();
    const newRole = roles.find(role => role !== currentRole);
    
    if (newRole) {
      cy.contains(newRole).click();
    }
  });
});
```

### Pattern 3: Permission-based testing
```typescript
it('tests based on permissions', () => {
  cy.visit('/team');
  
  // Only test invite if user has permission
  if (TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.permissions.includes('invite_members')) {
    checkUserPermissions('invite_members');
    cy.get('[data-testid="invite-btn"]').click();
    // ... invite test logic
  }
});
```

## 🐛 Troubleshooting

### Test fails with "No team members found"
```typescript
// Add this check at start of test
cy.get('body').then(($body) => {
  const memberCount = $body.find('[data-testid*="member"]').length;
  if (memberCount < 2) {
    cy.log('WARNING: Need more team members for this test');
  }
});
```

### Test fails with "Permission denied"
```typescript
// Validate user role first
cy.visit('/team');
validateTeamManagementPrerequisites();

// Check specific permission
checkUserPermissions('change_roles');
```

### UI elements not found
```typescript
// Use multiple selectors for robustness
cy.get('[data-testid*="invite"], button:contains("Invite"), [aria-label*="invite"]')
  .first()
  .click();
```

## 📊 Environment Variables

```bash
# Required
TEST_USER_EMAIL=<EMAIL>    # User with Owner role
TEST_USER_PASSWORD=qBraid2!              # User password

# Optional
CYPRESS_baseUrl=http://localhost:3000    # App URL
```

## ✅ Best Practices

1. **Always validate prerequisites** before running tests
2. **Use dynamic selectors** instead of hardcoded IDs
3. **Log assumptions** in beforeEach for debugging
4. **Handle edge cases** gracefully (no members, no permissions)
5. **Use the helper functions** instead of duplicating logic
