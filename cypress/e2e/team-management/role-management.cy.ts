// Happy path E2E for Team Role Management using real endpoints

import { TEAM_MANAGEMENT_ASSUMPTIONS, validateTeamManagementPrerequisites } from './setup';

/**
 * Team Management - Role Management Tests
 *
 * Prerequisites (defined in setup.ts):
 * - User has Owner role with full team management permissions
 * - Organization has multiple members with various roles
 * - User can change roles, remove members, and view activity
 * - Test user: <EMAIL> (assumed to be Owner)
 */
describe('Team Management - Role Management (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Validate that user has the expected Owner role and permissions
    cy.log(
      `Test assumptions: User ${email} has ${TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.role} role`,
    );
    cy.log(
      `Expected permissions: ${TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.permissions.join(', ')}`,
    );
  });

  //working
  it('filters team members by role', () => {
    cy.visit('/team');

    // Validate prerequisites before running the test
    validateTeamManagementPrerequisites();

    // Wait for team to load completely
    cy.findByTestId('team-members-table').should('be.visible');

    // Wait for data to load - ensure we have team members
    cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);

    // Wait a bit more for the table to fully render
    cy.wait(2000);

    // Get initial count
    cy.get('[data-testid="member-row-user"]').then(($rows) => {
      const initialCount = $rows.length;
      cy.log(`Initial team member count: ${initialCount}`);
    });

    // Filter by specific role - click on the trigger button, not the value
    cy.findByTestId('role-filter-select-trigger').click();

    // Wait for dropdown to open and try to find any admin role (case insensitive)
    cy.get('[role="listbox"]').should('be.visible');

    // Try to find admin role in various formats
    cy.get('body').then(($body) => {
      const options = $body.find('[role="option"]');
      let adminFound = false;

      // Look for admin in different cases
      options.each((_, option) => {
        const text = Cypress.$(option).text().toLowerCase();
        if (text.includes('admin')) {
          cy.wrap(option).click();
          adminFound = true;
          cy.log(`Found and clicked admin option: ${Cypress.$(option).text()}`);
          return false; // break the loop
        }
      });

      if (!adminFound) {
        // If no admin found, just select the first non-"All Roles" option
        cy.get('[role="option"]').not(':contains("All Roles")').first().click();
        cy.log('No admin role found, selected first available role for testing');
      }
    });

    // Wait for filter to apply
    cy.wait(2000);

    // Should show filtered results (may be 0 if no users with that role)
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="member-row-user"]').length > 0) {
        cy.get('[data-testid="member-row-user"]').should('exist');
        cy.log('Filtered users found and displayed');
      } else {
        cy.log('No users found with selected role - this is acceptable');
      }
    });

    // Reset filter - click trigger again to open dropdown
    cy.findByTestId('role-filter-select-trigger').click();
    cy.get('[role="option"]').contains('All Roles').click();

    // Wait for reset to apply and verify we're back to showing all users
    cy.wait(1000);
    cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);
  });
  //working
  it('handles insufficient permissions gracefully', () => {
    // This test simulates what happens when a user tries to perform actions they don't have permission for
    cy.visit('/team');

    // Check if change role buttons exist (they may not for users with insufficient permissions)
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
        // If buttons exist, test that they're properly disabled/enabled based on permissions
        cy.get('[data-testid*="change-role-btn"]')
          .first()
          .then(($btn) => {
            // Button might be disabled for certain roles
            if ($btn.is(':disabled')) {
              cy.log('Change role button is properly disabled for insufficient permissions');
            } else {
              cy.log('Change role button is enabled (user has sufficient permissions)');
            }
          });
      } else {
        cy.log('No change role buttons visible (user may not have permission)');
      }
    });
  });

  //working
  it('validates role change inputs', () => {
    cy.visit('/team');

    // Wait for team to load
    cy.findByTestId('team-members-table').should('be.visible');

    // Wait for data to load - ensure we have team members
    cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);

    // Wait for the table to fully render
    cy.wait(2000);

    // Test role change validation
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
        // Click the first available change role button
        cy.get('[data-testid*="change-role-btn"]').first().click();

        // Wait for modal to open
        cy.contains('Change User Role').should('be.visible');
        cy.contains('Update permissions for').should('be.visible');

        // Get the current role and available roles dynamically
        cy.get('body').then(($body) => {
          const modalText = $body.text();
          const emailMatch = modalText.match(/Update permissions for\s+([^\s]+@[^\s]+)/);
          const userEmail = emailMatch ? emailMatch[1] : '';
          cy.log(`Found user email from modal: ${userEmail}`);

          // Find the current role (marked with "Current Role")
          cy.get('body').then(($modalBody) => {
            const currentRoleElement = $modalBody.find(':contains("Current Role")').closest('div');
            let currentRole = '';

            if (currentRoleElement.length > 0) {
              // Extract current role name
              const roleText = currentRoleElement.text();
              if (roleText.includes('Admin')) currentRole = 'Admin';
              else if (roleText.includes('Manager')) currentRole = 'Manager';
              else if (roleText.includes('Viewer')) currentRole = 'Viewer';
              else if (roleText.includes('Owner')) currentRole = 'Owner';
              else if (roleText.includes('Member')) currentRole = 'Member';
            }

            cy.log(`Current role: ${currentRole}`);

            // Select a different role dynamically
            const availableRoles = ['Viewer', 'Manager', 'Admin', 'Member'];
            const targetRole = availableRoles.find((role) => role !== currentRole);

            if (targetRole) {
              cy.log(`Selecting new role: ${targetRole}`);
              cy.contains(targetRole).click();

              // Role change happens immediately - check for success toast notification
              // Wait a bit for the toast to appear and be more flexible with the text matching
              cy.wait(1000);
              cy.contains('Successfully changed', { timeout: 5000 }).should('be.visible');
              cy.contains(`role to ${targetRole.toLowerCase()}`, { timeout: 5000 }).should(
                'be.visible',
              );

              cy.log(`Role change from ${currentRole} to ${targetRole} completed successfully`);
            } else {
              cy.log('No different role available to select');
            }
          });
        });
      } else {
        cy.log('No change role buttons found - user may not have permissions');
      }
    });
  });

  it('removes team member with confirmation', () => {
    // Visit the team page to work with real data
    cy.visit('/team');

    // Wait for the page to load
    cy.contains('Team Members').should('be.visible');

    // Check if there are any team members to remove
    cy.get('body').then(($body) => {
      // Look for remove buttons (could be trash icons, "Remove" text, or similar)
      const removeButtons = $body.find(
        '[data-testid*="remove"], [data-testid*="delete"], button:contains("Remove"), [aria-label*="remove"], [aria-label*="delete"]',
      );

      if (removeButtons.length > 0) {
        // Click the first available remove button
        cy.get(
          '[data-testid*="remove"], [data-testid*="delete"], button:contains("Remove"), [aria-label*="remove"], [aria-label*="delete"]',
        )
          .first()
          .click();

        // Look for confirmation modal/dialog
        cy.get('body').then(($modalBody) => {
          if ($modalBody.find('[data-testid*="modal"], [role="dialog"], .modal').length > 0) {
            // Modal exists - check for confirmation content
            cy.get('[data-testid*="modal"], [role="dialog"], .modal').should('be.visible');

            // Look for email or user identifier in the confirmation text
            cy.get('body').then(($confirmBody) => {
              const confirmText = $confirmBody.text();
              const emailMatch = confirmText.match(/([^\s]+@[^\s]+)/);
              const userEmail = emailMatch ? emailMatch[1] : '';

              if (userEmail) {
                cy.log(`Removing user: ${userEmail}`);
                cy.contains(userEmail).should('be.visible');
              }

              // Look for and click confirm button
              cy.get('button')
                .contains(/confirm|remove|delete|yes/i)
                .click();

              // Check for success message
              cy.contains(/removed|deleted|success/i, { timeout: 10_000 }).should('be.visible');

              cy.log('User removal completed successfully');
            });
          } else {
            // No modal - removal might be immediate
            cy.log('No confirmation modal found - removal might be immediate');

            // Check for success message
            cy.contains(/removed|deleted|success/i, { timeout: 10_000 }).should('be.visible');
          }
        });
      } else {
        cy.log('No remove buttons found - user may not have permissions or no users to remove');
      }
    });
  });

  it('displays role change history', () => {
    cy.visit('/team');
    cy.contains('Activity Log').click();

    // Filter by role changes
    cy.findByTestId('activity-search-input').type('role change');

    // Should show activity log
    cy.findByTestId('activity-log-table').should('be.visible');
  });
});
