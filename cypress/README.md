# Cypress Testing Guide

This guide explains how to set up and run Cypress tests for the qBraid Partner Dashboard with different user roles.

## User Roles

The application has 4 user roles with different permission levels:

1. **Owner** (Level 4) - Complete control over organization
2. **Admin** (Level 3) - Full administrative access except ownership transfers
3. **Manager** (Level 2) - Can manage team members with lower roles
4. **Viewer** (Level 1) - Basic user with limited write access

## Setup

### 1. Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```bash
# Test user credentials
OWNER_EMAIL=<EMAIL>
OWNER_PASSWORD=secure-password-123
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-password-123
MANAGER_EMAIL=<EMAIL>
MANAGER_PASSWORD=secure-password-123
VIEWER_EMAIL=<EMAIL>
VIEWER_PASSWORD=secure-password-123

# AWS Cognito credentials (if needed)
AWS_COGNITO_USERNAME=your-cognito-username
A<PERSON>_COGNITO_PASSWORD=your-cognito-password
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start Development Server

```bash
npm run dev
```

### 4. Open Cypress

```bash
npx cypress open
```

## Running Tests

### Using the Test Runner Script

The `run-tests.sh` script makes it easy to run tests for specific roles:

```bash
# Run tests for a specific role
./run-tests.sh owner
./run-tests.sh admin
./run-tests.sh manager
./run-tests.sh viewer

# Run tests for all roles
./run-tests.sh all

# Run device visibility tests
./run-tests.sh device

# Run authentication tests
./run-tests.sh signup
./run-tests.sh signin
./run-tests.sh auth
./run-tests.sh role-signin
./run-tests.sh simple-auth
```

### Using Cypress CLI

```bash
# Run all tests
npx cypress run

# Run tests for a specific role
npx cypress run --spec "cypress/e2e/owner-role.cy.ts"
npx cypress run --spec "cypress/e2e/admin-role.cy.ts"
npx cypress run --spec "cypress/e2e/manager-role.cy.ts"
npx cypress run --spec "cypress/e2e/viewer-role.cy.ts"

# Run device visibility tests
npx cypress run --spec "cypress/e2e/device-visibility.cy.ts"
```

## Test Structure

### Test Files

### Role-based Tests
1. **`owner-role.cy.ts`** - Tests for Owner role permissions
2. **`admin-role.cy.ts`** - Tests for Admin role permissions
3. **`manager-role.cy.ts`** - Tests for Manager role permissions
4. **`viewer-role.cy.ts`** - Tests for Viewer role permissions
5. **`device-visibility.cy.ts`** - Tests for device access across all roles

### Authentication Tests
6. **`signup.cy.ts`** - Complete signup flow tests
7. **`signin.cy.ts`** - Complete signin flow tests
8. **`role-based-signin.cy.ts`** - Detailed signin tests for each role
9. **`simple-role-auth.cy.ts`** - Simplified role-based authentication tests
10. **`auth-error-handling.cy.ts`** - Authentication error handling tests
11. **`social-auth.cy.ts`** - OAuth authentication tests

### Custom Commands

The test suite includes custom commands in `cypress/support/commands.ts`:

#### Basic Commands
- `cy.login(role)` - Login as a specific user role
- `cy.logout()` - Logout the current user
- `cy.checkDeviceAccess(deviceName, hasAccess)` - Check device visibility
- `cy.checkNavigationAccess(role)` - Verify navigation menu items

#### Authentication Commands
- `cy.signup(userData)` - Register a new user
- `cy.signin(email, password)` - Sign in with credentials
- `cy.loginWithFixture(role)` - Login using credentials from fixture file
- `cy.getUserCredentials(role)` - Get user credentials from fixture

#### Validation Commands
- `cy.shouldHaveValidationError()` - Check form validation errors
- `cy.shouldSeeAlert()` - Check alert messages
- `cy.checkPasswordRequirements()` - Test password validation

#### Password Reset Commands
- `cy.initiatePasswordReset()` - Start password reset flow
- `cy.completePasswordReset()` - Complete password reset

### Test Data

Test user credentials and device data are defined in:
- `cypress/fixtures/test-users.json` - Test user data and device information
- `cypress.config.ts` - Environment variables for test credentials

### Fixture-based Authentication

The tests use `cypress/fixtures/test-users.json` to store user credentials:

```json
{
  "users": {
    "owner": {
      "email": "<EMAIL>",
      "password": "<EMAIL>",
      "fullName": "Test Owner",
      "role": "owner"
    },
    "admin": {
      "email": "<EMAIL>",
      "password": "<EMAIL>",
      "fullName": "Test Admin",
      "role": "admin"
    },
    "manager": {
      "email": "<EMAIL>",
      "password": "<EMAIL>",
      "fullName": "Test Manager",
      "role": "manager"
    },
    "viewer": {
      "email": "<EMAIL>",
      "password": "<EMAIL>",
      "fullName": "Test Viewer",
      "role": "viewer"
    }
  }
}
```

#### Using Fixture-based Login

```javascript
// Load credentials from fixture and login
cy.getUserCredentials('owner').then((user) => {
  cy.visit('/signin');
  cy.get('input[name="email"]').type(user.email);
  cy.get('input[name="password"]').type(user.password);
  cy.get('button[type="submit"]').click();
});

// Or use the convenience command
cy.loginWithFixture('admin');
```

## Test Coverage

### Owner Role Tests
- Dashboard with all metrics
- Access to all navigation items
- Admin panel access
- Full device management
- Team management with ownership transfer
- Earnings management
- Organization settings

### Admin Role Tests
- Dashboard with most metrics
- Navigation without admin panel
- Device management (no admin-only devices)
- Team management (no ownership transfer)
- No earnings management
- Job and provider management

### Manager Role Tests
- Limited dashboard
- Basic navigation
- No admin panel access
- View-only device access
- Limited team view
- No earnings access
- Read-only job access

### Viewer Role Tests
- Basic dashboard
- Minimal navigation
- No admin or team access
- Public device access only
- Read-only device details
- Profile editing only

### Device Visibility Tests
- Cross-role device access verification
- Device detail tab permissions
- Search and filter permissions
- Status visibility by role
- Access request functionality

## Best Practices

1. **Use Custom Commands**: Leverage the custom commands for common operations
2. **Session Management**: Tests use `cy.session()` for efficient login handling
3. **Role-Specific Testing**: Each test file focuses on a single user role
4. **Permission Verification**: Tests verify both visible and hidden elements
5. **Clean State**: Each test logs out after completion

## Troubleshooting

### Common Issues

1. **Login Failures**
   - Verify environment variables are set correctly
   - Ensure test users exist in the system
   - Check if the authentication flow is working

2. **Element Not Found**
   - Verify selectors match the actual application
   - Check if elements are conditionally rendered based on role
   - Use proper waiting strategies for dynamic content

3. **Test Flakiness**
   - Use `cy.wait()` for network requests when necessary
   - Add proper error handling
   - Use `cy.intercept()` for API mocking if needed

### Debug Mode

Run tests with debug information:

```bash
DEBUG=cypress:* npx cypress run
```

### Headed Mode

Run tests in headed mode for debugging:

```bash
npx cypress run --headed --browser chrome
```

## Adding New Tests

When adding new tests:

1. Determine which role(s) the test applies to
2. Use the appropriate custom commands
3. Follow the existing test structure
4. Update fixtures if new test data is needed
5. Add to the appropriate test file or create a new one

## Continuous Integration

The tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Cypress tests
  run: |
    npm run dev &
    sleep 15
    npx cypress run --spec "cypress/e2e/**/*.cy.ts"
  env:
    OWNER_EMAIL: ${{ secrets.OWNER_EMAIL }}
    OWNER_PASSWORD: ${{ secrets.OWNER_PASSWORD }}
    ADMIN_EMAIL: ${{ secrets.ADMIN_EMAIL }}
    ADMIN_PASSWORD: ${{ secrets.ADMIN_PASSWORD }}
    MANAGER_EMAIL: ${{ secrets.MANAGER_EMAIL }}
    MANAGER_PASSWORD: ${{ secrets.MANAGER_PASSWORD }}
    VIEWER_EMAIL: ${{ secrets.VIEWER_EMAIL }}
    VIEWER_PASSWORD: ${{ secrets.VIEWER_PASSWORD }}
```