{"users": {"owner": {"email": "<EMAIL>", "password": "<PERSON>@qbraid.com1", "fullName": "Test Owner", "role": "owner"}, "admin": {"email": "<EMAIL>", "password": "<EMAIL>", "fullName": "Test Admin", "role": "admin"}, "manager": {"email": "<EMAIL>", "password": "<EMAIL>", "fullName": "Test Manager", "role": "manager"}, "viewer": {"email": "<EMAIL>", "password": "<EMAIL>", "fullName": "Test Viewer", "role": "viewer"}}, "devices": {"publicDevice": {"name": "Public Quantum Device A", "type": "quantum", "status": "online", "accessLevel": "public"}, "internalDevice": {"name": "Internal Quantum Device B", "type": "quantum", "status": "online", "accessLevel": "internal"}, "restrictedDevice": {"name": "Restricted Quantum Device C", "type": "quantum", "status": "maintenance", "accessLevel": "restricted"}, "adminDevice": {"name": "Admin-Only <PERSON><PERSON> D", "type": "quantum", "status": "offline", "accessLevel": "admin"}}, "testData": {"organization": {"name": "Test Organization", "plan": "enterprise", "memberCount": 4}, "apiEndpoints": {"login": "/api/auth/login", "logout": "/api/auth/logout", "devices": "/api/devices", "team": "/api/team", "profile": "/api/user/profile"}}}