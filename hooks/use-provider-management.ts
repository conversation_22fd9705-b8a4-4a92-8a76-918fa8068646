import { useQuery, useMutation } from '@tanstack/react-query';
import { ProviderResponseType } from '@/types/provider';
import { getQueryClient, queryClient } from '@/lib/query-client';
import { toast } from 'sonner';
import { apiClient } from '@/hooks/use-api';

// Core provider management hooks
export function useProviders() {
  return useQuery<ProviderResponseType[]>({
    queryKey: ['providers'],
    queryFn: async (): Promise<ProviderResponseType[]> => {
      return apiClient('/api/providers');
    },
    staleTime: 1 * 60 * 60 * 1000, // 1 hour
    enabled: true,
  });
}

export function useOrgProvider(orgId: string) {
  return useQuery<ProviderResponseType | null>({
    queryKey: ['orgProvider', orgId],
    queryFn: async (): Promise<ProviderResponseType | null> => {
      try {
        const data = await apiClient(`/api/orgs/${orgId}/provider`);
        // The external client returns null when no provider is assigned
        // This is a valid response, not an error
        return data;
      } catch (error: any) {
        // If organization is not found (404), throw a specific error
        if (error.message?.includes('404') || error.message?.includes('not found')) {
          throw new Error('Organization not found');
        }
        throw new Error('Failed to fetch org provider');
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!orgId,
    retry: (failureCount, error: any) => {
      // Don't retry if organization is not found (404)
      if (error.message === 'Organization not found') return false;
      // Don't retry if we've failed 3 times
      if (failureCount >= 3) return false;
      return true;
    },
  });
}

export function useAssignProviderToOrg() {
  return useMutation<any, Error, { orgId: string; providerId: string; providerName: string }>({
    mutationFn: async ({ orgId, providerId, providerName }) => {
      return apiClient(`/api/orgs/${orgId}/provider`, {
        method: 'PUT',
        body: JSON.stringify({ providerId, providerName }),
      });
    },
    onSuccess: (result: any) => {
      queryClient.invalidateQueries({ queryKey: ['orgProvider'] });
      toast.success(`Provider assigned successfully`);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Hook for provider status checking (for onboarding gate)

// Legacy hook for backward compatibility - now returns array with single provider
export function useOrgProviders(orgId: string) {
  const { data: provider, ...rest } = useOrgProvider(orgId);
  return {
    ...rest,
    data: provider ? [provider] : [],
  };
}

// Legacy hook for backward compatibility
export function useAddProviderToOrg() {
  const assignProvider = useAssignProviderToOrg();

  return {
    ...assignProvider,
    mutate: ({
      orgId,
      providerId,
      providerName,
    }: {
      orgId: string;
      providerId: string | string[];
      providerName: string | string[];
    }) => {
      // Convert to single provider assignment
      const singleProviderId = Array.isArray(providerId) ? providerId[0] : providerId;
      const singleProviderName = Array.isArray(providerName) ? providerName[0] : providerName;

      assignProvider.mutate({
        orgId,
        providerId: singleProviderId,
        providerName: singleProviderName,
      });
    },
  };
}
