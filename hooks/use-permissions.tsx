'use client';

import React, { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Permission } from '@/types/auth';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useIsQbraidAdmin } from '@/hooks/use-qbraid-admin-role';
import { apiClient } from '@/hooks/use-api';

interface PermissionResponse {
  success: boolean;
  permissions: Permission[];
  roles: string[];
  timestamp: string;
  orgRoles?: Record<
    string,
    {
      orgId: string;
      orgName: string;
      role: string;
      permissions: Permission[];
    }
  >;
}

interface RefreshRolesResponse {
  success: boolean;
  message: string;
  data: {
    email: string;
    roles: string[];
    timestamp: string;
  };
}

// Query keys
export const permissionKeys = {
  all: ['permissions'] as const,
  current: () => [...permissionKeys.all, 'current'] as const,
  refresh: () => [...permissionKeys.all, 'refresh'] as const,
};

async function fetchPermissions(orgId?: string): Promise<PermissionResponse> {
  const url = new URL('/api/auth/permissions', globalThis.location.origin);
  if (orgId) {
    url.searchParams.set('orgId', orgId);
  }

  return apiClient(url.toString());
}

async function refreshRoles(): Promise<RefreshRolesResponse> {
  return apiClient('/api/refresh-roles', {
    method: 'POST',
  });
}

export function usePermissions() {
  const queryClient = useQueryClient();
  const { isQbraidAdmin } = useIsQbraidAdmin();

  // Get org context
  const [currentOrgId, setCurrentOrgId] = React.useState<string | undefined>();

  React.useEffect(() => {
    try {
      const orgContext = useOrgContext();
      setCurrentOrgId(orgContext.currentOrgId || undefined);
    } catch {
      // Fallback to localStorage
      if (globalThis.window) {
        setCurrentOrgId(localStorage.getItem('currentOrgId') || undefined);
      }
    }
  }, []);

  // Main permissions query
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: [...permissionKeys.current(), currentOrgId || 'global'],
    queryFn: () => fetchPermissions(currentOrgId || undefined),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
  });

  // Extract data
  const permissions = data?.permissions || [];
  const roles = data?.roles || [];
  const orgRoles = data?.orgRoles || {};

  // Org context change handler
  React.useEffect(() => {
    const handleOrgChange = (event: CustomEvent) => {
      setCurrentOrgId(event.detail.orgId);
      setTimeout(() => refetch(), 50);
    };

    globalThis.addEventListener('org-context-changed', handleOrgChange as EventListener);
    return () => {
      globalThis.removeEventListener('org-context-changed', handleOrgChange as EventListener);
    };
  }, [refetch]);

  // Refresh roles mutation
  const refreshRolesMutation = useMutation({
    mutationFn: refreshRoles,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });
    },
  });

  // Permission checking utilities
  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      return permissions.includes(permission) || isQbraidAdmin;
    },
    [permissions, isQbraidAdmin],
  );

  const hasAnyPermission = useCallback(
    (requiredPermissions: Permission[]): boolean => {
      return requiredPermissions.some((permission) => hasPermission(permission));
    },
    [hasPermission],
  );

  const hasAllPermissions = useCallback(
    (requiredPermissions: Permission[]): boolean => {
      return requiredPermissions.every((permission) => hasPermission(permission));
    },
    [hasPermission],
  );

  const hasRole = useCallback(
    (role: string): boolean => {
      return roles.includes(role);
    },
    [roles],
  );

  const hasAnyRole = useCallback(
    (requiredRoles: string[]): boolean => {
      return requiredRoles.some((role) => hasRole(role));
    },
    [hasRole],
  );

  // Org utilities
  const getOrganizations = () => Object.values(orgRoles);

  const getUserRoleInOrg = (orgId: string) => orgRoles[orgId]?.role || null;

  const getCurrentOrgContext = () => {
    if (globalThis.window) {
      const savedOrgId = localStorage.getItem('currentOrgId');
      if (savedOrgId && orgRoles[savedOrgId]) {
        return savedOrgId;
      }
    }
    const orgIds = Object.keys(orgRoles);
    return orgIds.length === 1 ? orgIds[0] : orgIds[0] || null;
  };

  const setCurrentOrgContext = (orgId: string) => {
    if (globalThis.window && orgRoles[orgId]) {
      localStorage.setItem('currentOrgId', orgId);
      globalThis.dispatchEvent(
        new CustomEvent('org-context-changed', {
          detail: { orgId, org: orgRoles[orgId] },
        }),
      );
    }
  };

  return {
    permissions,
    roles,
    orgRoles,
    loading: isLoading,
    error: error?.message || null,
    isError,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    isAdmin: isQbraidAdmin,
    getOrganizations,
    getUserRoleInOrg,
    getCurrentOrgContext,
    setCurrentOrgContext,
    refreshPermissions: refetch,
    refreshRolesFromAPI: () => refreshRolesMutation.mutate(),
    isRefreshing: refreshRolesMutation.isPending,
  };
}

// Permission Guard component
interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: string;
  roles?: string[];
  fallback?: React.ReactNode;
  requireAll?: boolean;
  showLoadingState?: boolean;
}

export const PermissionGuard = React.memo(function PermissionGuard({
  children,
  permission,
  permissions,
  role,
  roles,
  fallback = null,
  requireAll = false,
  showLoadingState = true,
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, hasAnyRole, loading } =
    usePermissions();

  if (loading && showLoadingState) {
    return <div className="h-4 w-full animate-pulse rounded bg-gray-200" />;
  }

  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
  }

  if (hasAccess && role) {
    hasAccess = hasRole(role);
  } else if (hasAccess && roles) {
    hasAccess = requireAll ? roles.every((r) => hasRole(r)) : hasAnyRole(roles);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
});

// Cache invalidation hook
export function useInvalidatePermissions() {
  const queryClient = useQueryClient();

  return {
    invalidatePermissions: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });
    },
  };
}

// Organization-aware permissions hook
export function useOrgPermissions(orgId?: string) {
  const { currentOrgId: contextOrgId } = useOrgContext();
  const {
    permissions: globalPermissions,
    roles: globalRoles,
    orgRoles: allOrgRoles,
    loading,
    error,
    refreshPermissions,
  } = usePermissions();

  const effectiveOrgId = orgId || contextOrgId;

  // Filter for specific org
  const orgSpecific = effectiveOrgId ? allOrgRoles[effectiveOrgId] : undefined;
  const filteredRoles = orgSpecific ? [orgSpecific.role] : globalRoles;
  const filteredPermissions = orgSpecific ? orgSpecific.permissions : globalPermissions;

  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      return filteredPermissions.includes(permission);
    },
    [filteredPermissions],
  );

  const hasRole = useCallback(
    (role: string): boolean => {
      return filteredRoles.includes(role);
    },
    [filteredRoles],
  );

  const hasAnyRole = useCallback(
    (requiredRoles: string[]): boolean => {
      return requiredRoles.some((role) => hasRole(role));
    },
    [hasRole],
  );

  const hasAllRoles = useCallback(
    (requiredRoles: string[]): boolean => {
      return requiredRoles.every((role) => hasRole(role));
    },
    [hasRole],
  );

  return {
    permissions: filteredPermissions,
    roles: filteredRoles,
    orgRoles: allOrgRoles,
    loading,
    hasError: !!error,
    error: error || undefined,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    currentOrgRole: orgSpecific?.role || null,
    isOrgSpecific: !!orgSpecific,
    orgContext: orgSpecific,
    currentOrgId: effectiveOrgId,
    refreshPermissions,
  };
}
