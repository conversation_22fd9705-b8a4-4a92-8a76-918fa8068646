// Extracted from team/page.tsx

import { useState, useCallback } from 'react';

export const usePagination = (initialPage = 0, initialPageSize = 10) => {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const goToPrevious = useCallback(() => setPage((p) => Math.max(0, p - 1)), []);
  const goToNext = useCallback(
    (maxPages: number) => setPage((p) => Math.min(maxPages - 1, p + 1)),
    [],
  );
  const resetPage = useCallback(() => setPage(0), []);
  const changePageSize = useCallback((newSize: number) => {
    setPageSize(newSize);
    setPage(0);
  }, []);

  return { page, pageSize, goToPrevious, goToNext, resetPage, changePageSize };
};
