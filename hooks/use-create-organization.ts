import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { apiClient } from '@/hooks/use-api';

interface CreateOrganizationData {
  name: string;
  description: string;
  marketing: boolean;
  owner: string;
  image?: File;
  darkImage?: File;
  qbraidGitHubAssistance: boolean;
  orgGitHubUrl: string;
  orgGitHubToken: string;
  qBookDomain: 'quera' | 'qbraid';
}

interface CreateOrganizationResponse {
  success: boolean;
  organization: {
    id: string;
    name: string;
    description: string;
    owner: string;
    email: string;
    billingAccount: any;
    createdAt: string;
  };
  billing: any;
  owner: any;
}

interface CreateOrganizationError {
  error: string;
  details?: string;
}

const createOrganization = async (
  data: CreateOrganizationData,
): Promise<CreateOrganizationResponse> => {
  const formData = new FormData();

  // Add all form fields
  formData.append('name', data.name.trim());
  formData.append('description', data.description.trim());
  formData.append('marketing', data.marketing.toString());
  formData.append('owner', data.owner.trim());
  formData.append('qbraidGitHubAssistance', data.qbraidGitHubAssistance.toString());
  formData.append('orgGitHubUrl', data.orgGitHubUrl.trim());
  formData.append('orgGitHubToken', data.orgGitHubToken.trim());
  formData.append('qBookDomain', data.qBookDomain);

  // Add files if present
  if (data.image) {
    formData.append('image', data.image);
  }
  if (data.darkImage) {
    formData.append('darkImage', data.darkImage);
  }

  return apiClient('/api/orgs/create', {
    method: 'POST',
    body: formData,
  });
};

export function useCreateOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createOrganization,
    onSuccess: (data, variables) => {

      // Show success toast
      toast.success(`Organization "${variables.name}" created successfully!`);

      // Invalidate and refetch organizations list
      queryClient.invalidateQueries({ queryKey: ['organizations'] });

      // Optionally refetch user's organizations
      queryClient.invalidateQueries({ queryKey: ['user-organizations'] });

      // Optionally refetch current user's context
      queryClient.invalidateQueries({ queryKey: ['current-user'] });
    },
    onError: (error: Error, variables) => {
      console.error('❌ [CREATE-ORG-MUTATION] Failed to create organization:', error);

      // Show error toast
      toast.error(error.message || 'Failed to create organization');
    },
  });
}
