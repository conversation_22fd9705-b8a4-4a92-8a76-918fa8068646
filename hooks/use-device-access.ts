import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  OrgDeviceAccess, 
  DeviceAccessLevel, 
  DeviceAccessRequest, 
  DeviceAccessRequestsResponse,
  DeviceAccessRequestsResponseQuery 
} from '@/types/provider';
import { queryClient } from '@/lib/query-client';
import { toast } from 'sonner';
import { apiClient } from '@/hooks/use-api';

// Device access management hooks
export function useOrgDeviceAccess(orgId: string) {
  return useQuery<OrgDeviceAccess>({
    queryKey: ['org-device-access', orgId],
    queryFn: async (): Promise<OrgDeviceAccess> => {
      return apiClient(`/api/orgs/${orgId}/device-access`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!orgId,
  });
}
export function useDeviceAccessRequests(status?: string, organizationId?: string) {
  return useQuery<DeviceAccessRequestsResponseQuery>({
    queryKey: ['device-access-requests', status, organizationId],
    queryFn: async (): Promise<DeviceAccessRequestsResponseQuery> => {
      const params = new URLSearchParams();
      if (status) params.append('status', status);
      if (organizationId) params.append('organizationId', organizationId);

      return apiClient(`/api/device-access-requests?${params.toString()}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useRequestDeviceAccess() {
  return useMutation<
    { message: string; request: DeviceAccessRequest },
    Error,
    {
      deviceId: string;
      deviceName: string;
      requestType: DeviceAccessLevel;
      justification: string;
      accessDuration?: string;
      orgId: string;
    }
  >({
    mutationFn: async ({
      deviceId,
      deviceName,
      requestType,
      justification,
      accessDuration,
      orgId,
    }) => {
      return apiClient(`/api/device-access-requests`, {
        method: 'POST',
        body: JSON.stringify({
          deviceId,
          deviceName,
          requestType,
          justification,
          accessDuration,
          orgId,
        }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['device-access-requests'] });
      toast.success('Device access request submitted successfully');
    },
  });
}

export function useApproveDeviceAccess() {
  return useMutation<
    { message: string; request: DeviceAccessRequest },
    Error,
    { requestId: string; action: 'approve' | 'deny'; deniedReason?: string; expiresAt?: string }
  >({
    mutationFn: async ({ requestId, action, deniedReason, expiresAt }) => {
      return apiClient(`/api/device-access-requests/${requestId}`, {
        method: 'PUT',
        body: JSON.stringify({ action, deniedReason, expiresAt }),
      });
    },
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: ['device-access-requests'] });
      queryClient.invalidateQueries({ queryKey: ['org-device-access'] });
      queryClient.invalidateQueries({ queryKey: ['provider-device-access-requests'] });
      toast.success(`Device access ${action === 'approve' ? 'approved' : 'denied'} successfully`);
    },
  });
}

// Hook to get a specific device access request details
export function useDeviceAccessRequest(requestId: string) {
  return useQuery<{ request: DeviceAccessRequest }>({
    queryKey: ['device-access-request', requestId],
    queryFn: async (): Promise<{ request: DeviceAccessRequest }> => {
      return apiClient(`/api/device-access-requests/${requestId}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!requestId,
  });
}

// Hook to get all device access requests for a provider (for owners to review)
export function useProviderDeviceAccessRequests(providerId: string) {
  return useQuery<DeviceAccessRequest[]>({
    queryKey: ['provider-device-access-requests', providerId],
    queryFn: async (): Promise<DeviceAccessRequest[]> => {
      return apiClient(`/api/providers/${providerId}/device-access-requests`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!providerId,
  });
}
