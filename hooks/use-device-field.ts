/**
 * useDeviceField Hook
 *
 * A custom React hook that manages the state and logic for editing device fields.
 * This hook encapsulates all the complexity of field editing, including:
 * - State management for hover, edit mode, and form values
 * - API calls for updating device data
 * - Toast notifications for user feedback
 * - Field validation and type conversion
 *
 * This hook follows the principle of separation of concerns by extracting
 * all field editing logic from the UI components, making them purely presentational.
 */

import { useState } from 'react';
import { useUpdateDeviceData } from '@/hooks/use-api';
import { toast } from 'sonner';
import { buildPatchObject, formatValueForInput } from '@/lib/device-patch-builder';
import { getFieldValidation } from '@/lib/device-field-validation';

/**
 * Props for the useDeviceField hook
 *
 * @param deviceId - The unique identifier for the device being edited
 * @param field - The field name to edit (e.g., 'paradigm', 'pricing.perMinute')
 * @param value - The current value of the field
 */
export interface UseDeviceFieldProps {
  deviceId: string;
  field: string;
  value: any;
}

/**
 * Custom hook for managing device field editing state and operations
 *
 * This hook provides a complete solution for field editing with:
 * - Hover state management
 * - Edit dialog state management
 * - Form value state management
 * - API integration with error handling
 * - Field validation integration
 *
 * @param props - The hook configuration (deviceId, field, value)
 * @returns Object containing all state and handlers for field editing
 *
 * Returned Object:
 * - hover: Boolean indicating if the field is being hovered
 * - setHover: Function to update hover state
 * - editOpen: Boolean indicating if the edit dialog is open
 * - newVal: The current value in the edit form
 * - setNewVal: Function to update the form value
 * - fieldConfig: The validation configuration for the field
 * - handleConfirm: Function to save the changes
 * - handleEdit: Function to open the edit dialog
 * - handleCancel: Function to close the edit dialog
 * - isUpdating: Boolean indicating if an update is in progress
 */
export function useDeviceField({ deviceId, field, value }: UseDeviceFieldProps) {
  // State management for UI interactions
  const [hover, setHover] = useState(false);
  const [editOpen, setEditOpen] = useState(false);

  // Initialize form value with proper formatting based on field type
  const [newVal, setNewVal] = useState(formatValueForInput(field, value));

  // API mutation for updating device data
  const updateMutation = useUpdateDeviceData();

  // Get field validation configuration for this field
  const fieldConfig = getFieldValidation(field);

  /**
   * Handle the confirmation of field changes
   *
   * This function:
   * 1. Builds the proper patch object for the backend
   * 2. Calls the API to update the device
   * 3. Shows success/error toast notifications
   * 4. Closes the edit dialog on completion
   */
  const handleConfirm = async () => {
    const patch = buildPatchObject(field, newVal);
    try {
      await updateMutation.mutateAsync({ deviceId, postBody: patch });
      toast.success('Device updated successfully');
    } catch {
      toast.error('Failed to update device');
    } finally {
      setEditOpen(false);
    }
  };

  /**
   * Handle opening the edit dialog
   *
   * Resets the form value to the current field value and opens the dialog.
   * This ensures the form always starts with the current value, not the
   * previously edited value.
   */
  const handleEdit = () => {
    setNewVal(formatValueForInput(field, value));
    setEditOpen(true);
  };

  /**
   * Handle canceling the edit operation
   *
   * Simply closes the edit dialog without saving any changes.
   */
  const handleCancel = () => {
    setEditOpen(false);
  };

  // Return all state and handlers for use in components
  return {
    hover,
    setHover,
    editOpen,
    setEditOpen,
    newVal,
    setNewVal,
    fieldConfig,
    handleConfirm,
    handleEdit,
    handleCancel,
    isUpdating: updateMutation.isPending,
    updateMutation,
  };
}
