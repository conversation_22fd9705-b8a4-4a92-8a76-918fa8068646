// React Query hooks that directly use our internal API gateway routes
// Eliminates the intermediate api-calls layer for cleaner architecture
import { useQuery, useMutation } from '@tanstack/react-query';
import type { DeviceData, DeviceCardProps } from '@/types/device';
import type { JobsRowProps } from '@/types/jobs';
import type { TeamMember } from '@/types/team';
import React from 'react';
import type { UserProfile } from '@/types/user';
import { getQueryClient } from '@/lib/query-client';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useDashboardStore } from '@/lib/stores/dashboard-store';

/**
 * Enhanced API client that handles React Query invalidations automatically
 */
export const apiClientWithInvalidation = async (
  url: string,
  options?: Parameters<typeof fetch>[1],
  queryClient?: any,
): Promise<any> => {
  let response;
  try {
    response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
    });
  } catch (error) {
    // Handle network errors (e.g., "Failed to fetch")
    if (error instanceof Error && error.name === 'TypeError') {
      throw new Error('Network error: Failed to connect to the server');
    }
    throw error;
  }

  // Check for invalidation headers
  const invalidationHeader = response.headers.get('X-Invalidate-Queries');
  if (invalidationHeader && queryClient) {
    try {
      const invalidationData = JSON.parse(invalidationHeader);

      // Invalidate each specified query
      for (const queryKey of invalidationData.queries) {
        await queryClient.invalidateQueries({ queryKey });
      }
    } catch {
      // Failed to process invalidation header
    }
  }

  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Request failed' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * API client for making requests to the Next.js API routes
 */
export const apiClient = async (
  url: string,
  options?: Parameters<typeof fetch>[1],
): Promise<any> => {
  // Don't override Content-Type for FormData - let the browser set it automatically
  const headers: Record<string, string> = {};
  if (!(options?.body instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
  }

  let response;
  try {
    response = await fetch(url, {
      headers,
      ...options,
    });
  } catch (error) {
    // Handle network errors (e.g., "Failed to fetch")
    if (error instanceof Error && error.name === 'TypeError') {
      throw new Error('Network error: Failed to connect to the server');
    }
    throw error;
  }

  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Request failed' }));
    throw new Error(error.message || `API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * Fetch all quantum devices via API gateway (admin only)
 * Supports filtering by provider, type, status, and availability
 */
export const useAllDevices = (
  orgId: string,
  filters?: { provider?: string; type?: string; status?: string },
) => {
  const paramsObj = { ...filters, orgId };
  const params = new URLSearchParams(paramsObj as Record<string, string>).toString();
  const url = `/api/quantum-devices?${params}`;
  return useQuery<DeviceCardProps[]>({
    queryKey: ['devices', orgId, filters],
    queryFn: () => apiClient(url),
    enabled: true,
    refetchOnWindowFocus: true,
  });
};

/**
 * Fetch organization-specific quantum devices via API gateway
 * Supports filtering by provider, type, status, and availability
 */
export const useOrgDevices = (
  orgId: string,
  filters?: { provider?: string; type?: string; status?: string },
) => {
  const paramsObj = { ...filters };
  const params = new URLSearchParams(paramsObj as Record<string, string>).toString();
  const url = `/api/orgs/${orgId}/quantum-devices?${params}`;
  return useQuery<DeviceCardProps[]>({
    queryKey: ['orgDevices', orgId, filters],
    queryFn: () => apiClient(url),
    enabled: Boolean(orgId),
    refetchOnWindowFocus: true,
  });
};

/**
 * Fetch quantum jobs for a specific device via API gateway
 * Supports fetching all pages when resultsPerPage is set to 'all'
 */
export const useJobsForDevice = (device: string, page?: number, resultsPerPage?: number | 'all') =>
  useQuery<{
    jobsArray: JobsRowProps[];
    total: number;
    uniqueUsers: number;
    qbraidDeviceId: string;
    deviceName: string;
    deviceProvider: string;
    hasJobs: boolean;
  }>({
    queryKey: ['jobs', device, page, resultsPerPage],
    queryFn: async () => {
      // If resultsPerPage is 'all', fetch all pages
      if (resultsPerPage === 'all') {
        let allJobs: JobsRowProps[] = [];
        let currentPage = 0;
        let hasMorePages = true;
        const pageSize = 1000; // Use large page size for efficiency

        while (hasMorePages) {
          const params = new URLSearchParams({
            page: currentPage.toString(),
            resultsPerPage: pageSize.toString(),
          });

          const response = await apiClient(`/api/quantum-jobs/${device}?${params.toString()}`);

          if (response.jobsArray && response.jobsArray.length > 0) {
            allJobs = allJobs.concat(response.jobsArray);

            // Check if there are more pages
            const totalPages = Math.ceil(response.total / pageSize);
            hasMorePages = currentPage < totalPages - 1;
            currentPage++;
          } else {
            hasMorePages = false;
          }
        }

        return {
          jobsArray: allJobs,
          total: allJobs.length,
          uniqueUsers: 0, // Will be calculated from the aggregated data
          qbraidDeviceId: device,
          deviceName: '',
          deviceProvider: '',
          hasJobs: allJobs.length > 0,
        };
      }

      // Single page fetch
      const params = new URLSearchParams({
        page: (page ?? 0).toString(),
        resultsPerPage: (resultsPerPage ?? 10).toString(),
      });
      const response = await apiClient(`/api/quantum-jobs/${device}?${params.toString()}`);

      // Add hasJobs flag to the response
      return {
        ...response,
        hasJobs: response.jobsArray && response.jobsArray.length > 0,
      };
    },
    enabled: Boolean(device),
  });

/**
 * Fetch quantum jobs chart data for devices over time
 * Aggregates jobs by date and device
 */
export const useQuantumJobsChartData = (orgId?: string) => {
  // Get organization-specific devices for the specified org
  const {
    data: devices,
    isLoading: devicesLoading,
    isError: devicesError,
  } = useOrgDevices(orgId || '');

  // Only consider devices for this org
  const devicesForOrg = devices || [];

  // Fetch jobs for each device in the org (no pagination)
  const {
    data: allJobsData,
    isLoading: jobsLoading,
    isError: jobsError,
  } = useQuery({
    queryKey: ['quantumJobs', 'chart', orgId, devicesForOrg.map((d) => d.qrn).sort()],
    queryFn: async ({ signal }) => {
      if (devicesForOrg.length === 0) return { jobsArray: [] };
      // For each device, fetch jobs by device id (no pagination)
      const jobsPromises = devicesForOrg.map(async (device) => {
        try {
          const response = await apiClient(`/api/quantum-jobs/${device.qrn}`, { signal });
          // Attach device and provider info to each job for charting
          return (response.jobsArray || []).map((job: any) => ({
            ...job,
            device: device.qrn,
            provider: job.provider,
          }));
        } catch {
          return [];
        }
      });
      // Flatten all jobs into a single array
      const jobsArray = (await Promise.all(jobsPromises)).flat();
      return { jobsArray };
    },
    enabled: !!orgId && devicesForOrg.length > 0,
  });

  return {
    data: allJobsData,
    isLoading: devicesLoading || jobsLoading,
    isError: devicesError || jobsError,
  };
};

/**
 * Update quantum device data via API gateway (mutation)
 */
export const useUpdateDeviceData = () => {
  const queryClient = getQueryClient();
  const { currentOrgId } = useOrgContext();

  return useMutation<DeviceData, Error, { deviceId: string; postBody: any; isFormData?: boolean }>({
    mutationFn: ({ deviceId, postBody, isFormData = false }) => {
      const options = {
        method: 'PATCH',
      } as const;

      (options as any).body = isFormData ? postBody : JSON.stringify(postBody);

      return apiClient(`/api/quantum-devices/${deviceId}?orgId=${currentOrgId || ''}`, options);
    },
    onSuccess: () => {
      // Invalidate all device lists (any filters) and the single device cache
      queryClient.invalidateQueries({ queryKey: ['devices'] });
      // Also invalidate admin devices cache for admin components
      queryClient.invalidateQueries({ queryKey: ['adminDevices'] });
    },
  });
};

/**
 * Fetch users in an organization via API gateway
 * Uses the consolidated orgs/users endpoint with enhanced error handling
 */
export const useOrgUsers = (orgID: string, page: number, pageSize: number) => {
  const query = useQuery<{
    users: TeamMember[];
    totalUsers: number;
    currentUserRole?: string | null;
    message?: string;
  }>({
    queryKey: ['orgUsers', orgID, page, pageSize],
    queryFn: async () => {
      const result = await apiClient(`/api/orgs/users/${orgID}/${page}/${pageSize}`);

      return result;
    },
    enabled: Boolean(orgID),
  });

  return query;
};

/**
 * Enhanced team member manipulation hooks using new team API endpoints
 */

/**
 * Invite user to organization
 * Uses the consolidated orgs/users/add endpoint with optimistic updates
 */
export const useInviteUser = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: { email: string; orgId: string; role: string; orgName: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/add',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onMutate: async (newUser) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['orgUsers', newUser.orgId] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData<{ users: TeamMember[]; total: number }>([
        'orgUsers',
        newUser.orgId,
      ]);

      // Optimistically update to the new value
      if (previousUsers) {
        queryClient.setQueryData(['orgUsers', newUser.orgId], {
          ...previousUsers,
          users: [
            ...previousUsers.users,
            {
              id: `temp-${Date.now()}`,
              email: newUser.email,
              name: newUser.email.split('@')[0],
              role: newUser.role,
              status: 'Invited',
              avatar: undefined,
              joinedAt: new Date().toISOString(),
            },
          ],
          total: previousUsers.total + 1,
        });
      }

      // Return a context with the previous value
      return { previousUsers };
    },
    onError: (err, newUser, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUsers) {
        queryClient.setQueryData(['orgUsers', newUser.orgId], context.previousUsers);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['orgUsers', variables.orgId] });
    },
  });
};

/**
 * Update user role in organization
 * Uses the consolidated orgs/users/update endpoint with optimistic updates
 */
export const useUpdateUserRole = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: {
      email: string;
      orgId: string;
      role: string;
      orgName: string;
      oldRole?: string; // For audit logging
      accepted?: boolean;
      credits?: number;
    }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/update',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onMutate: async (updatedUser) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['orgUsers', updatedUser.orgId] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData<{ users: TeamMember[]; total: number }>([
        'orgUsers',
        updatedUser.orgId,
      ]);

      // Optimistically update to the new value
      if (previousUsers) {
        queryClient.setQueryData(['orgUsers', updatedUser.orgId], {
          ...previousUsers,
          users: previousUsers.users.map((user) =>
            user.email === updatedUser.email ? { ...user, role: updatedUser.role } : user,
          ),
        });
      }

      // Return a context with the previous value
      return { previousUsers };
    },
    onError: (err, updatedUser, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUsers) {
        queryClient.setQueryData(['orgUsers', updatedUser.orgId], context.previousUsers);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['orgUsers', variables.orgId] });
    },
  });
};

/**
 * Remove user from organization
 * Uses the consolidated orgs/users/remove endpoint with optimistic updates
 */
export const useRemoveUser = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: { email: string; orgId: string; orgName: string; reason?: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/remove',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onMutate: async (removedUser) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['orgUsers', removedUser.orgId] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData<{ users: TeamMember[]; total: number }>([
        'orgUsers',
        removedUser.orgId,
      ]);

      // Optimistically update to the new value
      if (previousUsers) {
        queryClient.setQueryData(['orgUsers', removedUser.orgId], {
          ...previousUsers,
          users: previousUsers.users.filter((user) => user.email !== removedUser.email),
          total: previousUsers.total - 1,
        });
      }

      // Return a context with the previous value
      return { previousUsers };
    },
    onError: (err, removedUser, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUsers) {
        queryClient.setQueryData(['orgUsers', removedUser.orgId], context.previousUsers);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['orgUsers', variables.orgId] });
    },
  });
};

/**
 * Accept organization invitation
 * Uses the orgs/users/accept endpoint
 */
export const useAcceptInvitation = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: { orgId: string; email: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/accept',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onSuccess: () => {
      // Invalidate permissions query to refresh org context
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      // Invalidate user organizations query
      queryClient.invalidateQueries({ queryKey: ['userOrganizations'] });
    },
  });
};

/**
 * Reinvite user to organization
 * Uses the orgs/users/reinvite endpoint with optimistic updates
 */
export const useReinviteUser = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: {
      email: string;
      role: string;
      name: string;
      referer: string;
      orgId: string;
    }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/reinvite',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onMutate: async (reinvitedUser) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['orgUsers', reinvitedUser.orgId] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData<{ users: TeamMember[]; total: number }>([
        'orgUsers',
        reinvitedUser.orgId,
      ]);

      // Optimistically update to show reinvite in progress
      if (previousUsers) {
        queryClient.setQueryData(['orgUsers', reinvitedUser.orgId], {
          ...previousUsers,
          users: previousUsers.users.map((user) =>
            user.email === reinvitedUser.email ? { ...user, status: 'Reinviting...' as any } : user,
          ),
        });
      }

      // Return a context with the previous value
      return { previousUsers };
    },
    onError: (err, reinvitedUser, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUsers) {
        queryClient.setQueryData(['orgUsers', reinvitedUser.orgId], context.previousUsers);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['orgUsers', variables.orgId] });
    },
  });
};

/**
 * Cancel user invitation to organization
 * Uses the orgs/users/cancel-invite endpoint with optimistic updates
 */
export const useCancelInvite = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: { orgId: string; email: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/cancel-invite',
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onMutate: async (cancelledInvite) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['orgUsers', cancelledInvite.orgId] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData<{ users: TeamMember[]; total: number }>([
        'orgUsers',
        cancelledInvite.orgId,
      ]);

      // Optimistically update to remove the invited user
      if (previousUsers) {
        queryClient.setQueryData(['orgUsers', cancelledInvite.orgId], {
          ...previousUsers,
          users: previousUsers.users.filter((user) => user.email !== cancelledInvite.email),
          total: previousUsers.total - 1,
        });
      }

      // Return a context with the previous value
      return { previousUsers };
    },
    onError: (err, cancelledInvite, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUsers) {
        queryClient.setQueryData(['orgUsers', cancelledInvite.orgId], context.previousUsers);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['orgUsers', variables.orgId] });
    },
  });
};

/**
 * Decline organization invitation
 * Uses the orgs/users/remove endpoint with decline reason
 */
export const useDeclineInvitation = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: { orgName: string; email: string }) =>
      apiClientWithInvalidation(
        '/api/orgs/users/remove',
        {
          method: 'POST',
          body: JSON.stringify({
            ...body,
            reason: 'decline',
          }),
        },
        queryClient,
      ),
  });
};

/**
 * Get current user's profile information
 * Uses the external API /get-user-info endpoint
 */
export const useUserProfile = () => {
  return useQuery<UserProfile>({
    queryKey: ['userProfile'],
    queryFn: async () => {
      const response = await apiClient('/api/user/profile');
      // Extract the profile from the API response
      return response.profile || response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Get current user's organizations
 * Uses the external API /orgs/get/:page/:limit endpoint
 */
export const useUserOrganizations = (page: number = 0, limit: number = 10) => {
  return useQuery({
    queryKey: ['userOrganizations', page, limit],
    queryFn: async () => {
      return await apiClient(`/api/user/organizations/${page}/${limit}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Update user profile
 * Uses the external API to update user information
 */
export const useUpdateProfile = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (body: Partial<UserProfile>) =>
      apiClientWithInvalidation(
        '/api/user/profile',
        {
          method: 'PUT',
          body: JSON.stringify(body),
        },
        queryClient,
      ),
    onSuccess: () => {
      // Invalidate profile cache after successful update
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
  });
};

/**
 * Admin-specific hooks for device management
 */

/**
 * Check if current user is a qBraid admin using external API /user/role
 */
export const useAdminRole = () => {
  return useQuery<string>({
    queryKey: ['adminRole'],
    queryFn: () => apiClient('/api/user/role'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once for admin checks
  });
};

/**
 * Fetch unverified devices for admin approval
 */
export const useAdminDevices = () => {
  return useQuery<DeviceData[]>({
    queryKey: ['adminDevices'],
    queryFn: () => apiClient('/api/quantum-devices?verified=false'),
    staleTime: 30 * 1000, // 30 seconds for admin devices
    refetchOnWindowFocus: true,
  });
};

/**
 * Approve a device (mutation)
 */
export const useApproveDevice = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (deviceId: string) =>
      apiClient(`/api/quantum-devices/${deviceId}/approve`, {
        method: 'PATCH',
      }),
    onSuccess: () => {
      // Invalidate admin devices cache
      queryClient.invalidateQueries({ queryKey: ['adminDevices'] });
    },
  });
};

/**
 * Save device edits (mutation)
 */
export const useSaveDeviceEdits = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: ({ deviceId, orgId, edits }: { deviceId: string; orgId: string; edits: any }) =>
      apiClient(`/api/quantum-devices/${deviceId}?orgId=${orgId}`, {
        method: 'PATCH',
        body: JSON.stringify({ pendingEdits: edits }),
        headers: { 'Content-Type': 'application/json' },
      }),
    onSuccess: () => {
      // Invalidate admin devices cache
      queryClient.invalidateQueries({ queryKey: ['adminDevices'] });
    },
  });
};

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async (data: ChangePasswordRequest) => {
      const result = await apiClient('/api/user/change-password', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      return result;
    },
  });
}
/**
 * Delete a device request (mutation)
 */
export const useDeleteDeviceRequest = () => {
  const queryClient = getQueryClient();

  return useMutation({
    mutationFn: (deviceId: string) =>
      apiClient(`/api/quantum-devices/${deviceId}/request`, {
        method: 'DELETE',
      }),
    onSuccess: () => {
      // Invalidate admin devices cache
      queryClient.invalidateQueries({ queryKey: ['adminDevices'] });
    },
  });
};

/**
 * Calculate dashboard statistics for an organization including revenue and unique users
 */
export const useDashboardStats = (orgId?: string) => {
  // Get organization-specific devices for the specified org
  const {
    data: devices,
    isLoading: devicesLoading,
    isError: devicesError,
  } = useOrgDevices(orgId || '');

  // Only consider devices for this org
  const devicesForOrg = devices || [];

  // Fetch jobs for each device in the org (no pagination)
  const {
    data: allJobsData,
    isLoading: jobsLoading,
    isError: jobsError,
  } = useQuery({
    queryKey: ['dashboardStats', orgId, devicesForOrg.map((d) => d.qrn).sort()],
    queryFn: async ({ signal }) => {
      if (devicesForOrg.length === 0) {
        return { totalRevenue: 0, totalJobs: 0, uniqueUsers: 0, totalDevices: 0, hasJobs: false };
      }

      // For each device, fetch jobs by device id (no pagination)
      const jobsPromises = devicesForOrg.map(async (device) => {
        try {
          const response = await apiClient(`/api/quantum-jobs/${device.qrn}`, { signal });
          const jobs = response.jobsArray || [];
          return {
            jobsArray: jobs,
            uniqueUsers: response.uniqueUsers || 0,
          };
        } catch {
          return {
            jobsArray: [],
            uniqueUsers: 0,
          };
        }
      });

      // Aggregate all jobs and unique users
      const allResults = await Promise.all(jobsPromises);
      const allJobs = allResults.flatMap((result) => result.jobsArray);

      // All jobs are now valid since we removed the placeholder approach in the API
      const validJobs = allJobs;

      // Calculate total revenue (cost is in credits, convert to USD by dividing by 100)
      const totalCostCredits = validJobs.reduce(
        (acc: number, job: any) => acc + (job.cost || 0),
        0,
      );
      const totalRevenueUSD = totalCostCredits / 100;

      // Aggregate unique users from all device responses
      const totalUniqueUsers = allResults.reduce(
        (acc: number, result) => acc + (result.uniqueUsers || 0),
        0,
      );

      const hasJobs = validJobs.length > 0;

      return {
        totalRevenue: totalRevenueUSD,
        totalJobs: validJobs.length,
        uniqueUsers: totalUniqueUsers,
        totalDevices: devicesForOrg.length,
        hasJobs,
      };
    },
    enabled: !!orgId && devicesForOrg.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data: allJobsData,
    isLoading: devicesLoading || jobsLoading,
    isError: devicesError || jobsError,
  };
};

/**
 * Get organization access history (for auditing)
 */
export const useOrgAccessHistory = (orgId: string) => {
  return useQuery({
    queryKey: ['org-access-history', orgId],
    queryFn: () => apiClient(`/api/orgs/${orgId}/access-history`),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!orgId,
  });
};

/**
 * Fetch quantum data for dashboard with filtering capabilities
 * Uses the sample quantum job data structure provided
 */
export const useQuantumData = (orgId?: string) => {
  const { currentOrgId } = useOrgContext();
  const effectiveOrgId = orgId || currentOrgId;

  return useQuery({
    queryKey: ['quantumData', effectiveOrgId],
    queryFn: async () => {
      if (!effectiveOrgId) return [];

      try {
        // Fetch all devices for the organization
        const devicesResponse = await apiClient(`/api/orgs/${effectiveOrgId}/quantum-devices`);
        const devices = devicesResponse || [];

        // Fetch jobs for each device
        const jobsPromises = devices.map(async (device: any) => {
          try {
            const response = await apiClient(`/api/quantum-jobs/${device.qrn}`);
            return response.jobsArray || [];
          } catch {
            return [];
          }
        });

        // Wait for all job fetches to complete
        const allJobsArrays = await Promise.all(jobsPromises);
        const allJobs = allJobsArrays.flat();

        // Transform the data to match the QuantumJobData interface
        return allJobs.map((job: any) => ({
          timeStamps: {
            createdAt: job.createdAt || job.timeStamps?.createdAt || new Date().toISOString(),
            endedAt: job.endedAt || job.timeStamps?.endedAt || new Date().toISOString(),
            executionDuration: job.executionDuration || job.timeStamps?.executionDuration || 0,
          },
          circuitBatchNumQubits: job.circuitBatchNumQubits || [],
          circuitBatchDepth: job.circuitBatchDepth || [],
          sites: job.sites || [],
          rabiArray: job.rabiArray || [],
          detuningArray: job.detuningArray || [],
          phaseArray: job.phaseArray || [],
          filling: job.filling || [],
          queuePosition: job.queuePosition || null,
          queueDepth: job.queueDepth || null,
          measurements: job.measurements || [],
          prelight: job.prelight || false,
          _id: job._id || '',
          status: job.status || 'COMPLETED',
          qbraidStatus: job.qbraidStatus || job.status || 'COMPLETED',
          vendorDeviceId: job.vendorDeviceId || '',
          qbraidDeviceId: job.qbraidDeviceId || job.device || '',
          device: job.qbraidDeviceId || job.device || '',
          shots: job.shots || 0,
          escrow: job.escrow || 0,
          user: job.user || '',
          experimentType: job.experimentType || 'gate_model',
          vendor: job.vendor || '',
          provider: job.provider || '',
          logo: job.logo || '',
          logoDark: job.logoDark || '',
          circuitNumQubits: job.circuitNumQubits || 0,
          createdAt: job.createdAt || new Date().toISOString(),
          updatedAt: job.updatedAt || new Date().toISOString(),
          cost: job.cost || 0,
          measurementCounts: job.measurementCounts || null,
        }));
      } catch (error) {
        console.error('Error fetching quantum data:', error);
        return [];
      }
    },
    enabled: !!effectiveOrgId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Fetch quantum data for dashboard with filtering capabilities and store integration
 * Automatically updates the dashboard store when data is fetched
 */
export const useQuantumDataWithStore = (orgId?: string) => {
  const { currentOrgId } = useOrgContext();
  const effectiveOrgId = orgId || currentOrgId;
  const queryClient = getQueryClient();

  // Import the store dynamically to avoid circular dependencies
  const { setQuantumJobs, resetStore } = useDashboardStore();

  const query = useQuery({
    queryKey: ['quantumData', effectiveOrgId],
    queryFn: async () => {
      if (!effectiveOrgId) return [];

      try {
        // Fetch all devices for the organization
        const devicesResponse = await apiClient(`/api/orgs/${effectiveOrgId}/quantum-devices`);
        const devices = devicesResponse || [];

        // Fetch jobs for each device
        const jobsPromises = devices.map(async (device: any) => {
          try {
            const response = await apiClient(`/api/quantum-jobs/${device.qrn}`);
            return response.jobsArray || [];
          } catch {
            return [];
          }
        });

        // Wait for all job fetches to complete
        const allJobsArrays = await Promise.all(jobsPromises);
        const allJobs = allJobsArrays.flat();

        // Transform the data to match the QuantumJob interface for the store
        const transformedData = allJobs.map((job: any) => ({
          ...job,
          message: job.message || '',
          qbraidStatus: job.qbraidStatus || job.status || 'INITIALIZED',
          vendor: job.vendor || 'aws',
          provider: job.provider || 'aws',
          experimentType: job.experimentType || 'gate_model',
          cost: job.cost || 0,
          timeStamps: job.timeStamps || {
            createdAt: job.createdAt || new Date().toISOString(),
            endedAt: job.endedAt || new Date().toISOString(),
            executionDuration: job.executionDuration || 0,
          },
        }));

        // Update the store with the transformed data
        setQuantumJobs(transformedData);

        // Return the original data for backward compatibility
        return allJobs.map((job: any) => ({
          timeStamps: {
            createdAt: job.createdAt || job.timeStamps?.createdAt || new Date().toISOString(),
            endedAt: job.endedAt || job.timeStamps?.endedAt || new Date().toISOString(),
            executionDuration: job.executionDuration || job.timeStamps?.executionDuration || 0,
          },
          circuitBatchNumQubits: job.circuitBatchNumQubits || [],
          circuitBatchDepth: job.circuitBatchDepth || [],
          sites: job.sites || [],
          rabiArray: job.rabiArray || [],
          detuningArray: job.detuningArray || [],
          phaseArray: job.phaseArray || [],
          filling: job.filling || [],
          queuePosition: job.queuePosition || null,
          queueDepth: job.queueDepth || null,
          measurements: job.measurements || [],
          prelight: job.prelight || false,
          _id: job._id || '',
          status: job.status || 'COMPLETED',
          qbraidStatus: job.qbraidStatus || job.status || 'COMPLETED',
          vendorDeviceId: job.vendorDeviceId || '',
          qbraidDeviceId: job.qbraidDeviceId || '',
          device: job.device || '',
          shots: job.shots || 0,
          escrow: job.escrow || 0,
          user: job.user || '',
          experimentType: job.experimentType || 'gate_model',
          vendor: job.vendor || '',
          provider: job.provider || '',
          logo: job.logo || '',
          logoDark: job.logoDark || '',
          circuitNumQubits: job.circuitNumQubits || 0,
          createdAt: job.createdAt || new Date().toISOString(),
          updatedAt: job.updatedAt || new Date().toISOString(),
          cost: job.cost || 0,
          measurementCounts: job.measurementCounts || null,
        }));
      } catch {
        // Clear the store on error
        setQuantumJobs([]);
        return [];
      }
    },
    enabled: !!effectiveOrgId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Clear the store and invalidate related queries when the organization changes
  React.useEffect(() => {
    if (effectiveOrgId) {
      // Reset the entire store when org changes
      resetStore();

      // Invalidate all dashboard-related queries to force fresh data fetch
      queryClient.invalidateQueries({ queryKey: ['quantumData'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
      queryClient.invalidateQueries({ queryKey: ['quantumJobs'] });
      queryClient.invalidateQueries({ queryKey: ['orgDevices'] });
      queryClient.invalidateQueries({ queryKey: ['devices'] });
      queryClient.invalidateQueries({ queryKey: ['orgDevices', effectiveOrgId] });
    }
  }, [effectiveOrgId, resetStore, queryClient]);

  // Update the store when data is fetched
  React.useEffect(() => {
    if (query.data && query.data.length === 0) {
      // Clear the store if no data
      setQuantumJobs([]);
    }
    // The store is already updated in the queryFn, so we don't need to do anything here
  }, [query.data, setQuantumJobs]);

  return query;
};
