'use client';

import { Amplify } from 'aws-amplify';
import {
  signIn as amplifySignIn,
  signUp as amplifySignUp,
  signOut as amplifySignOut,
  confirmSignUp as amplifyConfirmSignUp,
  resetPassword,
  confirmResetPassword,
  getCurrentUser,
} from 'aws-amplify/auth';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthUser } from '@/types/auth';
import { useQuery } from '@tanstack/react-query';
import { permissionKeys } from './use-permissions';
import { getSession } from '@/app/(auth)/actions';
import { getQueryClient } from '@/lib/query-client';

interface AuthSession {
  email: string;
  userId?: string;
}

// Hook to get current session and detect user changes
// Returns session query data with automatic cache clearing on user change
export function useUserChangeDetection() {
  const queryClient = getQueryClient();
  const [lastUserId, setLastUserId] = useState<string | null>(null);

  const sessionQuery = useQuery<AuthSession | null>({
    queryKey: ['session'],
    queryFn: async () => {
      const session = await getSession();
      if (!session) return null;

      return {
        email: session.email,
        userId: session.userId || session.username,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  useEffect(() => {
    const session = sessionQuery.data;
    if (session?.userId && lastUserId && session.userId !== lastUserId) {
      // User has changed - clear all caches
            queryClient.clear();

      // Clear any client-side storage
      if (globalThis.window !== undefined) {
        localStorage.clear();
        sessionStorage.clear();
      }

      // Force refresh to ensure clean state
      globalThis.location.reload();
    }

    if (session?.userId) {
      setLastUserId(session.userId);
    }
  }, [sessionQuery.data?.userId, lastUserId, queryClient]);

  return sessionQuery;
}
