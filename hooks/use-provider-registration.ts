import React from 'react';
import { useMutation } from '@tanstack/react-query';
import { ProviderResponseType } from '@/types/provider';
import { getQueryClient } from '@/lib/query-client';
import { apiClient } from '@/hooks/use-api';
import { z } from 'zod';

// Types and interfaces for provider registration
export interface AddProviderFormProps {
  onSuccess?: (provider: any) => void;
  trigger?: React.ReactNode;
  orgId: string;
}

export interface AddProviderFormData {
  orgId: string;
  name: string;
  description: string;
  website: string;
  supportEmail: string | null;
  documentation: string | null;
  sdkLink: string | null;
  logo: File | null;
  logoDark: File | null;
}

export interface ProviderSubmissionResponse {
  success: boolean;
  data: ProviderResponseType;
  metadata: {
    requestId: string;
    processingTime: string;
    logoUrl?: string;
    logoDarkUrl?: string;
  };
}

export interface ApiError {
  error: string;
  requestId?: string;
  processingTime?: string;
}

// Validation schema with proper validations
export const addProviderSchema = z.object({
  orgId: z.string().min(1, 'Organization ID is required'),
  name: z
    .string()
    .min(1, 'Provider name is required')
    .max(100, 'Provider name must be less than 100 characters'),

  description: z
    .string()
    .min(1, 'Description is required')
    .max(500, 'Description must be less than 500 characters'),

  website: z.string().min(1, 'Website is required').url('Please enter a valid website URL'),

  supportEmail: z
    .string()
    .email('Please enter a valid email address')
    .nullable()
    .optional()
    .or(z.literal('')),

  documentation: z
    .string()
    .url('Please enter a valid documentation URL')
    .nullable()
    .optional()
    .or(z.literal('')),

  sdkLink: z
    .string()
    .url('Please enter a valid SDK URL')
    .nullable()
    .optional()
    .or(z.literal('')),

  logo: z
    .instanceof(File)
    .nullable()
    .refine((file) => {
      if (!file) return true; // Optional file
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      return allowedTypes.includes(file.type);
    }, 'Logo must be a valid image file (JPEG, PNG, GIF, or WebP)')
    .refine((file) => {
      if (!file) return true; // Optional file
      const maxSize = 5 * 1024 * 1024; // 5MB
      return file.size <= maxSize;
    }, 'Logo file size must be less than 5MB'),

  logoDark: z
    .instanceof(File)
    .nullable()
    .refine((file) => {
      if (!file) return true; // Optional file
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      return allowedTypes.includes(file.type);
    }, 'Dark logo must be a valid image file (JPEG, PNG, GIF, or WebP)')
    .refine((file) => {
      if (!file) return true; // Optional file
      const maxSize = 5 * 1024 * 1024; // 5MB
      return file.size <= maxSize;
    }, 'Dark logo file size must be less than 5MB'),
});

// Form validation helpers
export function validateProviderForm(data: AddProviderFormData): {
  success: boolean;
  errors?: Record<string, string>;
} {
  try {
    addProviderSchema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      for (const err of error.errors) {
        if (err.path.length > 0) {
          errors[err.path[0] as string] = err.message;
        }
      }
      return { success: false, errors };
    }
    return { success: false, errors: { general: 'Validation failed' } };
  }
}

// Helper to create FormData for API submission
export function createProviderFormData(data: AddProviderFormData): FormData {
  const formData = new FormData();

  formData.append('name', data.name);
  formData.append('description', data.description);
  formData.append('website', data.website);
  formData.append('supportEmail', data.supportEmail || '');
  formData.append('documentation', data.documentation || '');
  formData.append('sdkLink', data.sdkLink || '');
  formData.append('orgId', data.orgId);

  if (data.logo) {
    formData.append('logo', data.logo);
  }

  if (data.logoDark) {
    formData.append('logoDark', data.logoDark);
  }

  return formData;
}

// Add provider mutation using FormData for file uploads
export function useAddProvider() {
  const queryClient = getQueryClient();

  return useMutation<ProviderSubmissionResponse, Error, { formData: FormData }>({
    mutationFn: async ({ formData }) => {
      return apiClient('/api/providers', {
        method: 'POST',
        body: formData, // apiClient handles FormData automatically
      });
    },
    onSuccess: (result: ProviderSubmissionResponse) => {
      // Invalidate and refetch provider queries
      queryClient.invalidateQueries({ queryKey: ['providers'] });
      queryClient.invalidateQueries({ queryKey: ['orgProviders'] });
    },
  });
}

// Update provider mutation using FormData for file uploads
export function useUpdateProvider() {
  const queryClient = getQueryClient();

  return useMutation<ProviderSubmissionResponse, Error, { providerId: string; formData: FormData }>({
    mutationFn: async ({ providerId, formData }) => {
      return apiClient(`/api/providers/${providerId}`, {
        method: 'PATCH',
        body: formData, // apiClient handles FormData automatically
      });
    },
    onSuccess: (result: ProviderSubmissionResponse) => {
      // Invalidate and refetch provider queries
      queryClient.invalidateQueries({ queryKey: ['providers'] });
      queryClient.invalidateQueries({ queryKey: ['orgProviders'] });
      queryClient.invalidateQueries({ queryKey: ['provider-ownership'] });
    },
  });
}