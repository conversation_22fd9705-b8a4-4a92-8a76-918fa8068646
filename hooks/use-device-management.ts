import { useQuery, useMutation } from '@tanstack/react-query';
import { apiClient } from '@/hooks/use-api';
import type { AvailableDevice, PublicDevice, DeviceData } from '@/types/device';

// Get devices owned by a provider
export function useProviderDevices(providerId: string) {
  return useQuery<DeviceData[]>({
    queryKey: ['provider-devices', providerId],
    queryFn: async (): Promise<DeviceData[]> => {
      return apiClient(`/api/providers/${providerId}/devices`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!providerId,
  });
}

// Fixed Hook: useAvailableDevices - returns only public device information
export function useAvailableDevices(
  orgId: string,
  filters?: {
    provider?: string;
    type?: string;
    status?: string;
  },
) {
  return useQuery<AvailableDevice[]>({
    queryKey: ['available-devices', orgId, filters],
    queryFn: async (): Promise<AvailableDevice[]> => {
      const params = new URLSearchParams();

      // Add orgId to query params
      params.append('orgId', orgId);

      // Add filters
      if (filters?.provider) params.append('provider', filters.provider);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.status) params.append('deviceStatus', filters.status);

      return apiClient(`/api/devices/available?${params.toString()}`);
    },
    enabled: !!orgId, // Only run query if orgId exists
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get device details by ID
export function useDeviceDetails(qbraidId: string, orgId: string) {
  return useQuery<Device>({
    queryKey: ['device-details', qbraidId],
    queryFn: async (): Promise<Device> => {
      return apiClient(`/api/quantum-devices/${qbraidId}?orgId=${orgId}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!qbraidId && !!orgId,
  });
}

// Batch get device details (for access listings)
export function useBatchDeviceDetails(deviceIds: string[]) {
  return useQuery<Device[]>({
    queryKey: ['batch-device-details', deviceIds.sort()],
    queryFn: async (): Promise<Device[]> => {
      if (deviceIds.length === 0) return [];

      return apiClient(`/api/devices/batch`, {
        method: 'POST',
        body: JSON.stringify({ deviceIds }),
      });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: deviceIds.length > 0,
  });
}
