import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function useThemeHydrated() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before accessing theme
  useEffect(() => {
    setMounted(true);
  }, []);

  // Use resolvedTheme for more reliable theme detection
  const currentTheme = mounted ? resolvedTheme : 'light';
  const isDark = mounted ? resolvedTheme === 'dark' : false;

  return {
    theme,
    setTheme,
    resolvedTheme,
    currentTheme,
    isDark,
    mounted,
  };
}
