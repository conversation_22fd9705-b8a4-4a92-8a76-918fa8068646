import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/hooks/use-api';

/**
 * Interface for the external API /user/role response
 */
interface ExternalUserRoleResponse {
  role: string;
  isQbraidAdmin?: boolean;
  permissions?: string[];
  // Add any other fields that the external API might return
}

/**
 * Hook to check if current user is a qBraid admin using external API /user/role
 * This provides a dedicated and more robust admin check for sensitive operations
 */
export const useExternalQbraidAdminRole = () => {
  return useQuery<ExternalUserRoleResponse, Error>({
    queryKey: ['externalQbraidAdminRole'],
    queryFn: async () => {
      try {
        const data = await apiClient('/api/user/role');
        return {
          role: data,
          isQbraidAdmin: data === 'admin',
          permissions: data.permissions || [],
        };
      } catch (error) {
        console.error('❌ [EXTERNAL-ADMIN-ROLE] Admin role check failed:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once for admin checks
    refetchOnWindowFocus: false, // Don't refetch on window focus for security
  });
};

/**
 * Hook that returns a boolean for admin status
 * Useful for simple admin checks in components
 */
export const useIsQbraidAdmin = () => {
  const { data: adminData, isLoading, isError } = useExternalQbraidAdminRole();

  return {
    isQbraidAdmin: adminData?.role === 'admin',
    isLoading,
    isError,
    adminRole: adminData?.role,
  };
};
