import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/hooks/use-api';

interface CacheStats {
  totalKeys: number;
  cacheSize: string;
  ttl: number;
  healthStatus: string;
}

interface CacheMonitoringData {
  redis: {
    healthy: boolean;
    info?: any;
  };
  cache: CacheStats | null;
  performance: {
    estimatedDailyLogins: number;
    potentialAPICalls: number;
    estimatedCacheHitRate: string;
  };
  userScale: {
    totalUsers: number;
    cacheUtilization: string;
  };
  recommendations: string[];
  processingTime: string;
  timestamp: string;
}

/**
 * Hook for monitoring Redis cache performance with 19k users
 * Provides real-time insights into cache efficiency and recommendations
 */
export function useCacheMonitoring(enabled: boolean = true) {
  const [isPolling, setIsPolling] = useState(false);

  const { data, isLoading, error, refetch, isRefetching } = useQuery({
    queryKey: ['cache-stats'],
    queryFn: async (): Promise<CacheMonitoringData> => {
      const result = await apiClient('/api/cache-stats');

      if (result.status !== 'success') {
        throw new Error(result.message || 'Failed to get cache statistics');
      }

      return result.data;
    },
    enabled: enabled,
    refetchInterval: isPolling ? 30_000 : false, // Poll every 30 seconds when enabled
    staleTime: 10_000, // Data is fresh for 10 seconds
    retry: 2,
  });

  // Derived state for easy consumption
  const cacheHealth = data?.redis?.healthy ?? false;
  const totalCachedUsers = data?.cache?.totalKeys ?? 0;
  const cacheUtilization = data?.userScale?.cacheUtilization ?? '0%';
  const recommendations = data?.recommendations ?? [];

  // Performance metrics
  const estimatedSavings = data?.performance?.estimatedCacheHitRate ?? '0%';
  const potentialAPICallReduction = data?.performance?.potentialAPICalls ?? 0;

  // Helper functions
  const startPolling = () => setIsPolling(true);
  const stopPolling = () => setIsPolling(false);

  const refresh = () => {
    return refetch();
  };

  // Auto-stop polling on unmount
  useEffect(() => {
    return () => {
      setIsPolling(false);
    };
  }, []);

  return {
    // Data
    data,
    cacheStats: data?.cache,
    redisInfo: data?.redis,
    performance: data?.performance,
    userScale: data?.userScale,

    // Derived metrics
    cacheHealth,
    totalCachedUsers,
    cacheUtilization,
    recommendations,
    estimatedSavings,
    potentialAPICallReduction,

    // State
    isLoading: isLoading || isRefetching,
    error,
    isPolling,

    // Actions
    refresh,
    startPolling,
    stopPolling,

    // Helpers
    getHealthColor: () => {
      if (!cacheHealth) return 'text-red-500';
      if (totalCachedUsers > 15_000) return 'text-yellow-500';
      return 'text-green-500';
    },

    getUtilizationColor: () => {
      const utilization = Number.parseFloat(cacheUtilization);
      if (utilization < 5) return 'text-yellow-500';
      if (utilization > 80) return 'text-red-500';
      return 'text-green-500';
    },

    formatCacheSize: (size?: string) => {
      return size || '0 B';
    },

    getRecommendationSeverity: (recommendation: string) => {
      if (recommendation.toLowerCase().includes('monitor')) return 'warning';
      if (recommendation.toLowerCase().includes('memory')) return 'error';
      return 'info';
    },
  };
}

/**
 * Simplified hook for basic cache health monitoring
 */
export function useCacheHealth() {
  const { cacheHealth, totalCachedUsers, isLoading, error } = useCacheMonitoring();

  return {
    isHealthy: cacheHealth,
    cachedUsers: totalCachedUsers,
    isLoading,
    hasError: !!error,
  };
}

/**
 * Hook for bulk operations monitoring
 */
export function useBulkOperations() {
  const [isBulkRefreshing, setIsBulkRefreshing] = useState(false);
  const [bulkResults, setBulkResults] = useState<any>(null);

  const performBulkRefresh = async (emails: string[], batchSize: number = 10) => {
    setIsBulkRefreshing(true);
    setBulkResults(null);

    try {
      const result = await apiClient('/api/bulk-refresh-roles', {
        method: 'POST',
        body: JSON.stringify({
          emails,
          forceRefresh: true,
          batchSize,
        }),
      });
      setBulkResults(result);

      return result;
    } catch (error) {
      console.error('Bulk refresh failed:', error);
      setBulkResults({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    } finally {
      setIsBulkRefreshing(false);
    }
  };

  return {
    performBulkRefresh,
    isBulkRefreshing,
    bulkResults,
    clearResults: () => setBulkResults(null),
  };
}
