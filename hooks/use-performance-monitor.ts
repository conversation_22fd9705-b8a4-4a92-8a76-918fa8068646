import { useEffect, useRef } from 'react';

/**
 * Performance monitoring hook to track component render times
 * Only active in development mode
 *
 * @param name - Name of the component/hook being monitored
 * @param threshold - Milliseconds threshold for warning (default: 50ms)
 */
export function usePerformanceMonitor(name: string, threshold: number = 50) {
  const renderStart = useRef<number>(performance.now());
  const renderCount = useRef<number>(0);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const renderTime = performance.now() - renderStart.current;
    renderCount.current += 1;

    // Log warning for slow renders
    if (renderTime > threshold) {
      console.warn(`⚠️ [PERFORMANCE] ${name} slow render detected:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        renderCount: renderCount.current,
        threshold: `${threshold}ms`,
      });
    }

    // Reset timer for next render
    renderStart.current = performance.now();
  });

  // Track unmount time
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    return () => {
      console.info(`📊 [PERFORMANCE] ${name} unmounted after ${renderCount.current} renders`);
    };
  }, [name]);
}

/**
 * Hook to measure specific operation performance
 *
 * @returns Object with start and end functions to measure operations
 */
export function useOperationTimer() {
  const timers = useRef<Map<string, number>>(new Map());

  const startTimer = (operationName: string) => {
    if (process.env.NODE_ENV !== 'development') return;
    timers.current.set(operationName, performance.now());
  };

  const endTimer = (operationName: string, warnThreshold?: number) => {
    if (process.env.NODE_ENV !== 'development') return;

    const startTime = timers.current.get(operationName);
    if (!startTime) {
      console.warn(`⚠️ [PERFORMANCE] No start time found for operation: ${operationName}`);
      return;
    }

    const duration = performance.now() - startTime;
    timers.current.delete(operationName);

    const logData = {
      operation: operationName,
      duration: `${duration.toFixed(2)}ms`,
      timestamp: new Date().toISOString(),
    };

    if (warnThreshold && duration > warnThreshold) {
      console.warn(`⚠️ [PERFORMANCE] Slow operation detected:`, logData);
    } else {
      console.info(`📊 [PERFORMANCE] Operation completed:`, logData);
    }

    return duration;
  };

  return { startTimer, endTimer };
}
