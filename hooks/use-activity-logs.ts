import { useQuery } from '@tanstack/react-query';
import { ActivityLogAction, ResourceType } from '@/lib/audit-logger-server';
import { apiClient } from '@/hooks/use-api';

interface ActivityLog {
  _id: string;
  organizationId: string;
  userId: string;
  userEmail: string;
  action: ActivityLogAction;
  resourceType: ResourceType;
  resourceId?: string;
  resourceName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ActivityLogsResponse {
  success: boolean;
  data: {
    logs: ActivityLog[];
    pagination: {
      total: number;
      page: number;
      pages: number;
      limit: number;
    };
  };
}

/**
 * Hook to fetch activity logs with filtering and pagination
 * Uses internal API gateway instead of external client
 */
export const useActivityLogs = (params: {
  organizationId: string;
  page?: number;
  resultsPerPage?: number;
  userId?: string;
  action?: ActivityLogAction;
  resourceType?: ResourceType;
  startDate?: string;
  endDate?: string;
  enabled?: boolean;
}) => {
  const {
    organizationId,
    page = 0,
    resultsPerPage = 10,
    userId,
    action,
    resourceType,
    startDate,
    endDate,
    enabled = true,
  } = params;

  const queryParams = new URLSearchParams({
    organizationId,
    page: page.toString(),
    resultsPerPage: resultsPerPage.toString(),
  });

  // Add optional filters
  if (userId) queryParams.append('userId', userId);
  if (action) queryParams.append('action', action);
  if (resourceType) queryParams.append('resourceType', resourceType);
  if (startDate) queryParams.append('startDate', startDate);
  if (endDate) queryParams.append('endDate', endDate);

  return useQuery<ActivityLogsResponse>({
    queryKey: [
      'activityLogs',
      organizationId,
      page,
      resultsPerPage,
      userId,
      action,
      resourceType,
      startDate,
      endDate,
    ],
    queryFn: async () => {
      return apiClient(`/api/activity-logs?${queryParams.toString()}`);
    },
    enabled: enabled && Boolean(organizationId),
  });
};
