import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  // Get user session
  const { session, error } = await requireAuth();
  if (error) return error;
  if (!session?.email) {
    console.error('❌ [USER-ROLE-API] Authentication failed:', { requestId: request.url });
    return NextResponse.json(
      { error: 'Authentication required', requestId: request.url },
      { status: 401 },
    );
  }

  try {
    // Call external API /user/role endpoint
    const response = await externalClient.get('/get-role');
    console.log('🔍 [USER-ROLE-API] Response:', response.data);
    return NextResponse.json(response.data, { status: response.status });
  } catch (error: unknown) {
    console.error('❌ [USER-ROLE-API] Error getting user role:', {
      error: (error as { message: string }).message,
      status: (error as { status: number }).status,
      email: session.email,
      requestId: request.url,
    });
    return NextResponse.json(
      {
        error: (error as { message: string }).message || 'Failed to get user role',
      },
      { status: (error as { status: number }).status || 500 },
    );
  }
}
