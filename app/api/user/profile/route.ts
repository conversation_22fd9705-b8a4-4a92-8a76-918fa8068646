import { NextRequest, NextResponse } from 'next/server';

import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';
import type { UserProfile } from '@/types/user';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [USER-PROFILE-API] Starting user profile fetch request');
  console.log('📊 [USER-PROFILE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent')?.slice(0, 50) + '...',
    referer: request.headers.get('referer') || 'N/A',
  });

  try {
    // Use auth middleware for consistent authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      console.error('❌ [USER-PROFILE-API] Authentication failed:', { requestId });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    console.log('✅ [USER-PROFILE-API] Session retrieved:', {
      requestId,
      userEmail: session.email,
      userId: session.userId || 'N/A',
    });

    // Call external API (no fallback to sample data)
    console.log('📤 [USER-PROFILE-API] Calling external API...');
    const externalApiStart = Date.now();
    const apiRes = await externalClient.get('/get-user-info');
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [USER-PROFILE-API] External API response received:', {
      requestId,
      responseTime: externalApiTime + 'ms',
      responseStatus: apiRes.status || 'unknown',
      responseSize: JSON.stringify(apiRes.data).length + ' bytes',
      hasProfile: !!apiRes.data,
      dataKeys: apiRes.data ? Object.keys(apiRes.data) : [],
    });

    // Validate external API response
    if (!apiRes.data || typeof apiRes.data !== 'object') {
      console.error('❌ [USER-PROFILE-API] External API returned invalid data:', {
        requestId,
        hasData: !!apiRes.data,
        dataType: typeof apiRes.data,
      });
      return NextResponse.json(
        {
          error: 'Failed to fetch user profile from external API',
          requestId,
          success: false,
        },
        { status: 500 },
      );
    }

    console.log('✅ [USER-PROFILE-API] Using external API data:', {
      requestId,
      firstName: apiRes.data.firstName,
      lastName: apiRes.data.lastName,
      email: apiRes.data.email,
      hasPersonalInfo: !!apiRes.data.personalInformation,
    });

    const response = {
      success: true,
      profile: apiRes.data,
      requestId,
      timestamp: new Date().toISOString(),
    };

    // Final response logging
    const totalTime = Date.now() - startTime;
    const responseSize = JSON.stringify(response).length;

    console.log('🎉 [USER-PROFILE-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      dataSource: 'external-api',
      responseSize: responseSize + ' bytes',
      profileEmail: response.profile.email,
      firstName: response.profile.firstName,
      lastName: response.profile.lastName,
      hasPersonalInfo: !!response.profile.personalInformation,
      processingTime: totalTime + 'ms',
    });

    return NextResponse.json(response);
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-PROFILE-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [USER-PROFILE-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : JSON.stringify(error),
      stackTrace: error instanceof Error ? error.stack?.split('\n').slice(0, 5) : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: 'Failed to fetch user profile',
        requestId,
        timestamp: new Date().toISOString(),
        success: false,
      },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [USER-PROFILE-API] Starting profile update request');
  console.log('📊 [USER-PROFILE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent')?.slice(0, 50) + '...',
    contentType: request.headers.get('content-type'),
    contentLength: request.headers.get('content-length'),
  });

  try {
    // Parse request body
    const bodyParseStart = Date.now();
    const body = await request.json();
    const bodyParseTime = Date.now() - bodyParseStart;

    console.log('📋 [USER-PROFILE-API] Update body parsed:', {
      requestId,
      parseTime: bodyParseTime + 'ms',
      bodySize: JSON.stringify(body).length + ' bytes',
      updatedFields: Object.keys(body),
      hasPersonalInfo: !!body.personalInformation,
    });

    // Use auth middleware for consistent authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      console.error('❌ [USER-PROFILE-API] Authentication failed for update:', { requestId });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    console.log('✅ [USER-PROFILE-API] Session for update:', {
      requestId,
      userEmail: session.email,
    });

    // Validate updatable fields - only allow specific fields
    const allowedFields = [
      'firstName',
      'lastName',
      'personalInformation',
      // Add other allowed fields as needed
    ];

    const updateData: Partial<UserProfile> = {};
    const updatedFields: string[] = [];

    // Track changes for audit logging
    const changes: string[] = [];

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        (updateData as any)[field] = body[field];
        updatedFields.push(field);

        // Track specific changes for audit
        if (field === 'firstName') changes.push('first name');
        if (field === 'lastName') changes.push('last name');
        if (field === 'personalInformation') {
          const personalFields = Object.keys(body[field] || {});
          if (personalFields.length > 0) {
            changes.push(`personal information (${personalFields.join(', ')})`);
          }
        }
      }
    }

    if (updatedFields.length === 0) {
      console.warn('⚠️ [USER-PROFILE-API] No valid fields to update:', {
        requestId,
        receivedFields: Object.keys(body),
      });
      return NextResponse.json(
        { error: 'No valid fields provided for update', allowedFields, requestId },
        { status: 400 },
      );
    }

    console.log('📝 [USER-PROFILE-API] Proceeding with profile update:', {
      requestId,
      updatedFields,
      dataSize: JSON.stringify(updateData).length + ' bytes',
    });

    // Call external API to update profile
    const externalApiStart = Date.now();
    const apiRes = await externalClient.put('/update-user-info', updateData);
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [USER-PROFILE-API] External API response:', {
      requestId,
      status: apiRes.status,
      responseTime: externalApiTime + 'ms',
      success: !!apiRes.data?.success,
    });

    // TODO: Add profile update audit logging when function is implemented

    const response = {
      success: true,
      message: 'Profile updated successfully',
      updatedFields,
      requestId,
      timestamp: new Date().toISOString(),
    };

    // Final response logging
    const totalTime = Date.now() - startTime;
    const responseSize = JSON.stringify(response).length;

    console.log('�� [USER-PROFILE-API] Update completed:', {
      requestId,
      totalTime: totalTime + 'ms',
      updateSource: 'external-api',
      updatedFields,
    });

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['userProfile'], // Invalidate user profile cache
          ['profile'], // Alternative profile cache key
        ],
        message: 'User profile updated - refresh profile data',
      }),
    );

    return nextResponse;
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-PROFILE-API] Update failed:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      totalTime: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: 'Failed to update profile',
        requestId,
        timestamp: new Date().toISOString(),
        success: false,
      },
      { status: 500 },
    );
  }
}
