import { NextRequest, NextResponse } from 'next/server';
import { updatePassword, getCurrentUser } from 'aws-amplify/auth';
import { requireAuth } from '@/lib/auth-middleware';

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [CHANGE-PASSWORD-API] Starting password change request');
  console.log('📊 [CHANGE-PASSWORD-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse request body
    const body: ChangePasswordRequest = await request.json();
    const { currentPassword, newPassword, confirmPassword } = body;

    // Validate required fields
    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json({ error: 'All fields are required', requestId }, { status: 400 });
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      return NextResponse.json({ error: 'New passwords do not match', requestId }, { status: 400 });
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long', requestId },
        { status: 400 },
      );
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      return NextResponse.json(
        {
          error:
            'Password must contain at least one uppercase letter, one lowercase letter, and one number',
          requestId,
        },
        { status: 400 },
      );
    }

    // Use auth middleware for consistent authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      console.error('❌ [CHANGE-PASSWORD-API] Authentication failed:', { requestId });
      return NextResponse.json({ error: 'Authentication required', requestId }, { status: 401 });
    }

    console.log('✅ [CHANGE-PASSWORD-API] Session validated:', {
      requestId,
      userEmail: session.email,
    });

    // Get current user to verify they're authenticated
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return NextResponse.json({ error: 'User not authenticated', requestId }, { status: 401 });
    }

    // Change password using AWS Amplify
    await updatePassword({ oldPassword: currentPassword, newPassword });

    // TODO: Add specific audit logging for password changes
    console.log('✅ [AUDIT] Password change successful:', {
      userEmail: session.email,
      timestamp: new Date().toISOString(),
      requestId,
    });

    console.log('✅ [CHANGE-PASSWORD-API] Password changed successfully:', {
      requestId,
      userEmail: session.email,
      responseTime: Date.now() - startTime + 'ms',
    });

    return NextResponse.json({
      success: true,
      message: 'Password changed successfully',
      requestId,
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    console.error('❌ [CHANGE-PASSWORD-API] Password change failed:', error);

    // Handle specific AWS Amplify errors
    let errorMessage = 'Failed to change password. Please try again.';
    let statusCode = 500;

    switch (error.name) {
    case 'NotAuthorizedException': {
      errorMessage = 'Current password is incorrect';
      statusCode = 400;
    
    break;
    }
    case 'InvalidPasswordException': {
      errorMessage = 'New password does not meet requirements';
      statusCode = 400;
    
    break;
    }
    case 'LimitExceededException': {
      errorMessage = 'Too many password change attempts. Please try again later.';
      statusCode = 429;
    
    break;
    }
    // No default
    }

    // TODO: Add specific audit logging for failed password changes
    console.error('❌ [AUDIT] Password change failed:', {
      error: errorMessage,
      timestamp: new Date().toISOString(),
      requestId,
    });

    return NextResponse.json(
      {
        error: errorMessage,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: statusCode },
    );
  }
}
