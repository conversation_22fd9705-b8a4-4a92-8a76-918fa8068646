import { NextRequest, NextResponse } from 'next/server';
import { validateS3Config } from '@/lib/image/image-upload-server';
import {
  uploadImageToS3,
  extractFilesFromFormData,
  createUploadConfig,
  logUploadOperation,
  type ImageUploadConfig,
} from '@/lib/image/image-upload';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';
import { requireProviderPermissions } from '@/lib/auth-middleware';
import { logProviderCreated } from '@/lib/audit-logger-server';
import { ProviderResponseType } from '@/types/provider';
/**
 * GET /api/providers
 * Fetch providers from external API
 */
export async function GET() {
  try {
    const { session, error } = await requireAuth();

    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const response = await externalClient.get('/providers');
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('❌ [PROVIDERS-API] Error fetching providers:', error);
    return NextResponse.json({ error: 'Failed to fetch providers' }, { status: 500 });
  }
}

/**
 * POST /api/providers
 * Complete provider submission: upload images to S3, then submit to external API
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).slice(2, 11);
  const startTime = Date.now();

  console.log(`🚀 [PROVIDER-API] Starting provider submission { requestId: '${requestId}' }`);

  try {
    // 1. Check authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed { requestId: '${requestId}' }`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // 2. Validate S3 configuration
    const s3ConfigCheck = validateS3Config();
    if (!s3ConfigCheck.valid) {
      console.error(
        `❌ [PROVIDER-API] S3 config invalid { requestId: '${requestId}', error: '${s3ConfigCheck.error}' }`,
      );
      return NextResponse.json({ error: 'Upload service not configured' }, { status: 500 });
    }

    // 3. Parse form data
    const formData = await request.formData();

    // Extract form fields
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const website = formData.get('website') as string;
    const orgId = formData.get('orgId') as string;
    const supportEmail = formData.get('supportEmail') as string;
    const documentation = formData.get('documentation') as string;
    const sdkLink = formData.get('sdkLink') as string;

    // Validate required fields
    if (!name || !description || !website) {
      console.error(`❌ [PROVIDER-API] Missing required fields { requestId: '${requestId}' }`);
      return NextResponse.json(
        {
          error: 'Missing required fields: name, description, and website are required',
        },
        { status: 400 },
      );
    }

    // Validate website URL format
    try {
      new URL(website);
    } catch {
      console.error(
        `❌ [PROVIDER-API] Invalid website URL { requestId: '${requestId}', website: '${website}' }`,
      );
      return NextResponse.json({ error: 'Invalid website URL format' }, { status: 400 });
    }

    // Check provider permissions
    const { error: providerError, data } = await requireProviderPermissions('manage', orgId);
    if (providerError) return providerError;
    const userRole = data?.role;
    console.log(
      `📋 [PROVIDER-API] Form data received { requestId: '${requestId}', name: '${name}' }`,
    );

    const userId = session.userId || session.email;

    // 4. Extract and upload images using the reusable service
    const files = extractFilesFromFormData(formData, ['logo', 'logoDark']);

    let logoUrl = '';
    let logoDarkUrl = '';

    if (files.length > 0) {
      console.log(
        `📤 [PROVIDER-API] Uploading ${files.length} images { requestId: '${requestId}' }`,
      );

      // Option 1: Upload files individually (more control)
      for (const { fieldName, file } of files) {
        const uploadConfig: ImageUploadConfig = createUploadConfig(
          { orgId, userId, requestId, providerName: name, folderName: 'providers-assets' },
          fieldName,
        );

        logUploadOperation('start', uploadConfig, {
          fileName: file.name,
          fileSize: file.size,
        });

        const uploadResult = await uploadImageToS3(file, uploadConfig);

        if (uploadResult.success && uploadResult.url) {
          if (fieldName === 'logo') {
            logoUrl = uploadResult.url;
          } else if (fieldName === 'logoDark') {
            logoDarkUrl = uploadResult.url;
          }

          logUploadOperation('success', uploadConfig, {
            fileName: file.name,
            url: uploadResult.url,
            duration: Date.now() - startTime,
          });
        } else {
          logUploadOperation('error', uploadConfig, {
            fileName: file.name,
            error: uploadResult.error,
          });

          return NextResponse.json(
            {
              error: `Failed to upload ${fieldName}: ${uploadResult.error}`,
              requestId,
            },
            { status: 400 },
          );
        }
      }

      // Option 2: Batch upload (alternative approach)
      /*
      const uploadConfig = createUploadConfig(
        { orgId, userId, requestId },
        'provider-assets'
      );

      const batchResult = await uploadMultipleImagesToS3(files, uploadConfig);
      
      if (!batchResult.success) {
        return NextResponse.json(
          { 
            error: 'Failed to upload images',
            details: batchResult.errors,
            requestId 
          },
          { status: 400 }
        );
      }

      // Extract URLs from batch results
      batchResult.results.forEach(({ fieldName, result }) => {
        if (result.success && result.url) {
          if (fieldName === 'logo') logoUrl = result.url;
          if (fieldName === 'logoDark') logoDarkUrl = result.url;
        }
      });
      */

      console.log(
        `✅ [PROVIDER-API] Images uploaded successfully { requestId: '${requestId}', logoUrl: '${logoUrl}', logoDarkUrl: '${logoDarkUrl}' }`,
      );
    }
    // Main Provider schema
    // const ProviderSchema = new mongoose.Schema(
    // 	{
    // 		provider: {
    // 			type: String,
    // 			required: true,
    // 			unique: true
    // 		},
    // 		providerDescription: { type: String, required: false },
    // 		about: { type: String, required: false },
    // 		status: {
    // 			type: String,
    // 			required: true,
    // 			default: 'private',
    // 			enum: ['private', 'public', 'pending_approval', 'removed']
    // 		},
    // 		devices: [DeviceSchema],
    // 		logoUrl: { type: String, required: false },
    // 		logoUrlDark: { type: String, required: false }
    // 	},
    // 	{ timestamps: true }
    // );

    // 5. Prepare data for external API
    const providerData = {
      owner: orgId,
      createdBy: session.email,
      provider: name,
      providerDescription: description,
      about: website,
      status: 'private',
      ...(logoUrl && { logoUrl: logoUrl }),
      ...(logoDarkUrl && { logoUrlDark: logoDarkUrl }),
      assets: {
        documentation: documentation || '',
        links: sdkLink,
        supportEmail: supportEmail || '',
      },
      metadata: {
        submittedBy: session.email,
        userId: session.userId,
        orgId,
        submittedAt: new Date().toISOString(),
        requestId,
      },
    };
    console.log('🌐 [PROVIDER-API] Provider data', JSON.stringify(providerData, null, 2));

    console.log(`🌐 [PROVIDER-API] Sending to external API { requestId: '${requestId}' }`);

    // 6. Submit to external API
    const externalResponse = await externalClient.post('/providers', providerData);
    const providerResponse: ProviderResponseType = externalResponse.data;
    const totalDuration = Date.now() - startTime;
    console.log(
      `✅ [PROVIDER-API] Provider submitted successfully { requestId: '${requestId}', duration: '${totalDuration}ms' }`,
    );
    // add the provider id to org providers
    logProviderCreated(providerResponse._id, name, orgId, userRole || '', {
      email: session.email,
      userId: session.userId,
      requestId,
      processingTime: `${totalDuration}ms`,
    });

    return NextResponse.json({
      success: true,
      data: providerResponse,
      metadata: {
        requestId,
        processingTime: `${totalDuration}ms`,
        email: session.email,
        userId: session.userId,
        orgId,
      },
    });
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(
      `🚨 [PROVIDER-API] Submission failed { requestId: '${requestId}', duration: '${totalDuration}ms', error: }`,
      error,
    );

    // Handle specific error types
    let errorMessage = 'Provider submission failed';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('Invalid file type') || error.message.includes('File too large')) {
        errorMessage = error.message;
        statusCode = 400;
      } else if (error.message.includes('Missing required environment variable')) {
        errorMessage = 'Upload service configuration error';
        statusCode = 500;
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        requestId,
        processingTime: `${totalDuration}ms`,
      },
      { status: statusCode },
    );
  }
}
