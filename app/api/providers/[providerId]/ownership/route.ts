import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

// GET /api/providers/:providerId/ownership
// Check if organization owns a provider
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-OWNERSHIP-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId } = await params;
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');

    if (!providerId || !orgId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    try {
      // Check provider ownership
      const response = await externalClient.get(`/providers/${providerId}/ownership?orgId=${orgId}`);
      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ ownsProvider: false }, { status: 200 });
      }
      console.error('Error checking provider ownership from external API:', error);
      return NextResponse.json(
        { message: 'Failed to check provider ownership' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider ownership route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}