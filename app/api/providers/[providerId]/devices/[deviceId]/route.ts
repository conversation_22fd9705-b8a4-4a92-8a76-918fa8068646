import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth, requireDevicePermission } from '@/lib/auth-middleware';

// GET /api/providers/[providerId]/devices/[deviceId]
// Get specific device for a provider
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string; deviceId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-DEVICE-DETAILS-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId, deviceId } = await params;

    if (!providerId || !deviceId) {
      return NextResponse.json({ error: 'Provider ID and Device ID are required' }, { status: 400 });
    }

    try {
      const response = await externalClient.get(`/providers/${providerId}/devices/${deviceId}`);
      
      console.log(`🌐 [PROVIDER-DEVICE-DETAILS-API] Fetched provider device details`, {
        providerId,
        deviceId,
        deviceName: response.data?.name,
      });

      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ error: 'Device not found' }, { status: 404 });
      }
      console.error('Error fetching provider device details from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch device details' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider device details route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/providers/[providerId]/devices/[deviceId]
// Update device for a provider
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string; deviceId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-DEVICE-UPDATE-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId, deviceId } = await params;
    const updateData = await request.json();

    if (!providerId || !deviceId) {
      return NextResponse.json({ error: 'Provider ID and Device ID are required' }, { status: 400 });
    }

    // Check device management permissions
    const orgId = updateData.organizationId || updateData.orgId;
    if (orgId) {
      const { error: permError } = await requireDevicePermission('manage', orgId);
      if (permError) return permError;
    }

    try {
      const response = await externalClient.put(`/providers/${providerId}/devices/${deviceId}`, updateData);
      
      console.log(`🌐 [PROVIDER-DEVICE-UPDATE-API] Updated provider device`, {
        providerId,
        deviceId,
        updatedBy: session.email,
        fields: Object.keys(updateData),
      });

      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ error: 'Device not found' }, { status: 404 });
      }
      console.error('Error updating provider device via external API:', error);
      return NextResponse.json(
        { message: 'Failed to update device' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider device update route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/providers/[providerId]/devices/[deviceId]
// Delete device from a provider
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string; deviceId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-DEVICE-DELETE-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId, deviceId } = await params;
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');

    if (!providerId || !deviceId) {
      return NextResponse.json({ error: 'Provider ID and Device ID are required' }, { status: 400 });
    }

    // Check device management permissions
    if (orgId) {
      const { error: permError } = await requireDevicePermission('manage', orgId);
      if (permError) return permError;
    }

    try {
      const response = await externalClient.delete(`/providers/${providerId}/devices/${deviceId}`);
      
      console.log(`🌐 [PROVIDER-DEVICE-DELETE-API] Deleted provider device`, {
        providerId,
        deviceId,
        deletedBy: session.email,
        orgId,
      });

      return NextResponse.json({ message: 'Device deleted successfully' });
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ error: 'Device not found' }, { status: 404 });
      }
      console.error('Error deleting provider device via external API:', error);
      return NextResponse.json(
        { message: 'Failed to delete device' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider device delete route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}