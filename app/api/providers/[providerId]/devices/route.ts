import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth, requireDevicePermission } from '@/lib/auth-middleware';

// GET /api/providers/[providerId]/devices
// Get all devices for a specific provider
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-DEVICES-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }

    const queryParams = new URLSearchParams();
    if (status) queryParams.append('status', status);
    if (type) queryParams.append('type', type);

    try {
      const response = await externalClient.get(
        `/providers/${providerId}/devices?${queryParams.toString()}`
      );
      
      console.log(`🌐 [PROVIDER-DEVICES-API] Fetched provider devices`, {
        providerId,
        count: response.data?.length || 0,
        filters: { status, type },
      });

      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json([]);
      }
      console.error('Error fetching provider devices from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch provider devices' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider devices route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/providers/[providerId]/devices
// Create a new device for a provider
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-DEVICE-CREATE-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId } = await params;
    const deviceData = await request.json();

    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }

    // Check device management permissions
    const orgId = deviceData.organizationId || deviceData.orgId;
    if (orgId) {
      const { error: permError } = await requireDevicePermission('manage', orgId);
      if (permError) return permError;
    }

    try {
      const response = await externalClient.post(`/providers/${providerId}/devices`, deviceData);
      
      console.log(`🌐 [PROVIDER-DEVICE-CREATE-API] Created provider device`, {
        providerId,
        deviceName: deviceData.name,
        createdBy: session.email,
      });

      return NextResponse.json(response.data, { status: 201 });
    } catch (error: any) {
      console.error('Error creating provider device via external API:', error);
      return NextResponse.json(
        { message: 'Failed to create device' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider device creation route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}