import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

// GET /api/providers/[providerId]/status
// Get provider status information (used to check if user can create devices)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-STATUS-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId } = await params;

    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }

    try {
      const response = await externalClient.get(`/providers/${providerId}/status`);
      
      console.log(`🌐 [PROVIDER-STATUS-API] Fetched provider status`, {
        providerId,
        status: response.data?.status,
        canCreateDevices: response.data?.canCreateDevices,
      });

      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ 
          status: 'pending', 
          canCreateDevices: false,
          message: 'Provider not found'
        }, { status: 404 });
      }
      console.error('Error fetching provider status from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch provider status' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider status route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/providers/[providerId]/status
// Update provider status (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-STATUS-UPDATE-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is QBraid admin
    try {
      const roleResponse = await externalClient.get('/user/role');
      const role = roleResponse.data?.role;
      if (role !== 'admin') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    } catch (error_) {
      console.error('❌ [PROVIDER-STATUS-UPDATE-API] Error checking user role:', error_);
      return NextResponse.json({ error: 'Failed to verify admin status' }, { status: 401 });
    }

    const { providerId } = await params;
    const { status, reason } = (await request.json()) as {
      status: 'approved' | 'rejected' | 'pending';
      reason?: string;
    };

    if (!providerId || !status) {
      return NextResponse.json({ error: 'Provider ID and status are required' }, { status: 400 });
    }

    try {
      const response = await externalClient.put(`/providers/${providerId}/status`, {
        status,
        reason,
        updatedBy: session.email,
      });
      
      console.log(`🌐 [PROVIDER-STATUS-UPDATE-API] Updated provider status`, {
        providerId,
        status,
        updatedBy: session.email,
        reason,
      });

      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ error: 'Provider not found' }, { status: 404 });
      }
      console.error('Error updating provider status via external API:', error);
      return NextResponse.json(
        { message: 'Failed to update provider status' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in provider status update route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}