import { NextRequest, NextResponse } from 'next/server';
import { validateS3Config } from '@/lib/image/image-upload-server';
import {
  uploadImageToS3,
  extractFilesFromFormData,
  createUploadConfig,
  logUploadOperation,
  type ImageUploadConfig,
} from '@/lib/image/image-upload';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

/**
 * PATCH /api/providers/:providerId
 * Update an existing provider
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ providerId: string }> },
) {
  const requestId = Math.random().toString(36).slice(2, 11);
  const startTime = Date.now();

  console.log(`🔄 [PROVIDER-UPDATE-API] Starting provider update { requestId: '${requestId}' }`);

  try {
    // 1. Check authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-UPDATE-API] Authentication failed { requestId: '${requestId}' }`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { providerId } = await params;

    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }

    // 2. Parse FormData
    const formData = await request.formData();
    const orgId = formData.get('orgId') as string;
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const website = formData.get('website') as string;

    console.log(
      `📝 [PROVIDER-UPDATE-API] Provider data { requestId: '${requestId}', orgId, name }`,
    );

    if (!orgId || !name || !description || !website) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // 3. Validate S3 configuration
    const s3ConfigResult = validateS3Config();
    if (!s3ConfigResult.valid) {
      console.error(
        `❌ [PROVIDER-UPDATE-API] S3 configuration invalid { requestId: '${requestId}' }`,
        s3ConfigResult.error,
      );
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const userId = session.userId || session.email;

    // 4. Extract and process image files
    const files = extractFilesFromFormData(formData, ['logo', 'logoDark']);

    console.log(
      `📁 [PROVIDER-UPDATE-API] Files extracted { requestId: '${requestId}', fileCount: ${files.length} }`,
    );

    // 5. Upload images to S3 if provided
    let logoUrl = '';
    let logoUrlDark = '';

    for (const { fieldName, file } of files) {
      const uploadConfig: ImageUploadConfig = createUploadConfig(
        { orgId, userId, requestId, providerName: name, folderName: 'providers-assets' },
        fieldName,
      );

      const uploadResult = await uploadImageToS3(file, uploadConfig);
      if (!uploadResult.success) {
        console.error(
          `❌ [PROVIDER-UPDATE-API] ${fieldName} upload failed { requestId: '${requestId}' }`,
          uploadResult.error,
        );
        return NextResponse.json({ error: uploadResult.error }, { status: 500 });
      }

      if (fieldName === 'logo') logoUrl = uploadResult.url!;
      if (fieldName === 'logoDark') logoUrlDark = uploadResult.url!;

      logUploadOperation('success', uploadConfig, {
        fileName: file.name,
        url: uploadResult.url,
      });
    }

    // 6. Submit to external API
    const payload: any = {
      orgId,
      provider: name,
      providerDescription: description,
      about: website,
    };

    // Only include logo URLs if new files were uploaded
    if (logoUrl) payload.logoUrl = logoUrl;
    if (logoUrlDark) payload.logoUrlDark = logoUrlDark;

    console.log(
      `🌐 [PROVIDER-UPDATE-API] Submitting to external API { requestId: '${requestId}', providerId }`,
    );

    const response = await externalClient.patch(`/providers/${providerId}/details`, payload);

    const duration = Date.now() - startTime;
    console.log(
      `✅ [PROVIDER-UPDATE-API] Provider updated successfully { requestId: '${requestId}', duration: ${duration}ms, providerId }`,
    );

    return NextResponse.json({
      success: true,
      provider: response.data,
      requestId,
      duration,
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(
      `❌ [PROVIDER-UPDATE-API] Update failed { requestId: '${requestId}', duration: ${duration}ms }`,
      error,
    );

    if (error.response?.status === 404) {
      return NextResponse.json({ error: 'Provider not found' }, { status: 404 });
    }

    if (error.response?.status === 403) {
      return NextResponse.json(
        { error: 'Permission denied: You can only update providers you own' },
        { status: 403 },
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to update provider',
        details: error.response?.data?.message || error.message,
        requestId,
      },
      { status: 500 },
    );
  }
}
