import { NextRequest, NextResponse } from 'next/server';
import { clearAllOrgRolesCache } from '@/lib/external-roles';
import { requireAuth } from '@/lib/auth-middleware';

/**
 * POST /api/admin/clear-org-cache
 * Clear all organization roles cache (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    
    if (!session?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // TODO: Add admin role check here if needed
    // For now, any authenticated user can trigger cache clear

    console.log(`🗑️ [CACHE-CLEAR] Clearing all org roles cache triggered by ${session.email}`);
    
    await clearAllOrgRolesCache();
    
    return NextResponse.json({
      success: true,
      message: 'Organization roles cache cleared successfully',
    });
  } catch (error) {
    console.error('❌ [CACHE-CLEAR] Failed to clear cache:', error);
    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    );
  }
}