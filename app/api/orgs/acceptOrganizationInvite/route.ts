import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';
import { invalidateUserRoles } from '@/lib/external-roles';
import { requireAuth } from '@/lib/auth-middleware';

// Validation schema for accept invitation request
const acceptInviteSchema = z.object({
  orgId: z.string().min(1, { message: 'Organization ID is required' }),
  email: z.string().email({ message: 'Valid email address is required' }),
  orgName: z.string().optional(),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [ACCEPT-INVITE-API] Starting invitation acceptance request');
  console.log('📊 [ACCEPT-INVITE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = acceptInviteSchema.parse(body);

    console.log('✅ [ACCEPT-INVITE-API] Validation successful:', {
      requestId,
      orgId: validatedData.orgId,
      email: validatedData.email,
      orgName: validatedData.orgName,
    });

    // Get session for auth context (optional - user might not be logged in yet)
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', requestId: request.url },
        { status: 401 },
      );
    }
    console.log('🔐 [ACCEPT-INVITE-API] Session status:', {
      requestId,
      hasSession: !!session,
      sessionEmail: session?.email,
      inviteEmail: validatedData.email,
    });

    // Verify email match if user is authenticated
    if (session?.email && session.email.toLowerCase() !== validatedData.email.toLowerCase()) {
      return NextResponse.json(
        {
          error: 'Email mismatch',
          message: 'You can only accept invitations for your own email address',
          success: false,
          code: 'EMAIL_MISMATCH',
        },
        { status: 403 },
      );
    }

    // Call external API to accept the invitation
    console.log('📤 [ACCEPT-INVITE-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/accept', {
      orgId: validatedData.orgId,
      email: validatedData.email,
      ...(validatedData.orgName && { orgName: validatedData.orgName }),
    });

    console.log('📥 [ACCEPT-INVITE-API] External API response received:', {
      requestId,
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success
    const isSuccess =
      apiRes.data?.message === 'Invitation accepted' ||
      apiRes.data?.message?.includes('successfully') ||
      apiRes.data?.message?.includes('accepted') ||
      (!apiRes.data?.error && apiRes.data?.message);

    if (!isSuccess && apiRes.data?.error) {
      // Handle specific error messages from the external API
      const errorMessage =
        apiRes.data?.message || apiRes.data?.error || 'Failed to accept invitation';

      if (
        errorMessage.includes('already in organization') ||
        errorMessage.includes('already accepted')
      ) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'You are already a member of this organization',
            success: false,
            code: 'ALREADY_MEMBER',
          },
          { status: 409 },
        );
      }

      if (
        errorMessage.includes('invitation not found') ||
        errorMessage.includes('invalid invitation')
      ) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'Invitation not found or has expired',
            success: false,
            code: 'INVITATION_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'Organization not found',
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('expired')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'This invitation has expired',
            success: false,
            code: 'INVITATION_EXPIRED',
          },
          { status: 410 },
        );
      }

      // Generic error
      return NextResponse.json(
        {
          error: errorMessage,
          message: errorMessage,
          success: false,
        },
        { status: 400 },
      );
    }

    // Build successful response
    const response = {
      success: true,
      message:
        apiRes.data?.message ||
        `Successfully joined ${validatedData.orgName || 'the organization'}!`,
      orgId: validatedData.orgId,
      orgName: validatedData.orgName || apiRes.data?.orgName,
      email: validatedData.email,
      acceptedAt: new Date().toISOString(),
      requestId,
    };

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ACCEPT-INVITE-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      email: validatedData.email,
      orgId: validatedData.orgId,
      orgName: validatedData.orgName,
    });

    // Invalidate Redis cache for the user who accepted the invitation
    try {
      await invalidateUserRoles(validatedData.email);
      console.log('🗑️ [ACCEPT-INVITE-API] Invalidated Redis cache for user:', validatedData.email);
    } catch (cacheError) {
      console.warn('⚠️ [ACCEPT-INVITE-API] Failed to invalidate cache:', cacheError);
      // Continue - cache invalidation failure shouldn't fail the request
    }

    // Add React Query invalidation headers
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.orgId], // Invalidate org users list
          ['userOrgRole', validatedData.email, validatedData.orgId], // Invalidate specific user role
          ['permissions'], // Invalidate permissions cache
          ['userOrgRoles', validatedData.email], // Invalidate all org roles for user
          ['orgs'], // Invalidate organizations list
        ],
        message: 'Invitation accepted - refresh organizations and permissions',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ACCEPT-INVITE-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Invalid request data',
          details: error.errors,
          success: false,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data?.message) {
      const errorMessage = error.response.data.message;

      if (
        errorMessage.includes('already in organization') ||
        errorMessage.includes('already accepted')
      ) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'You are already a member of this organization',
            success: false,
            code: 'ALREADY_MEMBER',
          },
          { status: 409 },
        );
      }

      if (
        errorMessage.includes('invitation not found') ||
        errorMessage.includes('invalid invitation')
      ) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'Invitation not found or has expired',
            success: false,
            code: 'INVITATION_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('Organization not found')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'Organization not found',
            success: false,
            code: 'ORG_NOT_FOUND',
          },
          { status: 404 },
        );
      }

      if (errorMessage.includes('expired')) {
        return NextResponse.json(
          {
            error: errorMessage,
            message: 'This invitation has expired',
            success: false,
            code: 'INVITATION_EXPIRED',
          },
          { status: 410 },
        );
      }
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to accept invitation',
        message: error.message || 'An unexpected error occurred while accepting the invitation',
        success: false,
        requestId,
      },
      { status: error.status || 500 },
    );
  }
}
