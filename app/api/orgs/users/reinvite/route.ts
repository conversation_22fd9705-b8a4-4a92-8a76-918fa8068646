import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';
import { logUserReinvited } from '@/lib/audit-logger-server';
import { requireTeamManagementPermissions } from '@/lib/auth-middleware';

// Validation schema for reinvite request
const reinviteUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
  name: z.string().min(1, { message: 'Organization name is required' }),
  referer: z.string().min(1, { message: 'Referer is required' }),
  orgId: z.string().min(1, { message: 'Organization ID is required' }),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [REINVITE-USER-API] Starting user reinvite request');
  console.log('📊 [REINVITE-USER-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = reinviteUserSchema.parse(body);
    const { session, data, error } = await requireTeamManagementPermissions(
      validatedData.orgId,
      'invite',
    );
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', message: 'Authentication required', success: false },
        { status: 401 },
      );
    }
    if (error) return error;
    const userRole = data?.currentUserRole;
    console.log('✅ [REINVITE-USER-API] Validation successful:', {
      requestId,
      email: validatedData.email,
      role: validatedData.role,
      orgName: validatedData.name,
      referer: validatedData.referer,
    });

    // Business logic validation
    if (session.email === validatedData.email) {
      return NextResponse.json(
        {
          error: 'Cannot reinvite yourself',
          message: 'Cannot reinvite yourself',
          success: false,
          code: 'SELF_REINVITE_FORBIDDEN',
        },
        { status: 400 },
      );
    }

    // Call external API
    console.log('📤 [REINVITE-USER-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/reinvite', {
      email: validatedData.email,
      role: validatedData.role,
      name: validatedData.name,
      referer: validatedData.referer,
    });

    console.log('📥 [REINVITE-USER-API] External API response received:', {
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success
    const isSuccess =
      apiRes.data?.message === 'User reinvited' ||
      apiRes.data?.message?.includes('reinvited') ||
      (!apiRes.data?.error && apiRes.data?.message);

    // Audit logging for successful reinvites
    if (isSuccess) {
      try {
        await logUserReinvited({
          organizationName: validatedData.name,
          invitedUserEmail: validatedData.email,
          invitedUserRole: validatedData.role,
          userRole: userRole || '',
          invitedByEmail: session.email,
          timestamp: new Date(),
          metadata: {
            requestId,
            referer: validatedData.referer,
            apiResponseTime: Date.now() - startTime,
          },
        });
        console.log('📝 [REINVITE-USER-API] Audit log recorded successfully');
      } catch (auditError) {
        console.error('⚠️ [REINVITE-USER-API] Audit logging failed:', auditError);
        // Continue - audit failure shouldn't fail the request
      }
    }

    const totalTime = Date.now() - startTime;
    console.log('✅ [REINVITE-USER-API] Request completed:', {
      requestId,
      success: isSuccess,
      totalTime: totalTime + 'ms',
    });

    // Create response with React Query invalidation headers
    const response = {
      success: isSuccess,
      message: apiRes.data?.message || (isSuccess ? 'User reinvited' : 'Reinvite failed'),
      error: !isSuccess,
      data: apiRes.data,
      requestId,
      processingTime: totalTime + 'ms',
      timestamp: new Date().toISOString(),
    };

    const nextResponse = NextResponse.json(response, { status: isSuccess ? 200 : 400 });

    // Add React Query cache invalidation headers
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.name], // Invalidate org users list
          ['userRole'], // Invalidate user role queries
          ['permissions'], // Invalidate permissions cache
        ],
        message: 'User reinvited - refresh org users',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [REINVITE-USER-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Validation failed',
          details: error.errors,
          success: false,
          requestId,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data) {
      return NextResponse.json(
        {
          error: error.response.data.error || 'External API error',
          message: error.response.data.message || 'Reinvite failed',
          success: false,
          requestId,
          processingTime: totalTime + 'ms',
        },
        { status: error.response.status || 500 },
      );
    }

    // Handle other errors
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message || 'An unexpected error occurred',
        success: false,
        requestId,
        processingTime: totalTime + 'ms',
      },
      { status: 500 },
    );
  }
}
