import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { z } from 'zod';
import { logUserInviteCancelled } from '@/lib/audit-logger-server';
import { requireTeamManagementPermissions } from '@/lib/auth-middleware';

// Validation schema for cancel invite request
const cancelInviteSchema = z.object({
  orgId: z.string().min(1, { message: 'Organization ID is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [CANCEL-INVITE-API] Starting cancel invite request');
  console.log('📊 [CANCEL-INVITE-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = cancelInviteSchema.parse(body);
    const { session, data, error } = await requireTeamManagementPermissions(
      validatedData.orgId,
      'invite',
    );
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', message: 'Authentication required', success: false },
        { status: 401 },
      );
    }
    if (error) return error;
    const userRole = data?.currentUserRole;
    console.log('✅ [CANCEL-INVITE-API] Validation successful:', {
      requestId,
      orgId: validatedData.orgId,
      email: validatedData.email,
    });

    // Business logic validation - prevent self-cancellation
    if (session.email === validatedData.email) {
      return NextResponse.json(
        {
          error: 'Cannot cancel your own invitation',
          message: 'Cannot cancel your own invitation',
          success: false,
          code: 'SELF_CANCEL_FORBIDDEN',
        },
        { status: 400 },
      );
    }

    // Call external API
    console.log('📤 [CANCEL-INVITE-API] Making external API call...');
    const apiRes = await externalClient.post('/orgs/users/cancel-invite', {
      orgId: validatedData.orgId,
      email: validatedData.email,
    });

    console.log('📥 [CANCEL-INVITE-API] External API response received:', {
      status: apiRes.status,
      data: apiRes.data,
    });

    // Check if the response indicates success
    const isSuccess =
      apiRes.data?.message === 'Invitation cancelled successfully' ||
      apiRes.data?.message?.includes('cancelled') ||
      (!apiRes.data?.error && apiRes.data?.message);

    // Audit logging for successful cancellations
    if (isSuccess) {
      try {
        await logUserInviteCancelled({
          organizationId: validatedData.orgId,
          cancelledUserEmail: validatedData.email,
          cancelledByEmail: session.email,
          timestamp: new Date(),
          userRole: userRole || 'member',
          metadata: {
            requestId,
            apiResponseTime: Date.now() - startTime,
          },
        });
        console.log('📝 [CANCEL-INVITE-API] Audit log recorded successfully');
      } catch (auditError) {
        console.error('⚠️ [CANCEL-INVITE-API] Audit logging failed:', auditError);
        // Continue - audit failure shouldn't fail the request
      }
    }

    const totalTime = Date.now() - startTime;
    console.log('✅ [CANCEL-INVITE-API] Request completed:', {
      requestId,
      success: isSuccess,
      totalTime: totalTime + 'ms',
    });

    // Create response with React Query invalidation headers
    const response = {
      success: isSuccess,
      message:
        apiRes.data?.message || (isSuccess ? 'Invitation cancelled successfully' : 'Cancel failed'),
      error: !isSuccess,
      data: apiRes.data,
      requestId,
      processingTime: totalTime + 'ms',
      timestamp: new Date().toISOString(),
    };

    const nextResponse = NextResponse.json(response, { status: isSuccess ? 200 : 400 });

    // Add React Query cache invalidation headers
    nextResponse.headers.set(
      'X-Invalidate-Queries',
      JSON.stringify({
        queries: [
          ['orgUsers', validatedData.orgId], // Invalidate org users list
          ['userRole'], // Invalidate user role queries
          ['permissions'], // Invalidate permissions cache
        ],
        message: 'Invitation cancelled - refresh org users',
      }),
    );

    return nextResponse;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [CANCEL-INVITE-API] Request failed after', totalTime, 'ms', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: error.errors[0]?.message || 'Validation failed',
          details: error.errors,
          success: false,
          requestId,
        },
        { status: 400 },
      );
    }

    // Handle external API errors
    if (error.response?.data) {
      return NextResponse.json(
        {
          error: error.response.data.error || 'External API error',
          message: error.response.data.message || 'Cancel failed',
          success: false,
          requestId,
          processingTime: totalTime + 'ms',
        },
        { status: error.response.status || 500 },
      );
    }

    // Handle other errors
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message || 'An unexpected error occurred',
        success: false,
        requestId,
        processingTime: totalTime + 'ms',
      },
      { status: 500 },
    );
  }
}
