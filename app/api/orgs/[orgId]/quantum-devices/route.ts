import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireDevicePermission } from '@/lib/auth-middleware';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const { searchParams } = new URL(request.url);
  const { orgId } = await params;

  if (!orgId) {
    return NextResponse.json({ error: 'orgId is required' }, { status: 400 });
  }

  // Check device view permissions for the organization
  const { error } = await requireDevicePermission('view', orgId);
  if (error) return error;

  // Build params object for external API call
  const paramsObj: Record<string, string> = { organization: orgId };
  const supportedParams = ['qrn', 'provider', 'type', 'status', 'isAvailable', 'verified'];

  for (const key of supportedParams) {
    const value = searchParams.get(key);
    if (value !== null) {
      paramsObj[key] = value;
    }
  }

  try {
    // Call the external API endpoint for organization-specific devices
    const response = await externalClient.get(`/v2/quantum-devices/org/${orgId}`);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching organization quantum devices:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch organization quantum devices',
        data: paramsObj.qrn ? {} : [],
      },
      { status: error.status || 500 },
    );
  }
}
