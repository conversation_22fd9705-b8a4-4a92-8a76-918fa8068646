import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireProviderPermissions } from '@/lib/auth-middleware';
import { requireAuth } from '@/lib/auth-middleware';
import { logProviderAdded } from '@/lib/audit-logger-server';

// GET /api/orgs/:orgId/provider
// Returns single provider for a specific organization
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const startTime = Date.now();
  console.log(`🔍 [PROVIDER-API] GET request started for organization provider`);
  console.log(`🔍 [PROVIDER-API] Request URL:`, request.url);
  console.log(`🔍 [PROVIDER-API] Params object:`, params);
  console.log(`🔍 [PROVIDER-API] Params type:`, typeof params);
  console.log(`🔍 [PROVIDER-API] Params is null:`, params === null);
  console.log(`🔍 [PROVIDER-API] Params is undefined:`, params === undefined);

  try {
    console.log(`🔐 [PROVIDER-API] Authenticating user...`);
    const { session, error } = await requireAuth();
    if (error) {
      console.error(`❌ [PROVIDER-API] Authentication error:`, error);
      return error;
    }

    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed - missing session data`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    console.log(`✅ [PROVIDER-API] User authenticated: ${session.email}`);

    // Get orgId from params
    let orgId: string;
    try {
      console.log(`🔍 [PROVIDER-API] About to await params...`);
      const { orgId: paramOrgId } = await params;
      orgId = paramOrgId;
      console.log(`🏢 [PROVIDER-API] Organization ID from params: ${orgId}`);
    } catch {
      console.log(`⚠️ [PROVIDER-API] Failed to get orgId from params, trying URL extraction...`);
      // Fallback: extract orgId from URL
      const url = new URL(request.url);
      const pathParts = url.pathname.split('/');
      const orgIdIndex = pathParts.indexOf('orgs') + 1;
      orgId = pathParts[orgIdIndex];
      console.log(`🏢 [PROVIDER-API] Organization ID from URL: ${orgId}`);
    }

    if (!orgId) {
      console.error(`❌ [PROVIDER-API] Organization ID is required`);
      return NextResponse.json({ message: 'Organization ID is required' }, { status: 400 });
    }

    console.log(`🏢 [PROVIDER-API] Organization ID: ${orgId}`);

    console.log(`🔒 [PROVIDER-API] Checking provider permissions...`);
    const { error: providerError } = await requireProviderPermissions('view', orgId);
    if (providerError) {
      console.error(`❌ [PROVIDER-API] Provider permission error:`, providerError);
      return providerError;
    }
    console.log(`✅ [PROVIDER-API] Provider permissions verified`);

    if (!orgId) {
      console.error(`❌ [PROVIDER-API] Organization ID is required`);
      return NextResponse.json({ message: 'Organization ID is required' }, { status: 400 });
    }

    console.log(`🌐 [PROVIDER-API] Fetching provider data from external API for org: ${orgId}`);
    try {
      const response = await externalClient.get(`/organizations/${orgId}/provider`);
      const duration = Date.now() - startTime;
      console.log(`✅ [PROVIDER-API] Successfully fetched provider data in ${duration}ms`);
      console.log(
        `📊 [PROVIDER-API] Response status: ${response.status}, data keys:`,
        Object.keys(response.data || {}),
      );
      console.log(`🏢 [PROVIDER-API] Response data:`, response.data);
      
      // The external client already handles this correctly:
      // - 200 with null if no provider assigned
      // - 200 with provider data if provider exists
      // - 404 if organization not found
      // - 500 for server errors
      
      return NextResponse.json(response.data);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.error(`❌ [PROVIDER-API] External API error after ${duration}ms:`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        url: error.config?.url,
        method: error.config?.method,
      });

      // If the external client returns 404 (organization not found), propagate that error
      if (error.response?.status === 404) {
        return NextResponse.json(
          { message: 'Organization not found' },
          { status: 404 },
        );
      }

      return NextResponse.json(
        { message: 'Failed to fetch organization provider' },
        { status: 500 },
      );
    }
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`💥 [PROVIDER-API] Unexpected error after ${duration}ms:`, {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/orgs/:orgId/provider
// Assign a provider to organization
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const startTime = Date.now();
  console.log(`🔍 [PROVIDER-API] POST request started for assigning provider to organization`);

  const { orgId } = await params;
  console.log(`🏢 [PROVIDER-API] Organization ID: ${orgId}`);

  const { providerId, providerName } = (await request.json()) as {
    providerId: string;
    providerName: string;
  };

  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    if (!orgId) {
      console.error(`❌ [PROVIDER-API] Missing orgId `);
      return NextResponse.json({ error: 'Missing orgId' }, { status: 400 });
    }

    if (!providerId) {
      console.error(`❌ [PROVIDER-API] Missing providerId `);
      return NextResponse.json({ error: 'Missing providerId' }, { status: 400 });
    }

    const { error: providerError, data } = await requireProviderPermissions('manage', orgId);
    if (providerError) return providerError;
    const userRole = data?.role;

    // Check if org already has a provider
    try {
      const existingProvider = await externalClient.get(`/organizations/${orgId}/provider`);
      if (existingProvider.data) {
        return NextResponse.json(
          { error: 'Organization already has a provider assigned' },
          { status: 400 },
        );
      }
    } catch (error: any) {
      // 404 is expected if no provider exists
      if (error.response?.status !== 404) {
        throw error;
      }
    }

    // Assign provider to org
    const provider = await externalClient.put(`/organizations/${orgId}/provider`, {
      providerId,
      metadata: {
        assignedBy: session.email,
        orgId,
        assignedAt: new Date().toISOString(),
      },
    });

    console.log('🌐 [PROVIDER-API] Provider assigned', {
      providerName,
      providerId,
      orgId,
    });

    // Log the provider assignment
    try {
      logProviderAdded(providerId, providerName, orgId, userRole || '', {
        email: session.email,
        userId: session.userId,
        submittedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`❌ [PROVIDER-API] Error logging provider assignment`, error);
    }

    if (!provider.data) {
      console.error(`❌ [PROVIDER-API] Provider assignment failed `);
      return NextResponse.json({ error: 'Provider assignment failed' }, { status: 500 });
    }

    console.log(
      `🌐 [PROVIDER-API] Provider assigned successfully`,
      JSON.stringify(provider.data, null, 2),
    );

    return NextResponse.json(provider.data, { status: 201 });
  } catch (error) {
    console.error(`❌ [PROVIDER-API] Error assigning provider`, error);
    return NextResponse.json({ error: 'Failed to assign provider' }, { status: 500 });
  }
}

// PUT /api/orgs/:orgId/provider
// Update provider assignment for organization
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const startTime = Date.now();
  console.log(`🔍 [PROVIDER-API] PUT request started for updating provider assignment`);

  const { orgId } = await params;
  console.log(`🏢 [PROVIDER-API] Organization ID: ${orgId}`);

  const { providerId, providerName } = (await request.json()) as {
    providerId: string;
    providerName: string;
  };

  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    if (!orgId || !providerId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const { error: providerError, data } = await requireProviderPermissions('manage', orgId);
    if (providerError) return providerError;
    const userRole = data?.role;

    // Update provider assignment
    const provider = await externalClient.put(`/organizations/${orgId}/provider`, {
      providerId,
      metadata: {
        updatedBy: session.email,
        orgId,
        updatedAt: new Date().toISOString(),
      },
    });

    // Log the provider update
    try {
      logProviderAdded(providerId, providerName, orgId, userRole || '', {
        email: session.email,
        userId: session.userId,
        submittedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`❌ [PROVIDER-API] Error logging provider update`, error);
    }

    return NextResponse.json(provider.data, { status: 200 });
  } catch (error) {
    console.error(`❌ [PROVIDER-API] Error updating provider`, error);
    return NextResponse.json({ error: 'Failed to update provider' }, { status: 500 });
  }
}
