import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireProviderPermissions } from '@/lib/auth-middleware';
import { requireAuth } from '@/lib/auth-middleware';
import { logProviderAdded } from '@/lib/audit-logger-server';
// GET /api/orgs/:orgId/providers
// LEGACY: Returns all providers for a specific organization (now returns single provider as array)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const { orgId } = await params;
    const { error: providerError } = await requireProviderPermissions('view', orgId);
    if (providerError) return providerError;

    if (!orgId) {
      return NextResponse.json({ message: 'Organization ID is required' }, { status: 400 });
    }

    try {
      // Fetch single provider for org and return as array for backward compatibility
      const response = await externalClient.get(`/organizations/${orgId}/provider`);
      const provider = response.data;
      return NextResponse.json(provider ? [provider] : []);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json([]); // No provider assigned
      }
      console.error('Error fetching organization provider from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch organization provider' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in organization providers route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

//TODO: add permission check
/**
 * POST /api/organizations
 * User wants to add current public provider to their org
 */

// Add permission check

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const { orgId } = await params;
  const { providerId, providerName } = (await request.json()) as {
    providerId: string | string[];
    providerName: string | string[];
  };
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [PROVIDER-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    if (!orgId) {
      console.error(`❌ [PROVIDER-API] Missing orgId `);
      return NextResponse.json({ error: 'Missing orgId' }, { status: 400 });
    }

    if (!providerId) {
      console.error(`❌ [PROVIDER-API] Missing providerId `);
      return NextResponse.json({ error: 'Missing providerId' }, { status: 400 });
    }
    const { error: providerError, data } = await requireProviderPermissions('manage', orgId);
    if (providerError) return providerError;
    const userRole = data?.role;

    // Convert to single provider assignment for new architecture
    const singleProviderId = Array.isArray(providerId) ? providerId[0] : providerId;
    const provider = await externalClient.put(`/organizations/${orgId}/provider`, {
      providerId: singleProviderId,
      metadata: { submittedBy: session.email, orgId, submittedAt: new Date().toISOString() },
    });

    console.log('🌐 [PROVIDER-API] Provider name', providerName);
    console.log('🌐 [PROVIDER-API] Provider id', providerId);
    console.log('🌐 [PROVIDER-API] Org id', orgId);

    // Log the single provider assignment
    try {
      logProviderAdded(singleProviderId, providerName as string, orgId, userRole || '', {
        email: session.email,
        userId: session.userId,
        submittedAt: new Date().toISOString(),
      });
      console.log('🌐 [PROVIDER-API] User role', userRole);
    } catch (error) {
      console.error(`❌ [PROVIDER-API] Error logging provider added`, error);
    }
    if (!provider.data) {
      console.error(`❌ [PROVIDER-API] Provider not found `);
      return NextResponse.json({ error: 'Provider not found' }, { status: 404 });
    }

    console.log(`🌐 [PROVIDER-API] Provider data`, JSON.stringify(provider.data, null, 2));

    return NextResponse.json(provider.data, { status: 201 });
  } catch (error) {
    console.error(`❌ [PROVIDER-API] Error updating provider`, error);
    return NextResponse.json({ error: 'Failed to update provider' }, { status: 500 });
  }
}
