import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireDevicePermission } from '@/lib/auth-middleware';
import { requireAuth } from '@/lib/auth-middleware';

// GET /api/orgs/:orgId/device-access
// Returns device access permissions for organization
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const { orgId } = await params;
    const { error: providerError } = await requireDevicePermission('view', orgId);
    if (providerError) return providerError;

    if (!orgId) {
      return NextResponse.json({ message: 'Organization ID is required' }, { status: 400 });
    }

    try {
      const response = await externalClient.get(`/organizations/${orgId}/device-access`);
      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        // Return empty access if none found
        return NextResponse.json({
          orgId,
          deviceAccess: {
            read: [],
            write: [],
            custom: [],
          },
          updatedAt: new Date().toISOString(),
        });
      }
      console.error('Error fetching device access from external API:', error);
      return NextResponse.json({ message: 'Failed to fetch device access' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in device access route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

//admin only
// PUT /api/orgs/:orgId/device-access
// Update device access permissions for organization
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  const { orgId } = await params;
  const { deviceAccess } = (await request.json()) as {
    deviceAccess: {
      read: string[];
      write: string[];
      custom: string[];
    };
  };

  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    if (!orgId || !deviceAccess) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const { error: providerError, data: _data } = await requireDevicePermission('manage', orgId);
    if (providerError) return providerError;

    // Update device access
    const response = await externalClient.put(`/organizations/${orgId}/device-access`, {
      deviceAccess,
      metadata: {
        updatedBy: session.email,
        orgId,
        updatedAt: new Date().toISOString(),
      },
    });

    console.log(`🌐 [DEVICE-ACCESS-API] Device access updated for org ${orgId}`, deviceAccess);

    return NextResponse.json(response.data, { status: 200 });
  } catch (error) {
    console.error(`❌ [DEVICE-ACCESS-API] Error updating device access`, error);
    return NextResponse.json({ error: 'Failed to update device access' }, { status: 500 });
  }
}
