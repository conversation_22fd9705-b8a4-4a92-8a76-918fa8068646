export type DeviceAccessSample = {
  json: {
    read: any[];
    write: Array<{
      _id: string;
      numberQubits: number;
      vrn: string;
      statusRefresh: string;
      visibility: string;
      qrn: string;
      name: string;
      provider: string;
      paradigm: string;
      type: string;
      vendor: string;
      status: string;
      createdAt: string;
      updatedAt: string;
      processorType?: string;
      architecture?: string;
      pendingJobs: number;
      availabilityCD: string;
      isAvailable: boolean;
      pricing: { perTask: number; perShot: number; perMinute: number };
      nextAvailable?: string | null;
      noiseModels: any[];
      blackListedDomains: any[];
      permissionsNodes: any[];
      whiteListedDomains: any[];
      about?: string | null;
      deviceAbout: string;
      deviceDescription: string;
      deviceImage?: string | null;
      logo: { light: string; dark: string };
      spec: string[];
      runInputTypes: string[];
    }>;
    custom: any[];
  };
};
