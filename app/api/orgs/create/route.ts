import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { hasPermission } from '@/lib/permissions';
import { externalClient } from '@/app/api/_utils/external-client';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log('🚀 [CREATE-ORG-API] POST request started');

  try {
    // Get current user
    const { session, error } = await requireAuth();
    if (error || !session) {
      console.log('❌ [CREATE-ORG-API] No authenticated user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔐 [CREATE-ORG-API] User authenticated:', session.email);

    // // Check if user is qBraid admin via external API
    // try {
    //   const roleResponse = await externalClient.get('/user/role');
    //   const userRole = roleResponse.data;

    //   if (userRole !== 'admin') {
    //     console.log('❌ [CREATE-ORG-API] Permission denied - not qBraid admin:', {
    //       user: session.email,
    //       role: userRole,
    //     });
    //     return NextResponse.json(
    //       { error: 'Only qBraid administrators can create organizations' },
    //       { status: 403 },
    //     );
    //   }

    //   console.log('✅ [CREATE-ORG-API] qBraid admin check passed:', {
    //     user: session.email,
    //     role: userRole,
    //   });
    // } catch (error: any) {
    //   console.error('❌ [CREATE-ORG-API] Failed to check user role:', error);
    //   return NextResponse.json({ error: 'Failed to verify user permissions' }, { status: 500 });
    // }

    // Parse form data
    const formData = await request.formData();
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const marketing = formData.get('marketing') === 'true';
    const owner = formData.get('owner') as string;
    const qbraidGitHubAssistance = formData.get('qbraidGitHubAssistance') === 'true';
    const orgGitHubUrl = formData.get('orgGitHubUrl') as string;
    const orgGitHubToken = formData.get('orgGitHubToken') as string;
    const qBookDomain = formData.get('qBookDomain') as 'quera' | 'qbraid';

    // Validate required fields
    if (!name?.trim()) {
      return NextResponse.json({ error: 'Organization name is required' }, { status: 400 });
    }

    if (!owner?.trim()) {
      return NextResponse.json({ error: 'Owner email is required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(owner)) {
      return NextResponse.json({ error: 'Invalid owner email format' }, { status: 400 });
    }

    console.log('📝 [CREATE-ORG-API] Form data validated:', {
      name,
      owner,
      description: description?.slice(0, 50) + '...',
      marketing,
      qbraidGitHubAssistance,
      qBookDomain,
    });

    // Prepare payload for external API
    const payload = {
      name: name.trim(),
      description: description?.trim() || '',
      marketing,
      owner: owner.trim(),
      ownerEmail: owner.trim(),
      qbraidGitHubAssistance,
      orgGitHubUrl: orgGitHubUrl?.trim() || '',
      orgGitHubToken: orgGitHubToken?.trim() || '',
      qBookDomain: qBookDomain || 'qbraid',
    };

    // Handle file uploads if present
    const image = formData.get('image') as File;
    const darkImage = formData.get('darkImage') as File;

    if (image) {
      console.log('📁 [CREATE-ORG-API] Processing logo image');
      // In a real implementation, you would upload this to your file storage
      // For now, we'll just log that it was received
    }

    if (darkImage) {
      console.log('📁 [CREATE-ORG-API] Processing dark mode logo image');
      // In a real implementation, you would upload this to your file storage
      // For now, we'll just log that it was received
    }

    // Call external API to create organization
    console.log('🌐 [CREATE-ORG-API] Calling external API...');

    // This would be your actual external API call
    // For now, we'll simulate the API call
    const externalApiResponse = await externalClient.post('/orgs/create', payload);

    if (externalApiResponse.status !== 200) {
      const errorData = await externalApiResponse.data;
      console.error('❌ [CREATE-ORG-API] External API error:', errorData);

      if (externalApiResponse.status === 409) {
        return NextResponse.json(
          { error: 'Organization with this name already exists' },
          { status: 409 },
        );
      }

      return NextResponse.json(
        { error: errorData.message || 'Failed to create organization' },
        { status: externalApiResponse.status },
      );
    }

    const result = await externalApiResponse.data;
    const duration = Date.now() - startTime;

    console.log('✅ [CREATE-ORG-API] Organization created successfully:', {
      organizationId: result.organizationId,
      name: result.name,
      owner: result.owner,
      duration: `${duration}ms`,
    });

    // Return success response
    return NextResponse.json({
      success: true,
      organization: {
        id: result.organizationId,
        name: result.name,
        description: result.description,
        owner: result.owner,
        email: result.email,
        billingAccount: result.billingAccount,
        createdAt: result.createdAt,
      },
      billing: result.billing,
      owner: result.ownerDetails,
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error('❌ [CREATE-ORG-API] Unexpected error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      },
      { status: 500 },
    );
  }
}
