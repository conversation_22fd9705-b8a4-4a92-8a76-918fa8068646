import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

// ---------------------------------------------------------------------------
// Mock endpoint – `/api/orgs/get/:page/:limit`
// Returns a paginated list of organisations the signed-in user belongs to,
// together with their role inside each organisation.  The payload mirrors the
// real QBraid API so that the front-end can be wired up before the backend /
// auth cookies are fully configured.
// ---------------------------------------------------------------------------

// NOTE:  When wiring to the real API just replace `SAMPLE_DATA` with a call to
// `externalClient.get(`/orgs/get/${page}/${limit}`)` and pipe the response.

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ page: string; limit: string }> },
) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [ORGS-API] Starting organizations fetch request');
  console.log('📊 [ORGS-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent')?.slice(0, 100) + '...',
  });

  try {
    // Get current user's email from session or from headers (for server-side calls)
    console.log('🔍 [ORGS-API] Getting user session...');
    const sessionStart = Date.now();
    const { session, error } = await requireAuth();
    if (error) return error;
    const sessionTime = Date.now() - sessionStart;

    // For server-side calls, check headers for email and id-token
    const headerEmail = request.headers.get('email');
    const headerIdToken = request.headers.get('id-token');

    // Use email from headers (server-side) or session (client-side)
    const userEmail = headerEmail || session?.email || '<EMAIL>';

    console.log('✅ [ORGS-API] Session/headers retrieved in', sessionTime, 'ms:', {
      requestId,
      hasSession: !!session,
      hasHeaderEmail: !!headerEmail,
      hasHeaderToken: !!headerIdToken,
      userEmail: userEmail,
      userId: session?.userId || 'N/A',
      sessionRetrievalTime: sessionTime + 'ms',
      callType: headerEmail ? 'server-side' : 'client-side',
    });

    // Generate sample data with current user's email and role
    const SAMPLE_DATA = {
      organizations: [
        {
          org: {
            deductionRequest: {
              status: 'none',
              creditAmount: 0,
            },
            invited: true,
            accepted: true,
            orgBalance: 0,
            permissionsNodes: [],
            _id: '68659e5865f928946ac3b82e',
            user: '686439d35fa9811c5e7da57b',
            email: userEmail, // Use current user's email
            organization: {
              metadata: {
                acknowledgedTerms: true,
                marketing: false,
                newOrg: true,
              },
              courses: [],
              classes: [],
              devices: [],
              _id: '68659d197705c09bd8526521',
              name: 'Research Lab Alpha',
              description: 'Primary research organization',
              owner: '62d8533ecabc803ad0b7789b',
              orgEmail: '<EMAIL>',
              ownerEmail: '<EMAIL>',
              billing: '68659d197705c0f1f7526522',
              createdAt: '2025-07-02T20:56:58.248Z',
              updatedAt: '2025-07-04T16:16:58.527Z',
              __v: 0,
            },
            role: 'user', // Grant admin role for testing
            createdAt: '2025-07-02T21:02:16.769Z',
            updatedAt: '2025-07-02T21:02:51.398Z',
            __v: 0,
          },
          orgBilling: {
            oneTimeTransactions: [],
            allTransactions: [],
            activePlanName: 'Free',
            currentSubscriptionPriceId: '',
            currentSubscriptionRecuring: '',
            currentSubscriptionStartDate: null,
            tempPlanName: '',
            tempPlanAction: '',
            creditCardOnStripe: false,
            qBraidCredits: 10,
            cummulativeQBraidCredits: 0,
            cumulativePromotionalQBraidCredits: 0,
            _id: '68659d197705c0f1f7526522',
            organization: '68659d197705c09bd8526521',
            email: '<EMAIL>',
            stripeId: 'cus_SblEq7Eg6WCTPj',
            createdAt: '2025-07-02T20:56:58.319Z',
            updatedAt: '2025-07-02T21:00:43.481Z',
            __v: 0,
          },
        },
        // Add a second organization with member role
        {
          org: {
            deductionRequest: {
              status: 'none',
              creditAmount: 0,
            },
            invited: true,
            accepted: true,
            orgBalance: 0,
            permissionsNodes: [],
            _id: '68659e5865f928946ac3b83f',
            user: '686439d35fa9811c5e7da57c',
            email: userEmail, // Same user in different org
            organization: {
              metadata: {
                acknowledgedTerms: true,
                marketing: false,
                newOrg: false,
              },
              courses: [],
              classes: [],
              devices: [],
              _id: '68659d197705c09bd8526522',
              name: 'Quantum Computing Dept',
              description: 'University quantum computing department',
              owner: '62d8533ecabc803ad0b7789c',
              orgEmail: '<EMAIL>',
              ownerEmail: '<EMAIL>',
              billing: '68659d197705c0f1f7526523',
              createdAt: '2025-06-15T10:30:00.000Z',
              updatedAt: '2025-07-01T14:22:15.123Z',
              __v: 0,
            },
            role: 'member', // Different role in different org
            createdAt: '2025-06-15T10:35:00.000Z',
            updatedAt: '2025-06-15T10:35:00.000Z',
            __v: 0,
          },
          orgBilling: {
            oneTimeTransactions: [],
            allTransactions: [],
            activePlanName: 'Premium',
            currentSubscriptionPriceId: 'price_premium',
            currentSubscriptionRecuring: 'monthly',
            currentSubscriptionStartDate: '2025-06-15T10:30:00.000Z',
            tempPlanName: '',
            tempPlanAction: '',
            creditCardOnStripe: true,
            qBraidCredits: 100,
            cummulativeQBraidCredits: 500,
            cumulativePromotionalQBraidCredits: 50,
            _id: '68659d197705c0f1f7526523',
            organization: '68659d197705c09bd8526522',
            email: '<EMAIL>',
            stripeId: 'cus_UniversityQuantum123',
            createdAt: '2025-06-15T10:30:00.000Z',
            updatedAt: '2025-07-01T14:22:15.123Z',
            __v: 0,
          },
        },
      ],
      pagination: {
        currentPage: 0,
        limit: '50',
        totalPages: 1,
        totalOrganizations: 2,
      },
    };
    const { page, limit } = await params;

    console.log('📋 [ORGS-API] Request parameters:', {
      requestId,
      page: page,
      limit: limit,
      pageNumber: Number.parseInt(page),
      limitNumber: Number.parseInt(limit),
    });

    // Make external API call with graceful fallback to sample data
    console.log('📤 [ORGS-API] Attempting external API call...');
    let dataToReturn: any = SAMPLE_DATA;
    try {
      const externalApiStart = Date.now();
      const apiRes = await externalClient.get(`/organizations/get/${page}/${limit}`);
      const externalApiTime = Date.now() - externalApiStart;

      console.log('📥 [ORGS-API] External API response in', externalApiTime, 'ms:', {
        requestId,
        responseSize: JSON.stringify(apiRes.data).length,
        organizations: apiRes.data?.organizations?.length || 0,
      });

      dataToReturn = apiRes.data;
    } catch (apiError) {
      console.warn('⚠️ [ORGS-API] External API failed, using sample data:', {
        requestId,
        error: apiError instanceof Error ? apiError.message : apiError,
      });
    }

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ORGS-API] Request fulfilled in', totalTime, 'ms');

    return NextResponse.json(dataToReturn);
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ORGS-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [ORGS-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : JSON.stringify(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    // Return error response
    return NextResponse.json(
      {
        error: 'Failed to fetch organizations',
        requestId: requestId,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
