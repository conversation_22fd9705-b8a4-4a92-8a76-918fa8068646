import { NextRequest, NextResponse } from 'next/server';
import { getCurrentSessionId, getCognitoTokenCookies } from '@/lib/session';
import { processOrganizationsData, getCachedOrgRoles, UserOrgRoles } from '@/lib/external-roles';

import { Permission } from '@/types/auth';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

// Performance tracking for 19k users
const PERFORMANCE_THRESHOLD_MS = 5000; // Log slow requests

/**
 * GET /api/auth/permissions
 *
 * Fetches user roles and permissions with optimizations for high-volume usage.
 * Architecture: Check Session Cache -> Check Role Cache -> External API -> Process -> Cache
 *
 * Query Parameters:
 * - orgId: Optional organization ID to get organization-specific permissions
 * - email: Optional email to get permissions for a specific user (admin only)
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  const requestId = Math.random().toString(36).slice(2, 15);
  const startTime = Date.now();

  // Extract query parameters
  const { searchParams } = new URL(req.url);
  const orgId = searchParams.get('orgId');
  const email = searchParams.get('email');

  console.log(`🚀 [PERMISSIONS-API] =============== STARTING ROLES FLOW ===============`);
  console.log(`🚀 [PERMISSIONS-API] Request ID: ${requestId}`);
  console.log(`🚀 [PERMISSIONS-API] Timestamp: ${new Date().toISOString()}`);
  console.log(`🚀 [PERMISSIONS-API] Query Parameters:`, {
    orgId: orgId || 'none (global permissions)',
    email: email || 'current user',
    isOrgSpecific: !!orgId,
  });
  console.log(
    `🚀 [PERMISSIONS-API] User Agent: ${req.headers.get('user-agent')?.slice(0, 100)}...`,
  );

  try {
    console.log(`🔍 [PERMISSIONS-API] Step 1: Verifying user session...`);

    // 1. Verify user session
    const sessionStart = Date.now();
    const { session, error } = await requireAuth();
    if (error) return error;
    const sessionTime = Date.now() - sessionStart;

    if (!session?.email) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No valid session found`, { requestId });
      return NextResponse.json({ error: 'Unauthorized', requestId: req.url }, { status: 401 });
    }

    const userEmail = session.email;
    console.log(`👤 [PERMISSIONS-API] SUCCESS: User authenticated as: ${userEmail}`, { requestId });

    // 2. Get session ID for token retrieval and caching
    console.log(`🔍 [PERMISSIONS-API] Step 2: Getting session ID for caching...`);
    const sessionIdStart = Date.now();
    const sessionId = await getCurrentSessionId();
    const sessionIdTime = Date.now() - sessionIdStart;

    console.log(`📋 [PERMISSIONS-API] Session ID retrieval took ${sessionIdTime}ms`);
    console.log(
      `📋 [PERMISSIONS-API] Session ID: ${sessionId?.slice(0, 10)}... (length: ${sessionId?.length || 0})`,
    );

    if (!sessionId) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No session ID found`, { requestId });
      return NextResponse.json({ error: 'No session ID' }, { status: 401 });
    }

    console.log(`🔍 [PERMISSIONS-API] No roles in session, proceeding to cache checks...`);

    // 4. Check email-based org roles cache first (preferred method)
    console.log(`🔍 [PERMISSIONS-API] Step 4a: Checking email-based org roles cache...`);
    console.log(`🔍 [PERMISSIONS-API] Cache key pattern: orgroles:${userEmail}`);

    const orgCacheStart = Date.now();
    const cachedOrgRoles = await getCachedOrgRoles(userEmail);
    const orgCacheTime = Date.now() - orgCacheStart;

    console.log(`📋 [PERMISSIONS-API] Org roles cache check took ${orgCacheTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Org cache result:`, {
      hasOrgRoles: !!cachedOrgRoles,
      orgCount: cachedOrgRoles ? Object.keys(cachedOrgRoles).length : 0,
      orgRoles: cachedOrgRoles || {},
    });

    // If we got cached org roles, use them directly
    if (cachedOrgRoles && Object.keys(cachedOrgRoles).length > 0) {
      // Handle organization-specific vs global permissions
      let permissionScope: string;
      let relevantRoles: string[];

      if (orgId && cachedOrgRoles[orgId]) {
        // Organization-specific permissions: only use role for the specified org
        relevantRoles = [cachedOrgRoles[orgId].role];
        permissionScope = `org-specific (${cachedOrgRoles[orgId].orgName})`;
      } else {
        // Global permissions: use all roles across all organizations
        relevantRoles = Object.values(cachedOrgRoles).map((org: UserOrgRoles[string]) => org.role);
        permissionScope = 'global (all organizations)';
      }

      console.log(`⚡ [PERMISSIONS-API] SUCCESS: Found org roles in email-based cache!`, {
        requestId,
        orgCount: Object.keys(cachedOrgRoles).length,
        userEmail: userEmail,
        requestedOrgId: orgId || 'none',
        permissionScope,
        relevantRoles: relevantRoles,
        detailedOrgRoles: cachedOrgRoles,
      });

      const permissionStart = Date.now();

      const permissionTime = Date.now() - permissionStart;

      console.log(`🎭 [PERMISSIONS-API] Permission mapping took ${permissionTime}ms`);

      const duration = Date.now() - startTime;
      console.log(
        `✅ [PERMISSIONS-API] =============== COMPLETED (EMAIL CACHE) in ${duration}ms ===============`,
      );

      return NextResponse.json({
        source: 'email_cache',
        cached: true,
        duration: `${duration}ms`,
        orgRoles: cachedOrgRoles,
        // Organization context information
        orgContext: {
          requestedOrgId: orgId,
          isOrgSpecific: !!orgId,
          scope: permissionScope,
          currentOrg: orgId && cachedOrgRoles[orgId] ? cachedOrgRoles[orgId] : undefined,
        },
      });
    }

    console.log(`🔍 [PERMISSIONS-API] No org roles in email cache, proceeding to external API...`);

    // 5. Get Cognito tokens for external API calls
    console.log(`🔍 [PERMISSIONS-API] Step 5: Getting Cognito tokens...`);
    const tokenStart = Date.now();
    const tokens = await getCognitoTokenCookies(sessionId);
    const tokenTime = Date.now() - tokenStart;

    console.log(`📋 [PERMISSIONS-API] Token retrieval took ${tokenTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Token info:`, {
      hasAccessToken: !!tokens?.accessToken,
      hasIdToken: !!tokens?.idToken,
      accessTokenLength: tokens?.accessToken?.length || 0,
      idTokenLength: tokens?.idToken?.length || 0,
      idTokenPrefix: tokens?.idToken?.slice(0, 20) + '...' || 'none',
    });

    if (!tokens?.idToken) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No ID token found for session`, {
        requestId,
        sessionId: sessionId.slice(0, 10),
      });
      return NextResponse.json({ error: 'No authentication token' }, { status: 401 });
    }

    // 6. Fetch organizations directly from external QBraid API
    console.log(`🔍 [PERMISSIONS-API] Step 6: Calling external QBraid API...`);
    console.log(`📤 [PERMISSIONS-API] API Details:`, {
      endpoint: '/orgs/get/0/50',
      userEmail: userEmail,
      authMethod: 'Bearer Token',
      tokenPrefix: tokens.idToken.slice(0, 20) + '...',
    });

    // Fetch organizations list
    const apiStart = Date.now();
    const organizationsResponse = await externalClient.get('/orgs/get/0/50', {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const apiTime = Date.now() - apiStart;

    console.log(`📋 [PERMISSIONS-API] External API call took ${apiTime}ms`);
    console.log(`📋 [PERMISSIONS-API] API Response:`, {
      status: organizationsResponse.status,
      statusText: organizationsResponse.statusText,
      hasData: !!organizationsResponse.data,
      dataSize: JSON.stringify(organizationsResponse.data || {}).length,
    });

    if (organizationsResponse.status !== 200) {
      console.error(`❌ [PERMISSIONS-API] FAILURE: Organizations API failed:`, {
        status: organizationsResponse.status,
        statusText: organizationsResponse.statusText,
        requestId,
        responseData: organizationsResponse.data,
      });

      // Return empty but successful response to allow user to continue
      const permissions: Permission[] = [];
      const duration = Date.now() - startTime;
      console.log(
        `⚠️ [PERMISSIONS-API] =============== COMPLETED (API ERROR FALLBACK) in ${duration}ms ===============`,
      );

      return NextResponse.json({
        permissions,
        source: 'api_error_fallback',
        cached: false,
        error: 'Failed to fetch organizations',
        duration: `${duration}ms`,
        orgRoles: {},
      });
    }

    const organizationsData = organizationsResponse.data;
    console.log(`📋 [PERMISSIONS-API] Organizations data received:`, {
      organizationsCount: organizationsData?.organizations?.length || 0,
      totalOrganizations: organizationsData?.pagination?.totalOrganizations || 0,
      currentPage: organizationsData?.pagination?.currentPage || 0,
      sampleOrgData: organizationsData?.organizations?.[0]
        ? {
            hasOrg: !!organizationsData.organizations[0].org,
            orgId:
              organizationsData.organizations[0].org?.organization?._id?.slice(0, 10) + '...' ||
              'none',
            orgName: organizationsData.organizations[0].org?.organization?.name || 'none',
            userRole: organizationsData.organizations[0].org?.role || 'none',
            userEmail: organizationsData.organizations[0].org?.email || 'none',
          }
        : undefined,
    });

    // 7. Process organizations data to extract detailed org roles
    console.log(`🔍 [PERMISSIONS-API] Step 7: Processing organizations to extract org roles...`);
    const rolesTimer = Date.now();

    // Process detailed org roles only
    const orgRoles = await processOrganizationsData(userEmail, organizationsData);
    const orgRolesFetchDuration = Date.now() - rolesTimer;

    console.log(`📥 [PERMISSIONS-API] Org role processing completed in ${orgRolesFetchDuration}ms`);
    console.log(`📥 [PERMISSIONS-API] Processed org roles:`, {
      email: userEmail,
      orgRolesCount: Object.keys(orgRoles).length,
      orgRoles: orgRoles,
      processingDuration: `${orgRolesFetchDuration}ms`,
    });

    // 8. Map org roles to permissions
    console.log(`🔍 [PERMISSIONS-API] Step 8: Mapping org roles to permissions...`);
    const permissionStart = Date.now();

    // Extract roles for permission mapping (org-specific only - no global aggregation)
    let permissions: Permission[];
    let permissionScope: string;
    let currentOrgRole: string | undefined;

    if (orgId && orgRoles[orgId]) {
      // Org-specific permissions based on user's role in that org
      currentOrgRole = orgRoles[orgId].role;
      permissions = orgRoles[orgId].permissions;
      permissionScope = `org-specific (${orgRoles[orgId].orgName})`;
    } else {
      // No global permissions - require org context for security
      permissions = [];
      permissionScope = 'none (org context required)';
    }
    const permissionTime = Date.now() - permissionStart;

    console.log(`🎭 [PERMISSIONS-API] Permission mapping completed in ${permissionTime}ms`);
    console.log(`🎭 [PERMISSIONS-API] Permission mapping result:`, {
      permissionScope,
      orgId: orgId || 'none',
      outputPermissions: permissions,
      permissionCount: permissions.length,
      currentOrgRole,
    });

    // 9. Track performance for monitoring
    const totalDuration = Date.now() - startTime;
    console.log(`📊 [PERMISSIONS-API] Performance metrics:`, {
      sessionRetrievalTime: `${sessionTime}ms`,
      sessionIdTime: `${sessionIdTime}ms`,
      cacheCheckTime: `${orgCacheTime}ms`, // Changed from cacheTime to orgCacheTime
      tokenRetrievalTime: `${tokenTime}ms`,
      externalApiTime: `${apiTime}ms`,
      roleProcessingTime: `${orgRolesFetchDuration}ms`,
      permissionMappingTime: `${permissionTime}ms`,
      totalDuration: `${totalDuration}ms`,
    });

    if (totalDuration > PERFORMANCE_THRESHOLD_MS) {
      console.warn(`⚠️ [PERMISSIONS-API] SLOW REQUEST DETECTED:`, {
        requestId,
        userEmail,
        duration: `${totalDuration}ms`,
        threshold: `${PERFORMANCE_THRESHOLD_MS}ms`,
        breakdown: {
          sessionTime: `${sessionTime}ms`,
          cacheTime: `${orgCacheTime}ms`, // Changed from cacheTime to orgCacheTime
          apiTime: `${apiTime}ms`,
          processingTime: `${orgRolesFetchDuration}ms`,
        },
      });
    }

    console.log(`🎉 [PERMISSIONS-API] SUCCESS: All steps completed successfully!`);
    console.log(`📊 [PERMISSIONS-API] Final summary:`, {
      orgRolesCount: Object.keys(orgRoles).length,
      permissionsCount: permissions.length,
      source: Object.keys(orgRoles).length > 0 ? 'external_api' : 'empty',
      totalDuration: `${totalDuration}ms`,
      requestId,
    });

    console.log(
      `✅ [PERMISSIONS-API] =============== COMPLETED (EXTERNAL API) in ${totalDuration}ms ===============`,
    );

    // 11. Return successful response
    return NextResponse.json({
      permissions,
      source: 'external_api',
      cached: false,
      duration: `${totalDuration}ms`,
      orgRoles: orgRoles,
      // Organization context information (consistent with cached response)
      orgContext: {
        requestedOrgId: orgId,
        isOrgSpecific: !!orgId,
        scope: permissionScope,
        currentOrg: orgId && orgRoles[orgId] ? orgRoles[orgId] : undefined,
      },
    });
  } catch (error: unknown) {
    const duration = Date.now() - startTime;
    console.error(
      `❌ [PERMISSIONS-API] =============== CRITICAL ERROR AFTER ${duration}ms ===============`,
    );
    console.error(`❌ [PERMISSIONS-API] Error details:`, {
      requestId,
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      errorCause: error.cause,
      duration: `${duration}ms`,
    });

    // Return error response but allow frontend to continue with empty permissions
    return NextResponse.json(
      {
        error: 'Failed to fetch permissions',
        message: error.message,
        permissions: [],
        source: 'error',
        duration: `${duration}ms`,
        orgRoles: {},
      },
      { status: 500 },
    );
  }
}
