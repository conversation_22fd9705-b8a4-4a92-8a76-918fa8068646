import { NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';

/**
 * GET /api/auth/session
 * Returns current user session information
 */
export async function GET() {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session) {
      return NextResponse.json({ error: 'No active session' }, { status: 401 });
    }

    return NextResponse.json({
      email: session.email,
      userId: session.userId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ [SESSION-API] Error getting session:', error);
    return NextResponse.json({ error: 'Failed to get session' }, { status: 500 });
  }
}
