import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [ACTIVITY-LOGS-API] Starting activity logs fetch request');
  console.log('📊 [ACTIVITY-LOGS-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Use auth middleware for consistent authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', success: false },
        { status: 401 },
      );
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');
    const resourceType = searchParams.get('resourceType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    console.log('📋 [ACTIVITY-LOGS-API] Request parameters:', {
      requestId,
      organizationId,
      page,
      resultsPerPage,
      userId,
      action,
      resourceType,
      startDate,
      endDate,
    });

    // Validate required parameters
    if (!organizationId) {
      return NextResponse.json(
        { error: 'organizationId is required', success: false },
        { status: 400 },
      );
    }

    // Build query parameters for external API
    const queryParams: Record<string, string> = {
      organizationId,
      page,
      resultsPerPage,
    };

    // Add optional filters
    if (userId) queryParams.userId = userId;
    if (action) queryParams.action = action;
    if (resourceType) queryParams.resourceType = resourceType;
    if (startDate) queryParams.startDate = startDate;
    if (endDate) queryParams.endDate = endDate;

    // Call external API
    console.log('📤 [ACTIVITY-LOGS-API] Making external API call...');
    const externalApiStart = Date.now();
    const apiResponse = await externalClient.get('/activity-logs', queryParams);
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [ACTIVITY-LOGS-API] External API response received:', {
      requestId,
      responseTime: externalApiTime + 'ms',
      activitiesCount: apiResponse.data?.activities?.length || 0,
      totalActivities: apiResponse.data?.total || 0,
    });

    // Transform the response to match expected frontend structure
    const transformedResponse = {
      success: true,
      data: {
        logs: apiResponse.data?.activities || [],
        pagination: {
          total: apiResponse.data?.total || 0,
          page: Number.parseInt(page),
          pages: apiResponse.data?.pagination?.totalPages || 1,
          limit: Number.parseInt(resultsPerPage),
        },
      },
    };

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ACTIVITY-LOGS-API] Request completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      organizationId,
      logsReturned: transformedResponse.data.logs.length,
    });

    return NextResponse.json(transformedResponse);
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ACTIVITY-LOGS-API] Request failed after', totalTime, 'ms');
    console.error('🚨 [ACTIVITY-LOGS-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to fetch activity logs',
        success: false,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🚀 [ACTIVITY-LOGS-API] Starting activity log creation request');
  console.log('📊 [ACTIVITY-LOGS-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Use auth middleware for consistent authentication
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', success: false },
        { status: 401 },
      );
    }

    // Parse request body
    const activityData = await request.json();

    console.log('📋 [ACTIVITY-LOGS-API] Activity data received:', {
      requestId,
      organizationId: activityData.organizationId,
      action: activityData.action,
      resourceType: activityData.resourceType,
      resourceId: activityData.resourceId,
      resourceName: activityData.resourceName,
    });

    // Validate required fields
    if (!activityData.organizationId) {
      return NextResponse.json(
        { error: 'organizationId is required', success: false },
        { status: 400 },
      );
    }

    if (!activityData.action) {
      return NextResponse.json({ error: 'action is required', success: false }, { status: 400 });
    }

    if (!activityData.resourceType) {
      return NextResponse.json(
        { error: 'resourceType is required', success: false },
        { status: 400 },
      );
    }

    // Call external API to create activity log
    console.log('📤 [ACTIVITY-LOGS-API] Making external API call to create activity log...');
    const externalApiStart = Date.now();
    const apiResponse = await externalClient.post('/activity-logs', activityData);
    const externalApiTime = Date.now() - externalApiStart;

    console.log('📥 [ACTIVITY-LOGS-API] External API response received:', {
      requestId,
      responseTime: externalApiTime + 'ms',
      success: apiResponse.data?.success || false,
    });

    const totalTime = Date.now() - startTime;
    console.log('🎉 [ACTIVITY-LOGS-API] Activity log creation completed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
      organizationId: activityData.organizationId,
      action: activityData.action,
    });

    return NextResponse.json({
      success: true,
      message: 'Activity log created successfully',
      data: apiResponse.data,
    });
  } catch (error: unknown) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ACTIVITY-LOGS-API] Activity log creation failed after', totalTime, 'ms');
    console.error('🚨 [ACTIVITY-LOGS-API] Error details:', {
      requestId,
      errorType: error instanceof Error ? error.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      stackTrace: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      totalDuration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to create activity log',
        success: false,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
