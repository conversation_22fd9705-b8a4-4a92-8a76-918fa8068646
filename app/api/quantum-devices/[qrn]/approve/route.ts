import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ qrn: string }> },
) {
  const { qrn } = await params;
  if (!qrn) {
    return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
  }

  // Get user session
  const { session, error } = await requireAuth();
  if (error) return error;
  if (!session?.email) {
    return NextResponse.json(
      { error: 'Authentication required', requestId: request.url },
      { status: 401 },
    );
  }
  try {
    // Proxy to external API
    const response = await externalClient.patch(`/v2/quantum-devices/${qrn}/approve`);
    return NextResponse.json(response.data, { status: response.status });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error approving device:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to approve device' },
      { status: error.status || 500 },
    );
  }
}
