import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireDevicePermission } from '@/lib/auth-middleware';
import { getSession } from '@/lib/session';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ qrn: string }> },
) {
  const { qrn } = await params;
  if (!qrn) {
    return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
  }

  // Get user session
  const session = await getSession();
  if (!session?.email) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }

  let isQBraidAdmin = false;

  // Check if user is qBraid admin first
  try {
    const roleResponse = await externalClient.get('/get-role');
    console.log('🔍 [DELETE-DEVICE-REQUEST] Role response:', roleResponse.data);
    const role = roleResponse.data;
    if (role === 'admin') {
      isQBraidAdmin = true;
      console.log(
        '✅ [DELETE-DEVICE-REQUEST] qBraid admin bypass enabled for user:',
        session.email,
      );
    }
  } catch (error) {
    console.warn('⚠️ [DELETE-DEVICE-REQUEST] Could not check qBraid admin status:', error);
  }

  // If not qBraid admin, require orgId and check permissions
  if (!isQBraidAdmin) {
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');
    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const { error } = await requireDevicePermission('manage', orgId);
    if (error) return error;
  }

  try {
    // Proxy to external API
    const response = await externalClient.delete(`/v2/quantum-devices/${qrn}/request`);
    return NextResponse.json(response.data, { status: response.status });
  } catch (error: any) {
    console.log('HERE   s', error);
    console.error('❌ [API Gateway] Error deleting pending device request:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete pending device request' },
      { status: error.status || 500 },
    );
  }
}
