import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';
import { slackNotificationService } from '@/lib/slack-notifications';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ qbraid_id: string }> },
) {
  const { qbraid_id } = await params;
  if (!qbraid_id) {
    console.log('HERE   s', qbraid_id);
    return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
  }

  // Get user session
  const { session, error } = await requireAuth();
  if (error) return error;
  if (!session?.email) {
    return NextResponse.json(
      { error: 'Authentication required', requestId: request.url },
      { status: 401 },
    );
  }
  try {
    // Proxy to external API
    const response = await externalClient.patch(`/v2/quantum-devices/${qbraid_id}/approve`);
    
    // Send Slack notification for device approval
    try {
      await slackNotificationService.sendDeviceAddEditRequest({
        deviceName: response.data.name || response.data.deviceName || 'Unknown Device',
        deviceType: response.data.type || response.data.deviceType || 'Unknown',
        provider: response.data.provider || 'Unknown',
        organization: response.data.organizationId || response.data.orgId || 'Unknown',
        requestType: 'add',
        status: 'approved',
        adminNotes: `Device approved by ${session.name || session.email}`,
      });
    } catch (slackError) {
      console.error('Failed to send Slack notification:', slackError);
      // Don't fail the request if Slack notification fails
    }
    
    return NextResponse.json(response.data, { status: response.status });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error approving device:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to approve device' },
      { status: error.status || 500 },
    );
  }
}
