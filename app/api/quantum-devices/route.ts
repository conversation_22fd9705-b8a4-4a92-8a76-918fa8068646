import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import { logDeviceCreated } from '@/lib/audit-logger-server';
import { requireAuth, requireDevicePermission } from '@/lib/auth-middleware';
import { slackNotificationService } from '@/lib/slack-notifications';

export async function GET(request: NextRequest) {
  // Extract query parameters from incoming request FIRST
  const { searchParams } = new URL(request.url);
  const orgId = searchParams.get('orgId');

  // If orgId is missing, check if user is a qBraid admin
  if (!orgId) {
    // Get user session
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email) {
      return NextResponse.json(
        { error: 'Authentication required', requestId: request.url },
        { status: 401 },
      );
    }
    // Check qBraid admin role via external API
    try {
      const roleResponse = await externalClient.get('/get-role');
      const role = roleResponse.data;
      if (role === 'admin') {
        // qBraid admin: fetch all devices (no org filter)
        try {
          const response = await externalClient.get('/v2/quantum-devices', {});
          return NextResponse.json(response.data);
        } catch (error: any) {
          return NextResponse.json(
            { error: error.message || 'Failed to fetch quantum devices', data: [] },
            { status: error.status || 500 },
          );
        }
      } else {
        return NextResponse.json({ error: 'orgId is required' }, { status: 400 });
      }
    } catch (error_) {
      console.error('❌ [API Gateway] Error checking user role:', error_);
      return NextResponse.json({ error: 'Failed to check user role' }, { status: 401 });
    }
  }

  // Check device view permissions WITH orgId
  const { error } = await requireDevicePermission('view', orgId);
  if (error) return error;

  // Build params object dynamically for external API call
  const params: Record<string, string> = { organization: orgId };
  const supportedParams = [
    'qrn',
    'provider',
    'type',
    'status',
    'isAvailable',
    'organization',
    'verified',
  ];
  for (const key of supportedParams) {
    const value = searchParams.get(key);
    if (value !== null) {
      params[key] = value;
    }
  }
  try {
    // Proxy request to external QBraid API with any provided filters
    const response = await externalClient.get('/v2/quantum-devices', params);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching quantum devices:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch quantum devices',
        data: params.qrn ? {} : [],
      },
      { status: error.status || 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if the request is FormData (for image uploads) or JSON
    const contentType = request.headers.get('content-type') || '';
    const isFormData = contentType.includes('multipart/form-data');

    console.log('🔍 [QUANTUM-DEVICES-API] Request analysis:', {
      contentType,
      isFormData,
      hasContentType: !!contentType,
    });

    let body: any;
    let orgId: string;
    let deviceImageFile: File | null = null;
    let pricingFile: File | null = null;
    let runInputTypeFile: File | null = null;

    if (isFormData) {
      // Handle FormData submission with image upload
      console.log('📤 [QUANTUM-DEVICES-API] Processing FormData submission with image upload');

      const formData = await request.formData();

      // Extract device data from JSON string
      const deviceDataString = formData.get('deviceData') as string;
      console.log('🔍 [QUANTUM-DEVICES-API] Device data string:', {
        deviceDataString: deviceDataString?.slice(0, 100) + '...',
        length: deviceDataString?.length,
        hasDeviceData: !!deviceDataString,
      });

      if (!deviceDataString) {
        return NextResponse.json({ error: 'deviceData is required in FormData' }, { status: 400 });
      }

      try {
        body = JSON.parse(deviceDataString);
        console.log('✅ [QUANTUM-DEVICES-API] JSON parsed successfully');
      } catch (parseError) {
        console.error('❌ [QUANTUM-DEVICES-API] JSON parse error:', parseError);
        console.error('❌ [QUANTUM-DEVICES-API] Raw deviceDataString:', deviceDataString);
        return NextResponse.json(
          {
            error: 'Invalid JSON in deviceData',
            details: parseError instanceof Error ? parseError.message : 'Unknown parse error',
          },
          { status: 400 },
        );
      }

      orgId = body.organization || body.orgId;

      // Extract device image file
      const imageFile = formData.get('deviceImage') as File | null;
      if (imageFile && imageFile.size > 0) {
        deviceImageFile = imageFile;
        console.log('📁 [QUANTUM-DEVICES-API] Device image file found', {
          fileName: imageFile.name,
          fileSize: imageFile.size,
          fileType: imageFile.type,
        });
      }

      // Extract pricing file
      const pricingFileData = formData.get('pricingFile') as File | null;
      if (pricingFileData && pricingFileData.size > 0) {
        pricingFile = pricingFileData;
        console.log('📁 [QUANTUM-DEVICES-API] Pricing file found', {
          fileName: pricingFileData.name,
          fileSize: pricingFileData.size,
          fileType: pricingFileData.type,
        });
      }

      // Extract run input type file
      const runInputTypeFileData = formData.get('runInputTypeFile') as File | null;
      if (runInputTypeFileData && runInputTypeFileData.size > 0) {
        runInputTypeFile = runInputTypeFileData;
        console.log('📁 [QUANTUM-DEVICES-API] Run input type file found', {
          fileName: runInputTypeFileData.name,
          fileSize: runInputTypeFileData.size,
          fileType: runInputTypeFileData.type,
        });
      }
    } else {
      // Handle JSON submission (backward compatibility)
      console.log('📤 [QUANTUM-DEVICES-API] Processing JSON submission');
      body = await request.json();
      orgId = body.organizationId || body.orgId;
    }

    if (!orgId) {
      return NextResponse.json(
        { error: 'organizationId or orgId is required in request body' },
        { status: 400 },
      );
    }

    // Check device management permissions WITH orgId
    const { error, data, session } = await requireDevicePermission('manage', orgId);
    if (error) return error;
    const userRole = data?.role || 'member';

    // Extract user email and device ID for S3 path convention (userEmail/deviceID)
    const userEmail = session?.email || 'unknown-user';
    const deviceId = body.qrn || body.deviceId || body.name?.replace(/\s+/g, '_').toLowerCase();

    // Import upload utilities
    const {
      uploadImageToS3,
      createUploadConfig: createImageUploadConfig,
      logUploadOperation: logImageUploadOperation,
    } = await import('@/lib/image/image-upload');
    const {
      uploadFileToS3,
      createUploadConfig: createFileUploadConfig,
      logUploadOperation: logFileUploadOperation,
    } = await import('@/lib/files/file-upload');
    const { validateS3Config } = await import('@/lib/image/image-upload-server');

    // Validate S3 configuration once
    const s3ConfigCheck = validateS3Config();
    if (!s3ConfigCheck.valid) {
      console.error('❌ [QUANTUM-DEVICES-API] S3 config invalid:', s3ConfigCheck.error);
      return NextResponse.json({ error: 'Upload service not configured' }, { status: 500 });
    }

    // Handle image upload if present
    if (deviceImageFile) {
      console.log('📤 [QUANTUM-DEVICES-API] Uploading device image to S3');

      // Upload device image using device-images/userEmail/deviceID path convention
      const uploadConfig = createImageUploadConfig(
        {
          orgId,
          userId: userEmail,
          providerName: deviceId,
          folderName: `device-images/${userEmail}`,
        },
        'device-image',
      );

      logImageUploadOperation('start', uploadConfig, {
        fileName: deviceImageFile.name,
        fileSize: deviceImageFile.size,
      });

      const uploadResult = await uploadImageToS3(deviceImageFile, uploadConfig);

      if (uploadResult.success && uploadResult.url) {
        // Update body with the uploaded image URL
        body.deviceImage = uploadResult.url;

        logImageUploadOperation('success', uploadConfig, {
          fileName: deviceImageFile.name,
          url: uploadResult.url,
        });

        console.log('✅ [QUANTUM-DEVICES-API] Device image uploaded successfully', {
          url: uploadResult.url,
        });
      } else {
        logImageUploadOperation('error', uploadConfig, {
          fileName: deviceImageFile.name,
          error: uploadResult.error,
        });

        return NextResponse.json(
          {
            error: `Failed to upload device image: ${uploadResult.error}`,
          },
          { status: 400 },
        );
      }
    }

    // Handle pricing file upload if present
    if (pricingFile) {
      console.log('📤 [QUANTUM-DEVICES-API] Uploading pricing file to S3');

      // Upload pricing file using /pricing-files/userEmail/deviceID path convention
      const uploadConfig = createFileUploadConfig(
        {
          orgId,
          userId: userEmail,
          providerName: deviceId,
          folderName: `pricing-files/${userEmail}/${deviceId}`,
          allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/csv',
            'application/json',
            'text/plain',
            'text/x-python',
            'text/javascript',
            'application/javascript',
            'text/yaml',
            'application/x-yaml',
            'text/x-yaml',
          ],
          maxFileSize: 10 * 1024 * 1024, // 10MB for pricing documents
        },
        'pricing-file',
      );

      logFileUploadOperation('start', uploadConfig, {
        fileName: pricingFile.name,
        fileSize: pricingFile.size,
      });

      const uploadResult = await uploadFileToS3(pricingFile, uploadConfig);

      if (uploadResult.success && uploadResult.url) {
        // Update body with the uploaded pricing file URL
        // Initialize pricing object if it doesn't exist
        if (!body.pricing) {
          body.pricing = {};
        }
        body.pricing.pricingFile = uploadResult.url;

        logFileUploadOperation('success', uploadConfig, {
          fileName: pricingFile.name,
          url: uploadResult.url,
        });

        console.log('✅ [QUANTUM-DEVICES-API] Pricing file uploaded successfully', {
          url: uploadResult.url,
          metadata: uploadResult.metadata,
        });
      } else {
        logFileUploadOperation('error', uploadConfig, {
          fileName: pricingFile.name,
          error: uploadResult.error,
        });

        return NextResponse.json(
          {
            error: `Failed to upload pricing file: ${uploadResult.error}`,
          },
          { status: 400 },
        );
      }
    }

    // Handle run input type file upload if present
    if (runInputTypeFile) {
      console.log('📤 [QUANTUM-DEVICES-API] Uploading run input type file to S3');

      // Upload input type file using /run-input-type-files/userEmail/deviceID path convention
      const uploadConfig = createFileUploadConfig(
        {
          orgId,
          userId: userEmail,
          providerName: deviceId,
          folderName: `run-input-type-files/${userEmail}/${deviceId}`,
          allowedTypes: [
            'text/plain',
            'text/x-python',
            'application/json',
            'application/x-yaml',
            'text/yaml',
            'application/yaml',
            'text/javascript',
            'application/javascript',
          ],
          maxFileSize: 5 * 1024 * 1024, // 5MB for input type files
        },
        'input-type-file',
      );

      logFileUploadOperation('start', uploadConfig, {
        fileName: runInputTypeFile.name,
        fileSize: runInputTypeFile.size,
      });

      const uploadResult = await uploadFileToS3(runInputTypeFile, uploadConfig);

      if (uploadResult.success && uploadResult.url) {
        // Store the S3 URL in the runInputTypes array or a separate field
        // We'll add it as a special entry in runInputTypes with the S3 URL
        if (!body.runInputTypeFiles) {
          body.runInputTypeFiles = [];
        }
        body.runInputTypeFiles.push({
          fileName: runInputTypeFile.name,
          url: uploadResult.url,
          uploadedAt: new Date().toISOString(),
        });

        logFileUploadOperation('success', uploadConfig, {
          fileName: runInputTypeFile.name,
          url: uploadResult.url,
        });

        console.log('✅ [QUANTUM-DEVICES-API] Input type file uploaded successfully', {
          url: uploadResult.url,
          metadata: uploadResult.metadata,
        });
      } else {
        logFileUploadOperation('error', uploadConfig, {
          fileName: runInputTypeFile.name,
          error: uploadResult.error,
        });

        return NextResponse.json(
          {
            error: `Failed to upload input type file: ${uploadResult.error}`,
          },
          { status: 400 },
        );
      }
    }

    // Proxy POST request to external QBraid API
    const response = await externalClient.post('/v2/quantum-devices', body);

    // Create audit log for successful device creation
    if (response.status === 201 || response.status === 200) {
      try {
        await logDeviceCreated(
          body.name || body.deviceName || 'Unknown Device',
          response.data.id || response.data.qrn || 'unknown',
          body.organizationId || body.orgId,
          userRole,
        );
      } catch (auditError) {
        console.warn('⚠️ [QUANTUM-DEVICES-API] Failed to create audit log:', auditError);
        // Don't fail the request if audit logging fails
      }

      // Send Slack notification for device addition
      try {
        await slackNotificationService.sendDeviceAddEditRequest({
          deviceName: body.name || body.deviceName || 'Unknown Device',
          deviceType: body.type || body.deviceType || 'Unknown',
          provider: body.provider || 'Unknown',
          organization: body.organizationId || body.orgId || 'Unknown',
          requestType: 'add',
          status: 'pending',
          changes: body.description ? `Description: ${body.description}` : undefined,
        });
      } catch (slackError) {
        console.error('Failed to send Slack notification:', slackError);
        // Don't fail the request if Slack notification fails
      }
    }

    return NextResponse.json(response.data, { status: 201 });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error creating quantum device:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to create quantum device',
      },
      { status: error.status || 500 },
    );
  }
}
