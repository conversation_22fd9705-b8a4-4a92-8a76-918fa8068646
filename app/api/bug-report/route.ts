import { NextRequest, NextResponse } from 'next/server';
import { slackNotificationService } from '@/lib/slack-notifications';

interface BugReportData {
  title: string;
  description: string;
  steps: string;
  expected: string;
  actual: string;
  userEmail: string;
  route: string;
  userAgent: string;
  timestamp: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('🐛 [BUG-REPORT-API] Starting bug report request');
  console.log('📊 [BUG-REPORT-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { title, description, steps, expected, actual, userEmail, route, userAgent, timestamp } =
      body as BugReportData;

    console.log('📋 [BUG-REPORT-API] Bug report data:', {
      requestId,
      title,
      userEmail,
      route,
      hasSteps: !!steps,
      hasExpected: !!expected,
      hasActual: !!actual,
    });

    // Validate required fields
    if (!title || !description) {
      console.error('❌ [BUG-REPORT-API] Missing required fields:', {
        requestId,
        hasTitle: !!title,
        hasDescription: !!description,
      });
      return NextResponse.json(
        { error: 'Title and description are required', success: false },
        { status: 400 },
      );
    }

    // Prepare Slack message
    const slackMessage = {
      text: `🐛 Bug Report: ${title}`,
      attachments: [
        {
          color: '#ff0000',
          title: `🐛 Bug Report: ${title}`,
          title_link: `${process.env.NEXT_PUBLIC_APP_URL}${route}`,
          author_name: 'qBraid Partner Dashboard',
          author_link: process.env.NEXT_PUBLIC_APP_URL,
          author_icon: `${process.env.NEXT_PUBLIC_APP_URL}/favicon.ico`,
          fields: [
            {
              title: ':bust_in_silhouette: Reporter',
              value: userEmail || 'Anonymous',
              short: true,
            },
            {
              title: ':globe_with_meridians: Route',
              value: `\`${route}\``,
              short: true,
            },
            {
              title: ':memo: Description',
              value: description,
              short: false,
            },
          ],
          footer: 'qBraid Partner Dashboard',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    // Add optional fields if they exist (avoid multiple push calls)
    const additionalFields: any[] = [
      ...(steps
        ? [
            {
              title: ':footprints: Steps to Reproduce',
              value: steps,
              short: false,
            },
          ]
        : []),
      ...(expected && actual
        ? [
            {
              title: ':white_check_mark: Expected Behavior',
              value: expected,
              short: false,
            },
            {
              title: ':x: Actual Behavior',
              value: actual,
              short: false,
            },
          ]
        : []),
      {
        title: ':computer: Browser',
        value: userAgent ? userAgent.split(' ').slice(-2).join(' ') : 'Unknown',
        short: true,
      },
      {
        title: ':clock1: Reported At',
        value: new Date(timestamp).toLocaleString(),
        short: true,
      },
    ];

    // Add all additional fields to the attachment
    if (additionalFields.length > 0) {
      slackMessage.attachments[0].fields = [
        ...slackMessage.attachments[0].fields!,
        ...additionalFields,
      ];
    }

    // Send to Slack
    console.log('📤 [BUG-REPORT-API] Sending to Slack...');
    const slackSuccess = await slackNotificationService.sendNotification(slackMessage);

    if (!slackSuccess) {
      console.error('❌ [BUG-REPORT-API] Failed to send Slack notification');
      // Don't fail the request - just log it
    }

    const totalTime = Date.now() - startTime;
    console.log('✅ [BUG-REPORT-API] Bug report processed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
    });

    return NextResponse.json({
      success: true,
      message: 'Bug report received. Thank you for your feedback!',
      requestId,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [BUG-REPORT-API] Request failed after', totalTime, 'ms', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to process bug report',
        success: false,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
