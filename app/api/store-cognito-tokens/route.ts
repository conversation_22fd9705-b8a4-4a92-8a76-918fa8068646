import { NextRequest, NextResponse } from 'next/server';
import { setCognitoTokenCookies } from '@/lib/session';

/**
 * POST /api/store-cognito-tokens
 * Stores Cognito tokens (access and ID tokens) in secure server cookies
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { accessToken, idToken } = body;

    console.log('🍪 [API] Storing Cognito tokens in secure cookies:', {
      hasAccessToken: !!accessToken,
      hasIdToken: !!idToken,
    });

    // Validate that we have at least one token
    if (!accessToken && !idToken) {
      return NextResponse.json(
        { error: 'At least one token (access or ID) is required', success: false },
        { status: 400 },
      );
    }

    // Store tokens in secure HTTP-only cookies
    await setCognitoTokenCookies({
      accessToken,
      idToken,
    });

    console.log('🍪 [API] Successfully stored Cognito tokens in secure cookies');

    return NextResponse.json({
      success: true,
      message: 'Tokens stored in secure cookies',
      storedTokens: {
        hasAccessToken: !!accessToken,
        hasIdToken: !!idToken,
      },
    });
  } catch (error) {
    console.error('🍪 [API] Error storing Cognito tokens:', error);

    return NextResponse.json(
      {
        error: 'Failed to store tokens',
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

/**
 * GET /api/store-cognito-tokens
 * Returns current token status from cookies
 */
export async function GET() {
  try {
    const { getCognitoTokenCookies } = await import('@/lib/session');
    const tokens = await getCognitoTokenCookies();

    return NextResponse.json({
      success: true,
      tokens: {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
        accessTokenPreview: tokens.accessToken ? `${tokens.accessToken.slice(0, 20)}...` : null,
        idTokenPreview: tokens.idToken ? `${tokens.idToken.slice(0, 20)}...` : null,
      },
    });
  } catch (error) {
    console.error('🍪 [API] Error retrieving token status:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve token status',
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

// Disable other HTTP methods
export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
