import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';
import { logDeviceAccessApproved, logDeviceAccessDenied } from '@/lib/audit-logger-server';
import { slackNotificationService } from '@/lib/slack-notifications';

// PUT /api/device-access-requests/:requestId
// Approve or deny a device access request
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ requestId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { requestId } = await params;
    const { action, deniedReason, expiresAt } = (await request.json()) as {
      action: 'approve' | 'deny';
      deniedReason?: string;
      expiresAt?: string;
    };

    if (!requestId || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Update device access request
    const response = await externalClient.put(`/device-access-requests/${requestId}`, {
      action,
      deniedReason: action === 'deny' ? deniedReason : undefined,
      expiresAt: action === 'approve' ? expiresAt : undefined,
    });
    console.log('response.data==================>', response.data);

    if (action === 'approve') {
      logDeviceAccessApproved(
        response.data.request.deviceId,
        response.data.request.deviceName,
        response.data.request.requestType,
        response.data.request.organization,
        response.data.request.approverEmail,
        {
          email: session.email,
        },
      );

      // Send Slack notification for approval
      try {
        await slackNotificationService.sendDeviceAccessRequest({
          userName:
            response.data.request.userName || response.data.request.approverEmail || 'Unknown User',
          userEmail: response.data.request.approverEmail,
          deviceName: response.data.request.deviceName,
          deviceId: response.data.request.deviceId,
          organization: response.data.request.organization,
          justification: response.data.request.justification || 'No justification provided',
          requestType: 'access',
          status: 'approved',
          adminNotes: `Approved by ${session.email}`,
        });
      } catch (slackError) {
        console.error('Failed to send Slack notification:', slackError);
      }
    }

    if (action === 'deny') {
      logDeviceAccessDenied(
        response.data.request.deviceId,
        response.data.request.deviceName,
        response.data.request.requestType,
        response.data.request.organization,
        response.data.request.approverEmail,
        {
          email: session.email,
        },
      );

      // Send Slack notification for denial
      try {
        await slackNotificationService.sendDeviceAccessRequest({
          userName:
            response.data.request.userName || response.data.request.approverEmail || 'Unknown User',
          userEmail: response.data.request.approverEmail,
          deviceName: response.data.request.deviceName,
          deviceId: response.data.request.deviceId,
          organization: response.data.request.organization,
          justification: response.data.request.justification || 'No justification provided',
          requestType: 'access',
          status: 'denied',
          adminNotes: deniedReason || `Denied by ${session.email}`,
        });
      } catch (slackError) {
        console.error('Failed to send Slack notification:', slackError);
      }
    }

    return NextResponse.json(response.data, { status: 200 });
  } catch (error) {
    console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Error updating request`, error);
    return NextResponse.json({ error: 'Failed to update device access request' }, { status: 500 });
  }
}

// GET /api/device-access-requests/:requestId
// Get specific device access request
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ requestId: string }> },
) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { requestId } = await params;

    if (!requestId) {
      return NextResponse.json({ error: 'Request ID is required' }, { status: 400 });
    }

    try {
      const response = await externalClient.get(`/device-access-requests/${requestId}`);
      return NextResponse.json(response.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return NextResponse.json({ error: 'Request not found' }, { status: 404 });
      }
      console.error('Error fetching device access request from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch device access request' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in device access request route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
