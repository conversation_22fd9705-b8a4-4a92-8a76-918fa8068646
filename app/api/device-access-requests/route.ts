import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth, requireDevicePermission } from '@/lib/auth-middleware';

import { logDeviceAccessRequested } from '@/lib/audit-logger-server';
import { slackNotificationService } from '@/lib/slack-notifications';
// POST /api/device-access-requests
// Create a new device access request
export async function POST(request: NextRequest) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { deviceId, deviceName, requestType, justification, accessDuration, orgId } =
      (await request.json()) as {
        deviceId: string;
        deviceName: string;
        requestType: 'read' | 'write';
        justification: string;
        accessDuration?: string;
        orgId: string;
      };

    const { error: devicePermissionError, data } = await requireDevicePermission('manage', orgId);

    if (devicePermissionError) return devicePermissionError;

    if (!deviceId || !requestType || !justification) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create device access request
    const response = await externalClient.post('/device-access-requests', {
      deviceId,
      requestType,
      justification,
      accessDuration,
      metadata: {
        orgId,
        email: session.email,
        requestedAt: new Date().toISOString(),
      },
    });
    console.log('🌐 [DEVICE-ACCESS-REQUEST-API] Request created', response.data);

    logDeviceAccessRequested(deviceId, deviceName, requestType, orgId, data?.role || 'member', {
      email: session.email,
    });

    console.log(`🌐 [DEVICE-ACCESS-REQUEST-API] Request created`, {
      deviceId,
      requestType,
      requestedBy: session.email,
    });

    // Send Slack notification
    try {
      await slackNotificationService.sendDeviceAccessRequest({
        userName: session.username,
        userEmail: session.email,
        deviceName,
        deviceId,
        organization: orgId,
        justification,
        requestType: 'access',
        status: 'pending',
      });
    } catch (slackError) {
      console.error('Failed to send Slack notification:', slackError);
      // Don't fail the request if Slack notification fails
    }

    return NextResponse.json(response.data, { status: 201 });
  } catch (error) {
    console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Error creating request`, error);
    return NextResponse.json({ error: 'Failed to create device access request' }, { status: 500 });
  }
}

// GET /api/device-access-requests
// Get device access requests (filtered by query params)
//based on sample_res.json, the response is in the json.requests array create type for it

export async function GET(request: NextRequest) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [DEVICE-ACCESS-REQUEST-API] Authentication failed `);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'pending';
    const organizationId = searchParams.get('organizationId');

    const queryParams = new URLSearchParams();
    if (status) queryParams.append('status', status);
    if (organizationId) queryParams.append('organizationId', organizationId);

    try {
      const response = await externalClient.get(
        `/device-access-requests?${queryParams.toString()}`,
      );
      return NextResponse.json(response.data);
    } catch (error: unknown) {
      if ((error as { response?: { status: number } }).response?.status === 404) {
        return NextResponse.json([]);
      }
      console.error('Error fetching device access requests from external API:', error);
      return NextResponse.json(
        { message: 'Failed to fetch device access requests' },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error in device access requests route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
