import { NextRequest, NextResponse } from 'next/server';
import { slackNotificationService } from '@/lib/slack-notifications';

interface FeedbackData {
  type: string;
  issue: string;
  details: string;
  steps: string;
  priority: string;
  userEmail: string;
  page: string;
  browser: string;
  timestamp: string;
  orgName: string;
  orgId: string;
  userRole: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).slice(2, 15);

  console.log('💬 [FEEDBACK-API] Starting feedback request');
  console.log('📊 [FEEDBACK-API] Request details:', {
    requestId,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
  });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { type, issue, details, steps, priority, userEmail, page, browser, timestamp, orgName, orgId, userRole } = body as FeedbackData;

    console.log('📋 [FEEDBACK-API] Feedback data:', {
      requestId,
      type,
      issue,
      userEmail,
      page,
      hasSteps: !!steps,
      priority,
      orgName,
      orgId,
      userRole,
    });

    // Validate required fields
    if (!issue || !details) {
      console.error('❌ [FEEDBACK-API] Missing required fields:', {
        requestId,
        hasIssue: !!issue,
        hasDetails: !!details,
      });
      return NextResponse.json(
        { error: 'Issue and details are required', success: false },
        { status: 400 }
      );
    }

    // Configure different messages based on feedback type
    const feedbackConfig = {
      bug: {
        emoji: '🐛',
        color: '#ff0000', // Red for bugs
        title: 'Bug Report',
        priorityText: priority ? `Priority: ${priority.charAt(0).toUpperCase() + priority.slice(1)}` : undefined,
      },
      feature: {
        emoji: '💡',
        color: '#fbbf24', // Yellow for features
        title: 'Feature Request',
      },
      help: {
        emoji: '❓',
        color: '#3b82f6', // Blue for help
        title: 'Help Request',
      },
      other: {
        emoji: '💬',
        color: '#6b7280', // Gray for other
        title: 'General Feedback',
      },
    };

    const config = feedbackConfig[type as keyof typeof feedbackConfig] || feedbackConfig.other;

    // Use organization context from the request
    const orgContext = orgName || 'Not available';
    const role = userRole || 'Not available';

    // Prepare Slack message
    const slackMessage = {
      text: `${config.emoji} ${config.title}: ${issue}`,
      attachments: [
        {
          color: config.color,
          title: `${config.emoji} ${config.title}`,
          title_link: `${process.env.NEXT_PUBLIC_APP_URL}${page}`,
          author_name: 'qBraid Partner Dashboard',
          author_link: process.env.NEXT_PUBLIC_APP_URL,
          author_icon: `${process.env.NEXT_PUBLIC_APP_URL}/favicon.ico`,
          fields: [
            {
              title: ':bust_in_silhouette: From',
              value: userEmail || 'Anonymous',
              short: true,
            },
            {
              title: ':building_construction: Organization',
              value: orgContext,
              short: true,
            },
            {
              title: ':briefcase: Role',
              value: role,
              short: true,
            },
            {
              title: ':globe_with_meridians: Page',
              value: `\`${page}\``,
              short: true,
            },
            {
              title: ':speech_balloon: Issue',
              value: issue,
              short: false,
            },
            {
              title: ':memo: Details',
              value: details,
              short: false,
            },
          ],
          footer: `qBraid Partner Dashboard - ${config.title}`,
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    // Add steps if provided
    if (steps) {
      slackMessage.attachments[0].fields!.push({
        title: ':footprints: Steps Taken',
        value: steps,
        short: false,
      });
    }

    // Add priority for bugs
    if (type === 'bug' && priority) {
      slackMessage.attachments[0].fields!.push({
        title: '🚨 Priority',
        value: priority.charAt(0).toUpperCase() + priority.slice(1),
        short: true,
      });
    }

    // Add browser info
    slackMessage.attachments[0].fields!.push({
      title: ':computer: Browser',
      value: browser ? browser.split(' ').slice(-2).join(' ') : 'Unknown',
      short: true,
    });

    // Add timestamp
    slackMessage.attachments[0].fields!.push({
      title: ':clock1: Sent At',
      value: new Date(timestamp).toLocaleString(),
      short: true,
    });

    // Send to Slack
    console.log('📤 [FEEDBACK-API] Sending to Slack...');
    const slackSuccess = await slackNotificationService.sendNotification(slackMessage);

    if (!slackSuccess) {
      console.error('❌ [FEEDBACK-API] Failed to send Slack notification');
      // Don't fail the request - just log it
    }

    const totalTime = Date.now() - startTime;
    console.log('✅ [FEEDBACK-API] Feedback processed successfully:', {
      requestId,
      totalTime: totalTime + 'ms',
    });

    const successMessages = {
      bug: 'Thank you for reporting this bug! We\'ll investigate and fix it as soon as possible.',
      feature: 'Thank you for your feature suggestion! We\'ll review it and consider it for future updates.',
      help: 'Thank you for reaching out! We\'ll get back to you with help as soon as possible.',
      other: 'Thank you for your feedback! We appreciate you taking the time to share your thoughts.',
    };

    return NextResponse.json({
      success: true,
      message: successMessages[type as keyof typeof successMessages] || successMessages.other,
      requestId,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [FEEDBACK-API] Request failed after', totalTime, 'ms', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to process feedback',
        success: false,
        requestId,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}