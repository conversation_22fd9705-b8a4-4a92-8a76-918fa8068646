import { NextRequest, NextResponse } from 'next/server';
import { getRolesForUser, invalidateUserRoles } from '@/lib/external-roles';
import { getCognitoTokenCookies, getCurrentSessionId } from '@/lib/session';
import { requireAuth } from '@/lib/auth-middleware';

/**
 * Enhanced API endpoint to refresh current user's roles from external API
 * Optimized for TanStack Query mutations with proper response format
 */
export async function POST(request: NextRequest) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;

    if (!session) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          timestamp: new Date().toISOString(),
        },
        { status: 401 },
      );
    }

    const email = session.email;
    console.log(`🔄 [REFRESH-ROLES] Refreshing roles for ${email}`);

    // Get optional force refresh parameter
    const body = await request.json().catch(() => ({}));
    const forceRefresh = body.forceRefresh || true;

    // Invalidate cache first if force refresh
    if (forceRefresh) {
      await invalidateUserRoles(email);
    }

    // Get tokens for API call
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      console.warn('⚠️ [REFRESH-ROLES] No ID token available');
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication token unavailable',
          message: 'Missing ID token for external API call',
          timestamp: new Date().toISOString(),
        },
        { status: 401 },
      );
    }

    // Fetch fresh roles from external API
    const newRoles = await getRolesForUser(email, forceRefresh);

    console.log(`✅ [REFRESH-ROLES] Retrieved ${newRoles.length} roles for ${email}:`, newRoles);

    const response = {
      success: true,
      message: 'Roles refreshed successfully',
      data: {
        email,
        roles: newRoles,
        timestamp: new Date().toISOString(),
      },
    };

    const nextResponse = NextResponse.json(response);

    // Add headers to indicate cache invalidation
    nextResponse.headers.set('X-Cache-Invalidated', 'true');
    nextResponse.headers.set('X-Roles-Updated', newRoles.length.toString());

    return nextResponse;
  } catch (error) {
    console.error('❌ [REFRESH-ROLES] Failed to refresh roles:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to refresh roles',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    service: 'Role Refresh API',
    description: 'POST to refresh current user roles from external QBraid API',
    methods: ['POST'],
    parameters: {
      forceRefresh: 'boolean (optional) - Force cache invalidation before refresh',
    },
    timestamp: new Date().toISOString(),
  });
}
