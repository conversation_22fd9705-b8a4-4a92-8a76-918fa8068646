{"description": "Sample public device response schema - safe for users requesting access", "sample_public_response": [{"_id": "622979b4d3e5f145307a5fcd", "qrn": "aws_sv1", "name": "SV1", "provider": "AWS", "type": "Simulator", "numberQubits": 34, "status": "ONLINE", "isAvailable": true, "architecture": null, "processorType": "State vector", "deviceDescription": "State vector simulator (SV1)", "deviceAbout": "https://docs.aws.amazon.com/braket/latest/developerguide/braket-simulator-sv1.html", "logo": {"light": "https://qbraid-static.s3.amazonaws.com/lab-docs/logos-sqr/aws_light_theme.png", "dark": "https://qbraid-static.s3.amazonaws.com/lab-docs/logos-sqr/aws_dark_theme.png"}, "vendor": "AWS", "paradigm": "gate-based", "runPackage": "braket", "providerDescription": "Amazon Braket provides GPU powered simulators for quantum computing", "about": "https://qbraid.webflow.io/quantum-pricing/amazon-braket", "spec": ["SV1 is an on-demand, high-performance, universal state vector simulator. It can simulate circuits of up to 34 qubits. You can expect a 34-qubit, dense, and square circuit (circuit depth = 34) to take approximately 1–2 hours to complete, depending on the type of gates used and other factors."], "runInputTypes": ["braket"], "noiseModels": []}], "confidential_fields_removed": {"note": "The following fields are considered confidential and should NEVER be exposed in public APIs", "fields": ["pricing.perTask", "pricing.perShot", "pricing.perMinute", "pendingJobs", "avgQueueTime", "statusMsg", "statusRefresh", "nextAvailable", "availabilityCD", "verified", "requestedBy", "permissionsNodes", "whiteListedDomains", "blackListedD<PERSON>ins", "activeVersion", "organization", "providerId", "pendingEdits", "visibility"]}, "public_fields_explanation": {"_id": "Required for device identification in requests", "qrn": "Required for device identification", "name": "Device display name", "provider": "Provider name (AWS, IBM, etc.) - helps users identify vendor", "type": "QPU or Simulator - helps users understand device category", "numberQubits": "Technical specification - helps users plan circuits", "status": "ONLINE/OFFLINE - helps users know availability", "isAvailable": "Boolean availability flag", "architecture": "Technical specification if available", "processorType": "Technical specification", "deviceDescription": "Helps users understand device capabilities", "deviceAbout": "Link to public documentation", "logo": "Provider branding for UI", "vendor": "Device manufacturer", "paradigm": "Quantum computing paradigm (gate-based, etc.)", "runPackage": "SDK/package information", "providerDescription": "Public provider information", "about": "Public information links", "spec": "Technical specifications array", "runInputTypes": "Supported input formats", "noiseModels": "Available noise models (if applicable)"}}