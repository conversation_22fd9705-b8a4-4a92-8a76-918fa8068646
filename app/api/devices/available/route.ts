// Fixed API Route: /api/devices/available/route.ts
import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireDevicePermission } from '@/lib/auth-middleware';
import { AvailableDevice } from '@/types/device';
// GET /api/devices/available
// Get all public devices available for access requests
export async function GET(request: NextRequest) {
  try {
    // Get orgId from query params since it's not in the route path
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');

    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Authentication check
    const { session, error } = await requireDevicePermission('view', orgId);
    if (error) return error;

    if (!session?.email || !session?.userId) {
      console.error(`❌ [AVAILABLE-DEVICES-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Extract and validate query parameters
    const filters = {
      provider: searchParams.get('provider'),
      type: searchParams.get('type'),
      deviceStatus: searchParams.get('deviceStatus'),
    };

    // Build query parameters for external API
    const queryParams = new URLSearchParams();
    for (const [key, value] of Object.entries(filters)) {
      if (value) queryParams.append(key, value);
    }

    // Fetch devices from external API
    // current response is not in the correct format, we need to fix it, it returns everything in the data field
    // we need to return the data field as an array of AvailableDevice
    const response = await externalClient.get(`/v2/quantum-devices?${queryParams.toString()}`);

    const devices = response.data || [];
    let devicesResponse: AvailableDevice[] = [];
    devicesResponse = devices.map((device: any) => {
      return {
        _id: device._id,
        name: device.name,
        provider: device.provider,
        type: device.type,
        status: device.status,
        isAvailable: device.isAvailable,
        deviceDescription: device.deviceDescription,
        logo: device.logo,
        deviceAbout: device.deviceAbout,
        numberQubits: device.numberQubits,
        processorType: device.processorType,
        modality: device.modality,
        vendor: device.vendor,
        paradigm: device.paradigm,
        about: device.about,
        spec: device.spec,
        runInputTypes: device.runInputTypes,
        noiseModels: device.noiseModels,
        qrn: device.qrn,
        organization: device.organization,
        verified: device.verified,
      };
    });
    console.log(`🌐 [AVAILABLE-DEVICES-API] Fetched available devices`, {
      count: devices.length,
      orgId,
      filters: Object.fromEntries(Object.entries(filters).filter(([, v]) => v)),
    });

    return NextResponse.json(devicesResponse);
  } catch (error: unknown) {
    // Handle specific external API errors
    if ((error as { response?: { status: number } }).response?.status === 404) {
      console.log(`📭 [AVAILABLE-DEVICES-API] No devices found`);
      return NextResponse.json([]);
    }

    // Log error details for debugging
    console.error(`❌ [AVAILABLE-DEVICES-API] Error:`, {
      message: (error as { message: string }).message,
      status: (error as { response?: { status: number } }).response?.status,
    });

    // Return appropriate error response
    const isExternalAPIError = (error as { response?: { status: number } }).response?.status;
    return NextResponse.json(
      {
        error: isExternalAPIError ? 'Failed to fetch available devices' : 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: (error as { message: string }).message,
        }),
      },
      { status: isExternalAPIError ? 502 : 500 },
    );
  }
}
