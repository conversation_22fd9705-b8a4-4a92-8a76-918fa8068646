import { NextRequest, NextResponse } from 'next/server';
import externalClient from '@/app/api/_utils/external-client';
import { requireAuth } from '@/lib/auth-middleware';

// POST /api/devices/batch
// Get details for multiple devices by their IDs
export async function POST(request: NextRequest) {
  try {
    const { session, error } = await requireAuth();
    if (error) return error;
    if (!session?.email || !session?.userId) {
      console.error(`❌ [BATCH-DEVICES-API] Authentication failed`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { deviceIds } = (await request.json()) as {
      deviceIds: string[];
    };

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return NextResponse.json({ error: 'deviceIds array is required' }, { status: 400 });
    }

    if (deviceIds.length > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 devices can be requested at once' },
        { status: 400 },
      );
    }

    try {
      const response = await externalClient.post('/devices/batch', {
        deviceIds,
      });

      console.log(`🌐 [BATCH-DEVICES-API] Fetched batch device details`, {
        requestedCount: deviceIds.length,
        returnedCount: response.data?.length || 0,
      });

      return NextResponse.json(response.data);
    } catch (error: unknown) {
      if (error.response?.status === 404) {
        return NextResponse.json([]);
      }
      console.error('Error fetching batch device details from external API:', error);
      return NextResponse.json({ message: 'Failed to fetch device details' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in batch devices route:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
