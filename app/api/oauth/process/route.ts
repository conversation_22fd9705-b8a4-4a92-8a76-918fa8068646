import { NextRequest, NextResponse } from 'next/server';
import { createSession, setCognitoTokenCookies, verifySession } from '@/lib/session';
import { SESSION_COOKIE_NAME } from '@/lib/constant';

export async function POST(request: NextRequest) {
  try {
    const { accessToken, idToken } = await request.json();

    if (!accessToken || !idToken) {
      return NextResponse.json({ success: false, error: 'Missing OAuth tokens' }, { status: 400 });
    }

    // Parse user info from ID token (server-side)
    const userInfo = parseIdToken(idToken);
    if (!userInfo) {
      return NextResponse.json(
        { success: false, error: 'Invalid ID token format' },
        { status: 400 },
      );
    }

    console.log('🎯 [OAUTH-API] Processing OAuth tokens for user:', {
      email: userInfo.email,
      userId: userInfo.sub,
      name: userInfo.name,
    });

    // Create session token
    const sessionToken = await createSession({
      username: userInfo['cognito:username'] || userInfo.email,
      email: userInfo.email,
      userId: userInfo.sub,
    });

    // Create response first
    const response = NextResponse.json({
      success: true,
      redirectTo: '/',
    });

    // Set session cookie in the response with environment-aware settings
    const isProduction = process.env.NODE_ENV === 'production';
    response.cookies.set(SESSION_COOKIE_NAME, sessionToken, {
      httpOnly: true,
      secure: isProduction,
      sameSite: 'strict',
      path: '/',
      maxAge: 86_400,
      ...(isProduction && {
        domain: process.env.COOKIE_DOMAIN,
      }),
      ...(!isProduction && {
        domain: 'localhost',
      }),
    });

    // Store Cognito tokens in Redis with secure cookie fallback
    if (accessToken && idToken) {
      try {
        const sessionData = await verifySession(sessionToken);
        const sessionId = sessionData?.jti;

        if (sessionId) {
          const tokenData = {
            accessToken: accessToken.toString(),
            idToken: idToken.toString(),
          };

          // Store tokens using Redis-first approach with cookie fallback
          await setCognitoTokenCookies(tokenData, sessionId);
        }
      } catch (error) {
        console.warn('⚠️ [OAUTH-API] Failed to store tokens:', error);
        // Non-critical - continue without token storage
      }
    }

    console.log('🍪 [OAUTH-API] Session cookie set in response');
    console.log('🔐 [OAUTH-API] OAuth session created successfully with Redis-first token storage');

    return response;
  } catch (error) {
    console.error('❌ [OAUTH-API] Server error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
}

function parseIdToken(idToken: string) {
  try {
    const payload = idToken.split('.')[1];
    return JSON.parse(Buffer.from(payload, 'base64').toString());
  } catch (error) {
    console.error('❌ [OAUTH-API] Failed to parse ID token:', error);
    return;
  }
}
