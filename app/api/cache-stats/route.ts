import { NextResponse } from 'next/server';
import { getCacheStats } from '@/lib/external-roles';
import { isRedisHealthy, getRedisClient } from '@/lib/redis/redis';
/**
 * Cache monitoring API endpoint for 19k users
 * Provides real-time insights into Redis cache performance
 */
export async function GET() {
  const startTime = Date.now();

  try {
    // Verify authentication

    console.log('📊 [CACHE-STATS] Gathering cache statistics...');

    // Get Redis health status
    const redisHealthy = await isRedisHealthy();

    if (!redisHealthy) {
      return NextResponse.json({
        status: 'error',
        message: 'Redis is not healthy',
        data: {
          redis: { healthy: false },
          cache: undefined,
          recommendations: [
            'Check Redis connection',
            'Verify Redis server is running',
            'Check network connectivity',
          ],
        },
        processingTime: Date.now() - startTime + 'ms',
        timestamp: new Date().toISOString(),
      });
    }

    // Get detailed cache statistics
    const cacheStats = await getCacheStats();

    // Get Redis info (if available)
    let redisInfo;
    try {
      const redis = getRedisClient();

      // Get memory usage and other stats
      const info = await redis.info('memory');
      const dbsize = await redis.dbsize();

      redisInfo = {
        dbSize: dbsize,
        memoryInfo: (() => {
          const acc: Record<string, string> = {};
          const lines = info.split('\r\n');
          for (const line of lines) {
            if (line.includes(':')) {
              const [key, value] = line.split(':');
              acc[key] = value;
            }
          }
          return acc;
        })(),
      };
    } catch (error) {
      console.warn('⚠️ [CACHE-STATS] Could not get Redis info:', error);
    }

    // Calculate performance metrics
    const recommendations = [];

    if (cacheStats) {
      if (cacheStats.totalKeys > 5000) {
        recommendations.push('Consider implementing cache sharding for better performance');
      }
      if (cacheStats.totalKeys < 100) {
        recommendations.push('Cache usage is low - verify cache is being utilized');
      }
    }

    // Add org-specific cache analysis
    let orgCacheStats;
    try {
      const redis = getRedisClient();
      const orgRoleKeys = await redis.keys('orgroles:*');
      const legacyRoleKeys = await redis.keys('roles:*');

      orgCacheStats = {
        orgSpecificCache: {
          keys: orgRoleKeys.length,
          structure: 'user -> { orgId: { role, orgName, updated } }',
          benefits: [
            'Per-organization role tracking',
            'Selective cache invalidation',
            'Better audit trails',
            'Webhook-friendly updates',
          ],
        },
        legacyCache: {
          keys: legacyRoleKeys.length,
          structure: 'user -> [role1, role2]',
          limitations: [
            'No organization context',
            'All-or-nothing invalidation',
            'Limited audit capabilities',
          ],
        },
        optimization: {
          enabled: orgRoleKeys.length > 0,
          recommendation:
            orgRoleKeys.length === 0
              ? 'Enable org-specific caching for better role management'
              : 'Org-specific caching is active and optimized',
        },
      };
    } catch (error) {
      console.warn('⚠️ [CACHE-STATS] Could not analyze org cache structure:', error);
    }

    const totalDuration = Date.now() - startTime;
    console.log(`📊 [CACHE-STATS] Analysis completed in ${totalDuration}ms`);

    return NextResponse.json({
      status: 'success',
      data: {
        redis: {
          healthy: redisHealthy,
          info: redisInfo,
        },
        cache: cacheStats,
        orgCache: orgCacheStats,
        recommendations,
        performance: {
          analysisTime: totalDuration + 'ms',
        },
      },
      processingTime: totalDuration + 'ms',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ [CACHE-STATS] Failed to get cache statistics:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to retrieve cache statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime + 'ms',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
