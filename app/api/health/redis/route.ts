import { NextResponse } from 'next/server';
import {
  testRedisConnection,
  isRedisHealthy,
  getRedisStatus,
  getRedisStats,
} from '@/lib/redis/redis';

/**
 * Enhanced Redis health check endpoint
 * GET /api/health/redis
 *
 * Returns comprehensive Redis connectivity and performance information
 * Useful for monitoring, debugging, and alerting
 */
export async function GET() {
  try {
    // Perform comprehensive Redis test
    const connectionTest = await testRedisConnection();
    const isHealthy = await isRedisHealthy();
    const status = getRedisStatus();

    // Get detailed stats if Redis is available
    let stats;
    if (connectionTest.connected) {
      try {
        stats = await getRedisStats();
      } catch (statsError) {
        console.warn('⚠️ [HEALTH] Could not retrieve Redis stats:', statsError);
      }
    }

    const healthData = {
      timestamp: new Date().toISOString(),
      redis: {
        available: connectionTest.connected,
        healthy: isHealthy,
        status: status.status,
        connectionName: status.connectionName,
        latency: connectionTest.latency,
        operations: connectionTest.operations,
        serverInfo: connectionTest.serverInfo,
        error: connectionTest.error,
        stats,
      },
      environment: {
        redisUrl:
          process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL
            ? 'configured'
            : 'not configured',
        nodeEnv: process.env.NODE_ENV,
        hasAuth: !!(process.env.REDIS_PASSWORD || process.env.REDIS_USERNAME),
        tlsEnabled:
          process.env.NODE_ENV === 'production' &&
          !(process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL || '').includes('localhost'),
      },
    };

    // Return appropriate status codes
    const httpStatus = connectionTest.connected && isHealthy ? 200 : 503;

    return NextResponse.json(healthData, {
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('❌ [HEALTH] Redis health check error:', error);

    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        redis: {
          available: false,
          healthy: false,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        environment: {
          redisUrl:
            process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL
              ? 'configured'
              : 'not configured',
          nodeEnv: process.env.NODE_ENV,
          hasAuth: !!(process.env.REDIS_PASSWORD || process.env.REDIS_USERNAME),
        },
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json',
        },
      },
    );
  }
}
