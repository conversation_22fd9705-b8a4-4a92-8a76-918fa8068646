import { NextRequest, NextResponse } from 'next/server';
import { createSession, setSessionCookie, setCognitoTokenCookies } from '@/lib/session';

/**
 * POST /api/debug/test-login
 * Dev-only endpoint to seed an authenticated session for Cypress tests
 */
export async function POST(request: NextRequest) {
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not allowed in production' }, { status: 403 });
  }

  try {
    const body = await request.json().catch(() => ({}));
    const {
      email = '<EMAIL>',
      userId = '6229769a21fff74352121c46',
      username = 'rickyYoung',
      tokens,
    }: {
      email?: string;
      userId?: string;
      username?: string;
      tokens?: { accessToken?: string; idToken?: string };
    } = body || {};

    const sessionToken = await createSession({ email, userId, username });

    // Optionally store tokens so API calls that rely on them can proceed in tests
    if (tokens || process.env.USE_DUMMY_TEST_TOKENS !== 'false') {
      const makeBase64Url = (input: string) =>
        Buffer.from(input)
          .toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/g, '');

      const header = makeBase64Url(JSON.stringify({ alg: 'none', typ: 'JWT' }));
      const payload = makeBase64Url(
        JSON.stringify({
          email,
          sub: userId,
          'cognito:username': username,
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
        }),
      );
      const fakeJwt = `${header}.${payload}.signature`;

      await setCognitoTokenCookies(
        {
          accessToken: tokens?.accessToken || `test-access-${Date.now()}`,
          idToken: tokens?.idToken || fakeJwt,
        },
        sessionToken,
      );
    }

    await setSessionCookie(sessionToken);

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 },
    );
  }
}
