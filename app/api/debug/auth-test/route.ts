// NextRequest removed - not needed
import { Amplify } from 'aws-amplify';

/**
 * GET /api/debug/auth-test
 * Test Amplify configuration and token storage
 */
export async function GET() {
  const startTime = Date.now();

  console.log(`🧪 [DEBUG] Testing Amplify configuration...`);

  try {
    // Check if Amplify is configured
    const config = Amplify.getConfig();
    console.log(`📊 [DEBUG] Amplify config:`, {
      hasAuth: !!config.Auth,
      hasCognito: !!config.Auth?.Cognito,
      userPoolId: config.Auth?.Cognito?.userPoolId || 'missing',
      clientId: config.Auth?.Cognito?.userPoolClientId || 'missing',
      hasStorage: !!config.Storage,
    });

    // Check environment variables
    const envCheck = {
      userPoolId: !!process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID,
      clientId: !!process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID,
      userPoolIdValue:
        process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID?.slice(0, 10) + '...',
      clientIdValue: process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID?.slice(0, 10) + '...',
    };

    console.log(`🔧 [DEBUG] Environment variables:`, envCheck);

    const totalDuration = Date.now() - startTime;
    console.log(`✅ [DEBUG] Auth test completed in ${totalDuration}ms`);

    return Response.json({
      success: true,
      amplifyConfig: {
        hasAuth: !!config.Auth,
        hasCognito: !!config.Auth?.Cognito,
        userPoolId: config.Auth?.Cognito?.userPoolId || 'missing',
        clientId: config.Auth?.Cognito?.userPoolClientId || 'missing',
        hasStorage: !!config.Storage,
      },
      environment: envCheck,
      duration: totalDuration,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [DEBUG] Auth test error (${duration}ms):`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      duration,
    });
  }
}
