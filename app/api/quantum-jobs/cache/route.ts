import { NextRequest, NextResponse } from 'next/server';
import { invalidateQuantumJobsCache, clearAllQuantumJobsCache } from '@/lib/cache/quantum-jobs-cache';

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const qbraidDeviceId = searchParams.get('deviceId');
    const provider = searchParams.get('provider');
    const clearAll = searchParams.get('clearAll') === 'true';

    console.log('🗑️ [CACHE-CLEAR] Request params:', {
      qbraidDeviceId,
      provider,
      clearAll,
    });

    if (clearAll) {
      // Clear all quantum jobs cache
      const deletedCount = await clearAllQuantumJobsCache();
      console.log(`✅ [CACHE-CLEAR] Cleared all quantum jobs cache, ${deletedCount} entries removed`);
      return NextResponse.json({ 
        success: true, 
        message: `Cleared all quantum jobs cache, removed ${deletedCount} entries` 
      });
    }

    if (!qbraidDeviceId) {
      return NextResponse.json({ 
        error: 'Device ID is required when not clearing all cache' 
      }, { status: 400 });
    }

    // Clear cache for specific device
    await invalidateQuantumJobsCache(qbraidDeviceId, {
      provider: provider || undefined,
    });

    console.log(`✅ [CACHE-CLEAR] Cleared quantum jobs cache for device: ${qbraidDeviceId}`);
    return NextResponse.json({ 
      success: true, 
      message: `Cleared quantum jobs cache for device: ${qbraidDeviceId}` 
    });
  } catch (error: any) {
    console.error('❌ [CACHE-CLEAR] Error clearing cache:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to clear cache',
        success: false 
      },
      { status: 500 }
    );
  }
}