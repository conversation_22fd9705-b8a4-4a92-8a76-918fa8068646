import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import type { JobsRowProps } from '@/types/jobs';
import {
  getCachedQuantumJobs,
  cacheQuantumJobs,
  invalidateQuantumJobsCache,
} from '@/lib/cache/quantum-jobs-cache';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [QUANTUM-JOBS-BY-DEVICE] Endpoint hit');

    // Extract qbraidDeviceId from the URL path
    const qbraidDeviceId = request.nextUrl.pathname.split('/').pop();
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || null;
    const resultsPerPage = searchParams.get('resultsPerPage') || null;

    console.log('🔍 [QUANTUM-JOBS-BY-DEVICE] Request params:', {
      qbraidDeviceId,
      page,
      resultsPerPage,
    });

    if (!qbraidDeviceId) {
      console.log('❌ [QUANTUM-JOBS-BY-DEVICE] Device ID is required but not provided');
      return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
    }

    // Check cache first
    const cachedData = await getCachedQuantumJobs(qbraidDeviceId, {
      page: page ? Number.parseInt(page) : undefined,
      resultsPerPage: resultsPerPage
        ? resultsPerPage === 'all'
          ? 'all'
          : Number.parseInt(resultsPerPage)
        : undefined,
    });

    if (cachedData) {
      console.log('✅ [QUANTUM-JOBS-BY-DEVICE] Returning cached data for device:', qbraidDeviceId);
      return NextResponse.json(cachedData);
    }

    // Build params object for external API call
    const params: Record<string, string> = {};
    if (page) params.page = page;
    if (resultsPerPage) params.resultsPerPage = resultsPerPage;

    console.log('🔍 [QUANTUM-JOBS-BY-DEVICE] External API params:', params);

    // Call the external API with the device-specific endpoint
    console.log('🔄 [QUANTUM-JOBS-BY-DEVICE] Calling external API...');
    const response = await externalClient.get(`/quantum-jobs/${qbraidDeviceId}`, params);
    console.log('✅ [QUANTUM-JOBS-BY-DEVICE] External API response received:', {
      status: response.status,
      dataType: typeof response.data,
      hasData: !!response.data,
    });

    // Ensure response is an object
    let data: any = response.data;
    if (typeof data !== 'object' || data === null) {
      console.log('⚠️ [QUANTUM-JOBS-BY-DEVICE] Response data is not an object, using default');
      data = {
        jobsArray: [],
        total: 0,
        uniqueUsers: 0,
        qbraidDeviceId,
        deviceName: '',
        deviceProvider: '',
      };
    }

    // Handle empty results - return empty array instead of placeholder
    if (!data.jobsArray) {
      console.log('⚠️ [QUANTUM-JOBS-BY-DEVICE] No jobsArray in response, initializing empty array');
      data.jobsArray = [];
    }

    // Log when no jobs are found
    if (data.jobsArray.length === 0) {
      console.log('ℹ️ [QUANTUM-JOBS-BY-DEVICE] No jobs found for device:', qbraidDeviceId);
    }

    // Transform timestamps
    data.jobsArray = data.jobsArray.map((job: JobsRowProps) => {
      if (job.timeStamps?.createdAt && typeof job.timeStamps.createdAt === 'string') {
        job.timeStamps.createdAt = new Date(job.timeStamps.createdAt);
      }
      if (job.timeStamps?.endedAt && typeof job.timeStamps.endedAt === 'string') {
        job.timeStamps.endedAt = new Date(job.timeStamps.endedAt);
      }
      return job;
    });

    // Cache the data if jobs were found
    if (data.jobsArray && data.jobsArray.length > 0) {
      await cacheQuantumJobs(
        qbraidDeviceId,
        {
          ...data,
          cachedAt: new Date().toISOString(),
        },
        {
          page: page ? Number.parseInt(page) : undefined,
          resultsPerPage: resultsPerPage
            ? resultsPerPage === 'all'
              ? 'all'
              : Number.parseInt(resultsPerPage)
            : undefined,
        },
      );
    }

    console.log('✅ [QUANTUM-JOBS-BY-DEVICE] Returning data with', data.jobsArray.length, 'jobs');
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ [QUANTUM-JOBS-BY-DEVICE] Error fetching quantum jobs:', error);

    // Extract qbraidDeviceId from the URL path again for error response
    const errorQbraidDeviceId = request.nextUrl.pathname.split('/').pop();

    // Handle specific error cases
    if (error.status === 404) {
      return NextResponse.json({ error: 'Device not found' }, { status: 404 });
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch quantum jobs',
        jobsArray: [],
        total: 0,
        uniqueUsers: 0,
        qbraidDeviceId: errorQbraidDeviceId || '',
        deviceName: '',
        deviceProvider: '',
      },
      { status: error.status || 500 },
    );
  }
}
