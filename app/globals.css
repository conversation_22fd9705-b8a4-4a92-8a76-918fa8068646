@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Hide scrollbar utility */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  .animate-in {
    animation-duration: 0.3s;
    animation-fill-mode: both;
  }

  .fade-in {
    animation-name: fade-in;
  }

  .slide-in-from-top-2 {
    animation-name: slide-in-from-top-2;
  }

  .slide-in-from-right-2 {
    animation-name: slide-in-from-right-2;
  }

  .slide-in-from-bottom-2 {
    animation-name: slide-in-from-bottom-2;
  }

  .slide-in-from-left {
    animation-name: slide-in-from-left;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slide-in-from-top-2 {
    from {
      transform: translateY(-0.5rem);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-right-2 {
    from {
      transform: translateX(0.5rem);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-bottom-2 {
    from {
      transform: translateY(0.5rem);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-left {
    from {
      transform: translateX(-1rem);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Modal animations */
  .modal-fade-in {
    animation: modalFadeIn 0.3s ease-out;
  }

  .modal-scale-in {
    animation: modalScaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  @keyframes modalFadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }

  @keyframes modalScaleIn {
    from {
      transform: scale(0.95) translateY(10px);
      opacity: 0;
    }
    to {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
  }

  /* Smooth hover transitions */
  .hover-lift {
    transition: all 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px -5px rgba(139, 92, 246, 0.25);
  }

  /* Gradient text animation */
  .gradient-text {
    background: linear-gradient(to right, #8b5cf6, #a855f7, #8b5cf6);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Focus glow effect */
  .focus-glow {
    transition: all 0.3s ease;
  }

  .focus-glow:focus {
    box-shadow:
      0 0 0 3px rgba(139, 92, 246, 0.1),
      0 0 20px -5px rgba(139, 92, 246, 0.5);
  }

  /* Tab indicator animation */
  .tab-indicator {
    position: relative;
    overflow: hidden;
  }

  .tab-indicator::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, #8b5cf6, #a855f7);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
  }

  .tab-indicator[data-state='active']::after {
    transform: scaleX(1);
  }

  /* Shimmer effect for loading states */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }

  /* Pulse animation for icons */
  .pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulseSoft {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* Stagger animation for list items */
  .stagger-fade-in {
    opacity: 0;
    animation: staggerFadeIn 0.5s ease forwards;
  }

  .stagger-fade-in:nth-child(1) {
    animation-delay: 0ms;
  }
  .stagger-fade-in:nth-child(2) {
    animation-delay: 50ms;
  }
  .stagger-fade-in:nth-child(3) {
    animation-delay: 100ms;
  }
  .stagger-fade-in:nth-child(4) {
    animation-delay: 150ms;
  }

  @keyframes staggerFadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Blob animation for auth pages */
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
}

/* Enhanced scrollbar for modal content */
.modal-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.modal-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.modal-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 246, 0.5);
}

/* Glass morphism effect */
.glass {
  background: rgba(10, 10, 15, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Neon glow for active elements */
.neon-glow {
  box-shadow:
    0 0 20px rgba(139, 92, 246, 0.5),
    0 0 40px rgba(139, 92, 246, 0.3),
    0 0 60px rgba(139, 92, 246, 0.1);
}

/* Enhanced font rendering for better readability */
html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Improved text contrast classes */
.text-enhanced {
  color: #f1f5f9;
  font-weight: 500;
}

.text-enhanced-muted {
  color: #cbd5e1;
  font-weight: 500;
}

.text-enhanced-strong {
  color: #ffffff;
  font-weight: 600;
}

/* Better button text visibility */
.btn-enhanced {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Custom scrollbar styles */
.always-show-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #374151 #1f2937;
}

.always-show-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.always-show-scrollbar::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.always-show-scrollbar::-webkit-scrollbar-thumb {
  background: #374151;
  border-radius: 4px;
}

.always-show-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    /* Brand accent color (purple); lighter in light mode for better contrast */
    --brand: 269 70% 60%; /* hsl for #a855f7-ish */
    --brand-foreground: 0 0% 100%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    /* Light mode sidebar palette */
    --sidebar-background: 0 0% 98%; /* #fafafa – very light gray so subtle separation from white content */
    --sidebar-foreground: 240 5.3% 26.1%; /* slate-ish text */
    --sidebar-primary: 240 5.9% 10%; /* text/icon highlight inside sidebar */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 262 22% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    /* Brand accent stays more vivid in dark theme */
    --brand: 269 84% 58%; /* hsl for #8a2be2 */
    --brand-foreground: 0 0% 100%;
  }

  body {
    @apply text-foreground;
  }
  html,
  body {
    /* Use the theme background token rather than a hard-coded value so that light/dark & multi-brand themes work correctly */
    background-color: hsl(var(--background));
    overscroll-behavior-y: none;
  }
}

@layer utilities {
  .animate-dropdown {
    animation: dropdown-fade-in 0.1s ease;
  }
  /* Disable all on-load animate-in and slide/fade effects */
  .animate-in,
  .fade-in,
  .slide-in-from-bottom,
  .slide-in-from-left,
  .slide-in-from-right,
  .slide-in-from-top {
    animation: none !important;
  }
  @keyframes dropdown-fade-in {
    0% {
      opacity: 0;
      transform: scaleY(0.95) translateY(-8px);
    }
    100% {
      opacity: 1;
      transform: scaleY(1) translateY(0);
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
}

.always-show-scrollbar {
  scrollbar-width: auto; /* For Firefox */
  scrollbar-color: #888 #1a1a1a;

  /* For WebKit browsers */
}

.always-show-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.always-show-scrollbar::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.always-show-scrollbar::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  border: 2px solid #1a1a1a;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s infinite;
}
