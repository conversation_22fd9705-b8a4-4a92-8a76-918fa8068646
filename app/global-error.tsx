'use client';

import { useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, RefreshCw, Home, ArrowLeft, Bug, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log error to error reporting service
    console.error('Global error caught:', error);
  }, [error]);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#0a0a0f] via-[#0f0f0f] to-[#1a1a2e]">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-40 -top-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-red-500/10 to-orange-500/10 blur-3xl" />
        <div className="absolute -bottom-40 -left-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-purple-500/10 to-pink-500/10 blur-3xl delay-1000" />
        <div className="absolute left-1/2 top-1/2 size-96 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-gradient-to-br from-violet-500/5 to-fuchsia-500/5 blur-3xl delay-500" />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute size-1 animate-pulse rounded-full bg-white/20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Main error card */}
          <Card className="border-[#3b3b3b]/50 bg-gradient-to-br from-[#1a1a2e]/80 via-[#262131]/80 to-[#1a1a2e]/80 shadow-2xl backdrop-blur-xl">
            <CardContent className="p-8 md:p-12">
              {/* Header section */}
              <div className="mb-12 text-center">
                <div className="relative mb-6 inline-flex size-20 items-center justify-center">
                  <div className="absolute inset-0 animate-pulse rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 blur-xl" />
                  <div className="relative rounded-full border border-red-500/20 bg-gradient-to-br from-red-500/10 to-orange-500/10 p-5">
                    <AlertTriangle className="size-10 text-red-400" />
                  </div>
                </div>

                <h1 className="mb-4 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                  Something went wrong
                </h1>

                <p className="mx-auto max-w-2xl text-lg leading-relaxed text-gray-400">
                  We&apos;re sorry, but an unexpected error has occurred. Our team has been notified
                  and is working on a fix.
                </p>

                {/* Error digest for debugging */}
                {process.env.NODE_ENV === 'development' && error.digest && (
                  <div className="mt-4 inline-flex items-center gap-2 rounded-lg border border-white/10 bg-white/5 px-4 py-2">
                    <Bug className="size-4 text-red-400" />
                    <code className="text-sm text-gray-400">Error ID: {error.digest}</code>
                  </div>
                )}
              </div>

              {/* Error details in development */}
              {process.env.NODE_ENV === 'development' && (
                <Card className="mb-12 border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-red-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-orange-500/10 p-2">
                        <Bug className="size-5 text-orange-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Error Details</h3>
                      <Badge className="ml-auto border-orange-500/30 bg-orange-500/20 text-orange-300">
                        Development Mode
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <span className="text-sm text-gray-400">Message:</span>
                        <p className="mt-1 font-mono text-sm text-orange-300">{error.message}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">Stack:</span>
                        <pre className="mt-1 max-h-32 overflow-x-auto rounded bg-black/20 p-3 font-mono text-xs text-gray-300">
                          {error.stack}
                        </pre>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action suggestions */}
              <div className="mb-12 grid grid-cols-1 gap-6 md:grid-cols-2">
                <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-blue-500/10 p-2">
                        <RefreshCw className="size-5 text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Try Again</h3>
                    </div>
                    <p className="mb-4 text-gray-400">
                      Sometimes a simple refresh can resolve temporary issues.
                    </p>
                    <Button
                      onClick={reset}
                      className="w-full border-0 bg-gradient-to-r from-blue-600 to-cyan-600 text-white transition-all duration-300 hover:from-blue-700 hover:to-cyan-700"
                    >
                      <RefreshCw className="mr-2 size-4" />
                      Try Again
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-purple-500/10 p-2">
                        <Home className="size-5 text-purple-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Go Home</h3>
                    </div>
                    <p className="mb-4 text-gray-400">
                      Return to the dashboard and start fresh from a safe location.
                    </p>
                    <Button
                      asChild
                      className="w-full border-0 bg-gradient-to-r from-purple-600 to-pink-600 text-white transition-all duration-300 hover:from-purple-700 hover:to-pink-700"
                    >
                      <Link href="/">
                        <Home className="mr-2 size-4" />
                        Go to Dashboard
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Additional help */}
              <div className="mb-8 text-center">
                <p className="text-gray-400">
                  If this problem persists, please{' '}
                  <Link
                    href="/feedback"
                    className="text-blue-400 transition-colors hover:text-blue-300"
                  >
                    report this issue
                  </Link>{' '}
                  or contact our support team.
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Button
                  onClick={() => globalThis.location.reload()}
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                >
                  <RefreshCw className="mr-2 size-4 transition-transform group-hover:rotate-180" />
                  Reload Page
                </Button>

                <Button
                  onClick={() => globalThis.history.back()}
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                >
                  <ArrowLeft className="mr-2 size-4 transition-transform group-hover:-translate-x-1" />
                  Go Back
                </Button>

                <Button
                  asChild
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                >
                  <Link href="https://docs.qbraid.com" target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="mr-2 size-4" />
                    Documentation
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
