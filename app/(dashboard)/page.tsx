'use client';

import { SectionCards } from '@/components/dashboard/section-cards';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { AlertCircle, BarChart3, Activity, LayoutDashboard } from 'lucide-react';
import { ChartAreaInteractive } from '@/components/dashboard/chart-area-interactive';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { FilterPanel } from '@/components/dashboard/filter-panel';
import { QuantumJobMetrics } from '@/components/dashboard/quantum-job-metrics';
import { motion } from 'framer-motion';

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      {/* Enhanced Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        className="space-y-6 px-4 lg:px-6"
      >
        <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <motion.div
                initial={{ rotate: -180, scale: 0 }}
                animate={{ rotate: 0, scale: 1 }}
                transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                className="p-2 bg-gradient-to-br from-brand to-brand/80 rounded-lg"
              >
                <LayoutDashboard className="size-6 text-brand-foreground" />
              </motion.div>
              <motion.h1
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent"
              >
                Dashboard
              </motion.h1>
            </div>
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-lg text-muted-foreground max-w-2xl"
            >
              Monitor your quantum computing operations with comprehensive analytics and real-time
              insights
            </motion.p>
          </div>
        </div>
      </motion.div>

      {/* Basic dashboard access - all authenticated users can view */}
      <OrgPermissionGuard
        permission={[Permission.ViewDevices, Permission.ViewJobs, Permission.ViewEarnings]}
        fallback={
          <div className="flex items-center gap-3 rounded-lg border border-red-200 bg-red-50 p-6">
            <AlertCircle className="size-5 text-red-500" />
            <div>
              <h3 className="font-semibold text-red-800">Access Denied</h3>
              <p className="text-sm text-red-600">
                You need basic access permissions to view the dashboard.
              </p>
            </div>
          </div>
        }
      >
        <SectionCards />
      </OrgPermissionGuard>

      {/* Analytics and charts - requires view permissions */}
      <OrgPermissionGuard
        permission={[Permission.ViewDevices, Permission.ViewJobs]}
        fallback={
          <div className="flex items-center gap-3 rounded-lg border border-yellow-200 bg-yellow-50 p-6">
            <BarChart3 className="size-5 text-yellow-500" />
            <div>
              <h3 className="font-semibold text-yellow-800">Limited Access</h3>
              <p className="text-sm text-yellow-600">
                Device or job view permissions required to see analytics.
              </p>
            </div>
          </div>
        }
      >
        <div className="grid grid-cols-1 gap-4 px-4 lg:grid-cols-4 lg:px-6">
          {/* Main chart area - takes 3 columns on large screens */}
          <div className="lg:col-span-3">
            <ChartAreaInteractive />
          </div>

          {/* Filter panel - takes 1 column on large screens */}
          <div className="min-w-[300px] lg:col-span-1">
            <FilterPanel />
          </div>
        </div>

        {/* Quantum job metrics */}
        <div className="px-4 lg:px-6">
          <QuantumJobMetrics />
        </div>
      </OrgPermissionGuard>

      {/* Recent Activity - requires view permissions */}
      <OrgPermissionGuard
        permission={[Permission.ViewJobs, Permission.ViewTeam]}
        fallback={
          <div className="flex items-center gap-3 rounded-lg border border-blue-200 bg-blue-50 p-6">
            <Activity className="size-5 text-blue-500" />
            <div>
              <h3 className="font-semibold text-blue-800">Activity Access Required</h3>
              <p className="text-sm text-blue-600">
                Job view permissions required to see recent activity and jobs.
              </p>
            </div>
          </div>
        }
      >
        <div className="px-4 lg:px-6">
          <RecentActivity />
        </div>
      </OrgPermissionGuard>
    </div>
  );
}
