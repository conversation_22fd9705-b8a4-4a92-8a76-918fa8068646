'use client';

import { useState, useMemo, useCallback, useTransition, useEffect } from 'react';
import { toast } from 'sonner';
import {
  User,
  Building2,
  Settings,
  AlertCircle,
  CheckCircle,
  Edit3,
  Save,
  X,
  RefreshCw,
  UserPlus,
  Key,
  Book,
  Code,
} from 'lucide-react';
import Link from 'next/link';
import { useRemoveUser } from '@/hooks/use-api';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { LoaderTwo } from '@/components/ui/loader';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useUserProfile,
  useUpdateProfile,
  useUserOrganizations,
  useAcceptInvitation,
  useDeclineInvitation,
} from '@/hooks/use-api';
import type { UserProfile } from '@/types/user';
import { getRoleDisplayName } from '@/lib/permissions';
import { useOrgContext } from '@/components/org/org-context-provider';

// TypeScript interfaces
interface Organization {
  orgId: string;
  orgName: string;
  role: string;
  accepted: boolean;
  invited: boolean;
  description?: string;
  email?: string;
  credits?: number;
  devices?: unknown[];
  joinedAt?: string;
}

// Sidebar navigation items
const sidebarItems = [
  {
    id: 'profile',
    label: 'Personal Info',
    icon: User,
    description: 'Manage your personal details',
  },
  {
    id: 'organizations',
    label: 'Organizations',
    icon: Building2,
    description: 'Your organization memberships',
  },
  {
    id: 'invitations',
    label: 'Invitations',
    icon: UserPlus,
    description: 'Pending organization invites',
  },
];

export default function ProfilePage() {
  const [isPending, startTransition] = useTransition();
  const [activeSection, setActiveSection] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionDialogMessage, setActionDialogMessage] = useState('');
  const [actionDialogType, setActionDialogType] = useState<'success' | 'error'>('success');

  const {
    roles,
    permissions,
    loading: permissionsLoading,
    refreshPermissions,
  } = useOrgPermissions();

  // Fetch user profile data
  const {
    data: profile,
    isLoading: profileLoading,
    error: profileError,
    refetch: refetchProfile,
  } = useUserProfile();

  // Fetch user organizations
  const {
    data: orgsData,
    isLoading: orgsLoading,
    error: _orgsError,
    refetch: refetchOrganizations,
  } = useUserOrganizations(0, 50); // Fetch up to 50 organizations

  const { mutate: updateProfile } = useUpdateProfile();
  const { mutate: acceptInvitation } = useAcceptInvitation();
  const { mutate: declineInvitation } = useDeclineInvitation();
  const { mutate: removeUser } = useRemoveUser();
  const { switchOrganization } = useOrgContext();
  // Extract organizations from the response
  const userOrganizations = useMemo(() => {
    return orgsData?.organizations || [];
  }, [orgsData]);

  const [formData, setFormData] = useState<Partial<UserProfile>>({});

  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        personalInformation: { ...profile.personalInformation },
      });
    }
  }, [profile]);

  // Handle organizations error silently

  const handleInputChange = (field: keyof UserProfile, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handlePersonalInfoChange = (
    subfield: keyof NonNullable<UserProfile['personalInformation']>,
    value: any,
  ) => {
    setFormData((prev) => ({
      ...prev,
      personalInformation: {
        ...prev.personalInformation,
        [subfield]: value,
      },
    }));
  };
  const handleLeaveOrganization = useCallback(
    async (orgId: string, orgName: string) => {
      removeUser(
        {
          orgId: orgId,
          email: profile?.email || '',
          orgName: orgName,
          reason: 'Left organization',
        },
        {
          onSuccess: () => {
            setActionDialogMessage(`Successfully left ${orgName}`);
            setActionDialogType('success');
            refetchOrganizations();
            setActionDialogOpen(true);
          },
          onError: () => {
            setActionDialogMessage(`Failed to leave ${orgName}`);
            setActionDialogType('error');
            setActionDialogOpen(true);
          },
        },
      );
    },
    [profile?.email, removeUser, refetchOrganizations],
  );

  const handleSave = useCallback(async () => {
    startTransition(async () => {
      updateProfile(formData, {
        onSuccess: () => {
          setActionDialogMessage('Profile updated successfully');
          setActionDialogType('success');
          setIsEditing(false);
          refetchProfile();
          setActionDialogOpen(true);
        },
        onError: (_error: any) => {
          setActionDialogMessage('Failed to update profile');
          setActionDialogType('error');
          setActionDialogOpen(true);
        },
      });
    });
  }, [formData, updateProfile, refetchProfile, startTransition]);

  const handleCancel = useCallback(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        personalInformation: { ...profile.personalInformation },
      });
    }
    setIsEditing(false);
  }, [profile]);

  const refreshData = useCallback(() => {
    refreshPermissions();
    refetchProfile();
    refetchOrganizations();
    toast.success('Data refreshed successfully');
  }, [refreshPermissions, refetchProfile, refetchOrganizations]);

  // Invitation handlers
  const handleAcceptInvitation = useCallback(
    async (orgId: string, orgName: string) => {
      if (!profile?.email) {
        setActionDialogMessage('Profile email not available');
        setActionDialogType('error');
        setActionDialogOpen(true);
        return;
      }

      const userEmail = profile.email;
      acceptInvitation(
        { orgId: orgId, email: userEmail },
        {
          onSuccess: () => {
            setActionDialogMessage(`Successfully joined ${orgName}`);
            setActionDialogType('success');
            refreshPermissions();
            refetchOrganizations();
            
            // Switch to the newly accepted organization
            setTimeout(() => {
              switchOrganization(orgId).catch((error) => {
                console.warn('Failed to switch to newly accepted organization:', error);
              });
            }, 500); // Wait a bit for permissions to update
            
            setActionDialogOpen(true);
          },
          onError: () => {
            setActionDialogMessage(`Failed to join ${orgName}`);
            setActionDialogType('error');
            setActionDialogOpen(true);
          },
        },
      );
    },
    [profile?.email, acceptInvitation, refreshPermissions, refetchOrganizations],
  );

  const handleDeclineInvitation = useCallback(
    async (invitationId: string, orgName: string) => {
      if (!profile?.email) {
        setActionDialogMessage('Profile email not available');
        setActionDialogType('error');
        setActionDialogOpen(true);
        return;
      }

      const userEmail = profile.email;
      declineInvitation(
        { orgName, email: userEmail },
        {
          onSuccess: () => {
            setActionDialogMessage(`Declined invitation to ${orgName}`);
            setActionDialogType('success');
            refetchOrganizations();
            setActionDialogOpen(true);
          },
          onError: () => {
            setActionDialogMessage(`Failed to decline invitation`);
            setActionDialogType('error');
            setActionDialogOpen(true);
          },
        },
      );
    },
    [profile?.email, declineInvitation, refetchOrganizations],
  );

  const isLoading = profileLoading || permissionsLoading || orgsLoading;

  const stats = useMemo(
    () => ({
      totalOrganizations: userOrganizations.filter((org: Organization) => org.accepted).length,
      pendingInvitations: userOrganizations.filter(
        (org: Organization) => !org.accepted && org.invited,
      ).length,
      totalRoles: roles.length,
      activePermissions: permissions.length,
    }),
    [userOrganizations, roles.length, permissions.length],
  );

  return (
    <OrgPermissionGuard
      permission={Permission.ViewProfile}
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-background">
          <div className="max-w-md rounded-xl border border-gray-200 bg-white p-8 text-center shadow-2xl">
            <div className="mb-4 text-red-500">
              <AlertCircle className="mx-auto size-16" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-900">Access Denied</h2>
            <p className="mb-6 text-gray-600">
              You need profile view permissions to access this page.
            </p>
            <Link
              href="/"
              className="inline-flex items-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 text-white shadow-lg transition-all duration-200 hover:scale-105 hover:from-blue-700 hover:to-purple-700"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-background">
        {/* Profile Completion Banner */}
        {(!formData.firstName || !formData.lastName) && !profileLoading && (
          <div className="border-b border-orange-500/30 bg-gradient-to-r from-orange-600/20 to-yellow-600/20 px-6 py-3">
            <div className="mx-auto flex max-w-7xl items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertCircle className="size-5 text-orange-400" />
                <p className="text-sm font-medium text-orange-100">
                  Complete your profile to personalize your experience
                </p>
              </div>
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                className="bg-brand text-brand-foreground hover:bg-brand/90"
              >
                Complete Profile
              </Button>
            </div>
          </div>
        )}

        {/* Enhanced Header */}
        <div className="space-y-6">
          <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-gradient-to-br from-brand to-brand/80 p-2">
                  <User className="size-6 text-brand-foreground" />
                </div>
                <h1 className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent">
                  Profile
                </h1>
              </div>
              <p className="max-w-2xl text-lg text-muted-foreground">
                Manage your personal information, organization memberships, and account settings
              </p>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                onClick={refreshData}
                variant="outline"
                size="sm"
                className="border-sidebar-border bg-sidebar text-foreground hover:bg-muted"
                disabled={isPending || isLoading}
              >
                <RefreshCw
                  className={`mr-2 size-4 ${isPending || isLoading ? 'animate-spin' : ''}`}
                />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-12 gap-6">
            {/* Sidebar */}
            <div className="col-span-12 lg:col-span-3">
              <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-foreground">Account Settings</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <nav className="space-y-1">
                    {sidebarItems.map((item) => {
                      const Icon = item.icon;
                      const isActive = activeSection === item.id;
                      return (
                        <button
                          key={item.id}
                          onClick={() => setActiveSection(item.id)}
                          className={`
                            group w-full rounded-lg px-4 py-3 text-left transition-all duration-200
                            ${
                              isActive
                                ? 'border-l-4 border-brand bg-gradient-to-r from-brand/20 to-brand/30 text-brand'
                                : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground'
                            }
                          `}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon
                              className={`size-5 ${isActive ? 'text-brand' : 'text-muted-foreground group-hover:text-foreground'}`}
                            />
                            <div>
                              <div className="font-medium">{item.label}</div>
                              <div className="text-xs text-muted-foreground group-hover:text-foreground">
                                {item.description}
                              </div>
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </nav>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="mt-6 border-sidebar-border bg-sidebar backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-sm font-medium text-foreground">
                    Quick Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Organizations</span>
                    <Badge variant="secondary" className="bg-brand/20 text-brand">
                      {stats.totalOrganizations}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Pending Invites</span>
                    <Badge variant="secondary" className="bg-orange-500/20 text-orange-300">
                      {stats.pendingInvitations}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Active Roles</span>
                    <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-300">
                      {stats.totalRoles}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Permissions</span>
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      {stats.activePermissions}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main */}
            <div className="col-span-12 lg:col-span-9">
              {activeSection === 'profile' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center text-foreground">
                        <User className="mr-2 size-5 text-brand" />
                        Personal Information
                      </CardTitle>
                      <p className="mt-1 text-sm text-muted-foreground">
                        Manage your personal details and contact information
                      </p>
                    </div>
                    {isEditing ? (
                      <div className="flex space-x-2">
                        <Button
                          onClick={handleSave}
                          disabled={isPending}
                          className="bg-gradient-to-r from-emerald-600 to-green-600 text-white hover:from-emerald-700 hover:to-green-700"
                        >
                          <Save className="mr-2 size-4" />
                          {isPending ? 'Saving...' : 'Save'}
                        </Button>
                        <Button
                          onClick={handleCancel}
                          variant="outline"
                          className="border-sidebar-border text-muted-foreground hover:bg-muted"
                        >
                          <X className="mr-2 size-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        onClick={() => setIsEditing(true)}
                        className="bg-brand text-brand-foreground hover:bg-brand/90"
                      >
                        <Edit3 className="mr-2 size-4" />
                        Edit Profile
                      </Button>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {profileLoading ? (
                      <div className="flex items-center justify-center py-12">
                        <LoaderTwo />
                      </div>
                    ) : (profileError ? (
                      <div className="text-red-400">Error loading profile</div>
                    ) : (
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                          <Label htmlFor="firstName" className="text-foreground">
                            First Name
                          </Label>
                          {isEditing ? (
                            <Input
                              id="firstName"
                              value={formData.firstName || ''}
                              onChange={(e) => handleInputChange('firstName', e.target.value)}
                              className="mt-2 border-sidebar-border bg-sidebar text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-brand"
                              placeholder="Enter your first name"
                            />
                          ) : (
                            <div className="mt-2 text-foreground">
                              {formData.firstName || (
                                <span className="italic text-muted-foreground">
                                  Click Edit Profile to add
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        <div>
                          <Label htmlFor="lastName" className="text-foreground">
                            Last Name
                          </Label>
                          {isEditing ? (
                            <Input
                              id="lastName"
                              value={formData.lastName || ''}
                              onChange={(e) => handleInputChange('lastName', e.target.value)}
                              className="mt-2 border-sidebar-border bg-sidebar text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-brand"
                              placeholder="Enter your last name"
                            />
                          ) : (
                            <div className="mt-2 text-foreground">
                              {formData.lastName || (
                                <span className="italic text-muted-foreground">
                                  Click Edit Profile to add
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="email" className="text-foreground">
                            Email
                          </Label>
                          <div className="mt-2 text-foreground">
                            {profile?.email || 'Not provided'}
                          </div>
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="bio" className="text-foreground">
                            Bio
                          </Label>
                          {isEditing ? (
                            <Input
                              id="bio"
                              value={formData.personalInformation?.bio || ''}
                              onChange={(e) => handlePersonalInfoChange('bio', e.target.value)}
                              className="mt-2 border-sidebar-border bg-sidebar text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-brand"
                            />
                          ) : (
                            <div className="mt-2 text-foreground">
                              {formData.personalInformation?.bio || 'Not provided'}
                            </div>
                          )}
                        </div>
                        {/* Add more fields like linkedIn, githubUrl, etc. */}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Quantum Section */}
              {activeSection === 'quantum' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-foreground">
                      <Key className="mr-2 size-5 text-brand" />
                      Quantum Jobs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-muted" />
                    ) : (profile?.quantum?.quantumJobs?.length ? (
                      profile.quantum.quantumJobs.map((job, index) => (
                        <div key={index} className="border-b border-sidebar-border p-2">
                          {/* Display job details */}
                          Job {index + 1}
                        </div>
                      ))
                    ) : (
                      <div className="text-muted-foreground">No quantum jobs yet.</div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Learn Section */}
              {activeSection === 'learn' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-foreground">
                      <Book className="mr-2 size-5 text-brand" />
                      Learning Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-muted" />
                    ) : (
                      <>
                        <h4 className="mb-2 text-foreground">Registered Courses</h4>
                        {profile?.learn?.registeredCourses?.length ? (
                          profile.learn.registeredCourses.map((course, index) => (
                            <div key={index} className="border-b border-sidebar-border p-2">
                              Course ID: {course.$oid}
                            </div>
                          ))
                        ) : (
                          <div className="text-muted-foreground">No registered courses.</div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Lab Section */}
              {activeSection === 'lab' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-foreground">
                      <Code className="mr-2 size-5 text-brand" />
                      Lab Environments
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {profileLoading ? (
                      <Skeleton className="h-32 w-full bg-muted" />
                    ) : (
                      <>
                        <h4 className="mb-2 text-foreground">Environments</h4>
                        {profile?.lab?.environments?.length ? (
                          profile.lab.environments.map((env, index) => (
                            <div key={index} className="border-b border-sidebar-border p-2">
                              Environment {index + 1}
                            </div>
                          ))
                        ) : (
                          <div className="text-muted-foreground">No lab environments.</div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Organizations - use context */}
              {activeSection === 'organizations' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-foreground">
                      <Building2 className="mr-2 size-5 text-brand" />
                      Organizations
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Organizations you&apos;re a member of
                    </p>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="flex items-center justify-center py-12">
                        <LoaderTwo />
                      </div>
                    ) : (userOrganizations.filter((org: Organization) => org.accepted).length ===
                      0 ? (
                      <div className="py-12 text-center">
                        <Building2 className="mx-auto mb-4 size-12 text-muted-foreground" />
                        <h3 className="mb-2 font-medium text-foreground">No Organizations</h3>
                        <div className="text-sm text-muted-foreground">
                          You&apos;re not a member of any organizations yet.
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {userOrganizations
                          .filter((org: Organization) => org.accepted)
                          .map((org: Organization) => (
                            <div
                              key={org.orgId}
                              className="rounded-lg border border-sidebar-border bg-gradient-to-r from-sidebar to-sidebar/80 p-4 transition-all duration-200 hover:border-brand/50"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="flex size-12 items-center justify-center rounded-lg bg-gradient-to-r from-brand to-brand/70">
                                    <span className="text-lg font-semibold text-brand-foreground">
                                      {org.orgName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <h3 className="font-medium text-foreground">{org.orgName}</h3>
                                    <div className="mt-1 flex items-center space-x-2">
                                      <Badge className="border-brand/30 bg-brand/20 text-brand">
                                        {org.role}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-muted-foreground hover:text-foreground"
                                      >
                                        <Settings className="size-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="border-sidebar-border bg-sidebar">
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleLeaveOrganization(org.orgId, org.orgName)
                                        }
                                        className="text-destructive hover:bg-muted hover:text-destructive-foreground"
                                      >
                                        Leave Organization
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Invitations Section */}
              {activeSection === 'invitations' && (
                <Card className="border-sidebar-border bg-sidebar backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-foreground">
                      <UserPlus className="mr-2 size-5 text-brand" />
                      Pending Invitations
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Organization invitations waiting for your response
                    </p>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const pendingInvitations = userOrganizations.filter(
                        (org: Organization) => !org.accepted && org.invited,
                      );

                      if (pendingInvitations.length === 0) {
                        return (
                          <div className="py-12 text-center">
                            <UserPlus className="mx-auto mb-4 size-12 text-muted-foreground" />
                            <h3 className="mb-2 font-medium text-foreground">
                              No Pending Invitations
                            </h3>
                            <div className="text-sm text-muted-foreground">
                              You&apos;re all caught up! No organization invitations at the moment.
                            </div>
                          </div>
                        );
                      }

                      return (
                        <div className="space-y-4">
                          {pendingInvitations.map((org: Organization) => (
                            <div
                              key={org.orgId}
                              className="rounded-lg border border-orange-500/30 bg-gradient-to-r from-orange-500/10 to-yellow-500/10 p-4"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="flex size-12 items-center justify-center rounded-lg bg-gradient-to-r from-orange-500 to-yellow-600">
                                    <span className="text-lg font-semibold text-brand-foreground">
                                      {org.orgName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <h3 className="font-medium text-foreground">{org.orgName}</h3>
                                    <div className="mt-1 flex items-center space-x-4">
                                      <Badge className="border-orange-500/30 bg-orange-500/20 text-orange-300">
                                        {getRoleDisplayName(org.role)}
                                      </Badge>
                                      {org.description && (
                                        <span className="text-sm text-muted-foreground">
                                          {org.description}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    onClick={() => handleAcceptInvitation(org.orgId, org.orgName)}
                                    disabled={isPending}
                                    className="bg-gradient-to-r from-emerald-600 to-green-600 text-white hover:from-emerald-700 hover:to-green-700"
                                  >
                                    <CheckCircle className="mr-2 size-4" />
                                    Accept
                                  </Button>
                                  <Button
                                    onClick={() => handleDeclineInvitation(org.orgId, org.orgName)}
                                    disabled={isPending}
                                    variant="outline"
                                    className="border-sidebar-border text-muted-foreground hover:bg-muted"
                                  >
                                    <X className="mr-2 size-4" />
                                    Decline
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>

        {/* Dialog */}
        <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
          <DialogContent className="border-sidebar-border bg-sidebar">
            <DialogTitle className="flex items-center text-foreground">
              {actionDialogType === 'success' ? (
                <CheckCircle className="mr-2 size-5 text-emerald-400" />
              ) : (
                <AlertCircle className="mr-2 size-5 text-red-400" />
              )}
              {actionDialogType === 'success' ? 'Success' : 'Error'}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {actionDialogMessage}
            </DialogDescription>
            <div className="mt-4 flex justify-end">
              <Button
                onClick={() => setActionDialogOpen(false)}
                className="bg-brand text-brand-foreground hover:bg-brand/90"
              >
                OK
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </OrgPermissionGuard>
  );
}

export const dynamic = 'force-dynamic';
