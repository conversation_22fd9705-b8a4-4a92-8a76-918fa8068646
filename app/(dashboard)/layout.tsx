'use client';

import React from 'react';
import { RoleLoader } from '@/components/auth/role-loader';
import { OrgContextProvider } from '@/components/org/org-context-provider';
import { OrgCheck } from '@/components/auth/org-check';
import { AppSidebar } from '@/components/nav/app-sidebar';
import { SiteHeader } from '@/components/nav/site-header';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { Toaster } from '@/components/ui/sonner';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { useUserChangeDetection } from '@/hooks/use-auth';
import { FeedbackButton } from '@/components/ui/bug-report-button';
import { ErrorTracker } from '@/components/ui/error-tracker';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  // Detect user changes and clear cache when switching users
  useUserChangeDetection();

  return (
    <OrgCheck>
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error('OrgContextProvider error:', error, errorInfo);
        }}
      >
        <OrgContextProvider>
          <SidebarProvider>
            <Toaster />
            {/* Collapsible sidebar on the left */}
            <AppSidebar variant="inset" />

            {/* Main frame: header + routed page content */}
            <SidebarInset className="flex min-h-screen flex-col bg-background">
              <SiteHeader />
              <RoleLoader>
                <div className="flex-1 p-4 md:p-6 lg:p-8">{children}</div>
              </RoleLoader>
              <ErrorTracker>
                <FeedbackButton />
              </ErrorTracker>
            </SidebarInset>
          </SidebarProvider>
        </OrgContextProvider>
      </ErrorBoundary>
    </OrgCheck>
  );
}
