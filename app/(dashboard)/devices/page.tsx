'use client';

import { useState, useEffect, useMemo } from 'react';
import { Loader2, Cpu } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useOrgDevices } from '@/hooks/use-api';
import { DeviceOverviewCard } from '@/components/devices/device-overview-card';
import { DevicesSection } from '@/components/devices/devices-section';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useSearchParams } from 'next/navigation';
import { useOrgContext } from '@/components/org/org-context-provider';
import { motion } from 'framer-motion';

export default function DevicesPage() {
  const { currentOrgId } = useOrgContext();
  const { data: allDevices, isLoading, refetch } = useOrgDevices(currentOrgId || '');

  // Filter out pending add/edit requests - only show verified devices
  const devices = useMemo(() => {
    return allDevices?.filter((device) => device.verified !== false) || [];
  }, [allDevices]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | undefined>();
  const searchParams = useSearchParams();
  const queryId = searchParams.get('id');
  const router = useRouter();

  useEffect(() => {
    if (queryId) {
      setSelectedDeviceId(queryId);
    } else if (!selectedDeviceId && devices && devices.length > 0) {
      setSelectedDeviceId(devices[0].qrn);
    }
  }, [devices, queryId, selectedDeviceId]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="size-8 animate-spin text-brand" />
      </div>
    );
  }

  if (!currentOrgId) {
    return (
      <div className="flex min-h-screen items-center justify-center text-muted-foreground">
        No organization selected
      </div>
    );
  }

  if (!devices || devices.length === 0) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center gap-6 text-muted-foreground">
        <div>No devices found</div>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Visit the Device Management page to add new devices.
          </p>
        </div>
      </div>
    );
  }

  const selectedDevice = devices.find((d) => d.qrn === selectedDeviceId) || devices[0];

  return (
    <OrgPermissionGuard
      permission={Permission.ViewDevices}
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="max-w-md rounded-lg border border-red-200 bg-red-50 p-8 text-center">
            <h2 className="mb-2 text-xl font-semibold text-red-800">Access Denied</h2>
            <p className="mb-4 text-red-600">
              You need device view permissions to access this page.
            </p>
            <Link href="/" className="text-blue-600 underline hover:text-blue-800">
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <main data-testid="devices-page" className="w-full mx-auto p-6">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="space-y-6 mb-8"
        >
          <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ rotate: -180, scale: 0 }}
                  animate={{ rotate: 0, scale: 1 }}
                  transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                  className="p-2 bg-gradient-to-br from-brand to-brand/80 rounded-lg"
                >
                  <Cpu className="size-6 text-brand-foreground" />
                </motion.div>
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent"
                >
                  Quantum Devices
                </motion.h1>
              </div>
              <motion.p
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-lg text-muted-foreground max-w-2xl"
              >
                Monitor and manage your quantum computing devices with real-time insights and
                performance metrics
              </motion.p>
            </div>
          </div>
        </motion.div>

        {/* Overview cards */}
        <div
          className="mb-4 grid auto-rows-auto grid-cols-4 items-stretch gap-4"
          data-testid="device-cards-grid"
        >
          {devices.map((device: any) => (
            <div
              key={device.qrn}
              onClick={() => {
                setSelectedDeviceId(device.qrn);
                router.push(`/devices?id=${device.qrn}`);
              }}
              className={`h-full cursor-pointer transition-all duration-300 ${
                selectedDeviceId === device.qrn
                  ? 'scale-[1.04]'
                  : 'opacity-80 hover:-translate-y-1 hover:opacity-100'
              }`}
              data-testid={`device-card-${device.qrn}`}
            >
              <DeviceOverviewCard
                {...device}
                selected={selectedDeviceId === device.qrn}
                onDeleteRequest={refetch}
                showDropdownMenu={false}
              />
            </div>
          ))}
        </div>

        {/* Device details section */}
        <div className="mt-8">
          <DevicesSection device={selectedDevice} />
        </div>
      </main>
    </OrgPermissionGuard>
  );
}
