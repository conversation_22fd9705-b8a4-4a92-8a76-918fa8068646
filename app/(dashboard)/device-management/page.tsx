'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Eye } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useOrgProvider } from '@/hooks/use-providers';
import { AvailableDevicesTab } from '@/components/devices/available-devices-tab';
import { MyDevicesTab } from '@/components/devices/my-devices-tab';
import { motion, AnimatePresence } from 'framer-motion';

export default function DeviceManagementPage() {
  const { currentOrgId, isLoading: isOrgLoading } = useOrgContext();
  const { data: orgProvider } = useOrgProvider(currentOrgId || '');
  const [activeTab, setActiveTab] = useState('devices');

  if (isOrgLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="space-y-4 text-center"
        >
          <div className="mx-auto size-12 animate-spin rounded-full border-b-2 border-brand"></div>
          <p className="text-muted-foreground">Loading device management...</p>
        </motion.div>
      </div>
    );
  }

  // Determine if user is admin (for now, checking if they have a provider)
  const isAdmin = !!orgProvider;

  return (
    <main className="mx-auto space-y-8 p-2">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        className="space-y-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              initial={{ rotate: -180, scale: 0 }}
              animate={{ rotate: 0, scale: 1 }}
              transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
              className="rounded-lg bg-gradient-to-br from-brand to-brand/80 p-2"
            >
              <Settings className="size-6 text-brand-foreground" />
            </motion.div>
            <div>
              <motion.h1
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent"
              >
                Device Management
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-lg text-muted-foreground"
              >
                Manage quantum device access and submissions
              </motion.p>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="flex items-center gap-4"
          >
            {isAdmin && (
              <Badge variant="default">
                <Shield className="mr-1 size-3" />
                Admin Access
              </Badge>
            )}
          </motion.div>
        </div>
      </motion.div>

      {/* Main content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <TabsList
            className={`grid w-full grid-cols-2 relative overflow-hidden bg-muted/50 p-1 rounded-lg`}
          >
            <TabsTrigger
              value="devices"
              className="group relative z-10 transition-all duration-300 hover:bg-muted data-[state=active]:bg-brand data-[state=active]:text-brand-foreground data-[state=active]:shadow-md"
            >
              <Settings className="size-4 mr-2 transition-transform duration-300 group-data-[state=active]:scale-110" />
              My Devices & Access
            </TabsTrigger>
            <TabsTrigger
              value="available"
              className="group relative z-10 transition-all duration-300 hover:bg-muted data-[state=active]:bg-brand data-[state=active]:text-brand-foreground data-[state=active]:shadow-md"
            >
              <Eye className="mr-2 size-4 transition-transform duration-300 group-data-[state=active]:scale-110" />
              Available Devices
            </TabsTrigger>
          </TabsList>
        </motion.div>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{
              opacity: 0,
              x: activeTab === 'available' ? -20 : 20,
            }}
            animate={{ opacity: 1, x: 0 }}
            exit={{
              opacity: 0,
              x: activeTab === 'available' ? 20 : -20,
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {activeTab === 'available' && (
              <TabsContent value="available" className="mt-0">
                <AvailableDevicesTab />
              </TabsContent>
            )}
            {activeTab === 'devices' && (
              <TabsContent value="devices" className="mt-0">
                <MyDevicesTab onSwitchToAvailable={() => setActiveTab('available')} />
              </TabsContent>
            )}
          </motion.div>
        </AnimatePresence>
      </Tabs>
    </main>
  );
}
