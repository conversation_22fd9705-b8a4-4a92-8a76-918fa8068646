'use client';

import { Plus, Building2, Search, Globe, CheckCircle, Edit, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ProviderResponseType } from '@/types/provider';
import { ProviderFormManager } from '@/components/providers/provider-form-manager';
import { useProviders, useOrgProvider } from '@/hooks/use-provider-management';
import { useOrgContext } from '@/components/org/org-context-provider';
import { queryClient } from '@/lib/query-client';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import Image from 'next/image';
import Link from 'next/link';

// Component for provider onboarding flow
function ProviderOnboardingFlow({ dataTestId }: { dataTestId?: string }) {
  const { isLoading } = useProviders();

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="flex items-center justify-center py-16"
        data-testid={dataTestId}
      >
        <div className="size-8 animate-spin rounded-full border-b-2 border-brand"></div>
        <span className="ml-3 text-muted-foreground">Loading providers...</span>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mx-auto max-w-7xl space-y-6"
    >
      <Card className="border-border bg-gradient-to-br from-background via-background to-background/80 py-12 text-center shadow-lg transition-all duration-300 hover:shadow-xl">
        <CardContent className="space-y-6">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
            className="mx-auto flex size-16 items-center justify-center rounded-xl bg-gradient-to-br from-brand to-brand/80 shadow-lg"
          >
            <Building2 className="size-8 text-brand-foreground" />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-3"
          >
            <CardTitle className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-3xl font-bold text-transparent">
              Setup Your Provider
            </CardTitle>
            <CardDescription className="mx-auto max-w-2xl text-lg text-muted-foreground">
              Connect with an existing provider or register your own quantum computing
              infrastructure
            </CardDescription>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="pt-4"
          >
            <OrgPermissionGuard
              permission={Permission.ManageProviders}
              fallback={
                <Button disabled variant="outline" className="px-6 py-3 opacity-50">
                  <Plus className="mr-2 size-4" />
                  Create Provider
                </Button>
              }
            >
              <ProviderFormManager
                onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['provider-status'] });
                }}
                trigger={
                  <Button className="bg-brand px-6 py-3 font-semibold text-brand-foreground shadow-lg transition-all duration-300 hover:scale-105 hover:bg-brand/90">
                    <Plus className="mr-2 size-4" />
                    Create Provider
                  </Button>
                }
              />
            </OrgPermissionGuard>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Component for owned provider management
function OwnedProviderManagement({ provider }: { provider: ProviderResponseType }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mx-auto max-w-7xl space-y-6"
    >
      {/* Provider Overview Card */}
      <Card className="border-border bg-gradient-to-br from-background via-background to-background/80 shadow-lg transition-all duration-300 hover:shadow-xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                className="relative"
              >
                <div className="flex size-16 items-center justify-center rounded-xl bg-gradient-to-br from-brand to-brand/80 shadow-lg">
                  {provider.logoUrl || provider.logoUrlDark ? (
                    <div className="relative size-12 overflow-hidden rounded-lg">
                      {/* Light theme logo */}
                      {provider.logoUrl && (
                        <Image
                          src={provider.logoUrl}
                          alt={`${provider.provider} logo`}
                          fill
                          className="object-contain dark:hidden"
                        />
                      )}
                      {/* Dark theme logo or fallback to light */}
                      <Image
                        src={provider.logoUrlDark || provider.logoUrl}
                        alt={`${provider.provider} logo${provider.logoUrlDark ? ' (dark)' : ''}`}
                        fill
                        className="hidden object-contain dark:block"
                      />
                    </div>
                  ) : (
                    <Building2 className="size-8 text-brand-foreground" />
                  )}
                </div>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                  className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-green-500 shadow-lg"
                >
                  <CheckCircle className="size-3 text-white" />
                </motion.div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="flex items-center gap-3">
                  <CardTitle className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-3xl font-bold text-transparent">
                    {provider.provider}
                  </CardTitle>
                  <Badge
                    variant="secondary"
                    className="border-brand/20 bg-brand/10 font-semibold text-brand"
                  >
                    Owner
                  </Badge>
                </div>
                <CardDescription className="mt-2 text-base">
                  {provider.providerDescription || 'Quantum hardware provider'}
                </CardDescription>
                {provider.about && (
                  <div className="mt-3 flex items-center gap-2">
                    <ExternalLink className="size-4 text-brand" />
                    <a
                      href={provider.about}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-brand underline underline-offset-2 hover:text-brand/80"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
              </motion.div>
            </div>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              className="flex items-center gap-3"
            >
              <Badge
                variant="outline"
                className="border-green-200 bg-green-50 px-3 py-1 text-green-600 dark:bg-green-950/20"
              >
                <CheckCircle className="mr-1 size-3" />
                Active
              </Badge>
              <Badge
                variant="outline"
                className="border-blue-200 bg-blue-50 px-3 py-1 text-blue-600 dark:bg-blue-950/20"
              >
                <Globe className="mr-1 size-3" />
                {provider.status === 'public' ? 'Public' : 'Private'}
              </Badge>
            </motion.div>
          </div>
        </CardHeader>
        <CardContent>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="flex gap-3"
          >
            <OrgPermissionGuard
              permission={Permission.ManageProviders}
              fallback={
                <Button disabled variant="outline" className="opacity-50">
                  <Edit className="mr-2 size-4" />
                  Edit Details
                </Button>
              }
            >
              <ProviderFormManager
                onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['provider-status'] });
                }}
                trigger={
                  <Button className="bg-brand font-semibold text-brand-foreground shadow-lg hover:bg-brand/90">
                    <Edit className="mr-2 size-4" />
                    Edit Details
                  </Button>
                }
              />
            </OrgPermissionGuard>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default function ProvidersPage() {
  const { currentOrgId, isLoading: isOrgLoading } = useOrgContext();

  const { data: orgProvider, isLoading: orgProviderLoading } = useOrgProvider(currentOrgId || '');

  // Loading states
  if (isOrgLoading || orgProviderLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="p-8 text-center"
        >
          <div className="mx-auto mb-4 size-16 animate-spin rounded-full border-b-2 border-brand"></div>
          <h2 className="mb-2 text-xl font-semibold text-foreground">Loading Provider...</h2>
          <p className="text-muted-foreground">Please wait while we load your provider data.</p>
        </motion.div>
      </div>
    );
  }

  // Main routing logic based on provider status
  return (
    <OrgPermissionGuard
      permission={Permission.ManageProviders}
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-background">
          <div className="max-w-md rounded-xl border border-border bg-card p-8 text-center shadow-2xl">
            <div className="mb-4 text-red-500">
              <Building2 className="mx-auto size-16" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-foreground">Access Denied</h2>
            <p className="mb-6 text-muted-foreground">
              You need provider management permissions to access provider settings.
            </p>
            <Link
              href="/"
              className="inline-flex items-center gap-2 rounded-lg bg-brand px-6 py-3 text-brand-foreground shadow-lg transition-all duration-200 hover:scale-105 hover:bg-brand/90"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <div className="min-h-screen overflow-x-auto bg-background">
        <main className="mx-auto space-y-8 p-2">
          {/* Enhanced Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
            className="space-y-6"
          >
            <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <motion.div
                    initial={{ rotate: -180, scale: 0 }}
                    animate={{ rotate: 0, scale: 1 }}
                    transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                    className="rounded-lg bg-gradient-to-br from-brand to-brand/80 p-2"
                  >
                    <Building2 className="size-6 text-brand-foreground" />
                  </motion.div>
                  <motion.h1
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent"
                  >
                    Provider Management
                  </motion.h1>
                </div>
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="max-w-2xl text-lg text-muted-foreground"
                >
                  {orgProvider
                    ? 'Manage your quantum hardware provider with powerful infrastructure tools'
                    : 'Set up your hardware provider to get started with quantum computing'}
                </motion.p>
              </div>
            </div>
          </motion.div>

          {/* Route based on provider status */}
          <AnimatePresence mode="wait">
            {orgProvider ? (
              orgProvider ? (
                <motion.div
                  key="management"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.3 }}
                >
                  <OwnedProviderManagement provider={orgProvider} />
                </motion.div>
              ) : (
                <motion.div
                  key="error"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="py-12 text-center">
                    <CardContent className="space-y-4">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, type: 'spring' }}
                        className="mx-auto flex size-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/30"
                      >
                        <Search className="size-6 text-red-600 dark:text-red-400" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-red-700 dark:text-red-300">
                          Provider Error
                        </CardTitle>
                        <CardDescription className="mt-1">
                          Unable to load provider information. Please try refreshing.
                        </CardDescription>
                      </div>
                      <Button variant="outline" onClick={() => globalThis.location.reload()}>
                        Refresh Page
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            ) : (
              <motion.div
                key="onboarding"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
              >
                <ProviderOnboardingFlow />
              </motion.div>
            )}
          </AnimatePresence>
        </main>
      </div>
    </OrgPermissionGuard>
  );
}
