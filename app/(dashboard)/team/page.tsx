'use client';

import { UserPlus, Users, Shield, Activity, RefreshCw, CheckCircle } from 'lucide-react';
import { useState, useMemo, useCallback, useTransition } from 'react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

import { InviteModal } from '@/components/team/invite-modal';
import { RemoveUserButton } from '@/components/team/remove-user-button';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

import {
  useOrgUsers,
  useInviteUser,
  useUpdateUserRole,
  useRemoveUser,
  useReinviteUser,
  useCancelInvite,
} from '@/hooks/use-api';
import { useActivityLogs } from '@/hooks/use-activity-logs';
import type { TeamMember } from '@/types/team';

import { usePagination } from '@/hooks/use-pagination';

import { EnhancedTeamTable } from '@/components/team/enhanced-team-table';
import { RolesTab } from '@/components/team/roles-tab';
import { ActivityLogTab } from '@/components/team/activity-log-tab';
import { PageHeader } from '@/components/ui/page-header';
import { StatsCard } from '@/components/ui/stats-card';
import { LoadingState } from '@/components/ui/loading-state';
import { AccessDenied } from '@/components/ui/access-denied';
import { GradientButton } from '@/components/ui/gradient-button';

// Constants moved outside component to prevent recreation
import {
  ALL_ROLES,
  ROLE_ICONS,
  ROLE_GRADIENTS,
  ROLE_BORDERS,
  ROLE_SELECTED_BG,
} from '@/lib/constant';
import {
  externalRoleToPermissions,
  getRoleDescription,
  getRoleDisplayName,
} from '@/lib/permissions';

const ROLES = ALL_ROLES.map((role) => ({
  name: getRoleDisplayName(role),
  description: getRoleDescription(role),
  icon: ROLE_ICONS[role] || '👤',
  gradient: ROLE_GRADIENTS[role] || 'from-gray-500/20 to-slate-500/20',
  border: ROLE_BORDERS[role] || 'border-gray-500/30',
  selectedBg: ROLE_SELECTED_BG[role] || 'bg-gray-500/10',
  permissions: externalRoleToPermissions[role] || [],
}));

const ACTIONS = [
  'Add New Device',
  'Change Device Info',
  'Delete Device',
  'Invite User',
  'Remove User',
  'Change User Role',
  'Reinvite User',
  'Cancel Invite',
  'View Activity Log',
  'View Earnings',
  'Manage Earnings',
  'View Jobs',
  'Manage Jobs',
  'View Providers',
  'Manage Providers',
  'View Devices',
  'Manage Devices',
  'View Profile',
  'Edit Profile',
  'View Team',
] as const;

// Custom hooks for better separation of concerns
const useTeamStats = (users: TeamMember[], totalUsers: number) => {
  return useMemo(
    () => ({
      totalUsers,
      activeUsers: users.filter((u) => u.status === 'Active').length,
      pendingInvites: users.filter((u) => u.status === 'Invited').length,
      totalRoles: ROLES.filter((role) =>
        users.some((user) => user.role.toLowerCase() === role.name.toLowerCase()),
      ).length,
    }),
    [users, totalUsers],
  );
};

const useRolesWithMembers = (users: TeamMember[]) => {
  return useMemo(() => {
    return ROLES.map((role) => {
      // More flexible role matching
      const roleMembers = users.filter((member) => {
        const memberRole = member.role.toLowerCase();
        const roleName = role.name.toLowerCase();

        // Direct match
        if (memberRole === roleName) return true;

        // Handle variations like "organization owner" vs "owner"
        if (roleName.includes('owner') && memberRole.includes('owner')) return true;
        if (roleName.includes('admin') && memberRole.includes('admin')) return true;
        if (roleName.includes('manager') && memberRole.includes('manager')) return true;
        if (roleName.includes('viewer') && memberRole.includes('viewer')) return true;

        return false;
      });

      const roleKey = role.name.toLowerCase();
      const result = {
        ...role,
        color: ROLE_GRADIENTS[roleKey] || 'from-gray-500/20 to-slate-500/20',
        border: ROLE_BORDERS[roleKey] || 'border-gray-500/30',
        icon: ROLE_ICONS[roleKey] || '👤',
        members: roleMembers.map((member) => ({
          name: member.name,
          avatar: member.avatar,
          email: member.email,
        })),
        count: roleMembers.length,
      };

      return result;
    });
  }, [users]);
};

// Optimized ChangeRoleModal component
const ChangeRoleModal = ({
  user,
  onClose,
  onChangeRole,
  isUpdating,
}: {
  user: TeamMember | null;
  onClose: () => void;
  onChangeRole: (user: TeamMember, role: string) => Promise<void>;
  isUpdating: boolean;
}) => {
  if (!user) return null;

  return (
    <Dialog open={!!user} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl border-border bg-gradient-to-br from-background via-background to-background/80">
        <div className="space-y-6">
          <div className="space-y-2 text-center">
            <DialogTitle className="text-2xl font-semibold text-foreground">
              Change User Role
            </DialogTitle>
            <p className="text-muted-foreground">
              Update permissions for{' '}
              <span className="font-medium text-foreground">{user.email}</span>
            </p>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {ROLES.map((role) => {
              const isSelected = user.role.toLowerCase() === role.name.toLowerCase();

              return (
                <button 
                  data-testid={`change-role-btn-${user.email}`}
                  key={role.name}
                  disabled={isUpdating}
                  onClick={() => onChangeRole(user, role.name.toLowerCase())}
                  className={`
                    group relative rounded-xl border-2 p-4 text-left transition-all duration-200
                    hover:scale-[1.02] hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50
                    ${
                      isSelected
                        ? `${role.border} ${role.selectedBg} shadow-lg`
                        : 'border-border/50 bg-card/30 hover:border-border hover:bg-card/50'
                    }
                  `}
                >
                  <div
                    className={`absolute inset-0 rounded-xl bg-gradient-to-br ${role.gradient} opacity-0 transition-opacity duration-200 group-hover:opacity-100`}
                  />

                  <div className="relative space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{role.icon}</span>
                      <div>
                        <h3 className="font-semibold text-foreground">{role.name}</h3>
                        {isSelected && (
                          <span className="flex items-center gap-1 text-xs font-medium text-green-400">
                            <CheckCircle className="size-3" />
                            Current Role
                          </span>
                        )}
                      </div>
                    </div>

                    <p className="text-sm leading-relaxed text-muted-foreground">
                      {role.description}
                    </p>

                    <div className="flex flex-wrap gap-1">
                      {role.permissions.slice(0, 3).map((permission: string, idx: number) => (
                        <span
                          key={idx}
                          className="rounded-md border border-border/30 bg-background/50 px-2 py-1 text-xs text-muted-foreground"
                        >
                          {permission.replace('_', ' ')}
                        </span>
                      ))}
                      {role.permissions.length > 3 && (
                        <span className="rounded-md border border-border/30 bg-background/50 px-2 py-1 text-xs text-muted-foreground">
                          +{role.permissions.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {isSelected && (
                    <div className="absolute right-2 top-2">
                      <div className="flex size-6 items-center justify-center rounded-full bg-green-500">
                        <CheckCircle className="size-4 text-white" />
                      </div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>

          <div className="flex justify-end gap-3 border-t border-border/30 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isUpdating}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default function TeamPage() {
  const [isPending] = useTransition();
  const { currentOrgId, currentOrg } = useOrgContext();

  // Modal state
  const [removeUser, setRemoveUser] = useState<TeamMember | undefined>();
  const [changeRoleUser, setChangeRoleUser] = useState<TeamMember | undefined>();
  const [inviteOpen, setInviteOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('team');

  // Activity log pagination
  const actionLogPagination = usePagination(0, 15);

  // API hooks
  const {
    data: usersData,
    isLoading: usersLoading,
    refetch: refetchUsers,
  } = useOrgUsers(currentOrgId || '', 0, 100);

  const {
    data: actionLogsData,
    isLoading: actionLogsLoading,
    refetch: refetchActionLogs,
  } = useActivityLogs({
    organizationId: currentOrgId || '',
    page: actionLogPagination.page,
    resultsPerPage: actionLogPagination.pageSize,
    enabled: activeTab === 'activity',
  });

  // Mutation hooks
  const { mutate: inviteUser, isPending: isInviting } = useInviteUser();
  const { mutate: updateUserRole, isPending: isUpdatingRole } = useUpdateUserRole();
  const { mutate: removeUserMutation, isPending: isRemoving } = useRemoveUser();
  const { mutate: reinviteUser, isPending: isReinviting } = useReinviteUser();
  const { mutate: cancelInvite, isPending: isCancelling } = useCancelInvite();

  // Derived data
  const users = usersData?.users || [];
  const totalUsers = usersData?.totalUsers || 0;
  const currentUserRole = usersData?.currentUserRole || undefined;

  // Custom hooks for computed values
  const stats = useTeamStats(users, totalUsers);
  const rolesWithMembers = useRolesWithMembers(users);

  // Handle invite user with better error handling and optimistic updates
  const handleInviteUser = useCallback(
    async (
      email: string,
      role: string,
    ): Promise<{ message: string; currentUserRole?: string | undefined; error?: boolean }> => {
      // Starting invite process

      // Validate org context
      if (!currentOrgId || !currentOrg?.orgName) {
        const errorMsg = 'Organization context is missing';
        // No org context
        toast.error(errorMsg);
        return {
          message: errorMsg,
          currentUserRole: undefined,
          error: true,
        };
      }

      // Validate inputs
      if (!email || !role) {
        const errorMsg = 'Email and role are required';
        // Validation failed
        toast.error(errorMsg);
        return {
          message: errorMsg,
          currentUserRole: undefined,
          error: true,
        };
      }

      const invitePayload = {
        email: email.trim(),
        role: role.toLowerCase(),
        orgId: currentOrgId,
        orgName: currentOrg.orgName,
      };

      // Sending invite

      return new Promise((resolve) => {
        inviteUser(invitePayload, {
          onSuccess: (data) => {
            // Invite successful
            toast.success(`Successfully invited ${email}`);
            // No need to refetch - optimistic update handles it
            resolve(
              data as { message: string; currentUserRole?: string | undefined; error?: boolean },
            );
          },
          onError: (error: { message: string }) => {
            // Invite error
            const errorMessage = error.message || 'Failed to invite user';
            toast.error(errorMessage);
            resolve({
              message: errorMessage,
              currentUserRole: undefined,
              error: true,
            });
          },
        });
      });
    },
    [inviteUser, currentOrgId, currentOrg],
  );

  const handleRemoveUser = useCallback(
    async (user: TeamMember) => {
      if (!user || !currentOrgId) return;

      try {
        await removeUserMutation({
          email: user.email,
          orgId: currentOrgId,
          orgName: currentOrg?.orgName || '',
        });
        toast.success(`Successfully removed ${user.email}`);
        // No need to refetch - optimistic update handles it
      } catch (error: any) {
        toast.error(`Failed to remove user: ${error.message || 'Unknown error'}`);
      } finally {
        setRemoveUser(undefined);
      }
    },
    [removeUserMutation, currentOrgId, currentOrg],
  );

  const handleChangeRole = useCallback(
    async (user: TeamMember, newRole: string) => {
      if (!user || !newRole || !currentOrgId) return;

      try {
        await updateUserRole({
          email: user.email,
          role: newRole,
          oldRole: user.role,
          orgId: currentOrgId,
          orgName: currentOrg?.orgName || '',
        });
        toast.success(`Successfully changed ${user.email}'s role to ${newRole}`);
        // No need to refetch - optimistic update handles it
      } catch (error: any) {
        toast.error(`Failed to change role: ${error.message || 'Unknown error'}`);
      } finally {
        setChangeRoleUser(undefined);
      }
    },
    [updateUserRole, currentOrgId, currentOrg],
  );

  const handleReinvite = useCallback(
    (member: TeamMember) => {
      if (!currentOrg?.orgName) {
        toast.error('Organization name not available');
        return Promise.reject(new Error('Organization name not available'));
      }

      return new Promise((resolve, reject) => {
        reinviteUser(
          {
            email: member.email,
            role: member.role,
            orgId: currentOrgId || '',
            name: currentOrg.orgName,
            referer: process.env.NEXT_PUBLIC_REFERRER || 'qbraid.com',
          },
          {
            onSuccess: (data) => {
              // No need to refetch - optimistic update handles it
              toast.success(`Reinvite sent to ${member.email}`);
              resolve(data);
            },
            onError: (error) => {
              toast.error(`Failed to reinvite: ${error.message || 'Unknown error'}`);
              reject(error);
            },
          },
        );
      });
    },
    [reinviteUser, currentOrg?.orgName, currentOrgId],
  );

  const handleCancelInvite = useCallback(
    (member: TeamMember) => {
      if (!currentOrgId) {
        toast.error('Organization ID not available');
        return Promise.reject(new Error('Organization ID not available'));
      }

      return new Promise((resolve, reject) => {
        cancelInvite(
          {
            orgId: currentOrgId,
            email: member.email,
          },
          {
            onSuccess: (data) => {
              // No need to refetch - optimistic update handles it
              toast.success(`Cancelled invite for ${member.email}`);
              resolve(data);
            },
            onError: (error) => {
              toast.error(`Failed to cancel invite: ${error.message || 'Unknown error'}`);
              reject(error);
            },
          },
        );
      });
    },
    [cancelInvite, currentOrgId],
  );

  const refreshData = useCallback(() => {
    refetchUsers();
    if (activeTab === 'activity') {
      refetchActionLogs();
    }
    toast.success('Data refreshed successfully');
  }, [refetchUsers, refetchActionLogs, activeTab]);

  // Early returns for loading states
  if (!currentOrgId) {
    return (
      <LoadingState
        message="Loading Organization..."
        description="Please wait while we load your organization data."
      />
    );
  }

  return (
    <OrgPermissionGuard
      permission={Permission.ViewTeam}
      fallback={<AccessDenied description="You need team view permissions to access this page." />}
    >
      {/* Modals */}
      <InviteModal
        open={inviteOpen}
        onClose={() => setInviteOpen(false)}
        onInvite={handleInviteUser}
        orgName={currentOrg?.orgName || 'your organization'}
        orgId={currentOrgId}
        isLoading={isPending || isInviting}
      />

      <ChangeRoleModal
        user={changeRoleUser || null}
        onClose={() => setChangeRoleUser(undefined)}
        onChangeRole={handleChangeRole}
        isUpdating={isUpdatingRole}
      />

      {removeUser && (
        <RemoveUserButton
          member={removeUser}
          onRemove={() => handleRemoveUser(removeUser)}
          disabled={isRemoving}
        />
      )}

      <div className="min-h-screen overflow-x-auto bg-background">
        <main className="mx-auto space-y-8 p-2">
          <PageHeader
            title={currentOrg?.orgName || 'Team'}
            description="Manage your team members, roles, and permissions with powerful collaboration tools"
            icon={Users}
            actions={
              <>
                <Button
                  onClick={refreshData}
                  variant="outline"
                  size="sm"
                  className="border-sidebar-border bg-sidebar font-semibold text-foreground hover:bg-muted"
                  disabled={isPending}
                >
                  <RefreshCw className={`mr-2 size-4 ${isPending ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>

                <OrgPermissionGuard
                  permission={Permission.ManageTeam}
                  fallback={
                    <Button disabled variant="outline" size="sm" className="opacity-50">
                      <UserPlus className="mr-2 size-4" />
                      Invite Member
                    </Button>
                  }
                >
                  <GradientButton
                    onClick={() => setInviteOpen(true)}
                    icon={UserPlus}
                    disabled={isPending}
                    data-testid="invite-member-btn"
                  >
                    Invite Member
                  </GradientButton>
                </OrgPermissionGuard>
              </>
            }
          />

          {/* Statistics Cards */}
          <div data-testid="team-stats-container" className="grid grid-cols-1 gap-6 md:grid-cols-4">
            <StatsCard
              title="Total Members"
              value={usersLoading ? '...' : stats.totalUsers}
              description="All team members"
              icon={Users}
              colorVariant="blue"
              loading={usersLoading}
            />
            <StatsCard
              title="Active Users"
              value={usersLoading ? '...' : stats.activeUsers}
              description="Currently active"
              icon={CheckCircle}
              colorVariant="green"
              loading={usersLoading}
              data-testid="active-users-stat"
            />
            <StatsCard
              title="Pending Invites"
              value={usersLoading ? '...' : stats.pendingInvites}
              description="Awaiting response"
              icon={UserPlus}
              colorVariant="orange"
              loading={usersLoading}
              data-testid="pending-invitations-stat"
            />
            <StatsCard
              title="Roles"
              value={usersLoading ? '...' : stats.totalRoles}
              description="Active roles"
              icon={Shield}
              colorVariant="purple"
              loading={usersLoading}
              data-testid="active-roles-stat"
            />
          </div>

          {/* Enhanced Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="mx-auto max-w-7xl space-y-8"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <TabsList className="mx-auto grid w-full grid-cols-3 rounded-xl border border-sidebar-border bg-gradient-to-r from-sidebar to-sidebar/80 p-1">
                <TabsTrigger
                  value="team"
                  className="group rounded-lg font-semibold text-muted-foreground transition-all duration-300 hover:bg-muted data-[state=active]:bg-gradient-to-r data-[state=active]:from-brand data-[state=active]:to-brand/80 data-[state=active]:font-bold data-[state=active]:text-brand-foreground data-[state=active]:shadow-md"
                >
                  <Users className="mr-2 size-4 transition-transform duration-300 group-data-[state=active]:scale-110" />
                  Team Members
                  <Badge variant="secondary" className="ml-2 bg-muted font-medium text-foreground">
                    {totalUsers}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="roles"
                  className="group rounded-lg font-semibold text-muted-foreground transition-all duration-300 hover:bg-muted data-[state=active]:bg-gradient-to-r data-[state=active]:from-brand data-[state=active]:to-brand/80 data-[state=active]:font-bold data-[state=active]:text-brand-foreground data-[state=active]:shadow-md"
                >
                  <Shield className="mr-2 size-4 transition-transform duration-300 group-data-[state=active]:scale-110" />
                  Roles & Permissions
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  className="group rounded-lg font-semibold text-muted-foreground transition-all duration-300 hover:bg-muted data-[state=active]:bg-gradient-to-r data-[state=active]:from-brand data-[state=active]:to-brand/80 data-[state=active]:font-bold data-[state=active]:text-brand-foreground data-[state=active]:shadow-md"
                >
                  <Activity className="mr-2 size-4 transition-transform duration-300 group-data-[state=active]:scale-110" />
                  Activity Log
                  <Badge variant="secondary" className="ml-2 bg-muted font-medium text-foreground">
                    {actionLogsData?.data?.pagination?.total || 0}
                  </Badge>
                </TabsTrigger>
              </TabsList>
            </motion.div>

            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{
                  opacity: 0,
                  x: activeTab === 'team' ? -20 : activeTab === 'roles' ? 0 : 20,
                }}
                animate={{ opacity: 1, x: 0 }}
                exit={{
                  opacity: 0,
                  x: activeTab === 'team' ? 20 : activeTab === 'roles' ? 0 : -20,
                }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                {activeTab === 'team' && (
                  <TabsContent value="team" className="mx-auto mt-0 max-w-7xl space-y-6">
                    <EnhancedTeamTable
                      data={users}
                      currentUserRole={currentUserRole}
                      onChangeRole={setChangeRoleUser}
                      onRemove={handleRemoveUser}
                      onReinvite={handleReinvite}
                      onCancelInvite={handleCancelInvite}
                      isReinviting={isReinviting}
                      isCancelling={isCancelling}
                      isLoading={usersLoading}
                    />
                  </TabsContent>
                )}
                {activeTab === 'roles' && (
                  <div data-testid="roles-permissions-container">
                    <TabsContent value="roles" className="mx-auto mt-0 max-w-7xl space-y-6">
                      <RolesTab rolesWithMembers={rolesWithMembers as any} />
                    </TabsContent>
                  </div>
                )}
                {activeTab === 'activity' && (
                  <TabsContent value="activity" className="mx-auto mt-0 max-w-7xl space-y-6">
                    <div data-testid="activity-log-container">
                      <ActivityLogTab
                        data={actionLogsData?.data as any}
                        isLoading={actionLogsLoading}
                        pagination={actionLogPagination}
                        actions={ACTIONS}
                        data-testid="activity-log-container"
                      />
                    </div>
                  </TabsContent>
                )}
              </motion.div>
            </AnimatePresence>
          </Tabs>
        </main>
      </div>
    </OrgPermissionGuard>
  );
}
