'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  <PERSON>,
  <PERSON><PERSON>he<PERSON>,
  ClipboardList,
  Settings,
  AlertCircle,
  ShieldCheck,
  Building2,
} from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAdminDevices } from '@/hooks/use-api';
import { useQbraidAdminAuth } from '@/lib/auth-qbraid-admin-check';
import DeviceRequestsTab from '@/components/admin/device-requests-tab';
import AddRequestsTab from '@/components/admin/add-requests-tab';
import EditRequestsTab from '@/components/admin/edit-requests-tab';
import { useDeviceAccessRequests } from '@/hooks/use-device-access';
import { CreateOrgModal } from '@/components/nav/create-org-modal';

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('access-requests');
  const [createOrgOpen, setCreateOrgOpen] = useState(false);

  // Check permissions and roles using external API with comprehensive auth management
  const { isQbraidAdmin, isLoading: roleLoading, isError: roleError } = useQbraidAdminAuth();
  const { data: devices, isLoading: devicesLoading, isError: devicesError } = useAdminDevices();

  const { data: requestsData, isLoading: requestsLoading } = useDeviceAccessRequests();
  const isLoading = roleLoading || devicesLoading || requestsLoading;

  // Handle errors
  if (roleError) {
    toast.error('Failed to check admin status');
  }
  if (devicesError) {
    toast.error('Failed to fetch devices');
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-[600px] items-center justify-center">
        <div className="space-y-4 text-center">
          <div className="mx-auto size-12 animate-spin rounded-full border-b-2 border-brand"></div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // Access denied - check admin status using external API
  if (!isQbraidAdmin) {
    return (
      <main className="mx-auto space-y-8 p-2">
        <div className="mx-auto mt-16 max-w-2xl space-y-6">
          <Alert variant="destructive" className="border-red-200">
            <AlertCircle className="size-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription className="mt-2">
              <p>This page is restricted to qBraid administrators only.</p>
              <p className="mt-2 text-sm text-muted-foreground">
                If you believe you should have access, please contact system administrators.
              </p>
            </AlertDescription>
          </Alert>
        </div>
      </main>
    );
  }

  // Split devices for different tabs
  const addRequests = (devices || []).filter((d) => d.verified === false && !d.pendingEdits);
  const editRequests = (devices || []).filter((d) => d.pendingEdits);

  return (
    <main className="mx-auto space-y-8 p-2">
      {/* Header */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-gradient-to-br from-brand to-brand/80 p-2">
              <Shield className="size-6 text-brand-foreground" />
            </div>
            <div>
              <h1 className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-transparent">
                Admin Panel
              </h1>
              <p className="text-lg text-muted-foreground">
                Manage device requests and access permissions
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Badge variant="default" className="px-4 py-2">
              <ShieldCheck className="mr-2 size-4" />
              qBraid Admin
            </Badge>
          </div>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="mb-8 grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Access Requests</CardTitle>
            <ClipboardList className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{requestsData?.count}</div>
            <p className="text-xs text-muted-foreground">Awaiting review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Device Add Requests</CardTitle>
            <FileCheck className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{addRequests.length}</div>
            <p className="text-xs text-muted-foreground">New devices to review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Device Edit Requests</CardTitle>
            <Settings className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{editRequests.length}</div>
            <p className="text-xs text-muted-foreground">Pending modifications</p>
          </CardContent>
        </Card>
      </div>

      {/* Main content with tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger
            value="access-requests"
            className="data-[state=active]:bg-brand data-[state=active]:text-brand-foreground"
          >
            <ClipboardList className="mr-2 size-4" />
            Access Requests
          </TabsTrigger>
          <TabsTrigger
            value="add-devices"
            className="data-[state=active]:bg-brand data-[state=active]:text-brand-foreground"
          >
            <FileCheck className="mr-2 size-4" />
            Add Device Requests
            {addRequests.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {addRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="edit-devices"
            className="data-[state=active]:bg-brand data-[state=active]:text-brand-foreground"
          >
            <Settings className="mr-2 size-4" />
            Edit Device Requests
            {editRequests.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {editRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="create-organization"
            className="data-[state=active]:bg-brand data-[state=active]:text-brand-foreground"
          >
            <Building2 className="mr-2 size-4" />
            Create Organization
          </TabsTrigger>
        </TabsList>

        {/* Access Requests Tab */}
        <TabsContent value="access-requests" className="space-y-6">
          <div className="space-y-4">
            <Card className="border-0 shadow-none">
              <CardHeader className="px-0">
                <CardTitle className="text-2xl">Device Access Requests</CardTitle>
                <CardDescription>
                  Review and manage device access requests from organizations. All actions are
                  logged for audit purposes.
                </CardDescription>
              </CardHeader>
            </Card>
            <DeviceRequestsTab />
          </div>
        </TabsContent>

        {/* Add Device Requests Tab */}
        <TabsContent value="add-devices" className="space-y-6">
          <div className="space-y-4">
            <Card className="border-0 shadow-none">
              <CardHeader className="px-0">
                <CardTitle className="text-2xl">Add Device Requests</CardTitle>
                <CardDescription>
                  Review and approve new device submissions. Device approvals and deletions are
                  tracked in the activity log.
                </CardDescription>
              </CardHeader>
            </Card>
            <AddRequestsTab addRequests={addRequests} />
          </div>
        </TabsContent>

        {/* Edit Device Requests Tab */}
        <TabsContent value="edit-devices" className="space-y-6">
          <div className="space-y-4">
            <Card className="border-0 shadow-none">
              <CardHeader className="px-0">
                <CardTitle className="text-2xl">Edit Device Requests</CardTitle>
                <CardDescription>
                  Review and approve device modification requests. All edits are logged with
                  detailed change history.
                </CardDescription>
              </CardHeader>
            </Card>
            <EditRequestsTab editRequests={editRequests} />
          </div>
        </TabsContent>

        {/* Create Organization Tab */}
        <TabsContent value="create-organization" className="space-y-6">
          <div className="space-y-4">
            <Card className="border-0 shadow-none">
              <CardHeader className="px-0">
                <CardTitle className="text-2xl">Create Organization</CardTitle>
                <CardDescription>
                  Set up new organizations with customized settings and team management. Each
                  organization will have its own billing account, team management, and resource
                  allocation.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card
              className="cursor-pointer border-2 border-dashed border-brand/30 transition-shadow duration-200 hover:border-brand/50 hover:shadow-lg"
              onClick={() => setCreateOrgOpen(true)}
            >
              <CardContent className="flex flex-col items-center justify-center space-y-4 p-12 text-center">
                <div className="rounded-full bg-brand/10 p-4">
                  <Building2 className="size-8 text-brand" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">Create New Organization</h3>
                  <p className="max-w-md text-muted-foreground">
                    Set up a new organization with customized settings, team management, and billing
                    configuration.
                  </p>
                </div>
                <Button
                  className="mt-4"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCreateOrgOpen(true);
                  }}
                >
                  <Building2 className="mr-2 size-4" />
                  Create Organization
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create Organization Modal */}
      <CreateOrgModal
        open={createOrgOpen}
        onOpenChange={setCreateOrgOpen}
        onSuccess={() => {
                    toast.success('Organization created successfully!');
        }}
      />
    </main>
  );
}
