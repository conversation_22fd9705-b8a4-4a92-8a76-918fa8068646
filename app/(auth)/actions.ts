// app/auth/actions.ts
'use server';

import { signInWithRedirect } from 'aws-amplify/auth';
import { AuthWrapper } from '@/lib/auth-wrapper';

import { redirect } from 'next/navigation';
import {
  createSession,
  setSessionCookie,
  clearSession,
  generateCSRFToken,
  verifyCSRFToken,
  getSession as getSecureSession,
  setCognitoTokenCookies,
  cleanupOrphanedSessions,
  verifySession,
} from '@/lib/session';
import { invalidateUserRoles } from '@/lib/external-roles';
import { AuthResult, CognitoErrorType, AuthStepType, AuthFormState } from '@/types/auth';
import { AuthTokens } from '@aws-amplify/auth';

// Error mapping for consistent responses
const ERROR_MESSAGES = {
  UserNotConfirmedException: 'Invalid email or password',
  NotAuthorizedException: 'Invalid email or password',
  UserNotFoundException: 'Invalid email or password',
  PasswordResetRequiredException: 'Invalid email or password',
  TooManyRequestsException: 'Too many attempts. Please try again later.',
  UsernameExistsException: 'An account with this email already exists',
  InvalidPasswordException: 'Password does not meet requirements',
  InvalidParameterException: 'Invalid email format',
  CodeMismatchException: 'Invalid verification code',
  ExpiredCodeException: 'Verification code has expired',
  AliasExistsException: 'User is already verified',
} as const;

/**
 * Amplify configuration using AuthWrapper which handles UserAlreadyAuthenticatedException
 */
const ensureAmplifyConfig = async (): Promise<void> => {
  await AuthWrapper.configure();
};

/**
 * Optimized CSRF validation with early return
 */
const validateCSRFToken = (formData: FormData): Promise<boolean> => {
  const csrfToken = formData.get('csrfToken') as string;
  return csrfToken ? verifyCSRFToken(csrfToken) : Promise.resolve(false);
};

/**
 * Centralized error handling with consistent messaging
 */
const handleAuthError = (error: unknown, operation: string): AuthResult => {
  const errorName = error instanceof Error ? (error.name as CognitoErrorType) : undefined;
  const message =
    errorName && errorName in ERROR_MESSAGES
      ? ERROR_MESSAGES[errorName as keyof typeof ERROR_MESSAGES]
      : 'Authentication failed. Please try again.';

  // Special handling for verification required
  if (errorName === 'UserNotConfirmedException') {
    return { success: false, requiresVerification: true, error: message };
  }

  console.error(`❌ [${operation}] Error:`, {
    errorType: errorName,
    errorMessage: error instanceof Error ? error.message : String(error),
    timestamp: new Date().toISOString(),
  });

  return { success: false, error: message };
};

/**
 * Optimized session creation with parallel operations
 */
const createAuthSession = async (userData: {
  username: string;
  email: string;
  userId: string;
  tokens?: AuthTokens;
}): Promise<string> => {
  const { username, email, userId, tokens } = userData;

  // Create session
  const sessionToken = await createSession({ username, email, userId });

  // Store tokens and set cookie in parallel if tokens exist
  const operations = [setSessionCookie(sessionToken)];

  if (tokens) {
    const tokenData = {
      accessToken: tokens.accessToken?.toString(),
      idToken: tokens.idToken?.toString(),
    };

    // Extract sessionId for Redis storage
    try {
      const sessionData = await verifySession(sessionToken);
      const sessionId = sessionData?.jti;
      operations.push(setCognitoTokenCookies(tokenData, sessionId));
    } catch {
      operations.push(setCognitoTokenCookies(tokenData));
    }
  }

  await Promise.all(operations);

  // Background cleanup (non-blocking)
  cleanupOrphanedSessions(email, sessionToken).catch((error) =>
    console.warn('⚠️ Session cleanup failed (non-critical):', error),
  );

  return sessionToken;
};

/**
 * Input validation helper
 */
const validateRequired = (fields: Record<string, string | undefined>): string | undefined => {
  for (const [name, value] of Object.entries(fields)) {
    if (!value?.trim()) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} is required`;
    }
  }
  return undefined;
};

/**
 * Password validation helper
 */
const validatePassword = (password: string, confirmPassword?: string): string | undefined => {
  if (password.length < 8) {
    return 'Password must be at least 8 characters long';
  }

  if (confirmPassword && password !== confirmPassword) {
    return 'Passwords do not match';
  }

  return undefined;
};

/**
 * Optimized user authentication with reduced logging and parallel operations
 */
export async function authenticateUser(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  try {
    // Parallel validation and config
    const [, isCSRFValid] = await Promise.all([ensureAmplifyConfig(), validateCSRFToken(formData)]);

    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    // Input validation
    const validationError = validateRequired({ email, password });
    if (validationError) {
      return { success: false, error: validationError };
    }

    // Sign in with retry logic
    const result = await AuthWrapper.signInWithRetry(email, password);

    if (result.isSignedIn) {
      // Get user details and session in parallel
      const [userDetails, tokens] = await Promise.all([
        AuthWrapper.getCurrentUser().catch(() => ({ userId: email, username: email })),
        AuthWrapper.fetchAuthSession().catch(() => ({ tokens: undefined })),
      ]);

      const userId = userDetails.userId || userDetails.username || email;

      await createAuthSession({
        username: email,
        email,
        userId,
        tokens: tokens as AuthTokens,
      });

      return { success: true, redirectTo: '/' };
    }

    // Handle multi-step flows
    if (result.nextStep) {
      const stepType = result.nextStep.signInStep as AuthStepType;
      return stepType === 'CONFIRM_SIGN_UP'
        ? { success: false, requiresVerification: true, error: 'Invalid email or password' }
        : { success: false, error: 'Invalid email or password' };
    }

    return { success: false, error: 'Authentication failed' };
  } catch (error) {
    // Special handling for UnexpectedSignInInterruptionException
    if (error instanceof Error && error.name === 'UnexpectedSignInInterruptionException') {
      try {
        const userDetails = await AuthWrapper.getCurrentUser().catch(() => ({
          userId: email,
          username: email,
        }));
        const userId = userDetails.userId || userDetails.username || email;

        await createAuthSession({ username: email, email, userId });
        return { success: true, redirectTo: '/' };
      } catch {
        // Fall through to regular error handling
      }
    }

    return handleAuthError(error, 'AUTH');
  }
}

/**
 * Optimized user registration with streamlined validation
 */
export async function registerUser(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  try {
    // Parallel validation and config
    const [, isCSRFValid] = await Promise.all([ensureAmplifyConfig(), validateCSRFToken(formData)]);

    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    // Input validation
    const requiredError = validateRequired({ name, email, password });
    if (requiredError) return { success: false, error: requiredError };

    const passwordError = validatePassword(password, confirmPassword);
    if (passwordError) return { success: false, error: passwordError };

    // Sign up with retry logic
    const result = await AuthWrapper.signUpWithRetry(email, password, {
      userAttributes: {
        email,
        name,
        preferred_username: email,
      },
    });

    if (result.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
      return {
        success: true,
        nextStep: 'verify',
        email: email,
        error: 'Please check your email and click the verification link',
      };
    }

    return { success: true };
  } catch (error) {
    return handleAuthError(error, 'REGISTER');
  }
}

/**
 * Streamlined email verification (legacy)
 */
export async function verifyEmail(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const [, isCSRFValid] = await Promise.all([ensureAmplifyConfig(), validateCSRFToken(formData)]);

    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;

    const validationError = validateRequired({ email, code });
    if (validationError) return { success: false, error: validationError };

    await AuthWrapper.confirmSignUp(email, code);
    return { success: true };
  } catch (error) {
    return handleAuthError(error, 'VERIFY');
  }
}

/**
 * Optimized password reset initiation
 */
export async function initiatePasswordReset(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const [, isCSRFValid] = await Promise.all([ensureAmplifyConfig(), validateCSRFToken(formData)]);

    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    if (!email) return { success: false, error: 'Email is required' };

    await AuthWrapper.resetPassword(email);
    return {
      success: true,
      nextStep: 'reset',
      error: 'Password reset code sent to your email',
    };
  } catch (error) {
    return handleAuthError(error, 'RESET');
  }
}

/**
 * Optimized password reset completion
 */
export async function completePasswordReset(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const [, isCSRFValid] = await Promise.all([ensureAmplifyConfig(), validateCSRFToken(formData)]);

    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;
    const newPassword = formData.get('newPassword') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    const requiredError = validateRequired({ email, code, newPassword });
    if (requiredError) return { success: false, error: requiredError };

    const passwordError = validatePassword(newPassword, confirmPassword);
    if (passwordError) return { success: false, error: passwordError };

    await AuthWrapper.confirmResetPassword(email, code, newPassword);

    return { success: true };
  } catch (error) {
    return handleAuthError(error, 'RESET_COMPLETE');
  }
}

/**
 * Optimized logout with reduced logging
 */
export async function logout() {
  let currentSessionId: string | undefined;
  let userEmail: string | undefined;

  try {
    await ensureAmplifyConfig();

    // Get session details
    const currentSession = await getSecureSession();
    currentSessionId = currentSession?.jti || undefined;
    userEmail = currentSession?.email || undefined;

    // Sign out from Cognito
    await AuthWrapper.signOut();
  } catch (error) {
    console.error('❌ [LOGOUT] Error:', error);
  } finally {
    // Parallel cleanup operations
    const cleanupTasks = [clearSession(currentSessionId || undefined)];

    if (userEmail) {
      cleanupTasks.push(
        invalidateUserRoles(userEmail).catch((error) =>
          console.error('Failed to invalidate user roles:', error),
        ),
      );
    }

    await Promise.all(cleanupTasks);
    redirect('/signin');
  }
}

/**
 * Session retrieval (unchanged)
 */
export async function getSession() {
  return await getSecureSession();
}

/**
 * Session validation with automatic logout
 */
export async function validateSessionOrLogout() {
  const session = await getSecureSession();

  if (!session) {
    // Non-blocking logout
    logout().catch((error) => console.error('Failed to force logout:', error));
    return;
  }

  return session;
}

/**
 * CSRF token generation (unchanged)
 */
export async function getCSRFToken(): Promise<string> {
  return await generateCSRFToken();
}

/**
 * Google OAuth sign-in (unchanged)
 */
export async function signInWithGoogle(): Promise<void> {
  try {
    await ensureAmplifyConfig();
    await signInWithRedirect({
      provider: 'Google',
      customState: JSON.stringify({ returnUrl: '/' }),
    });
  } catch (error) {
    console.error('❌ [AUTH] Google sign-in error:', error);
    throw new Error('Failed to initialize Google sign-in');
  }
}

/**
 * Optimized OAuth callback handling
 */
export async function handleOAuthCallback(): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    // Get user details and session in parallel
    const [userDetails, authSession] = await Promise.all([
      AuthWrapper.getCurrentUser(),
      AuthWrapper.fetchAuthSession(),
    ]);

    if (!userDetails || !authSession) {
      return { success: false, error: 'OAuth authentication failed' };
    }

    const tokens = authSession;
    const userId = userDetails.userId || userDetails.username;
    const email = userDetails.signInDetails?.loginId || 'oauth-user';

    await createAuthSession({ username: email, email, userId, tokens });

    return { success: true, redirectTo: '/' };
  } catch (error) {
    return handleAuthError(error, 'OAUTH_CALLBACK');
  }
}

/**
 * Optimized OAuth session creation
 */
export async function createOAuthSession(userData: {
  userId: string;
  email: string;
  username?: string;
  tokens?: {
    accessToken: string;
    idToken: string;
  };
}): Promise<AuthResult> {
  try {
    await ensureAmplifyConfig();

    const { userId, email, username, tokens } = userData;

    // Convert tokens to AuthTokens format if provided
    const authTokens = tokens
      ? ({
          accessToken: { toString: () => tokens.accessToken },
          idToken: { toString: () => tokens.idToken },
        } as AuthTokens)
      : undefined;

    await createAuthSession({
      username: username || email,
      email,
      userId,
      tokens: authTokens,
    });

    return { success: true, redirectTo: '/' };
  } catch (error) {
    return handleAuthError(error, 'OAUTH_SESSION');
  }
}

// Helper to decode ID token on the server
function parseIdToken(token: string) {
  try {
    const payload = token.split('.')[1];
    return JSON.parse(Buffer.from(payload, 'base64').toString());
  } catch {
    return null;
  }
}

/**
 * Server Action: Finalize OAuth from client (implicit flow)
 * Accepts FormData with accessToken and idToken, creates session, stores tokens, and redirects.
 */
export async function finalizeOAuth(formData: FormData): Promise<{ ok: boolean }> {
  const accessToken = formData.get('accessToken') as string | null;
  const idToken = formData.get('idToken') as string | null;
  if (!accessToken || !idToken) return { ok: false };

  const user = parseIdToken(idToken);
  if (!user?.email || !user?.sub) return { ok: false };

  const sessionToken = await createSession({
    username: user['cognito:username'] || user.email,
    email: user.email,
    userId: user.sub,
  });

  await setSessionCookie(sessionToken);

  try {
    const sessionData = await verifySession(sessionToken);
    const sessionId = sessionData?.jti;
    await setCognitoTokenCookies({ accessToken, idToken }, sessionId);
  } catch {
    await setCognitoTokenCookies({ accessToken, idToken });
  }

  return { ok: true };
}
