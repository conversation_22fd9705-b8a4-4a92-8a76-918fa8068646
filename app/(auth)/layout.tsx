import React from 'react';
import { AuthProvider } from '@/components/auth/auth-provider';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <div className={`${inter.className} min-h-screen bg-[#18141f]`}>
        {/* Header */}

        {/* Main Auth Content */}
        <main className="relative z-10 flex min-h-[calc(100vh-8rem)] items-center justify-center px-4 py-12">
          <div className="w-full max-w-xl">{children}</div>
        </main>

        {/* Background decoration */}
        <div className="fixed inset-0 z-0">
          <div className="animate-blob absolute -left-4 top-0 size-72 rounded-full bg-purple-600/20 opacity-70 mix-blend-multiply blur-3xl" />
          <div className="animate-blob animation-delay-2000 absolute -right-4 top-0 size-72 rounded-full bg-pink-600/20 opacity-70 mix-blend-multiply blur-3xl" />
          <div className="animate-blob animation-delay-4000 absolute -bottom-8 left-20 size-72 rounded-full bg-blue-600/20 opacity-70 mix-blend-multiply blur-3xl" />
        </div>
      </div>
    </AuthProvider>
  );
}
