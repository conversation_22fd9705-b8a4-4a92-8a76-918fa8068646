import { Suspense } from 'react';
import { SignInForm } from '@/components/auth/sign-in-form';
import { requireNoAuth } from '@/lib/auth';

function SignInFormWithSuspense() {
  return (
    <Suspense
      fallback={
        <div className="h-96 w-full animate-pulse rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm"></div>
      }
    >
      <SignInForm />
    </Suspense>
  );
}

export const dynamic = 'force-dynamic';

export default async function SignInPage() {
  await requireNoAuth();

  return (
    <div className="flex size-full items-center justify-center ">
      <SignInFormWithSuspense />
    </div>
  );
}
