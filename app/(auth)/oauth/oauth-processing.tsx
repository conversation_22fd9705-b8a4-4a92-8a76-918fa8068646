'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { finalizeOAuth } from '@/app/(auth)/actions';

interface OAuthResult {
  ok: boolean;
  error?: string;
}

type Status = 'loading' | 'success' | 'error';
type ErrorType = 'auth_incomplete' | 'server_error' | 'timeout' | 'unknown';

interface ErrorState {
  type: ErrorType;
  message: string;
}

const OAUTH_TIMEOUT = 30_000; // 30 seconds
const SUPPORT_EMAIL = process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>';

// Custom hook for OAuth processing logic
function useOAuthProcessor() {
  const [status, setStatus] = useState<Status>('loading');
  const [error, setError] = useState<ErrorState | null>(null);
  const router = useRouter();
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const isProcessedRef = useRef(false);

  useEffect(() => {
    // Only process once
    if (isProcessedRef.current) return;
    isProcessedRef.current = true;

    const processTokens = async () => {
      const timeoutId = setTimeout(() => {
        setError({
          type: 'timeout',
          message: 'Authentication is taking longer than expected. Please try again.',
        });
        setStatus('error');
      }, OAUTH_TIMEOUT);

      try {
        // Extract and clear tokens immediately for security
        const hash = globalThis.location.hash.slice(1);
        globalThis.history.replaceState({}, document.title, globalThis.location.pathname);

        const params = new URLSearchParams(hash);
        const accessToken = params.get('access_token');
        const idToken = params.get('id_token');
        const oauthError = params.get('error');
        const errorDescription = params.get('error_description');

        if (oauthError) {
          throw new Error(`OAuth error: ${errorDescription || oauthError}`);
        }

        if (!accessToken || !idToken) {
          setError({
            type: 'auth_incomplete',
            message: 'Authentication tokens missing. Please try signing in again.',
          });
          setStatus('error');
          return;
        }

        // Submit to server
        const formData = new FormData();
        formData.set('accessToken', accessToken);
        formData.set('idToken', idToken);

        const result = (await finalizeOAuth(formData)) as OAuthResult;

        if (result?.ok) {
          setStatus('success');
          setTimeout(() => router.replace('/'), 1500);
        } else {
          setError({
            type: 'server_error',
            message: result?.error || 'Server authentication failed. Please try again.',
          });
          setStatus('error');
        }
      } catch (error_) {
        console.error('OAuth processing error:', error_);

        if (!error) {
          setError({
            type: 'unknown',
            message: 'An unexpected error occurred. Please try again.',
          });
        }
        setStatus('error');
      } finally {
        clearTimeout(timeoutId);
      }
    };

    processTokens();

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [router, error]);

  return { status, error };
}

export default function OAuthProcessing() {
  const { status, error } = useOAuthProcessor();
  const router = useRouter();

  const getErrorActions = (errorType: ErrorType) => {
    const baseClasses =
      'rounded-xl px-6 py-3 font-medium text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900';
    const primaryClasses = `${baseClasses} bg-purple-600 shadow-lg hover:bg-purple-700 hover:shadow-purple-600/25 focus:ring-purple-500`;
    const secondaryClasses = `${baseClasses} bg-slate-700/50 hover:bg-slate-700/70 focus:ring-slate-500`;

    switch (errorType) {
      case 'server_error':
        return (
          <div className="flex flex-col justify-center gap-3 pt-4 sm:flex-row">
            <button onClick={() => globalThis.location.reload()} className={primaryClasses}>
              Refresh Page
            </button>
            <button onClick={() => router.push('/signin')} className={secondaryClasses}>
              Sign In Again
            </button>
          </div>
        );
      default:
        return (
          <div className="flex flex-col justify-center gap-3 pt-4 sm:flex-row">
            <button onClick={() => router.push('/signin')} className={primaryClasses}>
              Try Again
            </button>
            <button onClick={() => router.push('/')} className={secondaryClasses}>
              Go Home
            </button>
          </div>
        );
    }
  };

  const containerClasses =
    'w-full rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] p-10 shadow-2xl backdrop-blur-sm lg:p-12';

  if (status === 'loading') {
    return (
      <div className={containerClasses}>
        <div className="space-y-6 text-center">
          <div className="mb-8">
            <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-2xl bg-purple-600/20">
              <div
                className="size-12 animate-spin rounded-full border-4 border-purple-500/30 border-t-purple-500 motion-reduce:animate-pulse"
                role="status"
                aria-label="Processing authentication"
              />
            </div>
            <h1 className="text-2xl font-bold text-white">Completing Sign In</h1>
          </div>

          <div className="space-y-2">
            <p className="text-slate-300">Processing your authentication...</p>
            <div className="flex justify-center gap-1 pt-2" aria-hidden="true">
              {[0, 150, 300].map((delay, i) => (
                <div
                  key={i}
                  className="size-2 animate-bounce rounded-full bg-purple-500 motion-reduce:animate-pulse"
                  style={{ animationDelay: `${delay}ms` }}
                />
              ))}
            </div>
          </div>

          <p className="mt-8 text-xs text-slate-500">Please don&apos;t close this window.</p>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className={containerClasses}>
        <div className="space-y-6 text-center">
          <div className="mb-8">
            <div className="mx-auto mb-4 flex size-20 animate-bounce items-center justify-center rounded-2xl bg-green-500/20 motion-reduce:animate-pulse">
              <svg
                className="size-10 text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                role="img"
                aria-label="Success"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-white">Welcome Back!</h1>
          </div>

          <div className="space-y-2">
            <p className="text-slate-300">Sign in successful</p>
            <p className="text-sm text-slate-400">Redirecting...</p>
          </div>

          <div className="h-1.5 w-full overflow-hidden rounded-full bg-slate-700/30">
            <div
              className="h-full rounded-full bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-1000 ease-out motion-reduce:transition-none"
              style={{ width: '100%' }}
              role="progressbar"
              aria-label="Redirecting"
            />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  return (
    <div className="w-full">
      <div className={containerClasses}>
        <div className="space-y-6 text-center">
          <div className="mb-8">
            <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-2xl bg-red-500/20">
              <svg
                className="size-10 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                role="img"
                aria-label="Error"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-white">Authentication Failed</h1>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-slate-300">
              {error?.message || "We couldn't complete the sign-in process. Please try again."}
            </p>

            {error?.type !== 'server_error' && error?.type !== 'timeout' && (
              <p className="text-sm text-slate-400">
                If this problem persists, please contact support.
              </p>
            )}
          </div>

          {getErrorActions(error?.type || 'unknown')}

          <p className="pt-4 text-xs text-slate-500">
            Need help? Contact support at {SUPPORT_EMAIL}
          </p>
        </div>
      </div>
    </div>
  );
}
