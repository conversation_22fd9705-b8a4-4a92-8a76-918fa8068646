'use client';

import { usePermissions } from '@/hooks/use-permissions';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  ArrowLeft,
  Building2,
  Shield,
  Key,
  Users,
  Home,
  User,
  Sparkles,
  Lock,
  Eye,
  Settings,
} from 'lucide-react';
import Link from 'next/link';

export default function UnauthorizedPage() {
  const { roles, permissions, orgRoles, getCurrentOrgContext } = usePermissions();

  // Get current org context safely
  const currentOrgId = getCurrentOrgContext();
  const currentOrg =
    currentOrgId && orgRoles[currentOrgId]
      ? {
          orgId: currentOrgId,
          orgName: orgRoles[currentOrgId].orgName,
          role: orgRoles[currentOrgId].role,
        }
      : null;

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#0a0a0f] via-[#0f0f0f] to-[#1a1a2e]">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-40 -top-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-purple-500/10 to-pink-500/10 blur-3xl" />
        <div className="absolute -bottom-40 -left-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-blue-500/10 to-cyan-500/10 blur-3xl delay-1000" />
        <div className="absolute left-1/2 top-1/2 size-96 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-gradient-to-br from-violet-500/5 to-fuchsia-500/5 blur-3xl delay-500" />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0">
        {Array.from({length: 20}).map((_, i) => (
          <div
            key={i}
            className="absolute size-1 animate-pulse rounded-full bg-white/20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Main unauthorized card */}
          <Card className="border-[#3b3b3b]/50 bg-gradient-to-br from-[#1a1a2e]/80 via-[#262131]/80 to-[#1a1a2e]/80 shadow-2xl backdrop-blur-xl">
            <CardContent className="p-8 md:p-12">
              {/* Header section */}
              <div className="mb-12 text-center">
                <div className="relative mb-6 inline-flex size-20 items-center justify-center">
                  <div className="absolute inset-0 animate-pulse rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 blur-xl" />
                  <div className="relative rounded-full border border-red-500/20 bg-gradient-to-br from-red-500/10 to-orange-500/10 p-5">
                    <Lock className="size-10 text-red-400" />
                  </div>
                </div>

                <h1 className="mb-4 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                  Access Restricted
                </h1>

                <p className="mx-auto max-w-2xl text-lg leading-relaxed text-gray-400">
                  You don&apos;t have the required permissions to access this resource. Your current
                  access level is shown below.
                </p>
              </div>

              <div className="mb-12 grid grid-cols-1 gap-8 lg:grid-cols-2">
                {/* Current Organization Context */}
                <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-blue-500/10 p-2">
                        <Building2 className="size-5 text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Organization Context</h3>
                    </div>

                    {currentOrg ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Organization</span>
                          <span className="font-medium text-white">{currentOrg.orgName}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Your Role</span>
                          <Badge className="border-blue-500/30 bg-blue-500/20 capitalize text-blue-300">
                            {currentOrg.role}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Scope</span>
                          <span className="text-sm text-blue-300">Organization-specific</span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8 text-gray-500">
                        <div className="text-center">
                          <Users className="mx-auto mb-2 size-8 opacity-50" />
                          <p>No organization selected</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Access Summary */}
                <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-purple-500/10 p-2">
                        <Shield className="size-5 text-purple-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Access Summary</h3>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="rounded-lg border border-white/10 bg-white/5 p-4 text-center">
                        <div className="mb-1 text-2xl font-bold text-purple-400">
                          {roles.length}
                        </div>
                        <div className="text-sm text-gray-400">Active Roles</div>
                      </div>
                      <div className="rounded-lg border border-white/10 bg-white/5 p-4 text-center">
                        <div className="mb-1 text-2xl font-bold text-cyan-400">
                          {permissions.length}
                        </div>
                        <div className="text-sm text-gray-400">Permissions</div>
                      </div>
                      <div className="rounded-lg border border-white/10 bg-white/5 p-4 text-center">
                        <div className="mb-1 text-2xl font-bold text-green-400">
                          {Object.keys(orgRoles).length}
                        </div>
                        <div className="text-sm text-gray-400">Organizations</div>
                      </div>
                      <div className="rounded-lg border border-white/10 bg-white/5 p-4 text-center">
                        <div className="mb-1 text-2xl font-bold text-orange-400">
                          {
                            Object.values(orgRoles).filter((org) =>
                              ['admin', 'owner'].includes(org.role),
                            ).length
                          }
                        </div>
                        <div className="text-sm text-gray-400">Admin Access</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Access Information */}
              <div className="mb-12 grid grid-cols-1 gap-8 lg:grid-cols-2">
                {/* Current Roles */}
                <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-emerald-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-green-500/10 p-2">
                        <User className="size-5 text-green-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Current Roles</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-green-500/30 text-green-300"
                      >
                        {roles.length}
                      </Badge>
                    </div>

                    <div className="custom-scrollbar max-h-32 space-y-2 overflow-y-auto">
                      {roles.length > 0 ? (
                        roles.map((role, index) => (
                          <div key={role} className="flex items-center gap-2">
                            <div className="size-2 rounded-full bg-green-400" />
                            <Badge
                              variant="secondary"
                              className="border-green-500/30 bg-green-500/10 capitalize text-green-300"
                            >
                              {role}
                            </Badge>
                          </div>
                        ))
                      ) : (
                        <div className="py-4 text-center text-gray-500">
                          <Eye className="mx-auto mb-2 size-6 opacity-50" />
                          <p className="text-sm">No roles assigned</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Current Permissions */}
                <Card className="border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-red-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="rounded-lg bg-orange-500/10 p-2">
                        <Key className="size-5 text-orange-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Permissions</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-orange-500/30 text-orange-300"
                      >
                        {permissions.length}
                      </Badge>
                    </div>

                    <div className="custom-scrollbar max-h-32 overflow-y-auto">
                      {permissions.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {permissions.slice(0, 8).map((permission) => (
                            <Badge
                              key={permission}
                              variant="outline"
                              className="border-orange-500/30 bg-orange-500/5 text-xs text-orange-300"
                            >
                              {permission.replaceAll(/([A-Z])/g, ' $1').trim()}
                            </Badge>
                          ))}
                          {permissions.length > 8 && (
                            <Badge
                              variant="outline"
                              className="border-gray-500/30 text-xs text-gray-400"
                            >
                              +{permissions.length - 8} more
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <div className="py-4 text-center text-gray-500">
                          <Lock className="mx-auto mb-2 size-6 opacity-50" />
                          <p className="text-sm">No permissions granted</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* All Organizations */}
              {Object.keys(orgRoles).length > 0 && (
                <Card className="mb-12 border-violet-500/20 bg-gradient-to-br from-violet-500/5 to-purple-500/5 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="mb-6 flex items-center gap-3">
                      <div className="rounded-lg bg-violet-500/10 p-2">
                        <Building2 className="size-5 text-violet-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Organization Access</h3>
                      <Badge
                        variant="outline"
                        className="ml-auto border-violet-500/30 text-violet-300"
                      >
                        {Object.keys(orgRoles).length} organizations
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      {Object.entries(orgRoles).map(([orgId, orgRole]) => (
                        <div
                          key={orgId}
                          className="group rounded-lg border border-white/10 bg-gradient-to-r from-white/5 to-white/10 p-4 transition-all duration-300 hover:border-violet-500/30 hover:shadow-lg hover:shadow-violet-500/10"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-white transition-colors group-hover:text-violet-300">
                                {orgRole.orgName}
                              </h4>
                              <p className="mt-1 font-mono text-xs text-gray-500">
                                {orgId.slice(0, 12)}...
                              </p>
                            </div>
                            <Badge
                              className={`ml-3 capitalize ${
                                ['admin', 'owner'].includes(orgRole.role)
                                  ? 'border-green-500/30 bg-green-500/20 text-green-300'
                                  : (orgRole.role === 'member'
                                    ? 'border-blue-500/30 bg-blue-500/20 text-blue-300'
                                    : 'border-gray-500/30 bg-gray-500/20 text-gray-300')
                              }`}
                            >
                              {orgRole.role}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Help Text */}
              <div className="mb-8 text-center">
                <div className="mb-4 inline-flex items-center gap-2 rounded-full border border-yellow-500/20 bg-yellow-500/10 px-4 py-2">
                  <Sparkles className="size-4 text-yellow-400" />
                  <span className="text-sm text-yellow-300">Need access?</span>
                </div>
                <p className="mx-auto max-w-2xl text-gray-400">
                  If you believe you should have access to this resource, please contact your
                  organization administrator or try switching to a different organization with the
                  appropriate permissions.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Button
                  asChild
                  className="group border-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl"
                >
                  <Link href="/" className="flex items-center gap-2">
                    <Home className="size-4 transition-transform group-hover:rotate-12" />
                    Return to Dashboard
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                >
                  <Link href="/profile" className="flex items-center gap-2">
                    <User className="size-4 transition-transform group-hover:scale-110" />
                    View Profile
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  className="group border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                >
                  <Link href="/team" className="flex items-center gap-2">
                    <Settings className="size-4 transition-transform duration-500 group-hover:rotate-90" />
                    Team Settings
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(139, 92, 246, 0.3);
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(139, 92, 246, 0.5);
        }
      `}</style>
    </div>
  );
}
