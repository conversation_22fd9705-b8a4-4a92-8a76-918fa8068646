'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, ArrowLeft, Bug } from 'lucide-react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log error to error reporting service
    console.error('Route segment error:', error);
  }, [error]);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#0a0a0f] via-[#0f0f0f] to-[#1a1a2e]">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-40 -top-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-orange-500/10 to-yellow-500/10 blur-3xl" />
        <div className="absolute -bottom-40 -left-40 size-80 animate-pulse rounded-full bg-gradient-to-br from-purple-500/10 to-pink-500/10 blur-3xl delay-1000" />
        <div className="absolute left-1/2 top-1/2 size-96 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-gradient-to-br from-violet-500/5 to-fuchsia-500/5 blur-3xl delay-500" />
      </div>

      <div className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-2xl">
          {/* Error card */}
          <Card className="border-[#3b3b3b]/50 bg-gradient-to-br from-[#1a1a2e]/80 via-[#262131]/80 to-[#1a1a2e]/80 shadow-2xl backdrop-blur-xl">
            <CardContent className="p-8">
              {/* Header */}
              <div className="mb-8 text-center">
                <div className="relative mb-4 inline-flex size-16 items-center justify-center">
                  <div className="absolute inset-0 animate-pulse rounded-full bg-gradient-to-br from-orange-500/20 to-yellow-500/20 blur-xl" />
                  <div className="relative rounded-full border border-orange-500/20 bg-gradient-to-br from-orange-500/10 to-yellow-500/10 p-4">
                    <AlertTriangle className="size-8 text-orange-400" />
                  </div>
                </div>

                <h1 className="mb-2 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-3xl font-bold text-transparent">
                  Oops! Something went wrong
                </h1>

                <p className="text-gray-400">
                  We encountered an error while loading this page. Please try again.
                </p>
              </div>

              {/* Error details in development */}
              {process.env.NODE_ENV === 'development' && (
                <Card className="mb-6 border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-yellow-500/5">
                  <CardContent className="p-4">
                    <div className="mb-3 flex items-center gap-2">
                      <Bug className="size-4 text-orange-400" />
                      <span className="text-sm font-medium text-white">Error Details</span>
                      <span className="ml-auto rounded border border-orange-500/30 bg-orange-500/20 px-2 py-1 text-xs text-orange-300">
                        DEV
                      </span>
                    </div>
                    <code className="block text-xs text-orange-300">{error.message}</code>
                  </CardContent>
                </Card>
              )}

              {/* Actions */}
              <div className="space-y-4">
                <Button
                  onClick={reset}
                  className="w-full border-0 bg-gradient-to-r from-orange-600 to-yellow-600 text-white transition-all duration-300 hover:from-orange-700 hover:to-yellow-700"
                >
                  <RefreshCw className="mr-2 size-4" />
                  Try Again
                </Button>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    asChild
                    variant="outline"
                    className="border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                  >
                    <Link href="/" className="flex items-center justify-center gap-2">
                      <Home className="size-4" />
                      <span>Home</span>
                    </Link>
                  </Button>

                  <Button
                    onClick={() => globalThis.history.back()}
                    variant="outline"
                    className="border-[#3b3b3b] text-gray-300 transition-all duration-300 hover:border-white/20 hover:bg-white/5 hover:text-white"
                  >
                    <ArrowLeft className="mr-2 size-4" />
                    Back
                  </Button>
                </div>
              </div>

              {/* Help text */}
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500">
                  If this problem persists, please{' '}
                  <Link
                    href="/feedback"
                    className="text-orange-400 transition-colors hover:text-orange-300"
                  >
                    contact support
                  </Link>
                  .
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
