# UserAlreadyAuthenticatedException Workaround

This document describes the implementation of a workaround for the AWS Cognito `UserAlreadyAuthenticatedException` error that occurs when multiple users try to authenticate in a server-side environment.

## Problem

When using AWS Amplify in a server-side environment (like Next.js server actions), <PERSON><PERSON><PERSON> may throw a `UserAlreadyAuthenticatedException` when:
1. A previous authentication session wasn't properly cleared
2. Multiple users authenticate in rapid succession
3. The server-side token storage has conflicting state

## Solution

The solution involves creating an `AuthWrapper` class that:

1. **Automatically clears authentication state** before each operation
2. **Implements retry logic** for `UserAlreadyAuthenticatedException`
3. **Provides a centralized configuration** for all Amplify operations
4. **Handles token storage cleanup** to prevent conflicts

## Implementation

### AuthWrapper Class (`/lib/auth-wrapper.ts`)

The `AuthWrapper` class provides static methods for all authentication operations:

- `configure()` - Ensures Amplify is properly configured
- `signInWithRetry()` - <PERSON><PERSON> sign-in with automatic retry on conflicts
- `signUpWithRetry()` - <PERSON>les sign-up with state clearing
- `confirmSignUp()`, `resetPassword()`, etc. - Wrapper methods for other operations
- `forceClearAuthState()` - Private method to clear all authentication state

### Key Features

1. **Automatic Retry Logic**
   ```typescript
   static async signInWithRetry(username: string, password: string, maxRetries = 2)
   ```

2. **State Management**
   - Clears token storage before operations
   - Resets Amplify configuration when conflicts occur
   - Ensures clean state for each authentication attempt

3. **Error Handling**
   - Catches `UserAlreadyAuthenticatedException` specifically
   - Implements exponential backoff for retries
   - Provides meaningful error messages

## Usage

### Migrating Existing Code

**Before:**
```typescript
import { signIn } from '@aws-amplify/auth';

const result = await signIn({ username: email, password });
```

**After:**
```typescript
import { AuthWrapper } from '@/lib/auth-wrapper';

const result = await AuthWrapper.signInWithRetry(email, password);
```

### Server Actions

All server actions in `/app/(auth)/legacy-actions.ts` have been updated to use the `AuthWrapper`:

- `authenticateUser()` - Uses `AuthWrapper.signInWithRetry()`
- `registerUser()` - Uses `AuthWrapper.signUpWithRetry()`
- `verifyEmail()` - Uses `AuthWrapper.confirmSignUp()`
- `logout()` - Uses `AuthWrapper.signOut()`

## Testing

The `/lib/auth-wrapper-test.ts` file provides utilities for testing the workaround:

```typescript
import { AuthWrapperTest } from '@/lib/auth-wrapper-test';

// Test retry logic
await AuthWrapperTest.testSignInRetry();

// Test rapid authentication attempts
await AuthWrapperTest.testRapidAuthAttempts();

// Test configuration clearing
await AuthWrapperTest.testConfigurationClearing();
```

## Benefits

1. **Eliminates `UserAlreadyAuthenticatedException`** errors in server-side environments
2. **Maintains session isolation** between different users
3. **Provides automatic recovery** from authentication conflicts
4. **Centralizes authentication logic** for better maintainability
5. **Includes comprehensive logging** for debugging

## Error Handling

The wrapper handles the following scenarios:

- `UserAlreadyAuthenticatedException` - Clears state and retries
- Other Cognito errors - Passes through with proper error messages
- Configuration errors - Attempts recovery with fresh configuration
- Token storage errors - Continues operation with warnings

## Logging

The wrapper provides detailed logging at each step:

```
🔧 [AUTH_WRAPPER] Configuring Amplify with fresh state...
🔧 [AUTH_WRAPPER] Cleared global auth state
🔧 [AUTH_WRAPPER] Cleared token storage
✅ [AUTH_WRAPPER] Amplify configured successfully
🔐 [AUTH_WRAPPER] Sign in attempt 1 for <EMAIL>
✅ [AUTH_WRAPPER] Sign in successful
```

This helps with debugging authentication issues in production environments.