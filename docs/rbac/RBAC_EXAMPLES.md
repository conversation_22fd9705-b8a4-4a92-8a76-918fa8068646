# RBAC Implementation Examples

## Real-World Implementation Examples for Multi-Organization RBAC

This document provides practical, copy-paste examples of implementing the RBAC system in various scenarios.

## 🏢 Scenario: Multi-Tenant SaaS Platform

### User Profile

- **Email**: `<EMAIL>`
- **Organization A**: `myneworg` → **admin** role
- **Organization B**: `Partner Dashboard Org` → **owner** role

### Role Hierarchy

| Role           | Level | Can Manage                     | Permissions Count |
| -------------- | ----- | ------------------------------ | ----------------- |
| **Owner**      | 6     | All roles below                | 11 permissions    |
| **SuperAdmin** | 5     | Admin, Manager, Member, Viewer | 11 permissions    |
| **Admin**      | 4     | Manager, Member, Viewer        | 11 permissions    |
| **Manager**    | 3     | Member, Viewer                 | 7 permissions     |
| **Member**     | 2     | None                           | 5 permissions     |
| **Viewer**     | 1     | None                           | 4 permissions     |

### Expected Behavior

- **Global permissions**: Combined admin + owner permissions (all 11 permissions)
- **Org A permissions**: Only admin permissions (11 permissions)
- **Org B permissions**: Only owner permissions (11 permissions)

## 🎯 Example 1: Dashboard with Organization Switching

```typescript
// components/dashboard/multi-org-dashboard.tsx
'use client';

import React from 'react';
import { useOrgContext } from '@/components/org/org-context-provider';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';

export function MultiOrgDashboard() {
  const {
    currentOrgId,
    organizations,
    switchOrganization,
    isLoading: orgLoading
  } = useOrgContext();

  const {
    roles,
    permissions,
    currentOrgRole,
    isOrgSpecific,
    hasPermission,
    isLoading: permissionsLoading,
    source,
    cached
  } = useOrgPermissions();

  if (orgLoading || permissionsLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Organization Switcher */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              Partner Dashboard
            </h1>

            {/* Organization Switcher */}
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">
                Organization:
              </label>
              <select
                value={currentOrgId || ''}
                onChange={(e) => switchOrganization(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Select Organization</option>
                {organizations.map((org) => (
                  <option key={org.orgId} value={org.orgId}>
                    {org.orgName} ({org.role})
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Current Context Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            Current Context
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-blue-700">Organization:</span>
              <p className="text-blue-900">{currentOrgId || 'None'}</p>
            </div>
            <div>
              <span className="font-medium text-blue-700">Your Role:</span>
              <p className="text-blue-900">{currentOrgRole || 'None'}</p>
            </div>
            <div>
              <span className="font-medium text-blue-700">Permissions:</span>
              <p className="text-blue-900">{permissions.length} total</p>
            </div>
            <div>
              <span className="font-medium text-blue-700">Source:</span>
              <p className="text-blue-900">{source} {cached ? '(cached)' : '(fresh)'}</p>
            </div>
          </div>
        </div>

        {/* Feature Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

          {/* Device Management */}
          <FeatureCard
            title="Device Management"
            description="Manage quantum devices and configurations"
            permission={Permission.ManageDevices}
            href="/devices"
            icon="🖥️"
          />

          {/* Team Management */}
          <FeatureCard
            title="Team Management"
            description="Manage team members and roles"
            permission={Permission.ManageTeam}
            href="/team"
            icon="👥"
          />

          {/* Earnings Dashboard */}
          <FeatureCard
            title="Earnings Dashboard"
            description="View financial reports and analytics"
            permission={Permission.ViewEarnings}
            href="/earnings"
            icon="💰"
          />

          {/* Job Management */}
          <FeatureCard
            title="Job Management"
            description="Monitor and manage quantum jobs"
            permission={Permission.ManageJobs}
            href="/jobs"
            icon="⚡"
          />

          {/* Admin Panel */}
          <OrgPermissionGuard
            role={['admin', 'owner']}
            fallback={
              <DisabledFeatureCard
                title="Admin Panel"
                description="Administrative controls"
                reason="Admin or Owner role required"
                icon="⚙️"
              />
            }
          >
            <FeatureCard
              title="Admin Panel"
              description="Administrative controls and settings"
              href="/admin"
              icon="⚙️"
              variant="admin"
            />
          </OrgPermissionGuard>

          {/* Profile Settings */}
          <FeatureCard
            title="Profile Settings"
            description="Manage your profile and preferences"
            permission={Permission.ViewProfile}
            href="/profile"
            icon="👤"
          />
        </div>

        {/* Permission Debug Panel (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <PermissionDebugPanel
            roles={roles}
            permissions={permissions}
            isOrgSpecific={isOrgSpecific}
            currentOrgRole={currentOrgRole}
          />
        )}
      </div>
    </div>
  );
}

// Feature Card Component
interface FeatureCardProps {
  title: string;
  description: string;
  permission?: Permission;
  role?: string | string[];
  href?: string;
  icon: string;
  variant?: 'default' | 'admin';
}

function FeatureCard({
  title,
  description,
  permission,
  role,
  href,
  icon,
  variant = 'default'
}: FeatureCardProps) {
  const { hasPermission, hasAnyRole } = useOrgPermissions();

  // Check access
  const hasAccess = permission
    ? hasPermission(permission)
    : role
    ? hasAnyRole(Array.isArray(role) ? role : [role])
    : true;

  if (!hasAccess) {
    return (
      <DisabledFeatureCard
        title={title}
        description={description}
        reason={permission ? `Permission required: ${permission}` : `Role required: ${role}`}
        icon={icon}
      />
    );
  }

  const cardClass = variant === 'admin'
    ? 'bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:from-purple-100 hover:to-purple-200'
    : 'bg-white border-gray-200 hover:bg-gray-50';

  return (
    <a
      href={href}
      className={`block p-6 rounded-lg border transition-colors ${cardClass}`}
    >
      <div className="flex items-center mb-3">
        <span className="text-2xl mr-3">{icon}</span>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      <p className="text-gray-600 text-sm">{description}</p>
      <div className="mt-4 flex items-center text-sm text-blue-600">
        <span>Access granted</span>
        <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>
    </a>
  );
}

// Disabled Feature Card
function DisabledFeatureCard({
  title,
  description,
  reason,
  icon
}: {
  title: string;
  description: string;
  reason: string;
  icon: string;
}) {
  return (
    <div className="p-6 rounded-lg border border-gray-200 bg-gray-50 opacity-60">
      <div className="flex items-center mb-3">
        <span className="text-2xl mr-3 grayscale">{icon}</span>
        <h3 className="text-lg font-semibold text-gray-500">{title}</h3>
      </div>
      <p className="text-gray-500 text-sm mb-3">{description}</p>
      <div className="flex items-center text-sm text-red-600">
        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span>{reason}</span>
      </div>
    </div>
  );
}

// Dashboard Skeleton
function DashboardSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          </div>
        </div>
      </header>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-white p-6 rounded-lg border">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-3"></div>
                <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Permission Debug Panel (Development)
function PermissionDebugPanel({
  roles,
  permissions,
  isOrgSpecific,
  currentOrgRole
}: {
  roles: string[];
  permissions: Permission[];
  isOrgSpecific: boolean;
  currentOrgRole: string | null;
}) {
  return (
    <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-yellow-900 mb-3">
        🔧 Debug Panel (Development)
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <h4 className="font-medium text-yellow-800 mb-2">Current State</h4>
          <ul className="space-y-1 text-yellow-700">
            <li>Org-Specific: {isOrgSpecific ? 'Yes' : 'No'}</li>
            <li>Current Role: {currentOrgRole || 'None'}</li>
            <li>Roles: [{roles.join(', ')}]</li>
            <li>Permissions: {permissions.length} total</li>
          </ul>
        </div>
        <div>
          <h4 className="font-medium text-yellow-800 mb-2">All Permissions</h4>
          <div className="max-h-32 overflow-y-auto">
            {permissions.map((permission) => (
              <div key={permission} className="text-xs text-yellow-600 font-mono">
                {permission}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 🎯 Example 2: API Route Protection

```typescript
// app/api/devices/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { getUserPermissionsForOrg } from '@/lib/permissions';
import { Permission } from '@/types/auth';

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getSession(request);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get organization from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');

    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Check permissions for specific organization
    const permissions = await getUserPermissionsForOrg(session.user.email, orgId);

    if (!permissions.includes(Permission.ViewDevices)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view devices' },
        { status: 403 },
      );
    }

    // Fetch devices for organization
    const devices = await getDevicesForOrganization(orgId);

    return NextResponse.json({
      success: true,
      devices,
      orgId,
      permissions: permissions.length,
    });
  } catch (error) {
    console.error('Device API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { orgId, deviceData } = body;

    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Check management permissions
    const permissions = await getUserPermissionsForOrg(session.user.email, orgId);

    if (!permissions.includes(Permission.ManageDevices)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage devices' },
        { status: 403 },
      );
    }

    // Create device
    const device = await createDeviceForOrganization(orgId, deviceData);

    return NextResponse.json({
      success: true,
      device,
      message: 'Device created successfully',
    });
  } catch (error) {
    console.error('Device creation error:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}
```

## 🎯 Example 3: Middleware for Route Protection

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { getUserPermissionsForOrg } from '@/lib/permissions';
import { Permission } from '@/types/auth';

// Route permission mapping
const ROUTE_PERMISSIONS: Record<string, Permission> = {
  '/devices': Permission.ViewDevices,
  '/devices/manage': Permission.ManageDevices,
  '/team': Permission.ViewTeam,
  '/team/manage': Permission.ManageTeam,
  '/earnings': Permission.ViewEarnings,
  '/earnings/manage': Permission.ManageEarnings,
  '/jobs': Permission.ViewJobs,
  '/jobs/manage': Permission.ManageJobs,
  '/admin': Permission.AdminAccess,
};

// Admin-only routes
const ADMIN_ROUTES = ['/admin', '/settings/organization'];

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  try {
    // Get user session
    const session = await getSession(request);
    if (!session?.user?.email) {
      return redirectToLogin(request);
    }

    // Get current organization from cookie or header
    const orgId = getCurrentOrgId(request);
    if (!orgId) {
      return redirectToOrgSelection(request);
    }

    // Check route-specific permissions
    const requiredPermission = getRequiredPermission(pathname);
    if (requiredPermission) {
      const permissions = await getUserPermissionsForOrg(session.user.email, orgId);

      if (!permissions.includes(requiredPermission)) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }
    }

    // Check admin routes
    if (isAdminRoute(pathname)) {
      const userRole = await getUserRoleInOrg(session.user.email, orgId);
      if (!['admin', 'owner'].includes(userRole)) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }
    }

    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.redirect(new URL('/error', request.url));
  }
}

function isPublicRoute(pathname: string): boolean {
  const publicRoutes = ['/login', '/signup', '/forgot-password', '/api/auth'];
  return publicRoutes.some((route) => pathname.startsWith(route));
}

function getCurrentOrgId(request: NextRequest): string | null {
  // Try cookie first
  const orgCookie = request.cookies.get('current-org-id');
  if (orgCookie?.value) return orgCookie.value;

  // Try header
  const orgHeader = request.headers.get('x-org-id');
  if (orgHeader) return orgHeader;

  return null;
}

function getRequiredPermission(pathname: string): Permission | null {
  // Exact match first
  if (ROUTE_PERMISSIONS[pathname]) {
    return ROUTE_PERMISSIONS[pathname];
  }

  // Pattern matching for dynamic routes
  for (const [route, permission] of Object.entries(ROUTE_PERMISSIONS)) {
    if (pathname.startsWith(route)) {
      return permission;
    }
  }

  return null;
}

function isAdminRoute(pathname: string): boolean {
  return ADMIN_ROUTES.some((route) => pathname.startsWith(route));
}

function redirectToLogin(request: NextRequest): NextResponse {
  const loginUrl = new URL('/login', request.url);
  loginUrl.searchParams.set('callbackUrl', request.url);
  return NextResponse.redirect(loginUrl);
}

function redirectToOrgSelection(request: NextRequest): NextResponse {
  const orgUrl = new URL('/select-organization', request.url);
  orgUrl.searchParams.set('callbackUrl', request.url);
  return NextResponse.redirect(orgUrl);
}

export const config = {
  matcher: ['/((?!api/auth|_next/static|_next/image|favicon.ico).*)'],
};
```

## 🎯 Example 4: React Hook for Complex Permission Logic

```typescript
// hooks/use-advanced-permissions.tsx
'use client';

import { useMemo } from 'react';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';

export interface AdvancedPermissionCheck {
  canViewDevices: boolean;
  canManageDevices: boolean;
  canViewTeam: boolean;
  canManageTeam: boolean;
  canViewEarnings: boolean;
  canManageEarnings: boolean;
  canAccessAdmin: boolean;
  isOrgAdmin: boolean;
  isOrgOwner: boolean;
  isOrgManager: boolean;
  isOrgSuperAdmin: boolean;
  hasFullAccess: boolean;
  accessLevel: 'none' | 'viewer' | 'member' | 'manager' | 'admin' | 'superadmin' | 'owner';
}

export function useAdvancedPermissions(orgId?: string): AdvancedPermissionCheck {
  const { hasPermission, hasRole, currentOrgRole, isLoading, hasError } = useOrgPermissions(orgId);

  return useMemo(() => {
    if (isLoading || hasError) {
      return {
        canViewDevices: false,
        canManageDevices: false,
        canViewTeam: false,
        canManageTeam: false,
        canViewEarnings: false,
        canManageEarnings: false,
        canAccessAdmin: false,
        isOrgAdmin: false,
        isOrgOwner: false,
        isOrgManager: false,
        isOrgSuperAdmin: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
      };
    }

    const canViewDevices = hasPermission(Permission.ViewDevices);
    const canManageDevices = hasPermission(Permission.ManageDevices);
    const canViewTeam = hasPermission(Permission.ViewTeam);
    const canManageTeam = hasPermission(Permission.ManageTeam);
    const canViewEarnings = hasPermission(Permission.ViewEarnings);
    const canManageEarnings = hasPermission(Permission.ManageEarnings);
    const canAccessAdmin = hasPermission(Permission.AdminAccess);

    const isOrgAdmin = hasRole('admin');
    const isOrgOwner = hasRole('owner');
    const isOrgManager = hasRole('manager');
    const isOrgSuperAdmin = hasRole('superadmin');
    const hasFullAccess = isOrgAdmin || isOrgOwner || isOrgSuperAdmin;

    // Determine access level based on role hierarchy
    let accessLevel: AdvancedPermissionCheck['accessLevel'] = 'none';
    if (isOrgOwner) {
      accessLevel = 'owner';
    } else if (isOrgSuperAdmin) {
      accessLevel = 'superadmin';
    } else if (isOrgAdmin) {
      accessLevel = 'admin';
    } else if (isOrgManager) {
      accessLevel = 'manager';
    } else if (canManageDevices || canManageTeam || canManageEarnings) {
      accessLevel = 'member';
    } else if (canViewDevices || canViewTeam || canViewEarnings) {
      accessLevel = 'viewer';
    }

    return {
      canViewDevices,
      canManageDevices,
      canViewTeam,
      canManageTeam,
      canViewEarnings,
      canManageEarnings,
      canAccessAdmin,
      isOrgAdmin,
      isOrgOwner,
      isOrgManager,
      isOrgSuperAdmin,
      hasFullAccess,
      accessLevel,
    };
  }, [hasPermission, hasRole, currentOrgRole, isLoading, hasError]);
}

// Usage example
export function useFeatureAccess(orgId?: string) {
  const permissions = useAdvancedPermissions(orgId);

  return {
    ...permissions,

    // Computed feature access
    canAccessDashboard: permissions.accessLevel !== 'none',
    canManageAnyResource: permissions.canManageDevices || permissions.canManageTeam,
    canViewFinancials: permissions.canViewEarnings,
    canManageFinancials: permissions.canManageEarnings,

    // UI helpers
    showAdminMenu: permissions.hasFullAccess,
    showManagementTools: ['manager', 'admin', 'superadmin', 'owner'].includes(
      permissions.accessLevel,
    ),
    showBasicTools: permissions.accessLevel !== 'none',

    // Access level descriptions
    accessLevelDescription: {
      none: 'No access',
      viewer: 'Read-only access',
      member: 'Limited management access',
      manager: 'Team management capabilities',
      admin: 'Full administrative access',
      superadmin: 'System-wide administration',
      owner: 'Complete organizational control',
    }[permissions.accessLevel],
  };
}
```

## 🎯 Example 5: Testing Organization-Specific Permissions

```typescript
// __tests__/rbac.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { OrgContextProvider } from '@/components/org/org-context-provider';
import { MultiOrgDashboard } from '@/components/dashboard/multi-org-dashboard';

// Mock the permissions API
jest.mock('@/hooks/use-permissions', () => ({
  useOrgPermissions: jest.fn(),
}));

const mockUseOrgPermissions = require('@/hooks/use-permissions').useOrgPermissions;

describe('RBAC System', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <OrgContextProvider>
          {component}
        </OrgContextProvider>
      </QueryClientProvider>
    );
  };

  describe('Organization-Specific Permissions', () => {
    it('should show admin features for admin role in org A', async () => {
      mockUseOrgPermissions.mockReturnValue({
        roles: ['admin'],
        permissions: [
          'view:devices', 'manage:devices', 'view:team', 'manage:team',
          'view:earnings', 'manage:earnings', 'view:jobs', 'manage:jobs',
          'admin:access'
        ],
        currentOrgRole: 'admin',
        isOrgSpecific: true,
        hasPermission: (permission: string) => true,
        hasRole: (role: string) => role === 'admin',
        isLoading: false,
        hasError: false,
      });

      renderWithProviders(<MultiOrgDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Admin Panel')).toBeInTheDocument();
        expect(screen.getByText('Device Management')).toBeInTheDocument();
        expect(screen.getByText('Team Management')).toBeInTheDocument();
      });
    });

    it('should hide admin features for member role', async () => {
      mockUseOrgPermissions.mockReturnValue({
        roles: ['member'],
        permissions: ['view:devices', 'view:earnings', 'view:jobs'],
        currentOrgRole: 'member',
        isOrgSpecific: true,
        hasPermission: (permission: string) =>
          ['view:devices', 'view:earnings', 'view:jobs'].includes(permission),
        hasRole: (role: string) => role === 'member',
        isLoading: false,
        hasError: false,
      });

      renderWithProviders(<MultiOrgDashboard />);

      await waitFor(() => {
        expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument();
        expect(screen.getByText('Admin or Owner role required')).toBeInTheDocument();
        expect(screen.getByText('Device Management')).toBeInTheDocument();
      });
    });

    it('should show different permissions for different organizations', async () => {
      // Test org switching behavior
      const mockSwitchOrg = jest.fn();

      // First render with org A (admin)
      mockUseOrgPermissions.mockReturnValue({
        roles: ['admin'],
        permissions: ['view:devices', 'manage:devices', 'admin:access'],
        currentOrgRole: 'admin',
        isOrgSpecific: true,
        hasPermission: (permission: string) => true,
        hasRole: (role: string) => role === 'admin',
        isLoading: false,
        hasError: false,
      });

      const { rerender } = renderWithProviders(<MultiOrgDashboard />);

      expect(screen.getByText('Admin Panel')).toBeInTheDocument();

      // Switch to org B (member)
      mockUseOrgPermissions.mockReturnValue({
        roles: ['member'],
        permissions: ['view:devices', 'view:earnings'],
        currentOrgRole: 'member',
        isOrgSpecific: true,
        hasPermission: (permission: string) =>
          ['view:devices', 'view:earnings'].includes(permission),
        hasRole: (role: string) => role === 'member',
        isLoading: false,
        hasError: false,
      });

      rerender(<MultiOrgDashboard />);

      await waitFor(() => {
        expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument();
        expect(screen.getByText('Admin or Owner role required')).toBeInTheDocument();
      });
    });
  });
});
```

## 🎯 Example 6: Role Management with Complete Hierarchy Validation

```typescript
// Complete role hierarchy validation
const ROLE_HIERARCHY = {
  viewer: 1,
  member: 2,
  manager: 3,
  admin: 4,
  superadmin: 5,
  owner: 6,
};

const ROLE_MANAGEMENT_PERMISSIONS = {
  owner: ['viewer', 'member', 'manager', 'admin', 'superadmin'],
  superadmin: ['viewer', 'member', 'manager', 'admin'],
  admin: ['viewer', 'member', 'manager'],
  manager: ['viewer', 'member'],
  member: [],
  viewer: [],
};

// Check if user can manage another user's role
export const canManageUserRole = (
  currentUserRole: string,
  targetUserRole: string,
  newRole: string
): { canManage: boolean; reason?: string } => {
  const currentLevel = ROLE_HIERARCHY[currentUserRole.toLowerCase()] || 0;
  const targetLevel = ROLE_HIERARCHY[targetUserRole.toLowerCase()] || 0;
  const newLevel = ROLE_HIERARCHY[newRole.toLowerCase()] || 0;

  // Cannot manage users at or above your level
  if (targetLevel >= currentLevel) {
    return {
      canManage: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  // Cannot assign roles at or above your level
  if (newLevel >= currentLevel) {
    return {
      canManage: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  // Check if current user can manage the new role
  const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[currentUserRole.toLowerCase()] || [];
  if (!managableRoles.includes(newRole.toLowerCase())) {
    return {
      canManage: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  return { canManage: true };
};

// Usage in team management component
export function TeamMemberRoleChanger({
  currentUser,
  targetUser,
  onRoleChange
}: {
  currentUser: { role: string };
  targetUser: { email: string; role: string };
  onRoleChange: (email: string, newRole: string) => void;
}) {
  const availableRoles = useMemo(() => {
    const managableRoles = ROLE_MANAGEMENT_PERMISSIONS[currentUser.role.toLowerCase()] || [];
    return managableRoles.map(role => ({
      value: role,
      label: role.charAt(0).toUpperCase() + role.slice(1),
      level: ROLE_HIERARCHY[role]
    })).sort((a, b) => a.level - b.level);
  }, [currentUser.role]);

  const handleRoleChange = (newRole: string) => {
    const validation = canManageUserRole(currentUser.role, targetUser.role, newRole);

    if (!validation.canManage) {
      alert(validation.reason);
      return;
    }

    onRoleChange(targetUser.email, newRole);
  };

  return (
    <Select onValueChange={handleRoleChange} defaultValue={targetUser.role}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {availableRoles.map(role => (
          <SelectItem key={role.value} value={role.value}>
            {role.label} (Level {role.level})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Test cases for role management
describe('Role Management', () => {
  test('owner can manage all roles', () => {
    expect(canManageUserRole('owner', 'admin', 'manager')).toEqual({ canManage: true });
    expect(canManageUserRole('owner', 'member', 'admin')).toEqual({ canManage: true });
  });

  test('admin cannot manage owner or superadmin', () => {
    expect(canManageUserRole('admin', 'owner', 'member')).toEqual({
      canManage: false,
      reason: "You don't have permission to manage users with owner role"
    });
    expect(canManageUserRole('admin', 'superadmin', 'member')).toEqual({
      canManage: false,
      reason: "You don't have permission to manage users with superadmin role"
    });
  });

  test('manager can only manage member and viewer', () => {
    expect(canManageUserRole('manager', 'member', 'viewer')).toEqual({ canManage: true });
    expect(canManageUserRole('manager', 'admin', 'member')).toEqual({
      canManage: false,
      reason: "You don't have permission to manage users with admin role"
    });
  });

  test('member and viewer cannot manage any roles', () => {
    expect(canManageUserRole('member', 'viewer', 'member')).toEqual({
      canManage: false,
      reason: "You don't have permission to assign member role"
    });
  });
});
```

## 🎯 Summary

These examples demonstrate:

1. **🏢 Multi-Organization Dashboard**: Complete UI with organization switching and permission-based feature access
2. **🔒 API Route Protection**: Server-side permission validation for secure endpoints
3. **🛡️ Middleware Protection**: Route-level access control with automatic redirects
4. **🎯 Advanced Permission Hooks**: Complex permission logic with computed access levels
5. **🧪 Comprehensive Testing**: Unit tests for organization-specific permission scenarios
6. **👥 Role Management**: Complete hierarchy validation with role change permissions

### Key Features Covered:

- **6-Tier Role Hierarchy**: Viewer → Member → Manager → Admin → SuperAdmin → Owner
- **Granular Permissions**: 11 distinct permissions across different resource types
- **Multi-Organization Support**: Context-aware permissions per organization
- **Real-time Updates**: Automatic cache invalidation and UI updates
- **Security-First Design**: Server-side validation with client-side guards
- **Developer Experience**: Type-safe hooks and comprehensive error handling

The system provides a complete, production-ready RBAC implementation that scales to enterprise requirements while maintaining developer-friendly APIs and ensuring security best practices.
