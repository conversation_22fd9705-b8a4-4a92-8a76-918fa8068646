# Bug Report Feature

This feature allows users to easily report bugs directly from the dashboard. Reports are sent to a Slack channel for development team review.

## Components

### 1. Floating Bug Report Button (`/components/ui/bug-report-button.tsx`)
- A red circular button fixed in the bottom-right corner of the dashboard
- Opens a modal form when clicked
- Auto-fills user information (email, route, browser info)
- Collects bug details including:
  - Title (required)
  - Description (required)
  - Steps to reproduce (optional)
  - Expected behavior (optional)
  - Actual behavior (optional)

### 2. API Endpoint (`/app/api/bug-report/route.ts`)
- Handles POST requests for bug reports
- Formats and sends notifications to Slack
- Includes all relevant information in the Slack message

## Features

- **Auto-filled Information**: User email, current route, browser info, and timestamp are automatically captured
- **Slack Integration**: Bug reports are sent to a configured Slack channel
- **Responsive Design**: Works on all screen sizes
- **User-Friendly**: Simple form with clear labels and validation
- **Error Handling**: Graceful error handling with user feedback

## Setup

1. Ensure `SLACK_WEBHOOK_URL` environment variable is set
2. The bug report button is automatically included in the dashboard layout
3. No additional configuration needed

## Testing

Use the test script to verify the bug report functionality:

```bash
node scripts/test-bug-report.js
```

## Slack Message Format

The Slack notification includes:
- Bug title and description
- Reporter's email
- Current route where the bug was reported
- Browser information
- Timestamp
- Steps to reproduce (if provided)
- Expected vs actual behavior (if provided)

## Security

- All user information is handled securely
- No sensitive data is logged
- Slack webhook URL is stored in environment variables