// Examples of how to use the reusable image upload service in different API routes

import { 
  uploadImageToS3, 
  uploadMultipleImagesToS3, 
  extractFilesFromFormData,
  createUploadConfig 
} from '@/lib/image-upload';

// ==========================================
// Example 1: User Avatar Upload API
// ==========================================

// app/api/users/avatar/route.ts
export async function POST(request: NextRequest) {
  const session = await getSession();
  const formData = await request.formData();
  const avatarFile = formData.get('avatar') as File;

  if (!avatarFile) {
    return NextResponse.json({ error: 'Avatar file is required' }, { status: 400 });
  }

  const uploadConfig = createUploadConfig(
    {
      orgId: `org-${session.userId}`,
      userId: session.userId,
      requestId: generateRequestId(),
    },
    'user-avatar',
    {
      maxFileSize: 2 * 1024 * 1024, // 2MB for avatars
    }
  );

  const result = await uploadImageToS3(avatarFile, uploadConfig);

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  // Update user profile with new avatar URL
  await updateUserProfile(session.userId, { avatarUrl: result.url });

  return NextResponse.json({
    success: true,
    avatarUrl: result.url,
    metadata: result.metadata,
  });
}

// ==========================================
// Example 2: Product Images Upload API
// ==========================================

// app/api/products/images/route.ts
export async function POST(request: NextRequest) {
  const session = await getSession();
  const formData = await request.formData();
  const productId = formData.get('productId') as string;

  // Extract multiple product images
  const files = extractFilesFromFormData(formData, [
    'mainImage',
    'thumbnailImage',
    'galleryImage1',
    'galleryImage2',
    'galleryImage3'
  ]);

  if (files.length === 0) {
    return NextResponse.json({ error: 'At least one image is required' }, { status: 400 });
  }

  const uploadConfig = createUploadConfig(
    {
      orgId: `org-${session.userId}`,
      userId: session.userId,
      requestId: generateRequestId(),
    },
    'product-images'
  );

  // Upload all product images in batch
  const batchResult = await uploadMultipleImagesToS3(files, uploadConfig);

  if (!batchResult.success) {
    return NextResponse.json({
      error: 'Failed to upload product images',
      details: batchResult.errors,
    }, { status: 400 });
  }

  // Process results and update product
  const imageUrls: Record<string, string> = {};
  batchResult.results.forEach(({ fieldName, result }) => {
    if (result.success && result.url) {
      imageUrls[fieldName] = result.url;
    }
  });

  // Update product with image URLs
  await updateProduct(productId, { images: imageUrls });

  return NextResponse.json({
    success: true,
    productId,
    images: imageUrls,
    uploadStats: {
      totalFiles: files.length,
      successfulUploads: batchResult.results.filter(r => r.result.success).length,
    },
  });
}

// ==========================================
// Example 3: Organization Logo Upload API
// ==========================================

// app/api/organizations/logo/route.ts
export async function POST(request: NextRequest) {
  const session = await getSession();
  const formData = await request.formData();
  
  // Extract logo files
  const files = extractFilesFromFormData(formData, ['logo', 'logoDark', 'favicon']);
  
  if (files.length === 0) {
    return NextResponse.json({ error: 'At least one logo file is required' }, { status: 400 });
  }

  const results: Record<string, string> = {};
  const errors: string[] = [];

  // Upload each logo type with specific configurations
  for (const { fieldName, file } of files) {
    let uploadConfig;

    // Customize config based on logo type
    switch (fieldName) {
      case 'favicon':
        uploadConfig = createUploadConfig(
          {
            orgId: session.orgId,
            userId: session.userId,
            requestId: generateRequestId(),
          },
          'org-favicon',
          {
            maxFileSize: 1 * 1024 * 1024, // 1MB for favicon
            allowedTypes: ['image/png', 'image/x-icon', 'image/vnd.microsoft.icon'],
          }
        );
        break;
      
      default:
        uploadConfig = createUploadConfig(
          {
            orgId: session.orgId,
            userId: session.userId,
            requestId: generateRequestId(),
          },
          `org-${fieldName}`
        );
    }

    const result = await uploadImageToS3(file, uploadConfig);

    if (result.success && result.url) {
      results[fieldName] = result.url;
    } else {
      errors.push(`${fieldName}: ${result.error}`);
    }
  }

  if (Object.keys(results).length === 0) {
    return NextResponse.json({
      error: 'All uploads failed',
      details: errors,
    }, { status: 400 });
  }

  // Update organization with logo URLs
  await updateOrganization(session.orgId, {
    logoUrl: results.logo,
    logoDarkUrl: results.logoDark,
    faviconUrl: results.favicon,
  });

  return NextResponse.json({
    success: true,
    logos: results,
    ...(errors.length > 0 && { warnings: errors }),
  });
}

// ==========================================
// Example 4: Blog Post Images Upload API
// ==========================================

// app/api/blog/images/route.ts
export async function POST(request: NextRequest) {
  const session = await getSession();
  const formData = await request.formData();
  const postId = formData.get('postId') as string;

  // Extract blog images
  const files = extractFilesFromFormData(formData, [
    'featuredImage',
    'heroImage',
    'inlineImage1',
    'inlineImage2',
    'inlineImage3'
  ]);

  const uploadResults: Array<{
    fieldName: string;
    url: string;
    originalName: string;
  }> = [];

  // Upload each image individually with optimization
  for (const { fieldName, file } of files) {
    const uploadConfig = createUploadConfig(
      {
        orgId: `blog-${session.userId}`,
        userId: session.userId,
        requestId: `blog-${postId}-${Date.now()}`,
      },
      'blog-images'
    );

    const result = await uploadImageToS3(file, uploadConfig);

    if (result.success && result.url) {
      uploadResults.push({
        fieldName,
        url: result.url,
        originalName: file.name,
      });

      // If this is a featured image, generate thumbnail
      if (fieldName === 'featuredImage') {
        // You could add thumbnail generation here
        // const thumbnailResult = await generateThumbnail(result.url, uploadConfig);
      }
    }
  }

  return NextResponse.json({
    success: true,
    postId,
    images: uploadResults,
    totalUploaded: uploadResults.length,
  });
}

// ==========================================
// Helper Functions
// ==========================================

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

async function updateUserProfile(userId: string, data: any) {
  // Implementation depends on your database
  console.log('Updating user profile:', userId, data);
}

async function updateProduct(productId: string, data: any) {
  // Implementation depends on your database
  console.log('Updating product:', productId, data);
}

async function updateOrganization(orgId: string, data: any) {
  // Implementation depends on your database
  console.log('Updating organization:', orgId, data);
}

// ==========================================
// Configuration Examples
// ==========================================

// Different upload configurations for different use cases
export const UPLOAD_CONFIGS = {
  USER_AVATAR: {
    maxFileSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  
  PRODUCT_IMAGE: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  
  COMPANY_LOGO: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/png', 'image/svg+xml', 'image/webp'],
  },
  
  FAVICON: {
    maxFileSize: 1 * 1024 * 1024, // 1MB
    allowedTypes: ['image/png', 'image/x-icon', 'image/vnd.microsoft.icon'],
  },
  
  BLOG_IMAGE: {
    maxFileSize: 8 * 1024 * 1024, // 8MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  },
};