# Slack Notifications Integration

This feature sends Slack notifications for device-related requests and actions in the qbraid Partner Dashboard.

## Setup

### 1. Create a Slack Incoming Webhook

1. Go to your Slack workspace
2. Navigate to **Settings & administration** > **Manage apps**
3. Search for "Incoming Webhooks"
4. Click "Add Configuration"
5. Choose a channel where notifications will be sent
6. Click "Add Incoming WebHooks integration"
7. Copy the **Webhook URL**

### 2. Configure Environment Variables

Add the following to your `.env.local` file:

```bash
# Slack Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Test the Integration

Run the test script to verify your configuration:

```bash
node scripts/test-slack-notifications.js
```

## Notification Types

### 1. Device Access Requests
- **Trigger**: When a user requests access to a device
- **Includes**: User info, device details, justification
- **Status**: Pending, Approved, Denied

### 2. Device Add/Edit Requests
- **Trigger**: When a provider submits a new device or edits an existing one
- **Includes**: Device name, type, provider, organization
- **Status**: Pending, Approved, Denied

### 3. Admin Actions
- **Trigger**: When an admin approves or denies requests
- **Includes**: Action taken, admin notes, device details

## Testing

### Local Development

1. Ensure your `.env.local` has the Slack webhook URL
2. Run the test script:
   ```bash
   node scripts/test-slack-notifications.js
   ```
3. Check your Slack channel for test notifications

### Production Testing

Test notifications will be sent for actual device operations:
- Request device access through the dashboard
- Submit a new device (as a provider)
- Approve/deny requests (as an admin)

## Troubleshooting

### Notifications Not Sending

1. **Check webhook URL**: Ensure it's correctly copied to `.env.local`
2. **Verify permissions**: Webhook must have permission to post to the channel
3. **Check logs**: Look for error messages in the server console
4. **Test manually**: Use curl to test the webhook:
   ```bash
   curl -X POST -H 'Content-type: application/json' \
   --data '{"text":"Test message"}' \
   $SLACK_WEBHOOK_URL
   ```

### Error Messages

- **"Slack webhook not configured"**: Add SLACK_WEBHOOK_URL to your environment
- **"Slack API error"**: Check webhook URL and permissions
- **"Failed to send"**: Network or Slack service issues

## Customization

### Modify Notification Templates

Edit the message formats in `/lib/slack-notifications.ts`:
- Change colors, emojis, or field layouts
- Add additional information fields
- Customize based on request type

### Add New Notification Types

1. Add a new method to `SlackNotificationService` class
2. Define the message structure
3. Call the method from the appropriate API route

## Security Considerations

- Webhook URLs are sensitive - treat them like passwords
- Use environment variables - never commit to version control
- Notifications don't contain sensitive data
- Failed notifications don't block device operations

## Future Enhancements

- [ ] Add thread replies for follow-up actions
- [ ] Support for multiple Slack channels
- [ ] Interactive buttons for quick approvals
- [ ] Notification preferences per user/organization
- [ ] Detailed audit logging for notifications