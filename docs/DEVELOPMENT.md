# Development Setup Guide

## Quick Start

### Development Setup

1. **Start Redis**:
```bash
# Using Homebrew (macOS)
brew install redis && brew services start redis

# Using Docker
docker run -d -p 6379:6379 redis:alpine

# Or manually
redis-server
```

2. **Start External API Server**:
```bash
# In the API repository directory
cd qbraid-api
npm install
npm run dev
```

3. **Configure environment** in `.env.local`:
```bash
# Session storage
REDIS_URL=redis://localhost:6379
SESSION_STORAGE_MODE=redis

# API endpoints
EXTERNAL_API_URL=http://localhost:3001/api

# Authentication (if needed)
QBRAID_API_TOKEN=your-dev-token
```

4. **Start the dashboard**:
```bash
npm run dev
```

## Troubleshooting

### "Valid session required" error

This usually means:
1. Redis is not running
2. External API server is not accessible
3. Session cookies are invalid

**Solutions:**
- Check Redis: `redis-cli ping` should return "PONG"
- Check API: Visit `http://localhost:3001/api/health`
- Clear browser cookies for localhost

### Checking Development Health

Create a debug route to check your setup:

```typescript
// app/api/dev/debug/route.ts
import { checkDevEnvironment } from '@/lib/dev-utils';

export async function GET() {
  const health = await checkDevEnvironment();
  return Response.json(health);
}
```

Visit `/api/dev/debug` to see issues.

## Environment Variables

### Required for Production
```bash
REDIS_URL=redis://your-redis-url:6379
SESSION_STORAGE_MODE=redis
EXTERNAL_API_URL=https://api.qbraid.com/api
QBRAID_API_TOKEN=production-token
```

### Development Only
```bash
NODE_ENV=development
```

## Common Issues

1. **Port conflicts**: Ensure ports 3000 (dashboard) and 3001 (API) are available
2. **Redis permissions**: Make sure your Redis instance accepts connections
3. **CORS issues**: The API server must accept requests from localhost:3000
4. **Cookie domain**: In development, cookies work without domain settings

## Docker Development Setup

Create a `docker-compose.yml` for easy development:

```yaml
version: '3.8'
services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  api:
    build: ./qbraid-api
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
  
  dashboard:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - EXTERNAL_API_URL=http://api:3001/api
    depends_on:
      - redis
      - api
```

Run with:
```bash
docker-compose up
```