# External Admin Auth Implementation

## Overview

This document describes the implementation of a robust admin authentication system that integrates with an external API endpoint `/user/role` to verify admin status. The implementation provides a centralized, secure, and maintainable approach to admin access control throughout the qBraid partner dashboard application.

## Architecture

### Core Components

1. **External API Integration** (`/app/api/user/role/route.ts`)
   - Proxies requests to the external `/user/role` endpoint
   - <PERSON>les authentication and error logging
   - Provides audit logging for security tracking

2. **Custom Hooks** (`/hooks/use-admin-role.ts`)
   - `useExternalAdminRole`: Comprehensive admin role checking
   - `useIsAdmin`: Simplified boolean admin status
   - Built-in caching and error handling

3. **Auth Management Utilities** (`/lib/auth-admin-check.tsx`)
   - `useAdminAuth`: Enhanced admin auth with helper methods
   - `verifyAdminStatus`: Server-side admin verification
   - `withAdminAuth`: Higher-order component for route protection
   - Type guards and permission level utilities

4. **Updated Admin Page** (`/app/(dashboard)/admin/page.tsx`)
   - Integrated with new external auth system
   - Comprehensive error handling and loading states
   - Secure access control

## Implementation Details

### 1. External API Route

**File**: `/app/api/user/role/route.ts`

```typescript
export async function GET(request: NextRequest) {
  const { session, error } = await requireAuth();
  if (error) return error;
  
  try {
    // Call external API /user/role endpoint
    const response = await externalClient.get('/user/role');
    
    // Log the role check for audit purposes
    console.log(`✅ [USER-ROLE-API] Role check successful for ${session.email}:`, {
      role: response.data?.role,
      isAdmin: response.data?.role === 'admin',
      requestId: request.url,
    });

    return NextResponse.json(response.data, { status: response.status });
  } catch (error: any) {
    // Comprehensive error logging
    console.error('❌ [USER-ROLE-API] Error getting user role:', {
      error: error.message,
      status: error.status,
      email: session.email,
      requestId: request.url,
    });
    return NextResponse.json(
      { error: error.message || 'Failed to get user role' },
      { status: error.status || 500 },
    );
  }
}
```

**Features**:
- Secure authentication middleware integration
- Comprehensive error logging with context
- Audit trail for all admin role checks
- Proper HTTP status code handling

### 2. Custom Admin Hooks

**File**: `/hooks/use-admin-role.ts`

```typescript
export const useExternalAdminRole = () => {
  return useQuery<ExternalUserRoleResponse, Error>({
    queryKey: ['externalAdminRole'],
    queryFn: async () => {
      const response = await apiClient('/api/user/role');
      return {
        role: response.role,
        isAdmin: response.role === 'admin',
        permissions: response.permissions || [],
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once for admin checks
    refetchOnWindowFocus: false, // Security: don't refetch on focus
  });
};

export const useIsAdmin = () => {
  const { data: adminData, isLoading, isError } = useExternalAdminRole();
  
  return {
    isAdmin: adminData?.role === 'admin',
    isLoading,
    isError,
    adminRole: adminData?.role,
  };
};
```

**Features**:
- React Query integration for optimal caching
- Security-focused configuration (no refetch on focus)
- Type-safe interfaces for external API responses
- Comprehensive loading and error states

### 3. Auth Management Utilities

**File**: `/lib/auth-admin-check.tsx`

#### Comprehensive Admin Auth Hook

```typescript
export const useAdminAuth = () => {
  const { isAdmin, isLoading, isError, adminRole } = useIsAdmin();
  
  return {
    isAdmin,
    isLoading,
    isError,
    adminRole,
    // Helper methods for common use cases
    canAccessAdminPanel: isAdmin && !isLoading && !isError,
    getAdminStatusMessage: () => {
      if (isLoading) return 'Checking admin permissions...';
      if (isError) return 'Error verifying admin status';
      if (!isAdmin) return 'Access denied: Admin privileges required';
      return 'Admin access granted';
    },
  };
};
```

#### Server-side Verification

```typescript
export async function verifyAdminStatus(request: Request): Promise<{
  isAdmin: boolean;
  error?: string;
  userRole?: string;
}> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/user/role`, {
      headers: {
        Authorization: request.headers.get('authorization') || '',
        Cookie: request.headers.get('cookie') || '',
      },
    });

    if (!response.ok) {
      return { isAdmin: false, error: `Failed to verify admin status: ${response.status}` };
    }

    const data = await response.json();
    return {
      isAdmin: data.role === 'admin',
      userRole: data.role,
    };
  } catch (error) {
    return {
      isAdmin: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
```

#### Higher-Order Component for Route Protection

```typescript
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType | React.ReactElement
) {
  return function AdminProtectedComponent(props: P) {
    const { isAdmin, isLoading, isError } = useIsAdmin();

    if (isLoading) {
      return <LoadingSpinner />;
    }

    if (isError || !isAdmin) {
      return fallback ? <FallbackComponent /> : <AccessDenied />;
    }

    return <Component {...props} />;
  };
}
```

**Features**:
- Centralized admin auth logic
- Server-side and client-side verification
- Reusable higher-order components
- Type-safe permission levels
- Comprehensive error handling

### 4. Updated Admin Page Integration

**File**: `/app/(dashboard)/admin/page.tsx`

```typescript
// Check permissions and roles using external API with comprehensive auth management
const { isAdmin, isLoading: roleLoading, isError: roleError, adminRole } = useAdminAuth();
const { data: devices, isLoading: devicesLoading, isError: devicesError } = useAdminDevices();

// Loading state with proper error handling
if (isLoading) {
  return <LoadingState />;
}

// Access denied - check admin status using external API
if (!isAdmin) {
  return <AccessDenied />;
}
```

**Features**:
- Seamless integration with existing admin functionality
- Proper loading and error states
- Secure access control
- Maintained user experience

## Security Considerations

### 1. External API Security
- All requests are proxied through our API gateway
- Authentication is handled by our middleware
- External API responses are validated and sanitized
- Comprehensive error logging for security monitoring

### 2. Caching Strategy
- Admin role checks are cached for 5 minutes to balance performance and security
- Cache invalidation on role changes
- No refetch on window focus to prevent unnecessary API calls

### 3. Error Handling
- Graceful degradation on API failures
- No sensitive information leaked in error messages
- Comprehensive logging for debugging and security monitoring

### 4. Access Control
- Multiple layers of verification (client-side and server-side)
- Type-safe permission checking
- Secure by default approach

## Usage Examples

### 1. Basic Admin Check

```typescript
import { useIsAdmin } from '@/hooks/use-admin-role';

function AdminComponent() {
  const { isAdmin, isLoading, isError } = useIsAdmin();

  if (isLoading) return <LoadingSpinner />;
  if (isError || !isAdmin) return <AccessDenied />;
  
  return <AdminContent />;
}
```

### 2. Comprehensive Admin Auth

```typescript
import { useAdminAuth } from '@/lib/auth-admin-check';

function SecureAdminComponent() {
  const { isAdmin, canAccessAdminPanel, getAdminStatusMessage } = useAdminAuth();

  if (!canAccessAdminPanel) {
    return <div>{getAdminStatusMessage()}</div>;
  }

  return <SecureAdminContent />;
}
```

### 3. Protected Routes

```typescript
import { withAdminAuth } from '@/lib/auth-admin-check';

const ProtectedAdminPage = withAdminAuth(AdminDashboard);

// Or with custom fallback
const ProtectedAdminPage = withAdminAuth(AdminDashboard, CustomAccessDenied);
```

### 4. Server-side Verification

```typescript
import { verifyAdminStatus } from '@/lib/auth-admin-check';

export async function GET(request: Request) {
  const { isAdmin, error } = await verifyAdminStatus(request);
  
  if (!isAdmin) {
    return NextResponse.json({ error: error || 'Access denied' }, { status: 403 });
  }

  return NextResponse.json({ data: 'Sensitive admin data' });
}
```

## Migration Guide

### From Old System to New System

1. **Replace `useAdminRole` hook**:
   ```typescript
   // Old
   const { data: adminRole } = useAdminRole();
   const isAdmin = adminRole === 'admin';
   
   // New
   const { isAdmin } = useIsAdmin();
   // Or
   const { isAdmin, canAccessAdminPanel } = useAdminAuth();
   ```

2. **Update API calls**:
   ```typescript
   // Old
   const response = await apiClient('/api/user/get-role');
   
   // New
   const response = await apiClient('/api/user/role');
   ```

3. **Add route protection**:
   ```typescript
   // Wrap components that need admin access
   const ProtectedComponent = withAdminAuth(MyComponent);
   ```

4. **Add server-side verification**:
   ```typescript
   // In API routes
   const { isAdmin } = await verifyAdminStatus(request);
   if (!isAdmin) {
     return NextResponse.json({ error: 'Access denied' }, { status: 403 });
   }
   ```

## Testing

### Unit Tests
- Test admin role verification logic
- Test error handling scenarios
- Test caching behavior
- Test permission level checking

### Integration Tests
- Test external API integration
- Test admin page access control
- Test server-side verification
- Test fallback components

### Security Tests
- Test unauthorized access attempts
- Test API failure scenarios
- Test cache poisoning prevention
- Test error message sanitization

## Monitoring and Logging

### Key Metrics
- Admin role check success/failure rates
- API response times for external role checks
- Cache hit/miss ratios
- Error rates by type

### Log Examples
```typescript
// Successful admin check
console.log(`✅ [USER-ROLE-API] Role check successful for ${session.email}:`, {
  role: response.data?.role,
  isAdmin: response.data?.role === 'admin',
  requestId: request.url,
});

// Failed admin check
console.error('❌ [USER-ROLE-API] Error getting user role:', {
  error: error.message,
  status: error.status,
  email: session.email,
  requestId: request.url,
});
```

## Future Enhancements

### 1. Granular Permissions
- Implement role-based access control (RBAC)
- Add permission levels (read-only, full admin, etc.)
- Support for custom admin roles

### 2. Multi-factor Authentication
- Add MFA requirements for admin access
- Implement step-up authentication for sensitive operations
- Support for hardware security keys

### 3. Audit Logging
- Comprehensive audit trail for all admin actions
- Real-time monitoring and alerting
- Integration with SIEM systems

### 4. Rate Limiting
- Implement rate limiting for admin role checks
- Protect against brute force attacks
- Add IP-based restrictions

## Conclusion

The new external admin auth implementation provides a robust, secure, and maintainable solution for admin access control in the qBraid partner dashboard. By integrating with the external `/user/role` API and implementing comprehensive security measures, the system ensures that admin access is properly verified and monitored throughout the application.

The modular architecture allows for easy extension and maintenance, while the comprehensive error handling and logging ensure that issues can be quickly identified and resolved. The implementation follows security best practices and provides multiple layers of protection against unauthorized access.
