# Teams Page Implementation Guide

This document provides a comprehensive technical implementation guide for the Teams page in the partner dashboard, covering modern API integration patterns, state management, RBAC implementation, and production-ready best practices.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Modern API Integration](#modern-api-integration)
4. [State Management](#state-management)
5. [RBAC Permission System](#rbac-permission-system)
6. [UI Components](#ui-components)
7. [Data Flow](#data-flow)
8. [Error Handling](#error-handling)
9. [Performance Optimizations](#performance-optimizations)
10. [Testing Strategy](#testing-strategy)
11. [Security Implementation](#security-implementation)
12. [Deployment & Monitoring](#deployment--monitoring)

## Overview

The Teams page (`/team`) is a production-ready team management interface built with Next.js 14+ that provides comprehensive user management capabilities with enterprise-grade security and performance.

### Core Capabilities

- **Multi-modal User Invitation**: Single, multiple, and bulk user invitation with role assignment
- **Hierarchical Role Management**: Six-tier role system with permission inheritance
- **Real-time Activity Monitoring**: Comprehensive audit logging and live updates
- **Advanced Search & Filtering**: Debounced search with role-based filtering
- **Responsive Design**: Mobile-first approach with dark theme
- **Enterprise Security**: RBAC with granular permissions and input validation

### Key Features

- **Tab-based Navigation**: Team Members, Roles & Permissions, Activity Log
- **Smart Pagination**: Server-side pagination with configurable page sizes
- **Permission Guards**: Component-level access control
- **Cache Management**: Intelligent React Query cache invalidation
- **Error Boundaries**: Graceful error handling and recovery
- **Accessibility**: WCAG 2.1 AA compliant interface

## Architecture

### Modern Component Architecture

```
app/(dashboard)/team/page.tsx (Main Container)
├── TeamStats Component
├── Tabs Navigation (team, roles, activity)
├── TeamMembersTab
│   ├── Search & Filter Controls
│   ├── TeamMemberRow Components
│   └── PaginationControls
├── RolesTab
│   └── Role Permission Cards
├── ActivityLogTab
│   ├── Activity Search & Filter
│   ├── Activity Log Entries
│   └── Activity Pagination
└── Modal System
    ├── InviteModal (Single/Multiple/Bulk)
    ├── ChangeRoleButton Modal
    └── RemoveUserButton Modal
```

### Technology Stack

- **Framework**: Next.js 14+ (App Router with Server Components)
- **Language**: TypeScript 5.0+ with strict mode
- **Styling**: Tailwind CSS with custom design system
- **State Management**: TanStack Query v5 + React useState/useReducer
- **Form Management**: React Hook Form + Zod validation
- **UI Components**: Radix UI + Custom component library
- **Icons**: Lucide React
- **Authentication**: Custom session management with Redis
- **Caching**: React Query + Redis server-side caching

### File Structure

```
app/(dashboard)/team/
├── page.tsx                    # Main team page
components/team/
├── team-stats.tsx             # Statistics cards
├── team-members-tab.tsx       # Team members table
├── team-member-row.tsx        # Individual member row
├── roles-tab.tsx              # Roles and permissions
├── activity-log-tab.tsx       # Activity monitoring
├── invite-modal.tsx           # User invitation modal
├── change-role-button.tsx     # Role change component
├── remove-user-button.tsx     # User removal component
└── pagination-controls.tsx    # Reusable pagination
hooks/
├── use-api.ts                 # API integration hooks
├── use-permissions.tsx        # Permission management
├── use-debounce.ts           # Search debouncing
└── use-pagination.ts         # Pagination logic
lib/
├── permissions.ts            # RBAC implementation
├── rbac.ts                   # Server-side RBAC
└── session.ts               # Session management
```

## Modern API Integration

### Modern React Query Hooks

#### 1. Organization Users (`useOrgUsers`)

```typescript
const {
  data: usersData,
  isLoading: usersLoading,
  refetch: refetchUsers,
  error,
} = useOrgUsers(currentOrgId || '', usersPagination.page, usersPagination.pageSize);

// Enhanced Response Structure
interface OrgUsersResponse {
  users: TeamMember[];
  totalUsers: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// TeamMember interface
interface TeamMember {
  email: string;
  name?: string;
  role: string;
  status: 'Active' | 'Invited' | 'Suspended';
  userCredits?: number;
  lastActive?: string;
  avatar?: string;
  joinedAt?: string;
  invitedBy?: string;
}
```

**Endpoint**: `GET /api/orgs/users/{orgId}?page={page}&limit={limit}`

#### 2. Modern User Management Hooks

```typescript
// Individual hooks for better error handling and cache management
const { mutate: inviteUser, isPending: isInviting } = useInviteUser();
const { mutate: updateUserRole, isPending: isUpdatingRole } = useUpdateUserRole();
const { mutate: removeUserMutation, isPending: isRemoving } = useRemoveUser();

// Usage with enhanced error handling
inviteUser(
  {
    email: '<EMAIL>',
    role: 'member',
    orgId: currentOrgId,
    orgName: currentOrg?.orgName || '',
  },
  {
    onSuccess: (data) => {
      // Handle success with proper typing
      setActionDialogMessage(`Successfully invited ${email}`);
      refetchUsers();
    },
    onError: (error: ApiError) => {
      // Structured error handling
      setActionDialogMessage(`Failed to invite user: ${error.message}`);
    },
  },
);
```

#### 3. Action Logs with Enhanced Filtering

```typescript
const {
  data: actionLogsData,
  isLoading: actionLogsLoading,
  refetch: refetchActionLogs,
} = useActionLogs('AWS', actionLogPagination.page, actionLogPagination.pageSize);

// Enhanced ActionLog interface
interface ActionLog {
  id: string;
  timestamp: string;
  action: 'user_invited' | 'role_changed' | 'user_removed' | 'user_accepted';
  actor: string;
  target?: string;
  details: {
    oldRole?: string;
    newRole?: string;
    orgId: string;
    orgName: string;
    reason?: string;
  };
  metadata: {
    userAgent: string;
    ipAddress: string;
    requestId: string;
  };
}
```

#### 4. Cache Management with Automatic Invalidation

```typescript
// Automatic cache invalidation on mutations
const apiClientWithInvalidation = async (
  url: string,
  options: RequestInit,
  queryClient: QueryClient,
) => {
  const response = await fetch(url, options);

  // Check for invalidation headers
  const invalidateHeader = response.headers.get('X-Invalidate-Queries');
  if (invalidateHeader) {
    const { queries } = JSON.parse(invalidateHeader);
    queries.forEach((queryKey: string[]) => {
      queryClient.invalidateQueries({ queryKey });
    });
  }

  return response.json();
};
```

**Modern API Endpoints**:

- `POST /api/orgs/users/add` - Enhanced user invitation
- `POST /api/orgs/users/update` - Role updates with validation
- `POST /api/orgs/users/remove` - Secure user removal
- `POST /api/orgs/users/accept` - Invitation acceptance
- `GET /api/audit-logs/{provider}` - Activity monitoring

### API Request/Response Examples

#### Invite User

```typescript
// Request
POST /api/orgs/users/add
{
  "email": "<EMAIL>",
  "role": "Manager",
  "orgId": "683f2d0f36e1cc5bd8f3ecec"
}

// Response
{
  "success": true,
  "message": "User invited successfully",
  "user": {
    "email": "<EMAIL>",
    "role": "Manager",
    "status": "Invited"
  }
}
```

#### Change User Role

```typescript
// Request
PUT /api/orgs/users/update
{
  "email": "<EMAIL>",
  "role": "Admin",
  "orgName": "My Organization"
}

// Response
{
  "success": true,
  "message": "Role updated successfully",
  "user": {
    "email": "<EMAIL>",
    "role": "Admin",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### Remove User

```typescript
// Request
DELETE /api/orgs/users/remove
{
  "email": "<EMAIL>",
  "orgName": "My Organization"
}

// Response
{
  "success": true,
  "message": "User removed successfully"
}
```

## State Management

### Local State Structure

```typescript
// Filter and Search State
const [roleFilter, setRoleFilter] = useState('All Roles');
const [searchTerm, setSearchTerm] = useState('');
const [actionLogFilter, setActionLogFilter] = useState('All Actions');
const [actionLogSearch, setActionLogSearch] = useState('');

// Modal State
const [removeUser, setRemoveUser] = useState<TeamMember | null>(null);
const [changeRoleUser, setChangeRoleUser] = useState<TeamMember | null>(null);
const [inviteOpen, setInviteOpen] = useState(false);

// UI State
const [activeTab, setActiveTab] = useState('team');
const [isPending, startTransition] = useTransition();

// Dialog State
const [actionDialogOpen, setActionDialogOpen] = useState(false);
const [actionDialogMessage, setActionDialogMessage] = useState('');
const [actionDialogType, setActionDialogType] = useState<'success' | 'error'>('success');
```

### Pagination State Management

```typescript
// Custom pagination hook
const usePagination = (initialPage = 0, initialPageSize = 10) => {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const goToPrevious = useCallback(() => setPage((p) => Math.max(0, p - 1)), []);
  const goToNext = useCallback(
    (maxPages: number) => setPage((p) => Math.min(maxPages - 1, p + 1)),
    [],
  );
  const resetPage = useCallback(() => setPage(0), []);
  const changePageSize = useCallback((newSize: number) => {
    setPageSize(newSize);
    setPage(0);
  }, []);

  return { page, pageSize, goToPrevious, goToNext, resetPage, changePageSize };
};

// Usage
const usersPagination = usePagination(0, 10);
const actionLogPagination = usePagination(0, 15);
```

### Debounced Search Implementation

```typescript
// Custom debounce hook
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Usage
const debouncedSearchTerm = useDebounce(searchTerm, 300);
const debouncedActionLogSearch = useDebounce(actionLogSearch, 300);
```

## Permission System

### RBAC Implementation

The Teams page uses a comprehensive Role-Based Access Control (RBAC) system:

#### Permission Guard Component

```typescript
<OrgPermissionGuard
  permission={Permission.ViewTeam}
  fallback={<AccessDeniedFallback />}
>
  {/* Team page content */}
</OrgPermissionGuard>
```

#### Role Definitions

```typescript
const roles = [
  {
    name: 'Owner',
    description: 'Has full control over the organization, including destructive actions.',
    permissions: [
      'Edit organization settings',
      'Purchase credits',
      'Transfer credits to others',
      'Delete the organization',
      'Manage all users and roles',
      'Access financial records',
    ],
    color: 'bg-gradient-to-r from-purple-600 to-pink-600',
  },
  {
    name: 'SuperAdmin',
    description: 'Manages the organization without destructive permissions.',
    permissions: [
      'Manage credits and allocations',
      'Manage users and permissions',
      'View all organizational data',
      'Configure system settings',
      'Access advanced features',
    ],
    color: 'bg-gradient-to-r from-blue-600 to-purple-600',
  },
  // ... other roles
];
```

#### Permission-Based UI Rendering

```typescript
<OrgPermissionGuard
  permission={Permission.ManageTeam}
  fallback={
    <Button disabled variant="outline" size="sm" className="opacity-50">
      <UserPlus className="w-4 h-4 mr-2" />
      Invite Member
    </Button>
  }
>
  <Button
    onClick={() => setInviteOpen(true)}
    className="bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a]"
  >
    <UserPlus className="w-4 h-4 mr-2" />
    Invite Member
  </Button>
</OrgPermissionGuard>
```

## UI Components

### Core Component Breakdown

#### 1. Statistics Cards

```typescript
const StatsCard = ({
  title,
  value,
  icon: Icon,
  gradient,
  isLoading,
}: {
  title: string;
  value: string | number;
  icon: any;
  gradient: string;
  isLoading?: boolean;
}) => (
  <Card className="bg-[#262131] border-[#3b3b3b] overflow-hidden">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-[#94a3b8] text-sm font-medium">{title}</p>
          {isLoading ? (
            <Skeleton className="h-8 w-16 mt-2 bg-[#3b3b3b]" />
          ) : (
            <p className="text-2xl font-bold text-white mt-2">{value}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${gradient}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </CardContent>
  </Card>
);
```

#### 2. Pagination Controls

```typescript
const PaginationControls = ({
  page,
  totalPages,
  onPrevious,
  onNext,
  pageSize,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25, 50],
  disabled = false,
  totalItems = 0,
}) => {
  const startItem = page * pageSize + 1;
  const endItem = Math.min((page + 1) * pageSize, totalItems);

  return (
    <div className="flex items-center justify-between mt-6 flex-wrap gap-4">
      {/* Pagination implementation */}
    </div>
  );
};
```

#### 3. Loading Skeletons

```typescript
const TableSkeleton = ({ rows = 5, cols = 6 }) => (
  <div className="space-y-3">
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex space-x-4">
        {Array.from({ length: cols }).map((_, j) => (
          <Skeleton key={j} className="h-12 flex-1 bg-[#3b3b3b]" />
        ))}
      </div>
    ))}
  </div>
);
```

### Component Dependencies

```typescript
// External Components
import { TeamMemberRow } from '@/components/team-member-row';
import { RemoveUserButton } from '@/components/remove-user-button';
import { ChangeRoleButton } from '@/components/change-role-button';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { InviteUsers } from '@/components/invite-member-button';
import { RoleCard } from '@/components/role-card';
import { ActionLogRow } from '@/components/action-log-row';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
```

## Data Flow

### 1. Initial Data Loading

```mermaid
graph TD
    A[Component Mount] --> B[Load Organization Data]
    B --> C[Load Users Data]
    B --> D[Load Action Logs]
    C --> E[Process User Data]
    D --> F[Process Activity Data]
    E --> G[Update Statistics]
    F --> G
    G --> H[Render UI]
```

### 2. User Actions Flow

```mermaid
graph TD
    A[User Action] --> B{Action Type}
    B -->|Invite| C[Open Invite Modal]
    B -->|Remove| D[Open Remove Confirmation]
    B -->|Change Role| E[Open Role Change Modal]
    C --> F[Submit Invite API]
    D --> G[Submit Remove API]
    E --> H[Submit Role Change API]
    F --> I[Refetch Users Data]
    G --> I
    H --> I
    I --> J[Update UI]
    I --> K[Show Success/Error Dialog]
```

### 3. Search and Filter Flow

```mermaid
graph TD
    A[User Types in Search] --> B[Debounce Input]
    B --> C[Update Search State]
    C --> D[Filter Data Locally]
    D --> E[Update Filtered Results]
    E --> F[Reset Pagination]
    F --> G[Re-render Table]
```

## Error Handling

### API Error Handling Strategy

```typescript
const handleInviteUser = useCallback(
  async (values: z.infer<typeof inviteUserSchema>) => {
    startTransition(async () => {
      try {
        await new Promise((resolve, reject) => {
          manipulateUsers(
            {
              action: 'invite',
              body: JSON.stringify({ email: values.email, role: values.role }),
            },
            {
              onSuccess: () => {
                setActionDialogMessage(`Successfully invited ${values.email}`);
                setActionDialogType('success');
                resolve(true);
                refetchUsers();
              },
              onError: (error: any) => {
                setActionDialogMessage(`Failed to invite user: ${error.message}`);
                setActionDialogType('error');
                reject(error);
              },
            },
          );
        });
      } catch (error) {
        console.error('Invite error:', error);
      } finally {
        setActionDialogOpen(true);
      }
    });
  },
  [manipulateUsers, refetchUsers],
);
```

### Error Display Components

```typescript
// Success/Error Dialog
<Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
  <DialogContent className="text-center bg-[#262131] border-[#3b3b3b]">
    <DialogTitle className="text-white text-xl">
      {actionDialogType === 'success' ? '✅ Success' : '❌ Error'}
    </DialogTitle>
    <div
      className={`mt-4 p-4 rounded-lg ${
        actionDialogType === 'success'
          ? 'bg-green-500/10 text-green-400'
          : 'bg-red-500/10 text-red-400'
      }`}
    >
      {actionDialogMessage}
    </div>
    <Button
      className="mt-6 bg-gradient-to-r from-[#8a2be2] to-[#6a1b9a]"
      onClick={() => setActionDialogOpen(false)}
    >
      Close
    </Button>
  </DialogContent>
</Dialog>
```

## Performance Optimizations

### 1. React Performance Optimizations

```typescript
// Memoized filtered data
const filteredUsers = useMemo(
  () =>
    users.filter((member) => {
      const matchesRole =
        roleFilter === 'All Roles' || member.role.toLowerCase() === roleFilter.toLowerCase();
      const matchesSearch =
        member.name?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        member.email?.toLowerCase().includes(debouncedSearchTerm.toLowerCase());
      return matchesRole && matchesSearch;
    }),
  [users, roleFilter, debouncedSearchTerm],
);

// Memoized callbacks
const handleRemoveUser = useCallback(async () => {
  // Implementation with useCallback for performance
}, [removeUser, manipulateUsers, orgInfo?.orgName, refetchUsers]);

// React Transitions for non-blocking updates
const [isPending, startTransition] = useTransition();
```

### 2. Search Debouncing

```typescript
// 300ms debounce for search inputs
const debouncedSearchTerm = useDebounce(searchTerm, 300);
const debouncedActionLogSearch = useDebounce(actionLogSearch, 300);
```

### 3. Pagination Optimization

```typescript
// Server-side pagination to handle large datasets
const { data: usersData } = useOrgUsers(orgID, page, pageSize);

// Local pagination for filtered results when dataset is small
const paginatedUsers = filteredUsers.slice(page * pageSize, (page + 1) * pageSize);
```

### 4. Loading States

```typescript
// Skeleton loading for better perceived performance
{usersLoading ? (
  <TableSkeleton rows={usersPagination.pageSize} />
) : (
  // Actual content
)}
```

## Sample Data Strategy

### Fallback Data Implementation

```typescript
// Sample users for demonstration and fallback
const sampleUsers: TeamMember[] = [
  {
    email: '<EMAIL>',
    name: 'Alex Chen',
    role: 'Owner',
    status: 'Active',
    userCredits: 15750,
    lastActive: '2024-01-15T10:30:00Z',
    avatar: '/placeholder-user.jpg',
  },
  // ... more sample users
];

// Smart fallback logic
const users = usersData?.users && usersData.users.length > 0 ? usersData.users : sampleUsers;
const totalUsers = usersData?.totalUsers || sampleUsers.length;
```

### Sample Data Benefits

1. **Development**: Allows frontend development without backend dependency
2. **Demo Mode**: Provides realistic data for demonstrations
3. **Testing**: Consistent data for testing scenarios
4. **Graceful Degradation**: Fallback when API fails

## Testing Strategy

### Unit Testing

```typescript
// Test pagination hook
describe('usePagination', () => {
  test('should initialize with correct values', () => {
    const { result } = renderHook(() => usePagination(0, 10));
    expect(result.current.page).toBe(0);
    expect(result.current.pageSize).toBe(10);
  });

  test('should handle page navigation', () => {
    const { result } = renderHook(() => usePagination(0, 10));
    act(() => {
      result.current.goToNext(5);
    });
    expect(result.current.page).toBe(1);
  });
});
```

### Integration Testing

```typescript
// Test API integration
describe('TeamPage API Integration', () => {
  test('should load users on mount', async () => {
    render(<TeamPage />);
    await waitFor(() => {
      expect(screen.getByText('Team Members')).toBeInTheDocument();
    });
  });

  test('should handle user invitation', async () => {
    render(<TeamPage />);
    // Test invite flow
  });
});
```

### E2E Testing (Cypress)

```typescript
// cypress/e2e/team-page.cy.ts
describe('Team Page', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/team');
  });

  it('should display team members', () => {
    cy.get('[data-testid="team-members-table"]').should('be.visible');
  });

  it('should allow inviting new members', () => {
    cy.get('[data-testid="invite-button"]').click();
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="role-select"]').select('Manager');
    cy.get('[data-testid="submit-invite"]').click();
    cy.get('[data-testid="success-message"]').should('contain', 'invited');
  });
});
```

## Deployment Considerations

### Environment Variables

```bash
# Required environment variables
NEXT_PUBLIC_API_BASE_URL=https://api.example.com
NEXT_PUBLIC_ORG_ID=683f2d0f36e1cc5bd8f3ecec
SESSION_DURATION_HOURS=24

# Optional
NEXT_PUBLIC_ENABLE_SAMPLE_DATA=true
NEXT_PUBLIC_DEBUG_MODE=false
```

### Build Optimizations

```typescript
// next.config.mjs
const nextConfig = {
  experimental: {
    optimizeCss: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};
```

### Performance Monitoring

```typescript
// Add performance monitoring
useEffect(() => {
  if (typeof window !== 'undefined' && window.performance) {
    const loadTime =
      window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
    console.log('Page load time:', loadTime);
  }
}, []);
```

## Security Considerations

### Input Validation

```typescript
// Zod schema for user invitation
const inviteUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
});
```

### Permission Checks

```typescript
// Server-side permission validation
app.post('/api/orgs/users/add', async (req, res) => {
  const userPermissions = await getUserPermissions(req.user.id);
  if (!userPermissions.includes('ManageTeam')) {
    return res.status(403).json({ error: 'Insufficient permissions' });
  }
  // Process request
});
```

### Data Sanitization

```typescript
// Sanitize user inputs
const sanitizeInput = (input: string) => {
  return input.trim().replace(/[<>\"']/g, '');
};
```

## Best Practices

### 1. Code Organization

- Separate API logic into custom hooks
- Use TypeScript interfaces for type safety
- Implement proper error boundaries
- Follow consistent naming conventions

### 2. Performance

- Implement debouncing for search inputs
- Use React.memo for expensive components
- Optimize re-renders with useCallback and useMemo
- Implement proper loading states

### 3. User Experience

- Provide immediate feedback for user actions
- Implement optimistic updates where appropriate
- Use skeleton loading for better perceived performance
- Ensure responsive design across devices

### 4. Maintenance

- Document API contracts
- Implement comprehensive testing
- Use proper error logging
- Regular dependency updates

## Conclusion

This implementation guide provides a comprehensive foundation for building a robust team management interface. The architecture emphasizes performance, user experience, and maintainability while providing flexibility for future enhancements.

Key takeaways:

- **Modular Design**: Separates concerns for better maintainability
- **Performance First**: Implements optimizations from the ground up
- **User-Centric**: Focuses on smooth user interactions
- **Scalable**: Handles large datasets efficiently
- **Secure**: Implements proper permission checks and input validation

For questions or contributions, refer to the team lead or create an issue in the project repository.
