# Teams Page Quick Reference

## Quick Actions

### Adding Users

#### Single User
1. Click **"Invite Member"** button
2. Enter email and select role
3. Click **"Send Invitation"**

#### Multiple Users
1. Click **"Invite Member"** → **"Multiple Invites"** tab
2. Add email/role pairs
3. Use **"Add Another"** for more users
4. Click **"Send Invitations"**

#### Bulk Users
1. Click **"Invite Member"** → **"Bulk Invite"** tab
2. Paste emails (comma/space/newline separated)
3. Select role for all users
4. Click **"Send Invitations"**

### Changing Roles

1. Find user in team list
2. Click **"Change Role"** button
3. Select new role from dropdown
4. Click **"Update Role"**
5. Confirm in dialog

### Removing Users

1. Find user in team list
2. Click **"Remove"** button
3. Confirm removal in dialog

⚠️ **Warning**: This action cannot be undone!

## Role Hierarchy

| Role | Level | Can Manage | Key Permissions |
|------|-------|------------|-----------------|
| **Owner** | 6 | All roles | Full control, billing, delete org |
| **Admin** | 4 | Manager, Member, Viewer | Team management, settings |
| **Manager** | 3 | Member, Viewer | Team oversight, basic admin |
| **Member** | 2 | None | Standard user access |
| **Viewer** | 1 | None | Read-only access |

## Permission Rules

### Who Can Invite Users?
- **Owner**: Can invite anyone
- **Admin**: Can invite Manager, Member, Viewer
- **Manager**: Can invite Member, Viewer
- **Member/Viewer**: Cannot invite users

### Who Can Change Roles?
- You can only change roles **below** your level
- Cannot promote users **to or above** your level
- Cannot manage **Owner** roles (unless you're Owner)

### Who Can Remove Users?
- Same rules as role changes
- Cannot remove yourself
- Cannot remove users with equal or higher roles

## Search & Filtering

### Search
- Type in search box to filter by name/email
- Real-time search with 300ms delay
- Case-insensitive matching

### Filter by Role
- Use role dropdown to filter by specific role
- Select "All Roles" to clear filter
- Maintains search term during filtering

### Pagination
- Use page size dropdown (10, 25, 50, 100)
- Navigate with Previous/Next buttons
- Shows total user count

## Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "Permission denied" | Insufficient role level | Contact admin for role upgrade |
| "Cannot invite yourself" | Trying to invite own email | Use different email address |
| "Invalid email address" | Malformed email | Check email format |
| "User already exists" | User already in organization | Check existing members |
| "Role not found" | Invalid role selection | Select from available roles |

## API Endpoints

### Invite User
```
POST /api/orgs/users/add
Body: { email, role, orgId, orgName }
```

### Update Role
```
POST /api/orgs/users/update
Body: { email, role, orgId, orgName }
```

### Remove User
```
POST /api/orgs/users/remove
Body: { email, orgId, orgName }
```

## React Hooks

### useInviteUser
```typescript
const { mutate: inviteUser, isPending } = useInviteUser();
```

### useUpdateUserRole
```typescript
const { mutate: updateRole } = useUpdateUserRole();
```

### useRemoveUser
```typescript
const { mutate: removeUser } = useRemoveUser();
```

### useOrgUsers
```typescript
const { data: users, isLoading, refetch } = useOrgUsers(orgId, page, pageSize);
```

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl/Cmd + K` | Focus search box |
| `Enter` | Submit form/confirm action |
| `Escape` | Close modal/cancel action |
| `Tab` | Navigate form fields |

## Troubleshooting

### Page Not Loading
1. Check internet connection
2. Refresh page (F5)
3. Clear browser cache
4. Check console for errors

### Users Not Appearing
1. Clear search filters
2. Check pagination
3. Verify organization selection
4. Refresh user list

### Invitation Not Sent
1. Verify email address
2. Check spam folder
3. Wait 5 minutes and retry
4. Contact support if persistent

### Role Change Failed
1. Verify your permissions
2. Check role hierarchy rules
3. Ensure target user exists
4. Try refreshing page

## Best Practices

### Security
- ✅ Assign minimum necessary permissions
- ✅ Review team members regularly
- ✅ Remove users immediately when they leave
- ✅ Maintain multiple admin users

### User Management
- ✅ Use descriptive role assignments
- ✅ Document role changes
- ✅ Verify email addresses before inviting
- ✅ Use bulk operations for efficiency

### Performance
- ✅ Use pagination for large teams
- ✅ Clear search filters when not needed
- ✅ Refresh data periodically
- ✅ Monitor for slow operations

## Support

### Getting Help
1. Check this quick reference
2. Review comprehensive guide
3. Check application logs
4. Contact system administrator

### Reporting Issues
Include:
- Current user role
- Action being attempted
- Error message (if any)
- Browser and version
- Steps to reproduce

---

*For detailed information, see [Teams Page Comprehensive Guide](./TEAMS_PAGE_COMPREHENSIVE_GUIDE.md)*
