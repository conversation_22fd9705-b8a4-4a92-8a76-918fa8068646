# Server-Side Activity Logging System Guide

This guide covers the comprehensive activity logging system for the partner dashboard application. **All activity logging is handled automatically by API routes** - no client-side logging is required.

## Overview

The activity logging system provides:

- **Automatic server-side logging** for all API routes
- **Seamless integration** with existing API endpoints
- **Type-safe interfaces** for all activity log types
- **Session management** and automatic user context
- **Zero client-side configuration** required

## How It Works

Activity logging is **automatically handled by the API routes**. When you make API calls from your components, the API routes themselves handle all activity logging. This ensures:

- **Consistent logging** across all operations
- **No missed activity logs** due to client-side errors
- **Better security** with server-side validation
- **Simplified development** - no logging code needed in components

## Activity Log Types

```typescript
export enum ActivityLogAction {
  // User Actions
  USER_INVITED = 'USER_INVITED',
  USER_REMOVED = 'USER_REMOVED',
  USER_ROLE_CHANGED = 'USER_ROLE_CHANGED',
  USER_ACCEPTED_INVITE = 'USER_ACCEPTED_INVITE',

  // Device Actions
  DEVICE_CREATED = 'DEVICE_CREATED',
  DEVICE_UPDATED = 'DEVICE_UPDATED',
  DEVICE_DELETED = 'DEVICE_DELETED',

  // Job Actions
  JOB_SUBMITTED = 'JOB_SUBMITTED',
  JOB_CANCELLED = 'JOB_CANCELLED',

  // System Actions
  SYSTEM_LOGIN = 'SYSTEM_LOGIN',
  SYSTEM_LOGOUT = 'SYSTEM_LOGOUT',
}

export enum ResourceType {
  USER = 'USER',
  DEVICE = 'DEVICE',
  ORGANIZATION = 'ORGANIZATION',
  JOB = 'JOB',
  PERMISSION = 'PERMISSION',
  SYSTEM = 'SYSTEM',
}
```

## Client-Side Components (No Activity Code Needed!)

Your components should simply call the APIs - activity logging happens automatically:

### Team Management Examples

```typescript
// components/team/invite-modal.tsx
export function InviteModal({ orgName, onInvite }) {
  const handleInvite = async (email: string, role: string) => {
    // Just call the API - activity logging handled automatically
    const result = await onInvite(email, role);

    if (result.success) {
      toast.success('User invited successfully');
    }
  };
}

// components/team/remove-user-button.tsx
export function RemoveUserButton({ member, onRemove }) {
  const handleRemove = async () => {
    // Just call the API - activity logging handled automatically
    await onRemove(member.email);
    toast.success('User removed');
  };
}

// components/team/change-role-button.tsx
export function ChangeRoleButton({ user, onRoleChange }) {
  const handleRoleChange = async (newRole: string) => {
    // Just call the API - activity logging handled automatically
    await onRoleChange(user.email, user.role, newRole);
    toast.success('Role updated');
  };
}
```

### Device Management Examples

```typescript
// components/devices/device-edit-modal.tsx
export function DeviceEditModal({ device }) {
  const handleSave = async (formData: DeviceData) => {
    // Just call the API - activity logging handled automatically
    const result = await updateDevice(device.id, formData);

    if (result.success) {
      toast.success('Device updated successfully');
    }
  };
}

// Creating devices
const handleCreateDevice = async (deviceData: DeviceData) => {
  // Just call the API - activity logging handled automatically
  const result = await createDevice(deviceData);

  if (result.success) {
    toast.success('Device created successfully');
  }
};

// Deleting devices
const handleDeleteDevice = async (deviceId: string) => {
  // Just call the API - activity logging handled automatically
  const result = await deleteDevice(deviceId);

  if (result.success) {
    toast.success('Device deleted successfully');
  }
};
```

## Server-Side Implementation (Where Activity Logging Happens)

### API Routes with Automatic Activity Logging

```typescript
// app/api/orgs/users/add/route.ts
import { logUserInvited } from '@/lib/audit-logger-server';

export async function POST(request: NextRequest) {
  const session = await getSession();
  const body = await request.json();

  // Your business logic
  const result = await inviteUserToOrg(body);

  // Automatic activity logging
  if (result.success) {
    await logUserInvited(body.email, body.role, body.orgId);
  }

  return NextResponse.json(result);
}

// app/api/quantum-devices/route.ts
import { logDeviceCreated } from '@/lib/audit-logger-server';

export async function POST(request: NextRequest) {
  const body = await request.json();

  // Create device
  const device = await createDevice(body);

  // Automatic activity logging
  if (device) {
    await logDeviceCreated(device.id, device.name, body.organizationId);
  }

  return NextResponse.json(device);
}
```

### Available Server Functions

```typescript
// User Management
await logUserInvited(email, role, orgId, metadata?)
await logUserRemoved(email, orgId, metadata?)
await logUserRoleChanged(email, oldRole, newRole, orgId, metadata?)
await logUserAcceptedInvite(email, orgId, metadata?)

// Device Management
await logDeviceCreated(deviceId, deviceName, orgId, metadata?)
await logDeviceUpdated(deviceId, deviceName, orgId, changes, metadata?)
await logDeviceDeleted(deviceId, deviceName, orgId, metadata?)

// Job Management
await logJobSubmitted(jobId, deviceId, orgId, metadata?)
await logJobCancelled(jobId, orgId, metadata?)
```

## Which APIs Have Activity Logging

✅ **Already Implemented:**

- `POST /api/orgs/users/add` - User invitations
- `POST /api/orgs/users/remove` - User removal
- `POST /api/orgs/users/update` - Role changes
- `POST /api/quantum-devices` - Device creation
- `PATCH /api/quantum-devices/[id]` - Device updates
- `DELETE /api/quantum-devices/[id]` - Device deletion

## Viewing Activity Logs

### Frontend Integration

The activity logs can be viewed in the Team page using the Activity Log tab:

```typescript
// hooks/use-activity-logs.ts
import { useActivityLogs } from '@/hooks/use-activity-logs';

const { data, isLoading } = useActivityLogs({
  organizationId: 'your-org-id',
  page: 0,
  resultsPerPage: 15,
  enabled: activeTab === 'activity', // Only fetch when tab is active
});
```

### API Gateway

Activity logs are accessed through the internal API gateway:

```typescript
// app/api/activity-logs/route.ts
GET /api/activity-logs?organizationId=abc&page=0&resultsPerPage=15
```

This route forwards requests to the external activity logs API with proper authentication and error handling.

## Integration Checklist

When creating new API routes that need activity logging:

- [ ] Import appropriate function from `@/lib/audit-logger-server`
- [ ] Identify the correct `ActivityLogAction` and `ResourceType`
- [ ] Add activity logging call after successful operation
- [ ] Include descriptive message and relevant metadata
- [ ] Handle activity logging errors gracefully (don't fail main operation)
- [ ] Test that logs appear in the activity log tab

## Benefits of Server-Side Only Approach

### 🔒 **Security**

- Activity logs can't be bypassed by client-side manipulation
- Server-side validation ensures data integrity
- Session management is handled securely and automatically

### 🚀 **Performance**

- No client-side logging code = smaller bundle sizes
- Server-side logging is more efficient
- Reduced client-side API calls

### 🛠 **Developer Experience**

- Components remain clean and focused on UI/UX
- No activity logging code needed in React components
- Simplified error handling
- Automatic session context extraction

### 📊 **Reliability**

- Guaranteed activity logging on successful operations
- Centralized logging logic
- Consistent activity log format across all operations
- Real-time activity updates when switching to Activity Log tab

## Error Handling

The activity logging system is designed to **never fail the main operation**:

```typescript
// Example from API route
try {
  await logUserInvited(email, role, orgId);
} catch (activityError) {
  console.warn('⚠️ Failed to create activity log:', activityError);
  // Main operation continues successfully
}
```

- Log activity failures as warnings
- Never throw errors that would break the main API response
- Graceful degradation if activity system is unavailable

## Data Structure

Activity logs follow this structure in the backend:

```json
{
  "activities": [
    {
      "_id": "activity-id",
      "userId": "user-id",
      "userEmail": "<EMAIL>",
      "userName": "User Name",
      "organizationId": "org-id",
      "action": "USER_INVITED",
      "actionDisplay": "User Invited",
      "resourceType": "USER",
      "resourceId": "<EMAIL>",
      "resourceName": "<EMAIL>",
      "description": "User <EMAIL> invited with role member",
      "metadata": {
        "assignedRole": "member"
      },
      "timestamp": "2025-01-15T20:16:16.836Z",
      "status": "SUCCESS"
    }
  ]
}
```

This server-side only approach ensures complete activity coverage while keeping your client-side code clean and focused on user experience.
