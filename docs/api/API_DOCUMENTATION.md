# API Documentation

## Overview

This document provides comprehensive documentation for all API endpoints in the Partner Dashboard authentication and user management system. The API is built using Next.js 15+ App Router with Redis-enhanced session management and AWS Cognito authentication.

### Base URL

```
Development: http://localhost:3000/api
Production: https://partner-dashboard.qbraid.com/api
```

### Authentication

All authenticated endpoints require a valid session cookie. The system uses Redis-based session management with automatic JWT fallback.

**Session Cookie Names:**

- Development: `session`
- Production: `__Secure-session`

**Authentication Headers (for external API calls):**

- `Authorization: Bearer {accessToken}` (when available)
- `id-token: {idToken}` (preferred for QBraid API)
- `email: {userEmail}` (required with access token)

## Table of Contents

1. [Authentication Endpoints](#authentication-endpoints)
2. [Session Management](#session-management)
3. [User Profile](#user-profile)
4. [Role & Permission Management](#role--permission-management)
5. [Organization Management](#organization-management)
6. [Token Management](#token-management)
7. [Debug Endpoints](#debug-endpoints)
8. [Webhook Endpoints](#webhook-endpoints)

---

### Quick Endpoint Index

```text
# Auth & Session
GET    /api/csrf-token
POST   /api/csrf-token
PUT    /api/csrf-token
DELETE /api/csrf-token
POST   /api/oauth/process
POST   /api/auth/oauth-session
GET    /api/auth/oauth-session
GET    /api/auth/session
GET    /api/auth/permissions
POST   /api/auth/resend-verification
POST   /api/auth/verify-link

# Role management
POST   /api/refresh-roles
GET    /api/refresh-roles
POST   /api/bulk-refresh-roles
GET    /api/bulk-refresh-roles
GET    /api/user/roles
POST   /api/user/roles
GET    /api/user/org-roles
POST   /api/user/org-roles
GET    /api/user/get-role
GET    /api/user/role

# Organizations & team
GET    /api/orgs/get/{page}/{limit}
POST   /api/orgs/create
POST   /api/orgs/acceptOrganizationInvite
GET    /api/orgs/users/{orgId}/{page}/{limit}
POST   /api/orgs/users/add
POST   /api/orgs/users/update
POST   /api/orgs/users/remove
POST   /api/orgs/users/reinvite
POST   /api/orgs/users/cancel-invite
POST   /api/orgs/users/accept

# Organization-scoped providers/devices
GET    /api/orgs/{orgId}/providers
PATCH  /api/orgs/{orgId}/providers
GET    /api/orgs/{orgId}/provider
POST   /api/orgs/{orgId}/provider
PUT    /api/orgs/{orgId}/provider
GET    /api/orgs/{orgId}/device-access
PUT    /api/orgs/{orgId}/device-access
GET    /api/orgs/{orgId}/quantum-devices

# User profile
GET    /api/user/profile
PUT    /api/user/profile
POST   /api/user/change-password

# Providers
GET    /api/providers
POST   /api/providers
PATCH  /api/providers/{providerId}
GET    /api/providers/{providerId}/status
PUT    /api/providers/{providerId}/status
GET    /api/providers/{providerId}/ownership
GET    /api/providers/{providerId}/devices
POST   /api/providers/{providerId}/devices
GET    /api/providers/{providerId}/devices/{deviceId}
PUT    /api/providers/{providerId}/devices/{deviceId}
DELETE /api/providers/{providerId}/devices/{deviceId}

# Devices
GET    /api/devices/available
POST   /api/devices/batch
GET    /api/quantum-devices
POST   /api/quantum-devices
GET    /api/quantum-devices/{qbraid_id}
PUT    /api/quantum-devices/{qbraid_id}
PATCH  /api/quantum-devices/{qbraid_id}
DELETE /api/quantum-devices/{qbraid_id}
PATCH  /api/quantum-devices/{qbraid_id}/approve
DELETE /api/quantum-devices/{qbraid_id}/request

# Device access requests
GET    /api/device-access-requests
POST   /api/device-access-requests
GET    /api/device-access-requests/{requestId}
PUT    /api/device-access-requests/{requestId}

# Jobs
GET    /api/quantum-jobs/all-by-provider
GET    /api/quantum-jobs/{qbraid_id}

# Token storage & debug
POST   /api/store-cognito-tokens
GET    /api/store-cognito-tokens
PUT    /api/store-cognito-tokens
DELETE /api/store-cognito-tokens
GET    /api/debug/tokens
POST   /api/debug/tokens
PUT    /api/debug/tokens
DELETE /api/debug/tokens
GET    /api/debug/token-storage
DELETE /api/debug/token-storage
GET    /api/debug/session-storage
DELETE /api/debug/session-storage
GET    /api/debug/auth-test

# Health & metrics
GET    /api/health/redis
GET    /api/cache-stats

# Webhook
POST   /api/update-roles
GET    /api/update-roles
```

---

## Authentication Endpoints

### CSRF Token Generation

**Endpoint:** `GET /api/csrf-token`

**Description:** Generates a CSRF token for form protection

**Authentication:** None required

**Response:**

```json
{
  "csrfToken": "secure-random-token",
  "success": true
}
```

**Error Response:**

```json
{
  "error": "Failed to generate CSRF token",
  "success": false
}
```

Note: For testing and tooling, this endpoint also supports POST, PUT, and DELETE methods which mirror the behavior of GET for CSRF token validation scenarios.

**Usage Example:**

```typescript
const response = await fetch('/api/csrf-token', {
  method: 'GET',
  credentials: 'same-origin',
});
const { csrfToken } = await response.json();
```

---

### OAuth Session Creation

**Endpoint:** `POST /api/auth/oauth-session`

**Description:** Creates a secure session after OAuth authentication

**Authentication:** None required (OAuth tokens provided in body)

**Request Body:**

```json
{
  "accessToken": "cognito-access-token",
  "idToken": "cognito-id-token",
  "userId": "user-uuid",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Session created successfully"
}
```

**Error Responses:**

- `400 Bad Request`: Missing required fields
- `500 Internal Server Error`: Session creation failed

---

### OAuth Session Status

**Endpoint:** `GET /api/auth/oauth-session`

**Description:** Returns current OAuth session status

**Authentication:** Session cookie required

**Response:**

```json
{
  "success": true,
  "session": {
    "isAuthenticated": true,
    "user": {
      "email": "<EMAIL>",
      "userId": "user-uuid",
      "username": "username"
    }
  },
  "tokens": {
    "hasAccessToken": true,
    "hasIdToken": true
  }
}
```

---

### OAuth Processing

**Endpoint:** `POST /api/oauth/process`

**Description:** Processes OAuth tokens from client-side authentication

**Authentication:** None required

**Request Body:**

```json
{
  "accessToken": "oauth-access-token",
  "idToken": "oauth-id-token"
}
```

**Response:**

```json
{
  "success": true,
  "redirectTo": "/"
}
```

---

### Resend Verification

**Endpoint:** `POST /api/auth/resend-verification`

**Description:** Resends email verification code

**Authentication:** None required

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Verification link sent successfully"
}
```

**Error Responses:**

- `400 Bad Request`: Various user-friendly error messages
  - "No account found with this email address"
  - "User account is already verified or disabled"
  - "Too many attempts. Please wait before requesting another link"
  - "Invalid email address format"

---

## Session Management

### Get Current Session

**Endpoint:** `GET /api/auth/session`

**Description:** Returns current user session information

**Authentication:** Session cookie required

**Response:**

```json
{
  "email": "<EMAIL>",
  "sessionId": "session-id",
  "timestamp": "2025-01-14T10:30:00.000Z"
}
```

**Error Response:**

```json
{
  "error": "No active session"
}
```

---

### Store Cognito Tokens

**Endpoint:** `POST /api/store-cognito-tokens`

**Description:** Stores Cognito tokens in secure Redis storage or cookies

**Authentication:** Session cookie required

**Request Body:**

```json
{
  "accessToken": "cognito-access-token",
  "idToken": "cognito-id-token"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Tokens stored in secure cookies",
  "storedTokens": {
    "hasAccessToken": true,
    "hasIdToken": true
  }
}
```

---

### Get Token Status

**Endpoint:** `GET /api/store-cognito-tokens`

**Description:** Returns current token storage status

**Authentication:** Session cookie required

**Response:**

```json
{
  "success": true,
  "tokens": {
    "hasAccessToken": true,
    "hasIdToken": true,
    "accessTokenPreview": "eyJraWQiOiJXNTNQR3...",
    "idTokenPreview": "eyJraWQiOiJYNTNQR3..."
  }
}
```

#### Additional Methods

- `PUT /api/store-cognito-tokens`: Updates existing stored tokens using the same request body as POST.
- `DELETE /api/store-cognito-tokens`: Removes stored tokens for the current session.

---

## User Profile

### Get User Profile

**Endpoint:** `GET /api/user/profile`

**Description:** Retrieves current user's profile information

**Authentication:** Session cookie required

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "personalInformation": {
      "companyName": "Example Corp",
      "jobTitle": "Software Engineer",
      "location": "San Francisco, CA"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2025-01-14T10:30:00Z"
  },
  "source": "external_api",
  "duration": 234,
  "requestId": "abc123"
}
```

---

### Update User Profile

**Endpoint:** `PUT /api/user/profile`

**Description:** Updates user profile information

**Authentication:** Session cookie required

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "personalInformation": {
    "companyName": "New Company",
    "jobTitle": "Senior Engineer",
    "location": "New York, NY"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "updatedFields": ["firstName", "lastName", "personalInformation"],
  "duration": 456,
  "requestId": "xyz789"
}
```

---

### Change Password

**Endpoint:** `POST /api/user/change-password`

**Description:** Changes the current user's password

**Authentication:** Session cookie required

**Request Body (example):**

```json
{
  "currentPassword": "********",
  "newPassword": "********"
}
```

**Responses:**

- `200 OK` on success with a confirmation message
- `400/401/403` with error details on validation or auth failure

---

## Role & Permission Management

### Get User Permissions

**Endpoint:** `GET /api/auth/permissions`

**Description:** Fetches user roles and permissions with organization support

**Authentication:** Session cookie required

**Query Parameters:**

- `orgId` (optional): Organization ID for org-specific permissions
- `email` (optional): Email for specific user (admin only)

**Response:**

```json
{
  "roles": ["admin", "member"],
  "permissions": ["view:devices", "manage:devices", "view:team", "manage:team"],
  "source": "email_cache",
  "cached": true,
  "duration": "45ms",
  "orgRoles": {
    "org123": {
      "orgId": "org123",
      "orgName": "Example Organization",
      "role": "admin",
      "updated": "2025-01-14T10:00:00Z"
    }
  },
  "orgContext": {
    "requestedOrgId": "org123",
    "isOrgSpecific": true,
    "scope": "org-specific (Example Organization)",
    "currentOrg": {
      "orgId": "org123",
      "orgName": "Example Organization",
      "role": "admin"
    }
  }
}
```

---

### Get User Roles

**Endpoint:** `GET /api/user/roles`

**Description:** Get user roles for specific organization

**Authentication:** Session cookie required

**Query Parameters:**

- `orgId` (required): Organization ID
- `email` (optional): Target user email
- `page` (optional, default: "0"): Page number
- `limit` (optional, default: "100"): Results per page

**Response:**

```json
{
  "success": true,
  "data": {
    "role": "admin",
    "orgId": "org123",
    "orgName": "Example Organization",
    "email": "<EMAIL>"
  },
  "duration": 234
}
```

---

### Get Organization Roles

**Endpoint:** `GET /api/user/org-roles`

**Description:** Get all organization roles for user

**Authentication:** Session cookie required

**Query Parameters:**

- `orgId` (optional): Specific organization ID
- `forceRefresh` (optional): Force cache refresh

**Response:**

```json
{
  "success": true,
  "orgRoles": {
    "org123": {
      "orgId": "org123",
      "orgName": "Example Org",
      "role": "admin",
      "updated": "2025-01-14T10:00:00Z"
    },
    "org456": {
      "orgId": "org456",
      "orgName": "Another Org",
      "role": "member",
      "updated": "2025-01-14T09:00:00Z"
    }
  },
  "duration": 123
}
```

---

### Refresh Roles

**Endpoint:** `POST /api/refresh-roles`

**Description:** Refreshes current user's roles from external API

**Authentication:** Session cookie required

**Request Body:**

```json
{
  "forceRefresh": true
}
```

**Response:**

```json
{
  "success": true,
  "roles": ["admin", "member"],
  "message": "Roles refreshed successfully",
  "source": "external_api",
  "cached": false,
  "timestamp": "2025-01-14T10:30:00.000Z"
}
```

---

### Bulk Refresh Roles

**Endpoint:** `POST /api/bulk-refresh-roles`

**Description:** Refresh roles for multiple users (admin only)

**Authentication:** Session cookie required (admin role)

**Request Body:**

```json
{
  "emails": ["<EMAIL>", "<EMAIL>"],
  "forceRefresh": true,
  "batchSize": 10
}
```

**Response:**

```json
{
  "success": true,
  "results": {
    "processed": 2,
    "successful": 2,
    "failed": 0,
    "errors": []
  },
  "details": [
    {
      "email": "<EMAIL>",
      "success": true,
      "roles": ["admin"],
      "cached": false
    },
    {
      "email": "<EMAIL>",
      "success": true,
      "roles": ["member"],
      "cached": false
    }
  ],
  "duration": 1234,
  "requestId": "bulk123"
}
```

---

## Organization Management

### Get Organizations

**Endpoint:** `GET /api/orgs/get/{page}/{limit}`

**Description:** Get paginated list of user's organizations

**Authentication:** Session cookie required

**Parameters:**

- `page`: Page number (0-based)
- `limit`: Results per page (max 100)

**Response:**

```json
{
  "success": true,
  "organizations": [
    {
      "orgId": "org123",
      "orgName": "Example Organization",
      "role": "admin",
      "joinedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 0,
    "limit": 10,
    "total": 15,
    "hasMore": true
  }
}
```

---

### Get Organization Users

**Endpoint:** `GET /api/orgs/users/{orgId}/{page}/{limit}`

**Description:** Get users in a specific organization

**Authentication:** Session cookie required

**Parameters:**

- `orgId`: Organization ID
- `page`: Page number (0-based)
- `limit`: Results per page (max 100)

**Response:**

```json
{
  "success": true,
  "orgUsers": [
    {
      "email": "<EMAIL>",
      "role": "admin",
      "firstName": "John",
      "lastName": "Doe",
      "joinedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "currentUserRole": "admin",
  "pagination": {
    "page": 0,
    "limit": 10,
    "total": 50,
    "hasMore": true
  }
}
```

---

### Accept Organization Invitation

**Endpoint:** `POST /api/orgs/users/accept`

**Description:** Accept an organization invitation

**Authentication:** Session cookie required

**Request Body:**

```json
{
  "orgId": "org123",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Invitation accepted successfully",
  "data": {
    "orgId": "org123",
    "orgName": "Example Organization",
    "role": "member"
  }
}
```

---

### Add User to Organization

**Endpoint:** `POST /api/orgs/users/add`

**Description:** Add a user to an organization (admin only)

**Authentication:** Session cookie required (admin role)

**Request Body:**

```json
{
  "orgId": "org123",
  "email": "<EMAIL>",
  "role": "member"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User added successfully"
}
```

---

### Update User Role

**Endpoint:** `POST /api/orgs/users/update`

**Description:** Update a user's role in an organization

**Authentication:** Session cookie required (admin role)

**Request Body:**

```json
{
  "orgId": "org123",
  "email": "<EMAIL>",
  "newRole": "admin"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Role updated successfully",
  "data": {
    "email": "<EMAIL>",
    "oldRole": "member",
    "newRole": "admin"
  }
}
```

---

### Remove User from Organization

**Endpoint:** `POST /api/orgs/users/remove`

**Description:** Remove a user from an organization

**Authentication:** Session cookie required (admin role)

**Request Body:**

```json
{
  "orgId": "org123",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User removed successfully"
}
```

---

### Organization Providers

Manage providers scoped to a specific organization.

- `GET /api/orgs/{orgId}/providers`: List providers linked to the organization
- `PATCH /api/orgs/{orgId}/providers`: Update provider links or metadata
- `GET /api/orgs/{orgId}/provider`: Get a single provider by query
- `POST /api/orgs/{orgId}/provider`: Create/link a provider
- `PUT /api/orgs/{orgId}/provider`: Update a provider

### Organization Device Access

- `GET /api/orgs/{orgId}/device-access`: View device access configuration
- `PUT /api/orgs/{orgId}/device-access`: Update device access configuration

### Organization Quantum Devices

- `GET /api/orgs/{orgId}/quantum-devices`: List devices visible to the organization

---

## Devices

### List Devices

**Endpoint:** `GET /api/quantum-devices`

**Auth:** Requires session. Org permission: `view` for `orgId`. If caller is qBraid admin, `orgId` is not required.

**Query params:**

- `orgId` (required unless qBraid admin)
- `qbraid_id`, `provider`, `type`, `status`, `isAvailable`, `verified` (optional filters)

**Example:**

```bash
curl \
  "/api/quantum-devices?orgId=org_123&type=QPU&status=ONLINE"
```

---

### Create Device

**Endpoint:** `POST /api/quantum-devices`

**Auth:** Requires session and device `manage` permission for the target org.

**Body:** JSON or multipart/form-data

- JSON: include `organizationId` or `orgId` and device fields
- Multipart fields:
  - `deviceData` (JSON string; must include `orgId` or `organization`)
  - Optional files: `deviceImage`, `pricingFile`, `runInputTypeFile`

**JSON example:**

```json
{
  "orgId": "org_123",
  "name": "IonQ Harmony",
  "provider": "IonQ",
  "type": "QPU",
  "description": "11-qubit trapped ion device"
}
```

Returns `201` with created device. On multipart upload success, response includes uploaded file URLs.

---

### Get Device by ID

**Endpoint:** `GET /api/quantum-devices/{qbraid_id}?orgId=org_123`

**Auth:** Requires session and device `view` permission for `orgId`.

Returns device details. Caching headers are set for short-term caching.

---

### Update Device (partial)

**Endpoint:** `PATCH /api/quantum-devices/{qbraid_id}?orgId=org_123`

**Auth:** Requires session and device `manage` permission for `orgId` (or qBraid admin bypass).

**Body:** JSON or multipart/form-data

- Multipart fields supported:
  - `deviceData` (JSON string)
  - Optional files: `deviceImage`, `pricingFile`, `runInputTypeFile`
  - Optional flags: `deviceImageFileUpdate`, `priceFileUpdate`, `runInputTypeFileUpdate` (all strings "true"/"false")

On success, response includes headers `x-invalidate-cache: quantum-devices` and `x-device-id` for client cache invalidation.

---

### Replace Device (full)

**Endpoint:** `PUT /api/quantum-devices/{qbraid_id}`

**Auth:** Requires session and device `manage` permission for `organizationId/orgId` in JSON body (or qBraid admin).

**Body (JSON):** includes `organizationId` or `orgId` and full replacement fields.

---

### Delete Device

**Endpoint:** `DELETE /api/quantum-devices/{qbraid_id}?orgId=org_123`

**Auth:** Requires session and device `manage` permission for `orgId` (or qBraid admin).

Returns success message. Also sets `x-invalidate-cache: quantum-devices` and `x-device-id` headers.

---

### Approve Pending Device

**Endpoint:** `PATCH /api/quantum-devices/{qbraid_id}/approve`

**Auth:** Requires session. Intended for privileged users (qBraid admins); org context not required.

Returns approved device.

---

### Delete Pending Device Request

**Endpoint:** `DELETE /api/quantum-devices/{qbraid_id}/request?orgId=org_123`

**Auth:** Requires session and device `manage` permission for `orgId` (or qBraid admin).

Cancels/removes a pending device request.

---

### Organization Devices

**Endpoint:** `GET /api/orgs/{orgId}/quantum-devices`

**Auth:** Requires session and device `view` permission for the organization.

Optional filters: `qbraid_id`, `provider`, `type`, `status`, `isAvailable`, `verified`.

---

### Available Devices (public catalog)

**Endpoint:** `GET /api/devices/available?orgId=org_123`

**Auth:** Requires session and device `view` permission for `orgId`.

Optional filters: `provider`, `type`, `deviceStatus`.

Returns a sanitized array of available devices.

---

### Batch Fetch Device Details

**Endpoint:** `POST /api/devices/batch`

**Auth:** Requires session.

**Body:**

```json
{ "deviceIds": ["aws_braket_sv1", "ionq_harmony"] }
```

Returns details for up to 100 devices.

---

## Providers

- `GET /api/providers`: List providers
- `POST /api/providers`: Create a provider
- `PATCH /api/providers/{providerId}`: Update a provider
- `GET /api/providers/{providerId}/status`: Get provider onboarding/status
- `PUT /api/providers/{providerId}/status`: Update provider status
- `GET /api/providers/{providerId}/ownership`: Ownership information
- `GET /api/providers/{providerId}/devices`: List devices owned by provider
- `POST /api/providers/{providerId}/devices`: Create device for provider
- `GET /api/providers/{providerId}/devices/{deviceId}`: Get provider device
- `PUT /api/providers/{providerId}/devices/{deviceId}`: Update provider device
- `DELETE /api/providers/{providerId}/devices/{deviceId}`: Remove provider device

---

## Device Access Requests

- `GET /api/device-access-requests`: List requests
- `POST /api/device-access-requests`: Create a new access request
- `GET /api/device-access-requests/{requestId}`: Get a specific request
- `PUT /api/device-access-requests/{requestId}`: Update request status/details

---

## Quantum Jobs

- `GET /api/quantum-jobs/all-by-provider`: List jobs grouped by provider
- `GET /api/quantum-jobs/{qbraid_id}`: Get a job by ID

---

## Activity Logs

- `GET /api/activity-logs`: List activity log entries
- `POST /api/activity-logs`: Create a log entry

---

## Health & Metrics

- `GET /api/health/redis`: Redis connection and health status
- `GET /api/cache-stats`: Cache statistics and diagnostics

---

## Token Management

### Debug Tokens

**Endpoint:** `GET /api/debug/tokens`

**Description:** Debug endpoint to check stored Cognito tokens (dev only)

**Authentication:** Session cookie required

**Response:**

```json
{
  "success": true,
  "tokens": {
    "hasAccessToken": true,
    "hasIdToken": true,
    "accessTokenPreview": "eyJraWQiOiJXNTNQR3...",
    "idTokenPreview": "eyJraWQiOiJYNTNQR3..."
  },
  "userEmail": "<EMAIL>",
  "duration": 45
}
```

---

## Debug Endpoints

### Session Storage Test

**Endpoint:** `GET /api/debug/session-storage`

**Description:** Test Redis-based session storage (development only)

**Authentication:** None required

**Response:**

```json
{
  "timestamp": "2025-01-14T10:30:00.000Z",
  "redis": {
    "available": true,
    "healthy": true
  },
  "storageMode": "redis",
  "tests": {
    "sessionCreation": true,
    "sessionVerification": true,
    "tokenStorage": true,
    "tokenRetrieval": true,
    "sessionCleanup": true
  },
  "performance": {
    "totalTime": 234,
    "sessionCreateTime": 45,
    "tokenStoreTime": 23,
    "tokenRetrieveTime": 12
  },
  "summary": {
    "passed": 5,
    "total": 5,
    "success": true,
    "recommendation": "Redis-based session storage is working correctly"
  }
}
```

---

### Auth Configuration Test

**Endpoint:** `GET /api/debug/auth-test`

**Description:** Test Amplify configuration (development only)

**Authentication:** None required

**Response:**

```json
{
  "success": true,
  "amplifyConfig": {
    "hasAuth": true,
    "hasCognito": true,
    "userPoolId": "us-east-1_...",
    "clientId": "7abc123...",
    "hasStorage": true
  },
  "environment": {
    "userPoolId": true,
    "clientId": true,
    "userPoolIdValue": "us-east-1_...",
    "clientIdValue": "7abc123..."
  },
  "duration": 12
}
```

---

## Webhook Endpoints

### Update Roles Webhook

**Endpoint:** `POST /api/update-roles`

**Description:** Webhook endpoint for external role updates

**Authentication:** Bearer token required

**Headers:**

```
Authorization: Bearer {webhook-token}
Content-Type: application/json
```

**Request Body (Update Organization Role):**

```json
{
  "email": "<EMAIL>",
  "action": "update-org-role",
  "orgId": "org123",
  "newRole": "admin",
  "orgName": "Example Organization"
}
```

**Request Body (Remove from Organization):**

```json
{
  "email": "<EMAIL>",
  "action": "remove-from-org",
  "orgId": "org123"
}
```

**Request Body (Sync Organization Roles):**

```json
{
  "email": "<EMAIL>",
  "action": "sync-org-roles",
  "orgRoles": {
    "org123": {
      "role": "admin",
      "orgName": "Example Org"
    },
    "org456": {
      "role": "member",
      "orgName": "Another Org"
    }
  }
}
```

**Request Body (Invalidate Cache):**

```json
{
  "email": "<EMAIL>",
  "action": "invalidate"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Role update successful",
  "data": {
    "email": "<EMAIL>",
    "action": "update-org-role",
    "orgId": "org123",
    "newRole": "admin"
  },
  "timestamp": "2025-01-14T10:30:00.000Z"
}
```

**Error Responses:**

- `401 Unauthorized`: Invalid or missing bearer token
- `400 Bad Request`: Invalid request body or action
- `500 Internal Server Error`: Update failed

---

### Get Webhook Documentation

**Endpoint:** `GET /api/update-roles`

**Description:** Returns webhook documentation and examples

**Authentication:** None required

**Response:**

```json
{
  "service": "Enhanced Role Update Webhook",
  "description": "Webhook endpoint for external role updates with organization-specific support",
  "authentication": "Bearer token in Authorization header",
  "endpoints": {
    "POST /api/update-roles": "Update user roles"
  },
  "actions": {
    "update-org-role": {
      "description": "Update user role in specific organization",
      "required": ["email", "orgId", "newRole"],
      "optional": ["orgName"],
      "example": {
        "email": "<EMAIL>",
        "action": "update-org-role",
        "orgId": "org123",
        "newRole": "admin",
        "orgName": "Example Organization"
      }
    }
  },
  "timestamp": "2025-01-14T10:30:00.000Z"
}
```

---

## Error Handling

All endpoints follow a consistent error response format:

```json
{
  "error": "Human-readable error message",
  "success": false,
  "requestId": "optional-request-id",
  "details": "optional-technical-details"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request parameters or body
- `401 Unauthorized`: Authentication required or invalid session
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

### Rate Limiting

API endpoints implement rate limiting to prevent abuse:

- Authentication endpoints: 5 requests per minute per IP
- Data endpoints: 100 requests per minute per user
- Webhook endpoints: 1000 requests per minute per token

### CORS Configuration

The API supports CORS for the following origins:

- Development: `http://localhost:3000`
- Production: `https://partner-dashboard.qbraid.com`

Custom headers allowed:

- `Content-Type`
- `Authorization`
- `id-token`
- `email`

---

## Best Practices

### 1. Authentication

- Always include session cookies with requests
- Handle 401 responses by redirecting to login
- Use CSRF tokens for all form submissions

### 2. Error Handling

- Implement exponential backoff for retries
- Log error details for debugging
- Show user-friendly error messages

### 3. Performance

- Use caching where appropriate
- Implement pagination for large datasets
- Minimize API calls by batching when possible

### 4. Security

- Never expose tokens in client-side code
- Validate all input on the server
- Use HTTPS in production
- Implement proper CORS policies

---

## Migration Guide

### From Cookie-Based to Redis Sessions

1. Sessions are now stored in Redis with configurable TTL (default 24 hours)
2. Session cookies only contain session IDs
3. Tokens are stored in Redis, linked to session IDs
4. Automatic fallback to JWT cookies if Redis unavailable

### API Changes

1. All role endpoints now support organization context
2. Permission checks are organization-aware
3. New webhook endpoints for external updates
4. Enhanced caching with `orgroles:` prefix

---

## Support

For API support and questions:

- Documentation: [https://docs.qbraid.com](https://docs.qbraid.com)
- Support Email: <EMAIL>
- GitHub Issues: [https://github.com/qbraid/partner-dashboard](https://github.com/qbraid/partner-dashboard)

---

_Last updated: 2025-08-08_
_API Version: 2.0_
