# Auth and Permission Implementation Guide

## Overview

This guide documents the comprehensive authentication and authorization system implemented for the Partner Dashboard API routes. The system provides role-based access control (RBAC) with organization-specific permissions, leveraging cached external role data for efficient permission checks.

## Architecture

### 1. Auth Middleware (`lib/auth-middleware.ts`)

The auth middleware provides specialized authentication functions for different use cases:

```typescript
// Basic authentication
requireAuth()

// Team management with role validation
requireTeamManagementPermissions(orgId, action, targetRole?, newRole?)

// Device-specific permissions (organization-scoped)
requireDevicePermission(action, orgId)

// Provider management permissions (organization-scoped)
requireProviderPermissions(action, orgId)

// Self-service utility
isSelfService(sessionEmail, targetEmail)
```

### 2. Permission System (`lib/permissions.ts`)

**Role Hierarchy:**

- `viewer` (1) - Read-only access
- `member` (2) - Basic user permissions
- `manager` (3) - Team management
- `admin` (4) - Full administrative access
- `superadmin` (5) - System-wide administration
- `owner` (6) - Organization owner

**Permission Types:**

```typescript
enum Permission {
  ViewDevices = 'view:devices',
  ManageDevices = 'manage:devices',
  ViewProfile = 'view:profile',
  EditProfile = 'edit:profile',
  ViewTeam = 'view:team',
  ManageTeam = 'manage:team',
  ViewEarnings = 'view:earnings',
  ManageEarnings = 'manage:earnings',
  ViewJobs = 'view:jobs',
  ManageJobs = 'manage:jobs',
  AdminAccess = 'admin:access',
  ViewProviders = 'view:providers',
  ManageProviders = 'manage:providers',
}
```

## Implementation Examples

### 1. Team Management Routes ✅

**Updated Routes:**

- `/api/orgs/users/update` - Role updates with validation
- `/api/orgs/users/add` - User invitations
- `/api/orgs/users/remove` - User removal (with self-service support)

**Example Implementation:**

```typescript
// Check team management permissions with role validation
const { session, currentUserRole, error } = await requireTeamManagementPermissions(
  validatedData.orgId,
  'update_role',
  validatedData.oldRole,
  validatedData.role,
);

if (error) return error;

// Prevent self-role elevation
if (
  isSelfService(session.email, validatedData.email) &&
  validatedData.oldRole !== validatedData.role
) {
  return NextResponse.json(
    {
      error: 'Self-role change forbidden',
      code: 'SELF_ROLE_CHANGE_FORBIDDEN',
    },
    { status: 403 },
  );
}
```

### 2. Device Management Routes ✅

**Updated Routes:**

- `/api/quantum-devices` - GET (view) and POST (create)
- `/api/quantum-devices/[id]` - PATCH (update) and DELETE (remove)

**Example Implementation:**

```typescript
// For viewing devices
const { session, error } = await requireDevicePermissions('view');
if (error) return error;

// For managing devices
const { session, error } = await requireDevicePermissions('manage');
if (error) return error;
```

### 3. Profile Routes (Ready to implement)

**Routes to Update:**

- `/api/user/profile` - GET and PUT

**Implementation Pattern:**

```typescript
// For viewing profile
const { session, error } = await requirePermissions([Permission.ViewProfile]);
if (error) return error;

// For editing profile (with self-service check)
const { session, error } = await requirePermissions([Permission.EditProfile]);
if (error) return error;

// Allow users to edit their own profile even without EditProfile permission
if (isSelfService(session.email, targetEmail)) {
  // Allow self-service
} else {
  // Require EditProfile permission
}
```

## How to Apply to Other Routes

### Step 1: Add Import

```typescript
import {
  requireAuth,
  requirePermissions,
  requireOrgPermissions,
  requireTeamManagementPermissions,
  requireDevicePermissions,
  requireAdminPermissions,
  isSelfService,
} from '@/lib/auth-middleware';
import { Permission } from '@/types/auth';
```

### Step 2: Replace Basic Auth Check

**Before:**

```typescript
const session = await getSession();
if (!session?.email) {
  return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
}
```

**After:**

```typescript
// Choose the appropriate check based on your needs:

// Basic auth only
const { session, error } = await requireAuth();
if (error) return error;

// Specific permissions
const { session, error } = await requirePermissions([Permission.ViewDevices]);
if (error) return error;

// Organization-specific
const { session, orgRole, error } = await requireOrgPermissions({
  orgId: 'org_123',
  requiredPermissions: [Permission.ManageTeam],
  allowOwnerOverride: true,
});
if (error) return error;

// Team management with role validation
const { session, currentUserRole, error } = await requireTeamManagementPermissions(
  orgId,
  'update_role',
  currentRole,
  newRole,
);
if (error) return error;
```

### Step 3: Handle Self-Service Cases

For routes where users can modify their own data:

```typescript
const isOwnData = isSelfService(session.email, targetEmail);

if (isOwnData) {
  // Allow self-service operations
} else {
  // Require admin permissions
  const { error } = await requirePermissions([Permission.ManageTeam]);
  if (error) return error;
}
```

## Route Permission Matrix

| Route                       | Action | Required Permission          | Notes                              |
| --------------------------- | ------ | ---------------------------- | ---------------------------------- |
| `/api/user/profile`         | GET    | `ViewProfile`                | Self-service allowed               |
| `/api/user/profile`         | PUT    | `EditProfile`                | Self-service allowed               |
| `/api/orgs/users/add`       | POST   | `ManageTeam` + org member    | Role hierarchy checked             |
| `/api/orgs/users/update`    | POST   | `ManageTeam` + org member    | Role validation, no self-elevation |
| `/api/orgs/users/remove`    | POST   | `ManageTeam` OR self-service | Self-removal always allowed        |
| `/api/quantum-devices`      | GET    | `ViewDevices`                |                                    |
| `/api/quantum-devices`      | POST   | `ManageDevices`              |                                    |
| `/api/quantum-devices/[id]` | PATCH  | `ManageDevices`              |                                    |
| `/api/quantum-devices/[id]` | DELETE | `ManageDevices`              |                                    |
| `/api/activity-logs`        | GET    | `ViewTeam` + org member      | Org-specific                       |
| `/api/cache-stats`          | GET    | `AdminAccess`                | Admin only                         |

## Error Responses

The auth middleware provides consistent error responses:

```typescript
// 401 - Authentication required
{
  "error": "Authentication required",
  "message": "Valid session required",
  "code": "AUTH_REQUIRED"
}

// 403 - Insufficient permissions
{
  "error": "Insufficient permissions",
  "message": "Required permissions: manage:team",
  "code": "INSUFFICIENT_PERMISSIONS"
}

// 403 - Organization specific
{
  "error": "Not a member of organization",
  "message": "User not found in organization org_123",
  "code": "NOT_ORG_MEMBER"
}

// 403 - Role management
{
  "error": "Cannot change user role",
  "message": "You cannot promote someone to a role equal or higher than your own",
  "code": "ROLE_CHANGE_FORBIDDEN",
  "currentUserRole": "admin",
  "targetUserRole": "member",
  "requestedRole": "owner"
}
```

## Testing the Implementation

### 1. Test Authentication

```bash
# Should require authentication
curl -X GET http://localhost:3000/api/quantum-devices
# Expected: 401 Unauthorized

# With valid session cookie
curl -X GET http://localhost:3000/api/quantum-devices \
  -H "Cookie: session=valid_session_token"
# Expected: 200 OK (if user has ViewDevices permission)
```

### 2. Test Permission Levels

```bash
# Test with viewer role (should work)
curl -X GET http://localhost:3000/api/quantum-devices \
  -H "Cookie: session=viewer_session"

# Test with viewer role trying to create (should fail)
curl -X POST http://localhost:3000/api/quantum-devices \
  -H "Cookie: session=viewer_session" \
  -d '{"name": "test"}'
# Expected: 403 Insufficient permissions
```

### 3. Test Role Management

```bash
# Admin trying to promote user to owner (should fail)
curl -X POST http://localhost:3000/api/orgs/users/update \
  -H "Cookie: session=admin_session" \
  -d '{
    "email": "<EMAIL>",
    "role": "owner",
    "oldRole": "member",
    "orgId": "org_123",
    "orgName": "Test Org"
  }'
# Expected: 403 Role change forbidden
```

## Next Steps

1. **Apply to remaining routes:**
   - Update all remaining API routes following the patterns shown
   - Focus on routes that modify data first (POST, PUT, PATCH, DELETE)

2. **Organization-specific permissions:**
   - Update routes that should be organization-scoped
   - Ensure users can only access their organization's data

3. **Admin-only routes:**
   - Identify and protect admin-only functionality
   - Add `requireAdminPermissions()` where appropriate

4. **Monitoring and logging:**
   - Add permission check logging for security monitoring
   - Track unauthorized access attempts

## Security Considerations

1. **Defense in depth:** Permission checks at both API and UI levels
2. **Principle of least privilege:** Users get minimum required permissions
3. **Audit logging:** All permission changes are logged
4. **Self-service safety:** Users can't elevate their own permissions
5. **Organization isolation:** Users can only access their organization's data

This implementation provides a robust, scalable authentication and authorization system that can be easily extended as new features are added.
