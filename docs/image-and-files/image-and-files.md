Of course. Here is a comprehensive guide to the `lib/image` and `lib/files` modules, detailing their structure, purpose, and how to use them.

---

## Comprehensive Guide: File and Image Handling in `lib/image` and `lib/files`

This guide covers the two main modules responsible for file and image uploads, validation, security, and management within the application: `lib/image` and `lib/files`. Both modules are designed with a clear separation of concerns between client-side and server-side logic, promoting security, reusability, and maintainability.

### Table of Contents

1.  [High-Level Architecture](#high-level-architecture)
2.  [Shared Configuration (`lib/constant.ts`)](#shared-configuration-libconstantts)
3.  [Image Handling (`lib/image`)](#image-handling-libimage)
    - [Client-Side (`image-upload-client.ts`)](#client-side-image-upload-clientts)
    - [Server-Side & S3 Logic (`image-upload-server.ts`)](#server-side--s3-logic-image-upload-serverts)
    - [Core Upload Orchestrator (`image-upload.ts`)](#core-upload-orchestrator-image-uploadts)
    - [Image-Specific Features](#image-specific-features)
4.  [General File Handling (`lib/files`)](#general-file-handling-libfiles)
    - [Security First (`file-security.ts`)](#security-first-file-securityts)
    - [Client-Side (`file-upload-client.ts`)](#client-side-file-upload-clientts)
    - [Server-Side & S3 Logic (`file-upload-server.ts`)](#server-side--s3-logic-file-upload-serverts)
    - [Core Upload Orchestrator (`file-upload.ts`)](#core-upload-orchestrator-file-uploadts)
5.  [Usage Examples](#usage-examples)
    - [Uploading a Provider Logo (Image)](#uploading-a-provider-logo-image)
    - [Uploading a General Document (File)](#uploading-a-general-document-file)
    - [Batch Uploading Files](#batch-uploading-files)
    - [Client-Side Validation](#client-side-validation)
6.  [Best Practices](#best-practices)

---

### High-Level Architecture

Both `lib/image` and `lib/files` follow a similar, three-tiered architecture:

1.  **Client-Side Utilities (`*-client.ts`)**:
    - **Environment**: Browser.
    - **Purpose**: Provide functions for UI components, such as file validation, preview generation, and formatting. This gives immediate feedback to the user without a server round-trip.
    - **Key Functions**: `validateClientFile`, `getFilePreviewUrl`, `formatFileSize`.

2.  **Server-Side & S3 Logic (`*-server.ts`)**:
    - **Environment**: Node.js (Next.js API routes).
    - **Purpose**: Handle the secure, server-side aspects of file management. This includes robust validation against server-side constants, S3 client configuration, connectivity checks, and key generation for organized storage.
    - **Key Functions**: `validateServerFile`, `validateS3Config`, `generateSecureFileKey`, `getS3Client`.

3.  **Core Upload Orchestrator (`*.ts`)**:
    - **Environment**: Node.js (Next.js API routes).
    - **Purpose**: This is the main entry point for API routes. It orchestrates the entire upload process by calling functions from the server-side logic, interacting with the AWS SDK (S3), and returning a standardized result.
    - **Key Functions**: `uploadImageToS3`, `uploadFileToS3`, `uploadMultipleImagesToS3`, `uploadMultipleFilesToS3`.

This separation ensures that client-side code is lightweight and fast, server-side code is secure and authoritative, and the core logic is clean and reusable.

---

### Shared Configuration (`lib/constant.ts`)

Both modules rely on shared constants defined in `lib/constant.ts` to ensure consistency across the application.

```typescript
// lib/constant.ts (example)
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  // ... other file types
];
export const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;
// ... other AWS and app constants
```

**Key Constants:**

- `MAX_FILE_SIZE`: The global maximum file size for uploads.
- `ALLOWED_IMAGE_TYPES` / `ALLOWED_FILE_TYPES`: An array of MIME types permitted for upload. This is the first line of defense against malicious file uploads.
- `BUCKET_NAME`: The S3 bucket name, sourced from environment variables.

---

### Image Handling (`lib/image`)

This module is specifically tailored for image uploads, including features like client-side resizing and thumbnail generation.

#### Client-Side (`image-upload-client.ts`)

This file contains utilities meant to run in the user's browser.

**Key Functions:**

- `validateClientFile(file: File, maxSizeBytes?, allowedTypes?)`: Validates a `File` object against size and type. Provides immediate feedback.
  ```typescript
  const file = fileInput.files[0];
  const validation = validateClientFile(file);
  if (!validation.valid) {
    alert(validation.error); // e.g., "File too large. Maximum size: 10MB"
  }
  ```
- `getFilePreviewUrl(file: File)`: Creates a local `blob:` URL for image previews.
  ```typescript
  const previewUrl = getFilePreviewUrl(file);
  // Use previewUrl in an <img src={previewUrl} /> tag
  ```
- `cleanupPreviewUrl(url: string)`: Revokes the `blob:` URL to free up memory. **Crucial for preventing memory leaks.**
  ```typescript
  // When the component unmounts or the preview is no longer needed
  cleanupPreviewUrl(previewUrl);
  ```
- `resizeImage(file: File, maxWidth?, maxHeight?, quality?)`: Asynchronously resizes an image on the client side before upload, reducing bandwidth and upload time.
  ```typescript
  const optimizedFile = await resizeImage(file, 800, 600, 0.8);
  // Now upload optimizedFile instead of the original file
  ```
- `generateThumbnail(file: File, size?)`: Creates a small, square thumbnail for gallery views.
- `formatFileSize(bytes: number)`: Formats a byte count into a human-readable string (e.g., "5.2 MB").

#### Server-Side & S3 Logic (`image-upload-server.ts`)

This file handles the server-side responsibilities for image uploads.

**Key Functions:**

- `validateServerFile(file: File, maxSizeBytes?, allowedTypes?)`: A server-side re-validation of the file. **Never trust client-side validation.**
- `validateS3Config()`: Checks if all required AWS environment variables are set and if the S3 bucket name is valid.
- `testS3Connection()`: Pings the S3 bucket to ensure the application can connect.
- `generateSecureFileKey(folderName, orgId, providerName, uploadType, themeExtension?)`: Creates a unique, organized S3 object key.
  - **Example Key**: `partner-dashboard/org-123/providers/my-provider-logo.png`
  - This structure is excellent for:
    - **Multi-tenancy**: Isolating files by `orgId`.
    - **Organization**: Grouping files by `folderName` (e.g., `providers`, `users`).
    - **Uniqueness**: Using `providerName` and a timestamp/random string to prevent overwrites.
- `getS3Client()`, `getBucketName()`: Getters for the configured S3 client and bucket name.

#### Core Upload Orchestrator (`image-upload.ts`)

This is the primary file you'll import into your API routes to handle image uploads.

**Key Interfaces:**

- `ImageUploadConfig`: Configuration object for an upload.
  ```typescript
  interface ImageUploadConfig {
    uploadType: string; // e.g., 'provider-logo', 'user-avatar'
    orgId: string; // Tenant ID
    userId: string; // User ID
    providerName: string; // Descriptive name for the file
    folderName: string; // S3 folder, e.g., 'providers'
    // ... other optional params
  }
  ```
- `ImageUploadResult`: The result of an upload attempt.
  ```typescript
  interface ImageUploadResult {
    success: boolean;
    url?: string; // Public S3 URL
    key?: string; // S3 object key
    error?: string;
    metadata?: {
      /* file details */
    };
  }
  ```

**Key Functions:**

- `uploadImageToS3(file: File, config: ImageUploadConfig)`: Uploads a single image.
  - **Process**:
    1.  Validates S3 configuration.
    2.  Re-validates the file on the server.
    3.  Generates a secure S3 key using `generateSecureFileKey`.
    4.  Prepares S3 `Metadata` (original name, user ID, etc.).
    5.  Uploads the file buffer to S3 using `PutObjectCommand`.
    6.  Returns the public S3 URL and metadata.

  ```typescript
  // In an API route (e.g., /api/upload/provider-logo)
  import { uploadImageToS3 } from '@/lib/image/image-upload';
  import { ImageUploadConfig } from '@/lib/image/image-upload';

  export async function POST(request: Request) {
    const formData = await request.formData();
    const file = formData.get('logo') as File;

    const config: ImageUploadConfig = {
      uploadType: 'provider-logo',
      orgId: 'org-123',
      userId: 'user-456',
      providerName: 'My Awesome Provider',
      folderName: 'providers',
    };

    const result = await uploadImageToS3(file, config);
    return NextResponse.json(result);
  }
  ```

- `uploadMultipleImagesToS3(files, config)`: Handles batch uploads of multiple images, returning an array of results.

#### Image-Specific Features

- **Client-Side Optimization**: The `resizeImage` function is a powerful tool for improving performance by reducing file dimensions and quality before upload.
- **Theme Support**: The `generateSecureFileKey` function's `themeExtension` parameter is designed for use cases like uploading both a light and dark version of a logo (e.g., `my-provider-dark-logo.png`).

---

### General File Handling (`lib/files`)

This module provides a robust and secure system for uploading and managing any type of file, with a strong emphasis on security scanning.

#### Security First (`file-security.ts`)

This is a critical component that performs a deep security scan on any file before it's uploaded.

**Key Interfaces:**

- `FileSecurityConfig`: A highly configurable object for the security scanner.
  ```typescript
  interface FileSecurityConfig {
    maxFileSize: number;
    allowedTypes: string[];
    allowedExtensions: string[];
    scanForMalware: boolean; // Conceptual, would integrate with a real scanner
    validateFileSignature: boolean; // Checks "magic numbers"
    checkForExecutableContent: boolean; // Scans text files for dangerous patterns
    // ... other options
  }
  ```
- `SecurityScanResult`: The detailed output of a security scan.
  ```typescript
  interface SecurityScanResult {
    safe: boolean; // Overall safety verdict
    issues: SecurityIssue[]; // List of all found issues
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  }
  ```

**Key Functions:**

- `scanFileSecurity(file: File, config?)`: The main function that runs a comprehensive battery of security checks.
  - **Basic Validation**: Size, type, extension.
  - **File Signature Validation**: Checks if the file's content matches its declared MIME type (e.g., a `.png` file actually starts with PNG bytes). This prevents file type spoofing.
  - **Content Analysis**: For text-based files (`.js`, `.py`, `.txt`), it scans for dangerous patterns like `eval()`, `exec()`, SQL injection snippets, and suspiciously high entropy (which can indicate obfuscation or encryption).
  - **Filename Validation**: Checks for path traversal attempts (`../`), reserved Windows names (`con`, `prn`), and dangerous characters.
- `sanitizeFileName(fileName: string)`: Cleans a filename to be S3-safe.
- `generateFileHash(file: File)`: Creates a SHA-256 or SHA-512 hash of the file, useful for integrity checking or deduplication.

#### Client-Side (`file-upload-client.ts`)

Similar to the image client, but for general files.

**Key Functions:**

- `validateClientFile(file, maxSize?, allowedTypes?)`: Client-side validation.
- `getFilePreviewUrl(file)`, `cleanupPreviewUrl(url)`: For previewable files (images, PDFs, etc.).
- `generateSafeFileName(fileName: string)`: Creates a unique filename on the client to avoid collisions.
- `getFileIcon(fileType: string)`: Returns an emoji icon based on the file type (e.g., 📄 for PDF, 📊 for Excel).
- `readFileAsDataURL(file)`, `readFileAsText(file)`: Utilities for reading file content on the client.

#### Server-Side & S3 Logic (`file-upload-server.ts`)

This file mirrors the structure of `image-upload-server.ts` but is generalized for any file type.

**Key Functions:**

- `validateServerFile()`, `validateS3Config()`, `testS3Connection()`: Same as their image counterparts.
- `generateSecureFileKey(folderName, orgId, providerName, uploadType, themeExtension?, fileName?)`: The key difference here is the `fileName` parameter, which is used to preserve the original file's extension.
  - **Example Key**: `partner-dashboard/org-123/documents/annual-report-2024.pdf`
- `getFileCategory(mimeType: string)`: Categorizes a file into 'image', 'video', 'document', etc., for better organization.
- `validateFileExtension(fileName: string, allowedTypes: string[])`: A utility to validate a file's extension against its MIME type.

#### Core Upload Orchestrator (`file-upload.ts`)

This is the main entry point for general file uploads.

**Key Interfaces:**

- `FileUploadConfig`: Configuration for a file upload. It's more flexible than `ImageUploadConfig`.
  ```typescript
  interface FileUploadConfig {
    uploadType: string;
    orgId: string;
    userId: string;
    folderName: string;
    isPublic?: boolean; // Sets S3 ACL to 'public-read'
    cacheControl?: string; // e.g., 'max-age=31536000'
    contentDisposition?: string; // e.g., 'attachment; filename="file.pdf"'
    // ... other params
  }
  ```
- `FileUploadResult`: The result of a file upload.

**Key Functions:**

- `uploadFileToS3(file: File, config: FileUploadConfig)`: The core function for uploading any file.
  - The process is very similar to `uploadImageToS3`, but it uses the generalized functions from `file-upload-server.ts`.
  - It supports additional S3 parameters like `ACL` for public access and `ContentDisposition` to control download behavior in the browser.
- `uploadMultipleFilesToS3(files, config)`: For batch uploads.
- `generateSignedUrl(fileKey, expiresIn?)`: Creates a time-limited, pre-signed URL for accessing private files securely.
- `deleteFileFromS3(fileKey)`: Deletes a file from S3.
- `fileExistsInS3(fileKey)`: Checks if a file exists in S3.
- `getFileMetadata(fileKey)`: Retrieves metadata (size, type, last modified) for a file in S3.

---

### Usage Examples

#### Uploading a Provider Logo (Image)

**1. Client-Side Component (`React`)**

```tsx
'use client';
import { useState } from 'react';
import { validateClientFile, resizeImage, formatFileSize } from '@/lib/image/image-upload-client';
import { MAX_FILE_SIZE, ALLOWED_IMAGE_TYPES } from '@/lib/constant';

export function ProviderLogoUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [status, setStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string>('');

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // 1. Client-side validation
    const validation = validateClientFile(selectedFile, MAX_FILE_SIZE, ALLOWED_IMAGE_TYPES);
    if (!validation.valid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    // 2. Optional: Resize image
    const optimizedFile = await resizeImage(selectedFile, 400, 400, 0.8);

    setFile(optimizedFile);
    setPreviewUrl(URL.createObjectURL(optimizedFile));
    setError('');
  };

  const handleUpload = async () => {
    if (!file) return;

    setStatus('uploading');
    const formData = new FormData();
    formData.append('logo', file);

    try {
      const response = await fetch('/api/providers/logo', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setStatus('success');
        console.log('Logo uploaded:', result.url);
      } else {
        setStatus('error');
        setError(result.error || 'Upload failed');
      }
    } catch (err) {
      setStatus('error');
      setError('An unexpected error occurred');
    }
  };

  return (
    <div>
      <input type="file" onChange={handleFileChange} accept="image/*" />
      {previewUrl && (
        <img src={previewUrl} alt="Logo preview" style={{ width: '100px', height: '100px' }} />
      )}
      {file && (
        <p>
          Selected: {file.name} ({formatFileSize(file.size)})
        </p>
      )}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button onClick={handleUpload} disabled={status === 'uploading'}>
        {status === 'uploading' ? 'Uploading...' : 'Upload Logo'}
      </button>
      {status === 'success' && <p>Upload successful!</p>}
    </div>
  );
}
```

**2. Server-Side API Route (`app/api/providers/logo/route.ts`)**

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { uploadImageToS3 } from '@/lib/image/image-upload';
import { requireAuth } from '@/lib/auth-middleware';

export async function POST(request: NextRequest) {
  // 1. Authenticate user
  const { session } = await requireAuth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('logo') as File;

    if (!file || file.size === 0) {
      return NextResponse.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    const config = {
      uploadType: 'provider-logo',
      orgId: session.orgId, // Assuming orgId is in the session
      userId: session.userId,
      providerName: 'some-provider-name', // This would come from request body or context
      folderName: 'providers',
    };

    const result = await uploadImageToS3(file, config);

    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Logo upload error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
}
```

#### Uploading a General Document (File)

This example includes the security scan.

**1. Client-Side Component (`React`)**

```tsx
// Similar to the image upload, but might use different client utils
import { validateClientFile, generateSafeFileName } from '@/lib/files/file-upload-client';
// ...
```

**2. Server-Side API Route (`app/api/documents/upload/route.ts`)**

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { uploadFileToS3 } from '@/lib/files/file-upload';
import { scanFileSecurity, DEFAULT_SECURITY_CONFIG } from '@/lib/files/file-security';
import { requireAuth } from '@/lib/auth-middleware';

export async function POST(request: NextRequest) {
  const { session } = await requireAuth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('document') as File;

    if (!file || file.size === 0) {
      return NextResponse.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    // 1. Perform a comprehensive security scan
    const securityScan = await scanFileSecurity(file, DEFAULT_SECURITY_CONFIG);
    if (!securityScan.safe) {
      console.warn('Security scan failed:', securityScan.issues);
      return NextResponse.json(
        {
          success: false,
          error: `File upload blocked due to security concerns. Risk level: ${securityScan.riskLevel}`,
          issues: securityScan.issues,
        },
        { status: 403 },
      ); // Forbidden
    }

    // 2. If safe, proceed with upload
    const config = {
      uploadType: 'user-document',
      orgId: session.orgId,
      userId: session.userId,
      folderName: 'documents',
      isPublic: false, // Private document
      contentDisposition: `attachment; filename="${file.name}"`, // Force download
    };

    const result = await uploadFileToS3(file, config);

    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Document upload error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
}
```

#### Batch Uploading Files

The `uploadMultipleFilesToS3` function simplifies handling multiple file inputs.

**API Route Example (`app/api/providers/assets/route.ts`)**

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { uploadMultipleFilesToS3, extractFilesFromFormData } from '@/lib/files/file-upload';
import { requireAuth } from '@/lib/auth-middleware';

export async function POST(request: NextRequest) {
  const { session } = await requireAuth();
  if (!session) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  try {
    const formData = await request.formData();
    const filesToUpload = extractFilesFromFormData(formData, ['logo', 'document', 'banner']);

    if (filesToUpload.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid files provided' },
        { status: 400 },
      );
    }

    const baseConfig = {
      orgId: session.orgId,
      userId: session.userId,
      folderName: 'providers',
      providerName: 'my-provider', // Get from context
    };

    const result = await uploadMultipleFilesToS3(filesToUpload, baseConfig);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Batch upload error:', error);
    return NextResponse.json({ success: false, error: 'Batch upload failed' }, { status: 500 });
  }
}
```

#### Client-Side Validation

Always provide immediate feedback to the user.

```tsx
import {
  validateClientFile,
  getAllowedTypesClient,
  getMaxFileSizeClient,
} from '@/lib/files/file-upload-client';

function MyFileUploader() {
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const allowedTypes = getAllowedTypesClient(); // e.g., ['application/pdf', ...]
    const maxSize = getMaxFileSizeClient(); // e.g., 10MB

    const { valid, error } = validateClientFile(file, maxSize, allowedTypes);

    if (!valid) {
      alert(`Invalid file: ${error}`);
      e.target.value = ''; // Clear the input
      return;
    }

    // Proceed with upload or preview
    setFile(file);
  };

  return <input type="file" onChange={handleFileSelect} />;
}
```

---

### Best Practices

1.  **Always Validate on Both Sides**: Use client-side validation for a good user experience, but **always** re-validate on the server. Never trust the client.
2.  **Use Security Scans for General Files**: For any file upload that isn't a known-safe image, run it through `scanFileSecurity`. This is your best defense against malicious uploads.
3.  **Organize S3 Keys**: Use the `generateSecureFileKey` function to create a structured S3 bucket. This is invaluable for multi-tenancy, finding files, and setting lifecycle policies.
4.  **Clean Up Client-Side URLs**: Always call `cleanupPreviewUrl` when you're done with a `blob:` URL to prevent memory leaks in the browser.
5.  **Handle Errors Gracefully**: The upload functions return detailed error objects. Log these on the server and provide clear, user-friendly error messages on the client.
6.  **Use Environment Variables**: Never hardcode credentials or bucket names. The `validateS3Config` function helps ensure your setup is correct.
7.  **Leverage Batch Uploads**: For forms with multiple file inputs, use the batch upload functions to reduce complexity and improve user experience by uploading everything in one go.
8.  **Be Mindful of Public vs. Private**: Use the `isPublic` flag in `FileUploadConfig` appropriately. Set `ACL: 'public-read'` for public assets (like profile pictures) and generate signed URLs for private assets (like user documents).

By following this guide and leveraging the provided modules, you can build a robust, secure, and user-friendly file and image upload system within your application.
